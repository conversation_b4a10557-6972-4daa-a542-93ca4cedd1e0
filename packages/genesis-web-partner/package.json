{"name": "genesis-web-partner", "version": "1.0.0", "private": true, "scripts": {"start": "cross-env PROGRESS=none pnpm max dev", "integrate:rspack": "cross-env NODE_ENV=production pnpm rspack serve -c rspack/rspack.prd.js --watch", "start:dev": "cross-env PORT=8080 UMI_ENV=prod PROGRESS=none PROXY_CHANNEL=dev pnpm max dev", "start:test1": "cross-env PORT=8080 UMI_ENV=prod PROGRESS=none PROXY_CHANNEL=test1 pnpm max dev", "start:sit": "cross-env PORT=8080 UMI_ENV=prod PROGRESS=none PROXY_CHANNEL=sit pnpm max dev", "start:stg": "cross-env PORT=8080 UMI_ENV=prod PROGRESS=none PROXY_CHANNEL=stg pnpm max dev", "start:ci": "cross-env PORT=8080 UMI_ENV=prod PROGRESS=none PROXY_CHANNEL=ci pnpm max dev", "build": "cross-env UMI_ENV=prod PROGRESS=none pnpm max build", "integrate": "pnpm build --watch --progress", "build:dev": "cross-env UMI_ENV=dev pnpm max build", "postinstall": "pnpm max setup", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "lint:prettier": "prettier --check \"src/**/*\" --end-of-line auto", "lint": "pnpm max g tmp && pnpm run lint:js && pnpm run lint:style && pnpm run lint:prettier", "lint-staged": "pnpm exec lint-staged", "lint-staged:js": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx ", "ack": "ts-node -T -P ./tsconfig.json ../../scripts/auto-collect-i18n-keys/index packages/genesis-web-partner/src"}, "dependencies": {"@ant-design/compatible": "1.0.8", "@ant-design/icons": "^4.8.1", "@ant-design/pro-layout": "^6.38.22", "@antv/x6": "^1.35.0", "@monaco-editor/react": "^4.6.0", "@umijs/fabric": "^2.14.1", "@umijs/max": "^4.1.0", "@umijs/plugin-qiankun": "^2.44.1", "@umijs/plugin-sass": "^1.1.1", "@umijs/utils": "^3.5.41", "@zhongan/nagrand-ui": "^2.7.5", "ahooks": "^3.7.8", "antd": "^5.15.2", "array-move": "^2.2.2", "axios": "^0.21.4", "classnames": "^2.5.1", "clsx": "^1.2.1", "cross-env": "^7.0.3", "dva-core": "^1.4.0", "eslint-plugin-react": "^7.33.2", "genesis-web-component": "workspace:*", "genesis-web-service": "workspace:*", "genesis-web-shared": "workspace:*", "i18next": "^21.10.0", "i18next-browser-languagedetector": "^6.1.8", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "moment": "^2.30.1", "path-to-regexp": "^6.2.1", "prop-types": "^15.8.1", "qiankun": "^2.10.16", "qs": "^6.11.2", "rc-field-form": "^1.41.0", "react": "~17.0.2", "react-activation": "^0.12.4", "react-dom": "~17.0.2", "react-highlight-words": "^0.20.0", "react-i18next": "^11.18.6", "react-redux": "^7.2.9", "react-router": "6.10.0", "react-router-dom": "6.10.0", "react-sortable-hoc": "^1.11.0", "redbox-react": "~1.6.0", "redux": "^4.2.1", "sass": "^1.69.7", "swr": "^1.3.0", "uuid": "^8.3.2"}, "devDependencies": {"@ant-design/moment-webpack-plugin": "^0.0.4", "@babel/core": "^7.23.7", "@rspack/cli": "^0.2.12", "@svgr/webpack": "^8.1.0", "@types/classnames": "^2.3.1", "@types/history": "^4.7.11", "@types/lodash-es": "^4.17.12", "@types/qs": "^6.9.11", "@types/react": "^17.0.74", "@types/react-dom": "^17.0.25", "@types/react-highlight-words": "^0.16.7", "@types/react-redux": "^7.1.33", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@umijs/test": "^4.1.0", "babel-loader": "^8.3.0", "eslint": "^7.32.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^26.9.0", "file-loader": "^6.2.0", "speed-measure-webpack-plugin": "^1.5.0", "stylelint": "^13.13.1", "tailwindcss": "^3.4.17"}, "dependenciesMeta": {"antd": {"injected": true}, "genesis-web-component": {"injected": true}, "genesis-web-shared": {"injected": true}, "genesis-web-service": {"injected": true}}}