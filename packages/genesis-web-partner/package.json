{"name": "genesis-web-partner", "version": "1.0.0", "private": true, "scripts": {"integrate:rspack": "cross-env NODE_ENV=production pnpm rspack serve -c rspack/rspack.prd.js --watch", "start": "cross-env PORT=8080 UMI_ENV=prod PROGRESS=none pnpm max dev", "start:dev": "cross-env PORT=8080 UMI_ENV=prod PROGRESS=none PROXY_CHANNEL=dev pnpm max dev", "start:test1": "cross-env PORT=8080 UMI_ENV=prod PROGRESS=none PROXY_CHANNEL=test1 pnpm max dev", "start:sit": "cross-env PORT=8080 UMI_ENV=prod PROGRESS=none PROXY_CHANNEL=sit pnpm max dev", "start:stg": "cross-env PORT=8080 UMI_ENV=prod PROGRESS=none PROXY_CHANNEL=stg pnpm max dev", "start:ci": "cross-env PORT=8080 UMI_ENV=prod PROGRESS=none PROXY_CHANNEL=ci pnpm max dev", "build": "cross-env UMI_ENV=prod PROGRESS=none pnpm max build", "integrate": "pnpm build --watch --progress", "build:dev": "cross-env UMI_ENV=dev pnpm max build", "postinstall": "pnpm max setup", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "lint:prettier": "prettier --check \"src/**/*\" --end-of-line auto", "lint": "pnpm max g tmp && pnpm run lint:js && pnpm run lint:style && pnpm run lint:prettier", "lint-staged": "pnpm exec lint-staged", "lint-staged:js": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx ", "ack": "ts-node -T -P ./tsconfig.json ../../scripts/auto-collect-i18n-keys/index packages/genesis-web-partner/src"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@monaco-editor/react": "^4.6.0", "@zhongan/nagrand-ui": "^3.1.5", "ahooks": "^3.8.4", "antd": "^5.22.3", "array-move": "^4.0.0", "clsx": "^2.1.1", "genesis-web-component": "workspace:*", "genesis-web-service": "workspace:*", "genesis-web-shared": "workspace:*", "lodash-es": "^4.17.21", "moment": "^2.30.1", "prop-types": "^15.8.1", "qs": "^6.13.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.4.0", "react-sortable-hoc": "^2.0.0", "redbox-react": "~1.6.0", "swr": "^2.2.5", "uuid": "^11.0.5"}, "devDependencies": {"@ant-design/moment-webpack-plugin": "^0.0.4", "@babel/core": "^7.23.7", "@rspack/cli": "^0.2.12", "@svgr/webpack": "^8.1.0", "@types/lodash-es": "^4.17.12", "@types/qs": "^6.9.11", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@umijs/fabric": "^2.14.1", "@umijs/max": "^4.1.0", "@umijs/test": "^4.1.0", "@umijs/utils": "^3.5.41", "babel-loader": "^8.3.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^26.9.0", "eslint-plugin-react": "^7.33.2", "file-loader": "^6.2.0", "sass": "^1.69.7", "speed-measure-webpack-plugin": "^1.5.0", "stylelint": "^13.13.1", "tailwindcss": "^3.4.17"}, "dependenciesMeta": {"antd": {"injected": true}, "genesis-web-component": {"injected": true}, "genesis-web-shared": {"injected": true}, "genesis-web-service": {"injected": true}}}