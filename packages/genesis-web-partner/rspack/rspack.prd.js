/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
const path = require('path');

// const { theme } = require('antd/lib');
// const { convertLegacyToken } = require('@ant-design/compatible/lib');

// const { defaultAlgorithm, defaultSeed } = theme;

// const mapToken = defaultAlgorithm(defaultSeed);
// const v4Token = convertLegacyToken(mapToken);
const { lessVars, prependDataNagrand } = require('@zhongan/nagrand-ui');

const BuildTimerPlugin = require('../../../scripts/build-timer-rspack-plugin');

const { generateSMPData, uploadData } = require('../../../scripts/buildProfiling');

const prdConfig = {
  mode: 'production',
  // devtool: 'eval-source-map',
  target: 'web',
  node: false,
  entry: path.resolve(__dirname, '../src/index.tsx'),
  output: {
    path: path.resolve(__dirname, '../../../dist/children/genesis-web-partner'),
    filename: '[name].[contenthash:8].js',
    chunkFilename: '[name].[contenthash:8].chunk.js',
    assetModuleFilename: '[name].[contenthash:8][ext]',
    libraryTarget: 'umd',
    library: 'genesis-web-partner',
    publicPath: '/children/genesis-web-partner/',
    clean: true,
  },
  cache: false,
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx', '.scss', '.sass'],
    // alias: {
    //   '@partner': path.resolve(__dirname, '../src'),
    // },
  },
  module: {
    rules: [
      {
        test: /\.(js|mjs)$/,
        include: [path.resolve(__dirname, '../src'), /genesis-web-/],
        type: 'jsx',
      },
      {
        test: /\.css$/,
        use: [
          {
            loader: 'postcss-loader',
          },
        ],
        type: 'css',
      },
      {
        test: /\.less$/,
        type: 'css',
        use: [
          {
            loader: 'less-loader',
            options: {
              lessOptions: {
                math: 'always',
                modifyVars: { ...lessVars },
                javascriptEnabled: true,
              },
            },
          },
        ],
      },
      {
        test: /\.s[ac]ss$/i,
        include: [path.resolve(__dirname, '../src'), /genesis-web-/],
        use: [
          {
            loader: 'sass-loader',
            options: {
              additionalData: prependDataNagrand,
            },
          },
        ],
        type: 'css',
      },
      {
        test: /\.module\.s[ac]ss$/i,
        include: [path.resolve(__dirname, '../src'), /genesis-web-/],
        use: [
          {
            loader: 'sass-loader',
            options: {
              additionalData: prependDataNagrand,
            },
          },
        ],
        type: 'css/module',
      },
      {
        test: /\.(woff|woff2|eot|ttf|otf|png|jpe?g|gif|ico)$/i,
        type: 'asset/resource',
      },
      {
        test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
        oneOf: [
          {
            type: 'asset/resource',
            issuer: /\.(css|pcss|less|sass|scss)$/,
          },
          {
            type: 'asset/resource',
            resourceQuery: /assets/,
          },
          {
            use: [
              {
                loader: '@svgr/webpack',
              },
            ],
          },
        ],
      },
    ],
  },
  builtins: {
    minifyOptions: {
      dropConsole: true,
    },
    css: {
      modules: {
        localsConvention: 'camelCase',
      },
    },
    html: [
      {
        hash: false,
        title: 'Graphene',
        filename: 'index.html',
        template: path.resolve(__dirname, '../../../public/index.html'),
        favicon: path.resolve(__dirname, '../../../public/favicon.ico'),
        inject: 'head',
      },
    ],
  },
  plugins: [
    new BuildTimerPlugin({
      outputFormat: jsonBlob => {
        return generateSMPData(require('../package.json').name, jsonBlob);
      },
      outputTarget: output => {
        if (process.env.APP_ENV !== 'development') {
          uploadData(output);
        }
      },
    }),
  ],
};
module.exports = prdConfig;
