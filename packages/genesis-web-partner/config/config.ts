/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-09-04 17:33:07
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-09-06 14:29:19
 * @Description:
 */
import { defineConfig } from '@umijs/max';
import { winPath } from '@umijs/utils';

import webpackPlugin from './plugin.config';
import routes from './routes.config';

const { developHelperConfig } = require('../../../config');
const pkgName = require('../package.json').name;

const { lessVars, prependDataNagrand } = require('@zhongan/nagrand-ui');

export default defineConfig({
  title: false,
  mountElementId: 'partner',
  base: '/partner/',
  publicPath: `/children/${pkgName}/`,
  outputPath: `../../dist/children/${pkgName}`,
  qiankun: {
    slave: {},
  },
  mfsu: false,
  fastRefresh: true,
  esbuildMinifyIIFE: true,
  hash: true,
  antd: false,
  model: {},
  dva: {},
  lessLoader: {
    javascriptEnabled: true,
    modifyVars: {
      // 以下两个配置使用前提是必须在按需引入那里配置"style": true，否则不起作用，因为这里要是用less变量
      // @primary-color是设置antd的主题色，默认是蓝色的
      // @ant-prefix是自定义antd组件类名前缀的，需要配合<ConfigProvider prefixCls="ymx">使用
      ...lessVars,
      '@ant-prefix': 'antd-partner',
    },
  },
  cssLoader: {
    url: {
      filter: (url: string) => {
        if (/\.(eot|woff|woff2|ttf)(\?.*)?$/.test(url)) {
          return false;
        }
        return true;
      },
    },
    modules: {
      exportLocalsConvention: 'camelCase',
      getLocalIdent: (
        context: {
          resourcePath: string;
        },
        _: string,
        localName: string
      ) => {
        if (
          context.resourcePath.includes('node_modules') ||
          context.resourcePath.includes('global.scss') ||
          context.resourcePath.includes('antd-legacy.scss') ||
          context.resourcePath.includes('tailwind.css') ||
          context.resourcePath.includes('.less') // 老页面存在less样式
        ) {
          return localName;
        }
        const match = context.resourcePath.match(/src(.*)/);
        if (match && match[1]) {
          const antdProPath = match[1].replace('.scss', '');
          const arr = winPath(antdProPath)
            .split('/')
            .map((a: string) => a.replace(/([A-Z])/g, '-$1'))
            .map((a: string) => a.toLowerCase());
          return `partner-${arr.join('-')}-${localName}`.replace(/--/g, '-');
        }
        return localName;
      },
    },
  },
  sassLoader: {
    additionalData: `${prependDataNagrand}$ant-prefix: 'antd-partner';`,
  },
  styleLoader: {
    insert: 'body', // 从 body 底部插入
    injectType: 'singletonStyleTag',
  },
  routes,
  chainWebpack: webpackPlugin,
  // todo 子包热更新相关
  // monorepoRedirect: {
  //   srcDir: ['main', 'lib', 'src'],
  // },
  extraBabelIncludes: [/genesis-web-/],
  develop: developHelperConfig,
  plugins: [require.resolve('../../../scripts/develop/plugin/umi.js')],
  tailwindcss: {},
});
