// config目录无法引用其他目录 https://github.com/umijs/umi/issues/4773#issuecomment-634779565

export const AgentManagementSubRoutes = [
  {
    path: '/agent-management/*',
    component: './common-party/agent-management',
    oneOfPermissions: ['channel.agent-management.view'],
    routers: [
      {
        path: 'headquarter',
        component: '@/pages/common-party/agent-management/content',
        oneOfPermissions: ['channel.agent-management.view', 'channel.agent-management.edit'],
      },
      {
        path: 'branch',
        component: '@/pages/common-party/agent-management/content',
        oneOfPermissions: ['channel.agent-management.view', 'channel.agent-management.edit'],
      },
    ],
  },
];

export const EmployeeManagementSubRoutes = [
  {
    path: '/employee-management/*',
    component: './common-party/employee-management',
    oneOfPermissions: ['channel.employee-management.view'],
    routers: [
      {
        path: 'headquarter',
        component: '@/pages/common-party/employee-management/content',
        oneOfPermissions: ['channel.employee-management.view', 'channel.employee-management.edit'],
      },
      {
        path: 'branch',
        component: '@/pages/common-party/employee-management/content',
        oneOfPermissions: ['channel.employee-management.view', 'channel.employee-management.edit'],
      },
    ],
  },
];

export const PolicyTransferSubRoutes = [
  {
    path: '/policy-transfer',
    component: './common-party/policy-transfer',
    oneOfPermissions: ['channel.policy-transfer.view'],
  },
];

export default [
  {
    path: '/',
    name: 'PartnerLayout',
    wrappers: ['@/wrappers/auth'],
    component: '@/layouts/PartnerLayout',
    routes: [
      {
        path: '/partner-setting',
        component: './common-party/partner-setting',
        oneOfPermissions: ['channel.partner-setting.view'],
      },
      {
        path: '/partner-setting-v2',
        component: './common-party/partner-setting-v2',
        oneOfPermissions: ['channel.partner-management.view'],
      },
      {
        path: '/partner-setting-v2/partner-details',
        component: './common-party/partner-setting-v2/pages/PartnerDetails',
        oneOfPermissions: ['channel.partner-management.view'],
      },
      {
        path: '/partner-setting-v2/partner-approval-details',
        component: './common-party/partner-setting-v2/pages/PartnerApprovalDetails',
        oneOfPermissions: ['channel.partner-management.view'],
      },
      {
        path: '/channel',
        component: './common-party/partner-setting/Channel/Detail',
        oneOfPermissions: ['channel.partner-setting.view', 'channel.partner-setting.edit'],
      },
      {
        path: '/bank',
        component: './common-party/partner-setting/Bank',
        oneOfPermissions: ['channel.partner-setting.view', 'channel.partner-setting.edit'],
      },
      {
        path: '/broker-company',
        component: './common-party/partner-setting/BrokerCompany',
        oneOfPermissions: ['channel.partner-setting.view', 'channel.partner-setting.edit'],
      },
      {
        path: '/leasing-company',
        component: './common-party/partner-setting/LeasingCompany',
        oneOfPermissions: ['channel.partner-setting.view', 'channel.partner-setting.edit'],
      },
      {
        path: '/front-line-channel',
        component: './common-party/partner-setting/FrontLineChannel/FrontLineChannel',
        oneOfPermissions: ['channel.partner-setting.view', 'channel.partner-setting.edit'],
      },
      {
        path: '/key-account-channel',
        component: './common-party/partner-setting/KeyAccountChannel/KeyAccountChannel',
        oneOfPermissions: ['channel.partner-setting.view', 'channel.partner-setting.edit'],
      },
      {
        path: '/agent-configuration',
        component: './agent-configuration/index',
        oneOfPermissions: ['configuration.agent.view', 'configuration.agent.edit'],
      },
      ...AgentManagementSubRoutes,
      ...PolicyTransferSubRoutes,
      ...EmployeeManagementSubRoutes,
      {
        path: '/relationship-management',
        component: './common-party/relationship',
        oneOfPermissions: ['channel.relationship.view'],
      },
      {
        path: '/partner-management-approval',
        component: './common-party/partner-management-approval',
        oneOfPermissions: ['channel.partner-approval.view', 'channel.partner-approval.edit'],
      },
    ],
  },
];
