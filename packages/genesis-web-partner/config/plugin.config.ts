import AntdMomentWebpackPlugin from '@ant-design/moment-webpack-plugin';

import path from 'path';
import type <PERSON>pack<PERSON>hain from 'webpack-chain';

const { generateSMPData, uploadData } = require('../../../scripts/buildProfiling');
const BuildTimerWebpackPlugin = require('../../../scripts/build-timer-webpack-plugin');
const { uploadBundleData } = require('../../../scripts/bundle');
const BuildBundlePlugin = require('../../../scripts/build-bundle-plugin');

const webpackPlugin = (config: WebpackChain) => {
  config.module
    .rule('ts-in-node_modules')
    .test(/\.(js|jsx|mjs|mjsx|ts|tsx)$/)
    .include.add(path.resolve(__dirname, '../../genesis-web-service'))
    .add(path.resolve(__dirname, '../../genesis-web-shared'))
    .add(path.resolve(__dirname, '../../genesis-web-component'))
    .end();

  config.module
    .rule('ttf')
    .test(/\.(eot|woff|woff2|ttf)(\?.*)?$/)
    .use('file-loader')
    .loader(require.resolve('file-loader'))
    .tap(options => ({
      ...options,
      name: '/static/[name].[hash:8].[ext]',
    }))
    .end();

  //重置svgr的使用方式,直接import的就是component组件
  config.module.rule('svgr').uses.delete('url-loader');
  config.module
    .rule('svgr')
    .test(/\.svg(\?v=\d+\.\d+\.\d+\.\d+)?$/)
    .resourceQuery({ not: [/asset/] });

  if (process.env.NODE_ENV === 'production') {
    config.plugin('build-timer-webpack-plugin').use(
      new BuildTimerWebpackPlugin({
        outputFormat: (jsonBlob: unknown) => {
          return generateSMPData(require('../package.json').name, jsonBlob);
        },
        outputTarget: (output: unknown) => {
          uploadData(output);
        },
      })
    );
    config.plugin('bundle-webpack-plugin').use(
      new BuildBundlePlugin({
        outputTarget: output => {
          if (process.env.APP_ENV !== 'development') {
            uploadBundleData(require('../package.json').name, output);
          }
        },
      })
    );
  }

  config.plugin('AntdMomentWebpackPlugin').use(
    new AntdMomentWebpackPlugin({
      disableDayjsAlias: true,
    })
  );

  return config;
};

export default webpackPlugin;
