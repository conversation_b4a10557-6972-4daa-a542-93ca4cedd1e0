import { useEffect, useMemo } from 'react';

import { usePermissionMap } from '@/hooks/usePermissions';
import { Outlet, useDispatch, useSelectedRoutes } from '@umijs/max';

export default () => {
  const selectedRoutes = useSelectedRoutes();
  const dispatch = useDispatch();
  const permissionMap = usePermissionMap();

  useEffect(() => {
    dispatch({ type: 'global/getPermissions' });
    dispatch({ type: 'global/getAuthUserInfo' });
  }, [dispatch]);

  return useMemo(() => {
    if (!permissionMap) return null;

    selectedRoutes.forEach(selectedRoute => {
      const oneOfPermissions = (selectedRoute.route as { oneOfPermissions?: string[] }).oneOfPermissions;

      if (
        !selectedRoute ||
        (permissionMap &&
          oneOfPermissions?.length &&
          oneOfPermissions.every(permission => !permissionMap?.[permission]))
      ) {
        window.history.pushState(null, '', '/404');
      }
    });

    return <Outlet />;
  }, [permissionMap, selectedRoutes]);
};
