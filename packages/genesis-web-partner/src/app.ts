import { l10nInit } from 'genesis-web-shared/lib/l10n';

export const dva = {
  config: {
    onError(e: any) {
      e.preventDefault();
    },
  },
};

export const qiankun = {
  // 应用加载之前
  async bootstrap() {},
  // 应用 render 之前触发
  async mount() {
    await l10nInit();
  },
  // 应用卸载之后触发
  async unmount() {},
};

// @ts-ignore
if (!window.__POWERED_BY_QIANKUN__) {
  (async () => {
    await l10nInit();
  })();
}
