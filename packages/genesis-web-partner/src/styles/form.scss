/*!
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
@import '../variables';

.form-with-block-item {
  :global {
    .#{$antd-prefix}-form-item {
      display: inline-block;
      width: $new-form-item-width;
      margin-right: $drawer-form-item-gap;
    }

    .#{$antd-prefix}-input-number {
      width: $new-form-item-width;
    }
  }

  .fit-content-item {
    display: flex;
    width: fit-content;
  }

  .two-cols-item {
    width: calc($new-form-item-width * 2 + $drawer-form-item-gap);
  }

  .all-cols-item {
    width: 100%;
    @extend .margin-right-0;

    :global {
      .#{$antd-prefix}-form-item-row {
        width: 100%;
      }
    }
  }

  .margin-right-0 {
    margin-right: 0;
  }
}
