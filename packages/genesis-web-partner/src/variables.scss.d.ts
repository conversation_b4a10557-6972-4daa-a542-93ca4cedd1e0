interface Vars {
  whiteBackground: string;
  textColor: string;
  textColorTertiary: string;
  errorColorTextDark: string;
  primaryColor: string;
  primaryColor5Percent: string;
  formItemWidth: string;
  newFormItemWidth: string;
  tagActiveColor: string;
  tagDefaultColor: string;
  tagActiveDotColor: string;
  tagDefaultDotColor: string;
  selectTagColor: string;
  invalidSelectTagColor: string;
  selectTagBackground: string;
  errorColor: string;
  disabledColor: string;
  borderDefault: string;
  avatarColorBg1: string;
  avatarColorBg2: string;
  avatarColorBg3: string;
}

export const vars: Vars;
export default vars;
