$antd-prefix: 'antd-partner';
$form-item-width: 240px;
$new-form-item-width: 280px;
$drawer-form-item-gap: 64px;
$form-range-item-width: 580px;
$min-btn-width: 80px;
$editable-border: var(--border-gray);
$card-actions-text: var(--text-color);
$card-hover-shadow: 0 4px 8px 0 var(--box-shadow-color);
$tag-default-color: transparent;
$hover-bg: #adadad;
$select-tag-color: var(--info-color-text-dark);
$invalid-select-tag-color: var(--error-color-text-dark);
$select-tag-background: var(--info-color-bg);
$li-active-color: #20d4cb;
$li-active-bg: rgba(32, 212, 203, 0.2);
$avatar-color-bg-1: #1004a1;
$avatar-color-bg-2: #bcccdc;
$avatar-color-bg-3: #54b5ff;

:export {
  whiteBackground: var(--white);
  textColor: var(--text-color);
  errorColorTextDark: var(--error-color-text-dark);
  primaryColor5Percent: var(--primary-color-5-percent);
  formItemWidth: $form-item-width;
  newFormItemWidth: $new-form-item-width;
  borderDefault: var(--border-default);
  tagActiveColor: var(--success-color-bg);
  tagErrorColor: var(--error-color-bg);
  tagDefaultColor: $tag-default-color;
  tagActiveDotColor: var(--success-color);
  tagErrorDotColor: var(--error-color-text-dark);
  tagDefaultDotColor: var(--border-default);
  selectTagColor: $select-tag-color;
  invalidSelectTagColor: $invalid-select-tag-color;
  selectTagBackground: $select-tag-background;
  primaryColor: var(--primary-color);
  infoColorBg: var(--info-color-bg);
  infoColorTextDark: var(--info-color-text-dark);
  disabledColor: var(--disabled-color);
  primaryDisabledColor: var(--primary-disabled-color);
  errorColor: var(--error-color);
  textColorTertiary: var(--text-color-tertiary);
  avatarColorBg1: $avatar-color-bg-1;
  avatarColorBg2: $avatar-color-bg-2;
  avatarColorBg3: $avatar-color-bg-3;
}
