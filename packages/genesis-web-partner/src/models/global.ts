/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import type { Effect, Reducer } from '@umijs/max';

import type {
  BizDictItem,
  ClientConfig,
  OAuthUserInfo,
  TimeZoneProps,
  UniqueKeyFieldResponse,
  ZeusTenantConfig,
  ZoneInfoResponse,
} from 'genesis-web-service';
import { MetadataService, SystemService, UserService } from 'genesis-web-service';
import type { Permission } from 'genesis-web-service/model/common';
import { DateFormat } from 'genesis-web-shared/lib/l10n';
import type { ModulesPermission, OptionList } from 'genesis-web-shared/lib/redux';
import type { LabeledValue } from 'genesis-web-shared/lib/util/interface';

import type { ConnectModel } from '@/types/common';

export interface GlobalState {
  tenantBizDict: Record<string, (BizDictItem & LabeledValue<string | number>)[]> & {
    dateFormat: string;
  };
  tenantTimeInfo: TimeZoneProps;
  modulePermissions: ModulesPermission[];
  permissionMap: Record<string, Permission>;
  oAuthUserInfo: OAuthUserInfo;
  uniqueIdentification: UniqueKeyFieldResponse[];
  clientConfig: ClientConfig;
  lang: string;
  tenantTimeFormatByZeus: ZeusTenantConfig['additionalConfigurations'];
}

export interface GlobalModelType extends ConnectModel<GlobalState> {
  effects: {
    getTenantBizDict: Effect;
    getTenantTimeInfo: Effect;
    getPermissions: Effect;
    getAuthUserInfo: Effect;
    getUniqueIdentification: Effect;
    getLocale: Effect;
    getClientConfig: Effect;
    getTenantTimeFormatByZeus: Effect;
  };
  reducers: {
    saveTenantBizDict: Reducer;
    deleteTenantBizDict: Reducer;
    saveTenantTimeInfo: Reducer;
    saveModulePermissions: Reducer;
    savePermissionMap: Reducer;
    saveAuthUserInfo: Reducer;
    saveUniqueIdentification: Reducer;
    saveLocale: Reducer;
    saveClientConfig: Reducer;
    saveTenantTimeFormatByZeus: Reducer;
  };
}

const GlobalModel: GlobalModelType = {
  namespace: 'global',
  state: {
    tenantBizDict: null,
    tenantTimeInfo: null,
    modulePermissions: [],
    permissionMap: null,
    oAuthUserInfo: null,
    uniqueIdentification: [],
    lang: 'en-US',
    clientConfig: null,
    tenantTimeFormatByZeus: null,
  },
  effects: {
    *getTenantTimeInfo(_, { call, put }) {
      const resp: ZoneInfoResponse = yield call(SystemService.getTenantZoneInfo);
      // 转换函数
      const transFunc = (item: Record<string, string>, dateTimeFormat = 'YYYY-MM-DD HH:mm:ss') => {
        const dateFormat = new DateFormat(dateTimeFormat);
        const newArr: OptionList = [];
        const keyArr = Object.keys(item);
        keyArr.forEach(key => {
          newArr.push({
            key,
            value: key,
            name: `${key} ${dateFormat.getZoneUTC(key)}`,
          });
        });
        return newArr;
      };

      yield put({
        type: 'saveTenantTimeInfo',
        payload: {
          ...resp,
          zoneOptionList: transFunc(resp.zoneInfoList, resp.dateFormat),
          defaultZoneOptionList: transFunc(resp.defaultZoneInfo, resp.dateFormat),
        },
      });
    },
    *getTenantBizDict({ payload: dictKeys }, { call, put }) {
      const data: BizDictItem[] = yield call(MetadataService.queryBizDict, {
        dictKeys,
      }) || [];
      const bizData = data.reduce((out: Record<string, BizDictItem[]>, item) => {
        const mappedItem: BizDictItem = {
          ...item,
          label: item.dictValueName,
          value: (item.enumItemName || item.dictValue) as string,
        };
        if (out[mappedItem.dictKey]) {
          out[mappedItem.dictKey] = [...out[mappedItem.dictKey], mappedItem];
        } else {
          out[mappedItem.dictKey] = [mappedItem];
        }
        return out;
      }, {});
      yield put({ type: 'saveTenantBizDict', payload: bizData });
    },
    *getPermissions(_, { call, put }) {
      const modulePermissions: ModulesPermission[] = yield call(UserService.getPermissions) || [];

      const permissionMap: Record<string, Permission> = {};
      modulePermissions.forEach(({ subModules }) => {
        subModules.forEach(({ permissions }) => {
          permissions.forEach(permission => {
            permissionMap[permission.id] = permission;
          });
        });
      });

      yield put({ type: 'saveModulePermissions', payload: modulePermissions });
      yield put({ type: 'savePermissionMap', payload: permissionMap });
    },
    *getAuthUserInfo(_, { call, put }) {
      const oAuthUserInfo: OAuthUserInfo = yield call(UserService.getOauthUserInfo);
      yield put({
        type: 'saveAuthUserInfo',
        payload: oAuthUserInfo,
      });
    },
    *getUniqueIdentification({ payload }, { call, put }) {
      const uniqueKeyConfig: { fields: UniqueKeyFieldResponse[] } = yield call(MetadataService.queryUniqueKeyConfig, {
        schemaDefType: 1,
        customerType: payload,
      });

      yield put({
        type: 'saveUniqueIdentification',
        payload: uniqueKeyConfig?.fields || [],
      });
    },
    *getLocale(_, { call, put }) {
      const { lang } = yield call(SystemService.getLocale);
      yield put({
        type: 'saveLocale',
        payload: lang,
      });
    },
    *getClientConfig(_, { call, put }) {
      const clientConfig: ClientConfig = yield call(SystemService.getConfiguration);
      yield put({
        type: 'saveClientConfig',
        payload: clientConfig,
      });
    },
    *getTenantTimeFormatByZeus({ payload }, { call, put }) {
      const resp: ZoneInfoResponse = yield call(() => SystemService.getZeusTenantConfigs(payload));

      yield put({
        type: 'saveTenantTimeFormatByZeus',
        payload: resp?.additionalConfigurations,
      });
    },
  },
  reducers: {
    saveTenantBizDict(state: GlobalState, { payload }) {
      return {
        ...state,
        tenantBizDict: {
          ...state.tenantBizDict,
          ...payload,
        },
      };
    },
    deleteTenantBizDict(state: GlobalState, { payload }) {
      const tenantBizDict = {
        ...state.tenantBizDict,
      };
      payload?.forEach((bizKey: string) => {
        delete tenantBizDict[bizKey];
      });
      return {
        ...state,
        tenantBizDict,
      };
    },
    saveTenantTimeInfo(state: GlobalState, { payload }) {
      return {
        ...state,
        tenantTimeInfo: payload,
      };
    },
    saveTenantTimeFormatByZeus(state: GlobalState, { payload }) {
      return {
        ...state,
        tenantTimeFormatByZeus: {
          ...state.tenantTimeFormatByZeus,
          ...payload,
        },
      };
    },
    saveModulePermissions(state: GlobalState, { payload }) {
      return {
        ...state,
        modulePermissions: payload,
      };
    },
    savePermissionMap(state: GlobalState, { payload }) {
      return {
        ...state,
        permissionMap: payload,
      };
    },
    saveAuthUserInfo(state: GlobalState, { payload }) {
      return {
        ...state,
        oAuthUserInfo: payload,
      };
    },
    saveUniqueIdentification(state: GlobalState, { payload }) {
      return {
        ...state,
        uniqueIdentification: payload,
      };
    },
    saveLocale(state: GlobalState, { payload }) {
      return {
        ...state,
        lang: payload,
      };
    },
    saveClientConfig(state: GlobalState, { payload }) {
      return {
        ...state,
        clientConfig: payload,
      };
    },
  },
};

export default GlobalModel;
