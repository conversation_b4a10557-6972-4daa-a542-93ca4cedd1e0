/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-09-04 17:33:07
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-09-04 18:53:17
 * @Description:
 */
import type { GlobalState } from './global';
import type { PartnerManagementState } from '@/pages/common-party/models/partnerManagement';
import type { Dispatch } from '@umijs/max';

export interface Loading {
  global: boolean;
  effects: Record<string, boolean | undefined>;
  models: {
    customerConfiguration: boolean;
  };
}

export interface ConnectState {
  loading: Loading;
  global: GlobalState;
  partnerManagement: PartnerManagementState;
}

export interface ConnectProps {
  dispatch?: Dispatch;
}
