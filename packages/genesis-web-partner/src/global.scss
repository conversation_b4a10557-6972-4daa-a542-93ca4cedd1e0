@import './variables';
@import '../tailwind.css';

$ant-prefix: 'antd-partner';

body {
  margin: 0;
}

.partner-global {
  background: var(--layout-body-background);

  a,
  a:hover {
    color: var(--primary-color);
  }

  .footer-bar {
    left: 208px;
  }

  canvas {
    display: block;
  }

  body {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  ul,
  ol {
    list-style: none;
  }

  .pull-right {
    float: right;
  }

  .w-100 {
    width: 100%;
  }

  .border-none {
    border: none;
  }

  .comment {
    .#{$ant-prefix}-comment-actions {
      text-align: right;
    }
  }

  .text-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .text-right {
    text-align: right;
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .bg-white {
    background: var(--white);
  }

  .flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .table-actions {
    :global {
      .#{$antd-prefix}-btn.#{$antd-prefix}-btn-link {
        width: fit-content;
        font-size: 16px;
        margin-left: 16px;

        &:hover {
          color: var(--primary-color);
        }

        &[disabled] {
          color: var(--text-disabled-color);
        }
        &:first-child {
          margin-left: 0;
        }
      }
    }
  }

  .margin-bottom-8 {
    margin-bottom: 8px;
  }

  .base-editable-table .editableTable {
    border: none;
  }

  @for $val from 0 through 20 {
    $var: $val * 2;
    $varWithPx: $var * 1px;

    .margin-top-#{$var} {
      margin-top: $varWithPx;
    }
    .margin-right-#{$var} {
      margin-right: $varWithPx;
    }
    .margin-bottom-#{$var} {
      margin-bottom: $varWithPx;
    }
    .margin-left-#{$var} {
      margin-left: $varWithPx;
    }
    .padding-top-#{$var} {
      padding-top: $varWithPx;
    }
    .padding-bottom-#{$var} {
      padding-bottom: $varWithPx;
    }
    .padding-right-#{$var} {
      padding-right: $varWithPx;
    }
    .padding-left-#{$var} {
      padding-left: $varWithPx;
    }
    .padding-#{$var} {
      padding: $varWithPx;
    }
    .margin-#{$var} {
      margin: $varWithPx;
    }
    .margin-lr-#{$var} {
      margin-right: $varWithPx;
      margin-left: $varWithPx;
    }

    .font-size-#{$var} {
      font-size: $varWithPx;
    }
  }
}
.#{$antd-prefix}-radio-checked:not(.#{$antd-prefix}-radio-disabled) {
  .#{$antd-prefix}-radio-inner {
    background-color: white;
    &::after {
      background-color: var(--primary-color);
      transform: scale(0.5);
    }
  }
}
