@import '@/variables.scss';

.partner-setting {
  height: 100vh;
  overflow: auto;
  .header {
    display: flex;
    align-items: center;
    height: 80px;
    padding-left: 20px;
    padding-right: 38px;
    font-size: 16px;
    font-weight: 700;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-light);
    > img {
      margin-right: 4px;
      width: 48px;
      height: 48px;
      border: 1px solid var(--border-light);
    }
    > span {
      display: -webkit-box;
      overflow: hidden;
      white-space: pre-line;
      word-break: break-word;
      line-height: 19px;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
    }
  }
  :global {
    .#{$antd-prefix}-layout-sider {
      background-color: var(--white);
    }
    .#{$antd-prefix}-menu-item {
      padding-top: 20px !important;
      padding-bottom: 20px !important;
      margin: 0 !important;
      text-overflow: inherit;
      white-space: pre-line;
      line-height: 20px !important;
      height: auto !important;
    }
    .#{$antd-prefix}-layout-content {
      background-color: var(--white);
      height: 100%;
      padding: 12px 32px;
      overflow: auto;
      display: flex;
      flex-direction: column;
    }
    .#{$antd-prefix}-col {
      margin-right: 40px;
    }
    .#{$antd-prefix}-col:last-child {
      margin-right: 0;
    }
    .#{$antd-prefix}-row {
      align-items: center;
    }
    .#{$antd-prefix}-form-item-row {
      align-items: flex-start;
    }
    .#{$antd-prefix}-menu-item-group-title {
      color: var(--label-color);
      font-weight: 700;
      font-size: 16px;
      line-height: 24px;
    }
  }

  .no-date-message {
    margin: 27px 47px;
  }
}

.channel-content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 50px);
  position: relative;
  background-color: var(--layout-body-background);
  .content-info {
    flex: 1;
    margin: 16px;
    overflow: auto;
    :global {
      .#{$antd-prefix}-picker {
        width: 100%;
      }
    }
  }
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  color: var(--text-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agreement-title {
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
}

.btn-link {
  border: none;
  color: var(--primary-color) !important;
  background: transparent;
  &:hover {
    background: var(--white);
  }
}

.content-header {
  padding: 16px 32px 24px 20px;
  color: var(--text-color);
  background-color: var(--white);
  .flex {
    display: flex;
    align-items: center;
    .bread-crumb {
      margin-left: 32px;
      .jump-bread-crumb {
        cursor: pointer;
        color: var(--primary-light);
        &:hover {
          color: var(--primary-color);
        }
        :global {
          .anticon {
            margin-right: 6px;
            font-size: 16px;
          }
        }
      }
    }
    .right {
      flex: 1;
      text-align: right;
    }
  }
  .header-name {
    margin-top: 16px;
    font-weight: bold;
    font-size: 20px;
  }
}

.content-title {
  padding: var(--gap-sm) var(--gap-lg);
  border-bottom: 1px solid var(--item-bg-secondary);
  height: 48px;
}
.main-search-button-group {
  padding: var(--gap-lg);
    display: flex;
    justify-content: space-between;
    button {
      font-weight: 500;
    }
}
.flex-between {
  display: flex;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.edit-table {
  :global {
    .#{$antd-prefix}-table-tbody > tr > td {
      line-height: inherit;
    }
  }
}

.delete-modal {
  width: 464px !important;
  :global {
    .ant-modal-content {
      border-radius: var(--border-radius-base);
    }
    .ant-modal-body {
      padding: 24px 32px 32px 32px;
      border-radius: var(--border-radius-base);
      .anticon-exclamation-circle {
        display: none;
      }
      .ant-modal-confirm-title {
        border-bottom: 1px dashed var(--border-light);
        padding-bottom: 16px;
      }
      .ant-modal-confirm-content {
        padding: 16px 0 40px 0;
        margin-left: 0 !important;
      }
      .ant-modal-confirm-btns {
        border-top: none;
        padding: 0;
        margin-top: 0;
        .ant-btn-primary {
          margin-left: 16px;
        }
      }
    }
  }
}

.warning-modal {
  :global {
    .ant-modal-confirm-content {
      max-height: 272px;
      overflow: scroll;
    }
  }
}

.structure-drawer {
  :global {
    .#{$antd-prefix}-drawer-header {
      border-bottom: 1px dashed var(--border-light);
      .#{$antd-prefix}-drawer-header-title {
        flex-direction: row-reverse;
        .#{$antd-prefix}-drawer-title {
          font-weight: 700;
        }
      }
    }
    .#{$antd-prefix}-drawer-body {
      display: flex;
      justify-content: center;
    }
  }
}

.no-data {
  background-color: var(--table-header-bg);
  text-align: center;
  padding: 20px;
  border-radius: 16px;
  > span {
    margin: 0 !important;
    transform: none !important;
  }
}

.basic-info-section {
  background-color: var(--white);
  margin-bottom: 16px;
  padding: 32px;
}

.extra-info-section {
  background-color: var(--white);
  padding: 32px;
}

.tied-steps {
  width: 50% !important;
  margin-bottom: 24px !important;
  :global {
    .#{$antd-prefix}-steps-icon {
      display: inline-block;
      color: transparent !important;
    }
  }
}
.tied {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 12px;
  .title {
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    margin-right: 8px;
  }
}

.tied-drawer {
  :global {
    .#{$antd-prefix}-drawer-title {
      color: var(--label-color);
      font-weight: 700;
      font-size: 16px;
      line-height: 24px;
    }
  }
}

:global {
  .#{$antd-prefix}-legacy-form-item-control.has-error
    .#{$antd-prefix}-legacy-form-explain {
    color: var(--error-color);
  }
  .base-editable-table .editableTable {
    border: none;
  }
  .drawer-form {
    .drawer-form-title {
      border-bottom: 1px solid var(--divider-color);
      margin-bottom: $gap-lg;
    }
    .divider {
      display: none;
    }
    .drawer-form-content {
      overflow-x: hidden;
    }
  }
}
.institute-upload {
  display: block;
  :global {
    .#{$antd-prefix}-upload {
      width: 100%;
      span {
        display: block;
      }
    }
  }
}

.institute-card-container {
  .institute-card-top {
    padding: $gap-md;
    box-shadow: 0 0.3px 0.5px 0px $border-line-color-secondary;
    :global {
      .nagrand-body-text.body {
        font-weight: 700 !important;
      }
      .#{$antd-prefix}-avatar {
        background-color: $input-placeholder-color;
      }
    }
    .status-wrapper {
      margin-top: $gap-md;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .institute-card-bottom {
    padding: $gap-md;
    > div {
      display: flex;
      justify-content: space-between;
      color: $text-color-quaternary;
      > div:last-child {
        color: $text-color-tertiary;
        text-align: end;
      }
      &:last-child {
        margin-top: $gap-xs;
      }
    }
  }
}
.transparent-wrapper {
  display: flex;
  flex-direction: row-reverse;
  position: absolute;
  width: 100%;
  bottom: 0px;
  background-color: $white;
  :global {
    .#{$antd-prefix}-btn-text {
      color: $text-color-tertiary;
      &:hover {
        color: $primary-color;
      }
    }
    .#{$antd-prefix}-btn-text[disabled] {
      color: $text-disabled-color;
    }
  }
  span {
    font-size: $font-size-h3;
    cursor: pointer;
    transition: 0.3s all;
    &:hover {
      background-color: rgba(0, 0, 0, 0.018);
    }
  }
}
.segment-controller {
  background-color: $primary-disabled-color;
  border-radius: $gap-md;
  padding: $gap-xss;
  :global {
    .#{$antd-prefix}-segmented-item {
      border-radius: $gap-md;
      .#{$antd-prefix}-segmented-item-label {
        padding: 0 $gap-sm;
      }
    }
    .#{$antd-prefix}-segmented-thumb-motion {
      border-radius: $gap-md;
    }
  }
}
