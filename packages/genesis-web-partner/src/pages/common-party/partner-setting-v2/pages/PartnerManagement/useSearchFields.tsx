/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-11-17 15:14:34
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-11-24 10:54:17
 * @Description:
 */
import { useMemo } from 'react';
import { FieldType } from '@/components/CommonForm';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import type { BizDictItem } from 'genesis-web-service';
import { useTranslation } from 'react-i18next';

export const useSearchFields = () => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  return useMemo(
    () => [
      {
        key: 'instituteName',
        label: t('Partner Name'),
        type: FieldType.Input,
        immutable: true,
        col: 8,
      },
      {
        key: 'instituteCode',
        label: t('Partner Code'),
        type: FieldType.Input,
        immutable: true,
        col: 8,
      },
      {
        key: 'instituteType',
        label: t('Partner Type'),
        type: FieldType.Select,
        col: 8,
        immutable: true,
        ctrlProps: {
          allowClear: true,
          options: enums?.instituteType,
        },
      },
      {
        key: 'instituteStatus',
        label: t('Partner Status'),
        type: FieldType.Select,
        col: 8,
        ctrlProps: {
          options: enums?.instituteStatus,
          allowClear: true,
        },
      },
    ],
    [enums],
  );
};
