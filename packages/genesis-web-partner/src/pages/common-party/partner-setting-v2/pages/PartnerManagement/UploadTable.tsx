import type { VFC } from 'react';
import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { InfoCircleOutlined } from '@ant-design/icons';
import { Button, Tooltip } from 'antd';
import type { ColumnProps } from 'antd/lib/table';

import { useRequest } from 'ahooks';
import clsx from 'clsx';

import { ConditionFilter, Icon, StatusTag, Table, TableActionsContainer } from '@zhongan/nagrand-ui';
import type { StatusType } from '@zhongan/nagrand-ui/dist/components/StatusTag';

import { FieldType } from 'genesis-web-component/lib/interface/enum.interface';
import type {
  BatchListUploadFilterParamsType,
  BizDictItem,
  ConditionFieldsConfig,
  QueryUploadRecordsParams,
  QueryUploadRecordsResult,
} from 'genesis-web-service';
import { ChannelService, UsageUploadHistoryState } from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { handleDate } from '@/pages/common-party/utils/util';

import { useEnumsMapping } from '../../hooks/useEnumsMapping';
import styles from './style.scss';

const verificationStatusColorMap: Record<string, StatusType> = {
  SUCCESS: 'success',
  FAILED: 'error',
  PROCESSING: 'no-status',
  PARTIAL_SUCCESS: 'warning',
};

interface Props {
  loading: boolean;
  searchParams: QueryUploadRecordsParams;
  data?: QueryUploadRecordsResult[];
  onSearch: (params: QueryUploadRecordsParams) => void;
  total: number;
  onDownload: (code: string) => void;
}

export const UploadTable: VFC<Props> = props => {
  const [t] = useTranslation(['partner']);

  const { loading, data = [], searchParams, total, onSearch, onDownload } = props;
  const L10nUtil = useL10n();
  const [filterParams, setFilterParams] = useState<BatchListUploadFilterParamsType>({});
  const batchUploadPolicyViewStatusMapping = useEnumsMapping('batchUploadPolicyViewStatus');
  const batchUploadPolicyViewStatusEnums = useTenantBizDict('batchUploadPolicyViewStatus') as BizDictItem[];

  const { data: creatorList } = useRequest(() => ChannelService.queryAllCreators());

  const pagination = useMemo(
    () => ({ current: searchParams.page + 1, pageSize: searchParams.size }),
    [searchParams.page, searchParams.size]
  );

  const columns: ColumnProps<QueryUploadRecordsResult>[] = [
    {
      title: t('Batch Number'),
      dataIndex: 'id',
    },
    {
      title: t('File Name'),
      dataIndex: 'fileName',
    },
    {
      title: t('File Upload Time'),
      dataIndex: 'uploadTime',
      render: (text: string) => dateFormatInstance.getDateTimeString(text, dateFormatInstance.defaultTimeZone),
    },
    {
      title: t('File Creator'),
      dataIndex: 'creator',
    },
    {
      title: t('Status'),
      dataIndex: 'status',
      render: (text: string) => (
        <StatusTag
          style={{ whiteSpace: 'nowrap' }}
          needDot
          statusI18n={batchUploadPolicyViewStatusMapping[text]}
          type={verificationStatusColorMap[text]}
        />
      ),
    },
    {
      title: () => (
        <>
          <span>{t('Number of Records')}</span>
          <Tooltip placement="top" title={t('Records / Number of total records')}>
            <InfoCircleOutlined
              style={{
                color: 'var(--text-disabled-color)',
                marginLeft: 'var(--gap-xss)',
              }}
            />
          </Tooltip>
        </>
      ),
      render: (text: string, record) => (
        <span
          className={clsx({
            [styles.failedText]: [UsageUploadHistoryState.FAILED, UsageUploadHistoryState.PARTIAL_SUCCESS].includes(
              record.status
            ),
          })}
        >
          {t('Records_TotalRecords', {
            records: record.successNum ?? '-',
            totalRecords: record.totalNum ?? '-',
          })}
        </span>
      ),
    },
    {
      title: t('Actions'),
      dataIndex: 'actions',
      key: 'actions',
      fixed: 'right',
      align: 'right',
      width: 60,
      render: (text, record) => (
        <TableActionsContainer>
          {record.status !== UsageUploadHistoryState.PROCESSING && (
            <Tooltip placement="top" title={t('Download')}>
              <Button type="text" onClick={() => onDownload(record.fileUniqueCode)}>
                <Icon type="download" />
              </Button>
            </Tooltip>
          )}
        </TableActionsContainer>
      ),
    },
  ];

  const handleFilterChange = useCallback(
    (value: BatchListUploadFilterParamsType) => {
      const { uploadDate } = value;
      const uploadDateObj = handleDate('uploadStartTime', 'uploadEndTime', uploadDate);

      setFilterParams(value);
      onSearch({
        ...searchParams,
        page: 0,
        creator: value?.creator,
        status: value?.status,
        uploadStartTime: uploadDateObj?.uploadStartTime,
        uploadEndTime: uploadDateObj?.uploadEndTime,
      });
    },
    [searchParams]
  );

  const panelConditions: ConditionFieldsConfig[] = [
    {
      label: t('File Creator'),
      fieldKey: 'creator',
      type: FieldType.Select,
      options: creatorList?.map(item => ({ label: item, value: item })) || [],
    },
    {
      label: t('Status'),
      fieldKey: 'status',
      type: FieldType.Select,
      options: batchUploadPolicyViewStatusEnums,
    },
    {
      label: t('Upload Date'),
      fieldKey: 'uploadDate',
      type: FieldType.RangePicker,
    },
  ];

  return (
    <div className={styles.uploadHistory}>
      <div className={styles.title}>{t('Partner List Upload History')}</div>

      <ConditionFilter
        mode="simple"
        panelConditions={panelConditions}
        appliedCondition={filterParams}
        onChange={handleFilterChange}
        optionalProps={{
          dateTimeRender: (date: moment.Moment) => L10nUtil.l10n.dateFormat.getDateString(date)!,
        }}
      />

      <Table
        rowKey="id"
        loading={loading}
        columns={columns}
        dataSource={data}
        scroll={{ x: 'max-content' }}
        pagination={{
          size: 'small',
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: total,
          onChange: (current, pageSize) => {
            const uploadDateObj = handleDate('uploadStartTime', 'uploadEndTime', filterParams.uploadDate);
            onSearch({
              creator: filterParams?.creator,
              status: filterParams?.status,
              uploadStartTime: uploadDateObj?.uploadStartTime,
              uploadEndTime: uploadDateObj?.uploadEndTime,
              page: current - 1,
              size: pageSize,
            });
          },
        }}
      />
    </div>
  );
};
