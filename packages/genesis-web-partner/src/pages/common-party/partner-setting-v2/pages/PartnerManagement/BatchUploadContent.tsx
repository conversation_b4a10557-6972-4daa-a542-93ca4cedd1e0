import type { VFC } from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import { useRequest } from 'ahooks';
import { Button, Dropdown, Form, Radio, Upload, message } from 'antd';

import { Icon, Select, UploadDragCard, UploadFileItem } from '@zhongan/nagrand-ui';

import type { BizDictItem, ChannelDocumentV2, QueryUploadRecordsParams } from 'genesis-web-service';
import { ChannelService, DownloadOrUploadType, InstituteTypeEnum } from 'genesis-web-service';
import { security } from 'genesis-web-shared/lib';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';

import { UploadTable } from './UploadTable';
import styles from './style.scss';

export const UploadPartnerTypeFieldName = 'partnerType';

enum TemplateTypeEnum {
  Empty = 'empty',
  CompleteData = 'completeData',
}

export const BatchUploadContent: VFC = () => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const uploadPartnerType = Form.useWatch(UploadPartnerTypeFieldName, form);
  const timer = useRef<any>();

  const typeEnums = useTenantBizDict('instituteType') as BizDictItem[];

  const [templateType, setTemplateType] = useState<TemplateTypeEnum | undefined>();
  const [currentFile, setCurrentFile] = useState<ChannelDocumentV2 | undefined>(undefined);
  const [searchParams, setSearchParams] = useState<QueryUploadRecordsParams>({
    page: 0,
    size: 10,
  });

  const typeList = useMemo(
    () => typeEnums?.filter(item => item?.enumItemName !== InstituteTypeEnum.SERVICE_COMPANY),
    [typeEnums]
  );

  const { loading: downloadFileLoading, run: downloadInstitute } = useRequest(
    (instituteType?: string | number) => {
      return ChannelService.download({
        type: DownloadOrUploadType[instituteType],
        ext: JSON.stringify({ instituteType }),
        isTemplate: templateType === TemplateTypeEnum.Empty,
      });
    },
    {
      manual: true,
      onSuccess: result => {
        downloadFile(result);
      },
      onError: error => {
        message.error(error?.message || t('Download failed'));
      },
    }
  );

  const dropdownMenuItems = useMemo(
    () =>
      typeList?.map(item => ({
        key: item.value,
        onClick: () => downloadInstitute(item.value),
        label: item.label,
      })),
    [typeList, downloadInstitute]
  );

  const uploadProps = useMemo(() => {
    const customRequest = (options: Record<string, any>) => {
      const formData = new FormData();
      formData.append('file', options.file);
      ChannelService.channelUpload(formData, {
        type: DownloadOrUploadType[uploadPartnerType],
        ext: JSON.stringify({ uploadPartnerType }),
        onlyUpload: true,
      })
        .then(response => {
          message.success(t('Uploaded successfully'));
          setCurrentFile(response.value);
        })
        .catch((error: Error) => {
          message.error(error?.message);
        });
    };
    return {
      name: 'file',
      accept: '.xlsx',
      headers: { ...security.csrf() },
      showUploadList: false,
      withCredentials: true,
      customRequest,
    };
  }, [uploadPartnerType]);

  const {
    data: uploadRecords,
    loading: queryUploadRecordsLoading,
    runAsync: queryUploadRecords,
  } = useRequest(
    () => {
      return ChannelService.queryUploadRecords(searchParams);
    },
    {
      refreshDeps: [searchParams],
    }
  );

  const handleDelAttachment = useCallback(() => {
    setCurrentFile(undefined);
  }, []);

  const handleDownloadFile = useCallback(
    (fileUniqueCode?: string) => {
      ChannelService.download({
        fileUniqueCode,
        type: DownloadOrUploadType.COMMON_ENTITY_FILE,
      })
        .then(downloadFile)
        .catch((error: Error) => message.error(error?.message || t('Download failed')));
    },
    [currentFile]
  );

  const startPolling = useCallback(() => {
    timer.current = setTimeout(() => {
      queryUploadRecords().then(res => {
        if (res.data[0].status === 'PROCESSING') {
          startPolling();
        }
      });
    }, 5000);
  }, []);

  useEffect(() => {
    return () => {
      clearTimeout(timer.current);
    };
  }, []);

  const handleClearUpload = useCallback(() => {
    form.resetFields();
    setCurrentFile(undefined);
  }, [form]);

  const handleFinish = useCallback(() => {
    if (currentFile) {
      ChannelService.batchCreatePartners({
        type: DownloadOrUploadType[uploadPartnerType],
        ext: JSON.stringify({ instituteType: uploadPartnerType }),
        fileUniqueCode: currentFile.fileUniqueCode,
      })
        .then(() => {
          setSearchParams(old => ({ ...old, current: 1, pageSize: 10 }));
          startPolling();
        })
        .finally(() => handleClearUpload());
    } else {
      message.error(t('Please Upload File'));
    }
  }, [currentFile, uploadPartnerType]);

  return (
    <div className={styles.batchUploadContent}>
      <div>
        <div className={styles.title}>
          1. <Trans i18nKey="Please Download Template First" ns="partner" />
        </div>
        <div className={styles.downloadContent}>
          <div className={styles.downloadTip}>
            <div>
              {t(
                'Blank Template: An upload template that does not contain any data. Users can add new data to create, edit, or delete partner information and its internal details (deletion applies only to some related information).'
              )}
            </div>
            <div>
              {t(
                'Complete Data Set: An upload template that contains all data. Users can select portions of this data for processing.'
              )}
            </div>
          </div>

          <Radio.Group onChange={event => setTemplateType(event.target.value)} value={templateType}>
            <Radio value={TemplateTypeEnum.Empty} style={{ marginRight: 'var(--gap-huge)' }}>
              {t('Blank Template')}
            </Radio>
            <Radio value={TemplateTypeEnum.CompleteData}>{t('Complete Data Set')}</Radio>
          </Radio.Group>

          <div className={styles.downloadButton}>
            <Dropdown menu={{ items: dropdownMenuItems }} trigger={['click']} disabled={!templateType}>
              <Button
                loading={downloadFileLoading}
                icon={<Icon type="download" style={{ fontSize: 'var(--font-size-lg)' }} />}
              >
                {t('Download Template')}
              </Button>
            </Dropdown>
          </div>
        </div>
      </div>
      <div>
        <div className={styles.title}>2. {t('Upload File')}</div>
        <div className={styles.uploadContent}>
          <Form layout="vertical" form={form} onFinish={handleFinish}>
            <Form.Item
              label={t('Partner Type')}
              name="partnerType"
              rules={[
                {
                  required: true,
                  message: t('Please Select'),
                },
              ]}
            >
              <Select options={typeList} placeholder={t('Please Select')} />
            </Form.Item>

            <div className={styles.uploadDocumentWrapper}>
              {currentFile ? (
                <UploadFileItem
                  key={currentFile?.fileUniqueCode}
                  style={{ maxWidth: 440 }}
                  isShowHover={true}
                  needPreview={false}
                  fileName={currentFile.fileName}
                  hoverInfoList={[
                    {
                      icon: (
                        <Upload {...uploadProps}>
                          <Icon type="reload" />
                        </Upload>
                      ),
                    },
                    {
                      icon: <Icon type="download" />,
                      onClick: () => handleDownloadFile(currentFile.fileUniqueCode),
                    },
                    {
                      icon: <Icon type="delete" />,
                      onClick: () => handleDelAttachment(),
                    },
                  ]}
                />
              ) : (
                <Upload {...uploadProps}>
                  <UploadDragCard uploadTypeHint="" />
                </Upload>
              )}
            </div>

            <div className={styles.uploadBottom}>
              <Button style={{ marginRight: 'var(--gap-md)' }} onClick={handleClearUpload}>
                {t('Clear')}
              </Button>

              <Button htmlType="submit" type="primary" disabled={queryUploadRecordsLoading}>
                {t('Confirm')}
              </Button>
            </div>
          </Form>
        </div>
      </div>

      <UploadTable
        loading={queryUploadRecordsLoading}
        searchParams={searchParams}
        data={uploadRecords?.data}
        total={uploadRecords?.totalElements || 0}
        onDownload={handleDownloadFile}
        onSearch={setSearchParams}
      />
    </div>
  );
};
