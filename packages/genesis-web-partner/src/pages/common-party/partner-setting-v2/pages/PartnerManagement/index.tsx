import { Fragment, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Divider, Dropdown, Form, Menu, Row, Upload, message } from 'antd';

import { useDispatch, useLocation, useNavigate } from '@umijs/max';

import { useRequest, useToggle } from 'ahooks';
import qs from 'qs';

import {
  AddNewButton,
  OperationContainer,
  Pagination,
  QueryResultContainer,
  RenderMode,
  RenderModeSwitch,
  TextBody,
} from '@zhongan/nagrand-ui';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { ChannelService, InstituteTypeEnum } from 'genesis-web-service';
import type { BizDictItem } from 'genesis-web-service/lib/foundation/foundation.interface';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { CardList } from '@/pages/common-party/partner-setting-v2/components/CardList/CardList';
import { ConditionalSearch } from '@/pages/common-party/partner-setting-v2/components/ConditionalSearch';
import { usePartnerManagementPermission } from '@/pages/common-party/partner-setting-v2/hooks/usePartnerSettingPermission';
import type {
  InstituteRouteState,
  InstituteSearchForm,
} from '@/pages/common-party/partner-setting-v2/models/index.interface';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';
import { NewChannelService } from '@/services/channel/channel.service.new';
import { NewMetadataService } from '@/services/metadata/metadata.service.new';
import { DefaultPaginationMapping, ModeEnum, SelectType } from '@/types/common';

import { ApproveModeContext } from '../../hooks/useContext';
import style from '../../style.scss';
import { BatchUploadContent } from './BatchUploadContent';
import { PartnerListTable } from './PartnerListTable';
import { useSearchFields } from './useSearchFields';

export const supportOldVersionTypes = [InstituteTypeEnum.CLINIC, InstituteTypeEnum.HOSPITAL];

/**
 *
 * @description partner setting institute menu component
 */
export const PartnerManagement = () => {
  const { t } = useTranslation('partner');
  const navigate = useNavigate();
  const [searchForm] = Form.useForm();
  const dispatch = useDispatch();
  const { state } = useLocation() as {
    state: InstituteRouteState<InstituteSearchForm>;
  };
  const { canEdit } = usePartnerManagementPermission();

  const [currentMode, setCurrentMode] = useState<RenderMode>(state?.currentMode || RenderMode.Table);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: DefaultPaginationMapping[RenderMode.Card],
  });
  const [searchValue, setSearchValue] = useState<Record<string, any>>({});
  const [batchUploadDrawerVisible, { toggle: toggleBatchUploadDrawerVisible }] = useToggle();
  const [uploading, setUploading] = useState(false);
  const typeEnums = useTenantBizDict('instituteType') as BizDictItem[];
  const partnerDataUpdateModeEnums = useTenantBizDict('partnerDataUpdateMode') as BizDictItem[];
  const [enableApproval, setEnableApproval] = useState(false);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: ['instituteType', 'instituteStatus', 'batchUploadPolicyViewStatus', 'partnerDataUpdateMode'],
    });
  }, [dispatch]);

  useEffect(() => {
    NewMetadataService.TenantConfigMgmtService.tenantConfig('partner-approval').then(res => {
      setEnableApproval(res.enableApproval);
    });
  }, []);

  useEffect(() => {
    setPagination({
      current: pagination?.current || 1,
      pageSize: DefaultPaginationMapping[RenderMode.Card],
    });
  }, [currentMode]);

  // 初始化
  useEffect(() => {
    if (state?.pagination) {
      setPagination(state?.pagination);
    }
    if (state?.searchForm) {
      setSearchValue(state?.searchForm);
      searchForm.setFieldsValue(state?.searchForm);
    }
  }, []);

  const {
    data: instituteListResp,
    run: getInstituteList,
    loading,
  } = useRequest(
    async () => {
      return await ChannelService.getInstitutesSearch({
        pageIndex: pagination?.current - 1,
        pageSize: pagination?.pageSize,
        condition: searchValue,
      });
    },
    {
      refreshDeps: [pagination],
      debounceWait: 300,
    }
  );

  const addInstitute = useCallback(() => {
    navigate(
      `/partner-setting-v2/partner-details?${qs.stringify({
        modeType: ModeEnum.ADD,
      })}`,
      {
        state: {
          pagination: pagination,
          searchForm: searchValue,
          currentMode: currentMode,
        },
      }
    );
  }, [pagination, searchValue, currentMode]);

  const viewDetail = useCallback(
    async (id: number, modeType: ModeEnum) => {
      if (modeType === ModeEnum.EDIT) {
        const editRecord = instituteListResp.data.find(item => item.id === id);
        const res = await NewChannelService.InstitutePackToApproveService.check({
          instituteCode: editRecord.instituteCode,
          instituteType: editRecord.instituteType,
        });
        if (!res.canEdit) {
          message.error(t('There are still modification tasks pending approval. Please complete the approval first.'));
          return;
        }
      }
      navigate(
        `/partner-setting-v2/partner-details?${qs.stringify({
          id: String(id),
          modeType: modeType || ModeEnum.READ,
        })}`,
        {
          state: {
            pagination: pagination,
            searchForm: searchValue,
            currentMode: currentMode,
          },
        }
      );
    },
    [pagination, searchValue, currentMode, instituteListResp]
  );

  const handleCloseDrawer = () => {
    toggleBatchUploadDrawerVisible();
    getInstituteList();
  };

  const downloadInstitute = useCallback((instituteType: string) => {
    ChannelService.download({
      // type: UploadType.HospitalAndClinic,
      type: 'HOSPITAL_CLINIC_FULL',
      ext: JSON.stringify({ instituteType }),
    }).then(downloadFile);
  }, []);

  const uploadInstitute = useCallback((instituteType: string) => {
    const customRequest = options => {
      const formData = new FormData();
      formData.append('file', options.file);
      formData.append('type', 'HOSPITAL_CLINIC_FULL');
      formData.append('ext', JSON.stringify({ instituteType }));
      ChannelService.upload(formData)
        .then(response => {
          options.onSuccess(response);
        })
        .catch((error: Error) => {
          message.error(error?.message);
          setUploading(false);
        });
    };
    return getUploadPropsNew(
      `/api/channel/v2/institutes/template-data/${instituteType}/`,
      () => {
        setSearchValue({ ...searchValue });
        setPagination({ current: 1, pageSize: pagination?.pageSize });
      },
      setUploading,
      null,
      customRequest
    );
  }, []);

  const getDropdownMenu = useCallback(
    (type: SelectType) => {
      const clickFuncMap: Record<SelectType, Function> = {
        [SelectType.UPLOAD]: () => {},
        [SelectType.DOWNLOAD]: downloadInstitute,
      };

      return (
        <Menu>
          {typeEnums
            ?.filter(item => supportOldVersionTypes.includes(item.dictValue as InstituteTypeEnum))
            ?.map(item => (
              <Menu.Item onClick={() => clickFuncMap[type](item.value)} key={item.value}>
                {type === SelectType.UPLOAD ? (
                  <Upload className={style.instituteUpload} {...uploadInstitute(item.value)}>
                    {item.label}
                  </Upload>
                ) : (
                  <a>{item.label}</a>
                )}
              </Menu.Item>
            ))}
        </Menu>
      );
    },
    [typeEnums, addInstitute, downloadInstitute, uploadInstitute]
  );

  const searchFields = useSearchFields();

  return (
    <ApproveModeContext.Provider value={{ enableApproval: enableApproval }}>
      <Fragment>
        <div style={{ backgroundColor: 'var(--white)' }}>
          <Row className={style.contentTitle}>
            <TextBody type="h5" weight="bold">
              {t('Partner Management')}
            </TextBody>
          </Row>

          <Row className={style.mainSearchButtonGroup}>
            <ConditionalSearch
              form={searchForm}
              fields={searchFields}
              onSearch={values => {
                setSearchValue(values);
                setPagination({ current: 1, pageSize: pagination?.pageSize });
              }}
              loading={loading}
              mainSearchFieldKey={['instituteCodeOrName']}
              initialValue={searchValue}
            />
          </Row>
        </div>

        <QueryResultContainer>
          <OperationContainer>
            <OperationContainer.Left>
              <AddNewButton type="primary" ghost disabled={!canEdit} onClick={addInstitute}>
                {t('Add New')}
              </AddNewButton>
            </OperationContainer.Left>
            <OperationContainer.Right>
              {partnerDataUpdateModeEnums?.[0].enumItemName === 'FULL_DATA_REPLACEMENT' && (
                <>
                  <Dropdown overlay={getDropdownMenu(SelectType.UPLOAD)} trigger={['click']} disabled={!canEdit}>
                    <Button loading={uploading}>{t('Upload')}</Button>
                  </Dropdown>
                  <Dropdown overlay={getDropdownMenu(SelectType.DOWNLOAD)} trigger={['click']}>
                    <Button>{t('Download')}</Button>
                  </Dropdown>
                </>
              )}
              {!enableApproval && <Button onClick={toggleBatchUploadDrawerVisible}>{t('Batch Upload')}</Button>}
              {(partnerDataUpdateModeEnums?.[0].enumItemName === 'FULL_DATA_REPLACEMENT' || !enableApproval) && (
                <Divider type="vertical" />
              )}
              <RenderModeSwitch value={currentMode} onChange={setCurrentMode} />
            </OperationContainer.Right>
          </OperationContainer>
          {currentMode === RenderMode.Card ? (
            <CardList
              data={instituteListResp?.data}
              rowKey="id"
              loading={loading}
              handleIconClick={viewDetail}
              canEdit={canEdit}
            />
          ) : (
            <PartnerListTable loading={loading} data={instituteListResp?.data} viewDetail={viewDetail} />
          )}

          <Pagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            pageSizeOptions="singlePageTwelve"
            total={instituteListResp?.totalElements}
            onChange={(current, pageSize) => setPagination({ current, pageSize })}
          />
        </QueryResultContainer>

        <DrawerForm
          title={t('Batch Upload')}
          visible={batchUploadDrawerVisible}
          onClose={handleCloseDrawer}
          cancelText={t('Close')}
          submitBtnShow={false}
          getContainer={() => document.getElementsByClassName('partner-global')[0] as HTMLElement}
        >
          <BatchUploadContent />
        </DrawerForm>
      </Fragment>
    </ApproveModeContext.Provider>
  );
};
