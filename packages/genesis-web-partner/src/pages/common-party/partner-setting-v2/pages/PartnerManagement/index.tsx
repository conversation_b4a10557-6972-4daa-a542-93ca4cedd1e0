import { Fragment, useCallback, useEffect, useState } from 'react';
import KeepAlive, { useAliveController } from 'react-activation';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router-dom';

import { useDispatch, useLocation } from '@umijs/max';

import { useRequest, useToggle } from 'ahooks';
import { Avatar, Button, Dropdown, Form, Menu, Row, Segmented, Tooltip, Upload, message } from 'antd';
import clsx from 'clsx';
import qs from 'qs';

import {
  Icon,
  OperationContainer,
  QueryResultContainer,
  StatusTag,
  TextBody,
  TextEllipsisDetect,
} from '@zhongan/nagrand-ui';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { InstituteDetailV2 } from 'genesis-web-service';
import { ChannelService, InstituteTypeEnum } from 'genesis-web-service';
import type { BizDictItem } from 'genesis-web-service/lib/foundation/foundation.interface';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { PaginationComponent } from '@/components/Pagination';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { CardList } from '@/pages/common-party/partner-setting-v2/components/CardList/CardList';
import { ConditionalSearch } from '@/pages/common-party/partner-setting-v2/components/ConditionalSearch';
import { useEnumsMapping } from '@/pages/common-party/partner-setting-v2/hooks/useEnumsMapping';
import { usePartnerManagementPermission } from '@/pages/common-party/partner-setting-v2/hooks/usePartnerSettingPermission';
import type {
  InstituteRouteState,
  InstituteSearchForm,
} from '@/pages/common-party/partner-setting-v2/models/index.interface';
import { InstituteStatus } from '@/pages/common-party/partner-setting-v2/models/index.interface';
import { CardPageSizeOptions } from '@/pages/common-party/utils/constants';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';
import { NewChannelService } from '@/services/channel/channel.service.new';
import { NewMetadataService } from '@/services/metadata/metadata.service.new';
import { DefaultPaginationMapping, ListShownType, ModeEnum, SelectType } from '@/types/common';
import { PlusOutlined } from '@ant-design/icons';

import { ApproveModeContext } from '../../hooks/useContext';
import style from '../../style.scss';
import { BatchUploadContent } from './BatchUploadContent';
import { PartnerListTable } from './PartnerListTable';
import { useSearchFields } from './useSearchFields';

export const supportOldVersionTypes = [InstituteTypeEnum.CLINIC, InstituteTypeEnum.HOSPITAL];

/**
 *
 * @description partner setting institute menu component
 */
export const PartnerManagement = () => {
  const { t } = useTranslation('partner');
  const navigate = useNavigate();
  const [searchForm] = Form.useForm();
  const dispatch = useDispatch();
  const { drop } = useAliveController();
  const { state } = useLocation() as {
    state: InstituteRouteState<InstituteSearchForm>;
  };
  const [searchParams] = useSearchParams();
  const { reload } = qs.parse(searchParams.toString()) as unknown as { reload: 1 | 0 };
  const { canEdit } = usePartnerManagementPermission();

  const [currentMode, setCurrentMode] = useState<ListShownType>(state?.currentMode || ListShownType.TABLE);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: DefaultPaginationMapping[ListShownType.CARD],
  });
  const [searchValue, setSearchValue] = useState<Record<string, any>>({});
  const [batchUploadDrawerVisible, { toggle: toggleBatchUploadDrawerVisible }] = useToggle();
  useToggle();
  const [uploading, setUploading] = useState(false);
  const typeEnums = useTenantBizDict('instituteType') as BizDictItem[];
  const partnerDataUpdateModeEnums = useTenantBizDict('partnerDataUpdateMode') as BizDictItem[];
  const instituteStatusMapping = useEnumsMapping('instituteStatus');
  const instituteTypeMapping = useEnumsMapping('instituteType');
  const [enableApproval, setEnableApproval] = useState(false);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: ['instituteType', 'instituteStatus', 'batchUploadPolicyViewStatus', 'partnerDataUpdateMode'],
    });
  }, [dispatch]);

  useEffect(() => {
    NewMetadataService.TenantConfigMgmtService.tenantConfig('partner-approval').then(res => {
      setEnableApproval(res.enableApproval);
    });
  }, []);

  useEffect(() => {
    setPagination({
      current: pagination?.current || 1,
      pageSize: DefaultPaginationMapping[ListShownType.CARD],
    });
  }, [currentMode]);

  useEffect(() => {
    setPagination({ current: 1, pageSize: pagination?.pageSize });
  }, [searchValue]);

  useEffect(() => {
    if (reload?.toString() === '1') {
      // 新增完回来刷新页面
      setPagination({
        ...pagination,
      });
    }
  }, [reload]);

  // 初始化
  useEffect(() => {
    if (state) {
      setPagination(state?.pagination);
      setSearchValue(state?.searchForm);
    }
  }, []);

  const {
    data: instituteListResp,
    run: getInstituteList,
    loading,
  } = useRequest(
    async () => {
      return await ChannelService.getInstitutesSearch({
        pageIndex: pagination?.current - 1,
        pageSize: pagination?.pageSize,
        condition: searchValue,
      });
    },
    {
      refreshDeps: [pagination],
      debounceWait: 300,
    }
  );

  const addInstitute = useCallback(() => {
    navigate(
      `/partner-setting-v2/partner-details?${qs.stringify({
        modeType: ModeEnum.ADD,
      })}`,
      {
        state: {
          pagination: pagination,
          searchForm: searchForm.getFieldsValue(),
          currentMode: currentMode,
        },
      }
    );
  }, [pagination, searchForm]);

  const viewDetail = useCallback(
    async (id: number, modeType: ModeEnum) => {
      drop('cardActionButton');
      if (modeType === ModeEnum.EDIT) {
        const editRecord = instituteListResp.data.find(item => item.id === id);
        const res = await NewChannelService.InstitutePackToApproveService.check({
          instituteCode: editRecord.instituteCode,
          instituteType: editRecord.instituteType,
        });
        if (!res.canEdit) {
          message.error(t('There are still modification tasks pending approval. Please complete the approval first.'));
          return;
        }
      }
      navigate(
        `/partner-setting-v2/partner-details?${qs.stringify({
          id: String(id),
          modeType: modeType || ModeEnum.READ,
        })}`,
        {
          state: {
            pagination: pagination,
            searchForm: searchForm.getFieldsValue(),
            currentMode: currentMode,
          },
        }
      );
    },
    [pagination, searchValue, currentMode, instituteListResp]
  );

  const renderCardContent = (record: InstituteDetailV2) => (
    <div className={style.instituteCardContainer}>
      <div className={style.instituteCardTop}>
        <TextBody type="body">{t('Partner Code')}</TextBody>
        <TextBody type="body">
          <Tooltip title={record?.instituteCode}>
            <TextEllipsisDetect text={record?.instituteCode} maxWidth={300} />
          </Tooltip>
        </TextBody>
        <div className={style.statusWrapper}>
          <StatusTag
            statusI18n={<>{instituteStatusMapping[record?.instituteStatus]}</>}
            type={record?.instituteStatus === InstituteStatus.ACTIVE ? 'info' : 'no-status'}
          />
          <Avatar size={24} gap={6}>
            {record?.instituteName?.[0]}
          </Avatar>
        </div>
      </div>
      <div className={style.instituteCardBottom}>
        <div>
          <TextBody type="caption">{t('Partner Type')}</TextBody>
          <TextBody type="caption">{instituteTypeMapping[record?.instituteType]}</TextBody>
        </div>
        <div>
          <TextBody type="caption">{t('Partner Name')}</TextBody>
          <TextBody type="caption">
            <TextEllipsisDetect text={record?.instituteName} maxWidth={200} />
          </TextBody>
        </div>
      </div>
    </div>
  );

  const renderHoverContent = (record: InstituteDetailV2) => (
    <div className={clsx([style.instituteCardContainer, style.transparentWrapper])}>
      <KeepAlive name="cardActionButton">
        <div className={style.instituteCardBottom}>
          <Tooltip title={t('View')}>
            <Button type="text" style={{ padding: 0 }}>
              <Icon type="view" onClick={() => viewDetail(record.id, ModeEnum.READ)} />
            </Button>
          </Tooltip>
          <Tooltip title={t('Edit')}>
            <Button type="text" style={{ padding: 0, marginLeft: 16 }} disabled={!canEdit}>
              <Icon type="edit" onClick={() => viewDetail(record.id, ModeEnum.EDIT)} />
            </Button>
          </Tooltip>
        </div>
      </KeepAlive>
    </div>
  );

  const handleCloseDrawer = () => {
    toggleBatchUploadDrawerVisible();
    getInstituteList();
  };

  const downloadInstitute = useCallback((instituteType: string) => {
    ChannelService.download({
      // type: UploadType.HospitalAndClinic,
      type: 'HOSPITAL_CLINIC_FULL',
      ext: JSON.stringify({ instituteType }),
    }).then(downloadFile);
  }, []);

  const uploadInstitute = useCallback((instituteType: string) => {
    const customRequest = options => {
      const formData = new FormData();
      formData.append('file', options.file);
      formData.append('type', 'HOSPITAL_CLINIC_FULL');
      formData.append('ext', JSON.stringify({ instituteType }));
      ChannelService.upload(formData)
        .then(response => {
          options.onSuccess(response);
        })
        .catch((error: Error) => {
          message.error(error?.message);
          setUploading(false);
        });
    };
    return getUploadPropsNew(
      `/api/channel/v2/institutes/template-data/${instituteType}/`,
      () => {
        setSearchValue({ ...searchValue });
      },
      setUploading,
      null,
      customRequest
    );
  }, []);

  const getDropdownMenu = useCallback(
    (type: SelectType) => {
      const clickFuncMap: Record<SelectType, Function> = {
        [SelectType.UPLOAD]: () => {},
        [SelectType.DOWNLOAD]: downloadInstitute,
      };

      return (
        <Menu>
          {typeEnums
            ?.filter(item => supportOldVersionTypes.includes(item.dictValue as InstituteTypeEnum))
            ?.map(item => (
              <Menu.Item onClick={() => clickFuncMap[type](item.value)} key={item.value}>
                {type === SelectType.UPLOAD ? (
                  <Upload className={style.instituteUpload} {...uploadInstitute(item.value)}>
                    {item.label}
                  </Upload>
                ) : (
                  <a>{item.label}</a>
                )}
              </Menu.Item>
            ))}
        </Menu>
      );
    },
    [typeEnums, addInstitute, downloadInstitute, uploadInstitute]
  );

  const searchFields = useSearchFields();

  return (
    <ApproveModeContext.Provider value={{ enableApproval: enableApproval }}>
      <Fragment>
        <div style={{ backgroundColor: 'var(--white)' }}>
          <Row className={style.contentTitle}>
            <TextBody type="h5" weight="bold">
              {t('Partner Management')}
            </TextBody>
          </Row>

          <Row className={style.mainSearchButtonGroup}>
            <ConditionalSearch
              form={searchForm}
              fields={searchFields}
              onSearch={values => {
                setSearchValue(values);
                setPagination({ current: 1, pageSize: pagination?.pageSize });
              }}
              loading={loading}
              mainSearchFieldKey={['instituteCodeOrName']}
              initialValue={searchValue}
            />
            <div className="flex-between">
              {partnerDataUpdateModeEnums?.[0].enumItemName === 'FULL_DATA_REPLACEMENT' && (
                <>
                  <Dropdown overlay={getDropdownMenu(SelectType.UPLOAD)} trigger={['click']} disabled={!canEdit}>
                    <Button
                      size="large"
                      shape="round"
                      icon={<Icon type="upload" style={{ fontSize: 'var(--font-size-lg)' }} />}
                      style={{ marginRight: 'var(--gap-sm)' }}
                      loading={uploading}
                    >
                      {t('Upload')}
                    </Button>
                  </Dropdown>
                  <Dropdown overlay={getDropdownMenu(SelectType.DOWNLOAD)} trigger={['click']}>
                    <Button
                      size="large"
                      shape="round"
                      style={{ marginRight: 'var(--gap-sm)' }}
                      icon={<Icon type="download" style={{ fontSize: 'var(--font-size-lg)' }} />}
                    >
                      {t('Download')}
                    </Button>
                  </Dropdown>
                </>
              )}
              {!enableApproval && (
                <Button
                  size="large"
                  shape="round"
                  icon={<Icon type="upload" style={{ fontSize: 'var(--font-size-lg)' }} />}
                  style={{ marginRight: 'var(--gap-sm)' }}
                  onClick={toggleBatchUploadDrawerVisible}
                >
                  {t('Batch Upload')}
                </Button>
              )}
              <Button
                onClick={addInstitute}
                disabled={!canEdit}
                style={{ marginLeft: 'var(--gap-sm)' }}
                icon={<PlusOutlined style={{ fontSize: 'var(--font-size-lg)' }} />}
                size="large"
                shape="round"
                type="primary"
              >
                {t('Add New')}
              </Button>
            </div>
          </Row>
        </div>

        <QueryResultContainer>
          <OperationContainer>
            <OperationContainer.Left>
              <TextBody weight="bold">{t('Partner List')}</TextBody>
            </OperationContainer.Left>
            <OperationContainer.Right>
              <Segmented
                className={style.segmentController}
                value={currentMode}
                onChange={value => {
                  setCurrentMode(value as ListShownType);
                }}
                options={[
                  {
                    value: ListShownType.TABLE,
                    icon: <Icon type="business-category" style={{ fontSize: 'var(--font-size-lg)' }} />,
                  },
                  {
                    value: ListShownType.CARD,
                    icon: <Icon type="card" style={{ fontSize: 'var(--font-size-lg)' }} />,
                  },
                ]}
              />
            </OperationContainer.Right>
          </OperationContainer>
          {currentMode === ListShownType.CARD ? (
            <CardList<InstituteDetailV2>
              data={instituteListResp?.data}
              rowKey="id"
              loading={loading}
              renderCardContent={renderCardContent}
              renderHoverContent={renderHoverContent}
            />
          ) : (
            <PartnerListTable loading={loading} data={instituteListResp?.data} viewDetail={viewDetail} />
          )}
          <PaginationComponent
            pagination={pagination}
            total={instituteListResp?.totalElements}
            pageSizeOptions={CardPageSizeOptions}
            showSizeChanger={currentMode === ListShownType.TABLE}
            handlePaginationChange={(current, pageSize) => setPagination({ current, pageSize })}
          />
        </QueryResultContainer>

        <DrawerForm
          title={t('Batch Upload')}
          visible={batchUploadDrawerVisible}
          onClose={handleCloseDrawer}
          cancelText={t('Close')}
          submitBtnShow={false}
          getContainer={() => document.getElementsByClassName('partner-global')[0] as HTMLElement}
        >
          <BatchUploadContent />
        </DrawerForm>
      </Fragment>
    </ApproveModeContext.Provider>
  );
};
