import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Button } from 'antd';
import type { ColumnProps } from 'antd/es/table';

import { Icon, StatusTag, Table, TableActionsContainer } from '@zhongan/nagrand-ui';

import type { BizDictItem, InstituteDetailV2 } from 'genesis-web-service';

import { RenderEnums } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { usePartnerManagementPermission } from '@/pages/common-party/partner-setting-v2/hooks/usePartnerSettingPermission';
import { ModeEnum } from '@/types/common';

import { InstituteStatus } from '../../models/index.interface';

interface TableProp {
  loading: boolean;
  data: InstituteDetailV2[];
  viewDetail: (id: number, mode: ModeEnum) => void;
}

/**
 *
 * @param loading 是否在加载
 * @param data 表格数据
 * @param viewDetail 跳转method
 * @description 仅用于partner setting institute menu component； 数据展示
 */
export const PartnerListTable = ({ loading, data, viewDetail }: TableProp) => {
  const { t } = useTranslation('partner');
  const typeEnums = useTenantBizDict('instituteType') as BizDictItem[];
  const { canEdit } = usePartnerManagementPermission();

  const columns: ColumnProps<InstituteDetailV2>[] = useMemo(
    () => [
      {
        title: t('Partner Code'),
        fixed: 'left',
        dataIndex: 'instituteCode',
      },
      {
        title: t('Partner Name'),
        dataIndex: 'instituteName',
      },
      {
        title: t('Partner Type'),
        dataIndex: 'instituteType',
        render: text => <RenderEnums enums={typeEnums} keyName={text} />,
      },
      {
        title: t('Partner Status'),
        dataIndex: 'instituteStatus',
        render: text => {
          return (
            <StatusTag
              statusI18n={text === InstituteStatus.ACTIVE ? t('Active') : t('Inactive')}
              type={text === InstituteStatus.ACTIVE ? 'info' : 'no-status'}
              needDot
            />
          );
        },
      },
      {
        title: t('Email'),
        dataIndex: 'email',
      },
      {
        title: t('Phone No.'),
        dataIndex: 'phoneNo',
      },
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (_, record: InstituteDetailV2) => (
          <TableActionsContainer>
            <Icon
              type="view"
              style={{ fontSize: 16, cursor: 'pointer' }}
              onClick={() => viewDetail(record.id, ModeEnum.READ)}
            />
            <Button type="link" disabled={!canEdit} style={{ padding: 0 }}>
              <Icon
                type="edit"
                style={{ fontSize: 16, cursor: 'pointer' }}
                onClick={() => viewDetail(record.id, ModeEnum.EDIT)}
              />
            </Button>
          </TableActionsContainer>
        ),
      },
    ],
    [typeEnums, canEdit, viewDetail, t]
  );

  return (
    <Table
      emptyType="icon"
      scroll={{ x: 'max-content' }}
      loading={loading}
      dataSource={data}
      columns={columns}
      rowKey="id"
      pagination={false}
      style={{ marginBottom: 16, marginTop: 16 }}
    />
  );
};
