import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, Upload, message } from 'antd';

import { Icon, UploadFileItem } from '@zhongan/nagrand-ui';

import { ChannelService, DownloadOrUploadType } from 'genesis-web-service';
import { security } from 'genesis-web-shared/lib';
import { isImgFile } from 'genesis-web-shared/lib/util/fileType';

import { DownloadOutlined } from '@ant-design/icons';

export const UploadButton = () => {
  const { t } = useTranslation('partner');
  const form = Form.useFormInstance();
  const attachments = Form.useWatch('attachments', form);

  const uploadProps = useMemo(() => {
    const customRequest = (options: Record<string, any>) => {
      const formData = new FormData();
      formData.append('file', options.file);
      formData.append('type', DownloadOrUploadType.APPROVAL_ATTACHMENT);
      formData.append('ext', JSON.stringify({ type: 3 }));

      ChannelService.channelUpload(formData)
        .then(response => {
          message.success(t('Uploaded successfully'));
          options.onSuccess(response.value);
        })
        .catch((error: Error) => {
          message.error(error?.message);
        });
    };
    return {
      name: 'file',
      accept: '.pdf,.doc,.xls,.xlsx,.png,.jpg,',
      headers: { ...security.csrf() },
      withCredentials: true,
      customRequest,
      showUploadList: false,
    };
  }, []);

  return (
    <>
      <Form.Item label={t('Attachment')} name="attachments">
        <Upload {...uploadProps}>
          <Button icon={<Icon type="upload" />}>{t('Upload')}</Button>
          <p className="text-xs text-[var(--text-disabled-color)]">{t('You can only upload PDF/DOC/XLS/PNG/JPG')}</p>
        </Upload>
      </Form.Item>
      {attachments?.fileList?.map(attachment => (
        <UploadFileItem
          key={attachment.id}
          fileName={attachment.name || ''}
          fileUrl={attachment?.response?.fileInsideUrl}
          needPreview={isImgFile(attachment.name)}
          hoverInfoList={[
            {
              key: 'downloadBtn',
              icon: (
                <a href={attachment?.response?.fileInsideUrl}>
                  <DownloadOutlined style={{ color: '#102A43' }} />
                </a>
              ),
              onClick: () => {},
            },
          ]}
        />
      ))}
    </>
  );
};
