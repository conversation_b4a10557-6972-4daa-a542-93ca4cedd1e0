import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Row } from 'antd';

import { useRequest } from 'ahooks';
import { get } from 'lodash-es';

import { EllipsisWithCount, Image, SectionContainer, SimpleSectionHeader } from '@zhongan/nagrand-ui';

import type { InstitutePackToApproveRequest } from 'genesis-web-service/service-types/channel-types/package';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { FieldType } from '@/components/CommonForm/interface';
import { renderEnumsWithString } from '@/components/RenderEnums';
import { useSchemaFormItemFieldsByCategory } from '@/pages/common-party/partner-setting-v2/hooks/useChannelPartnerSchemaFieldsByCategory';
import { NewMetadataService } from '@/services/metadata/metadata.service.new';

import { covertPartnerType2SchemaPartnerType } from '../../hooks/useChannelPartnerSchemaFieldsByCategory';
import { useInstituteBasicInfoFields } from '../../hooks/useFormFields';

interface Props {
  partner?: InstitutePackToApproveRequest;
}

export const BasicInfo = ({ partner }: Props): JSX.Element => {
  const [t] = useTranslation(['partner', 'common']);

  const [form] = Form.useForm();

  const { data: addressBizdicts } = useRequest(() =>
    NewMetadataService.BizDictMgmtService.queryBizDictConfigFile('address1')
  );

  const customDisplay = (customDisplayKey?: string) => {
    if (customDisplayKey === 'Investigation_Area') {
      return (
        <Col span={6}>
          <Form.Item label={t('Investigation Area')}>
            {partner?.areaList?.length > 0 ? (
              <EllipsisWithCount
                items={
                  partner?.areaList?.map(value => renderEnumsWithString(value, addressBizdicts || []) || value) || []
                }
              />
            ) : (
              t('--')
            )}
          </Form.Item>
        </Col>
      );
    }

    if (customDisplayKey === 'Contact_Number') {
      return (
        <Col span={6}>
          <Form.Item label={t('Contact Number')}>
            {partner.countryCode && partner.phoneNo ? `${partner.countryCode} ${partner.phoneNo}` : t('--')}
          </Form.Item>
        </Col>
      );
    }

    return t('--');
  };

  useEffect(() => {
    form.setFieldsValue(partner);
  }, [partner]);

  const fields = useInstituteBasicInfoFields({
    instituteDetail: partner,
    disabled: false,
    instituteTypeValue: partner?.instituteType,
    form,
  });

  const { formItems: dynamicFields } = useSchemaFormItemFieldsByCategory({
    staticOrDynamic: 'DYNAMIC',
    category: 'BASE',
    disabled: true,
    partnerType: covertPartnerType2SchemaPartnerType(partner?.instituteType),
  });

  return (
    <SectionContainer>
      <SimpleSectionHeader type="h5" weight="bold" style={{ marginTop: 8 }}>
        {t('Basic Info')}
      </SimpleSectionHeader>
      {partner.logoUrl && (
        <Row>
          <Col span={24}>
            <Image src={partner.logoUrl} width={118} height={118} />
          </Col>
        </Row>
      )}
      <Form style={{ marginTop: 24 }} layout="vertical">
        <Row>
          {[...fields.splice(1), ...dynamicFields].map(field => {
            if (field.type === FieldType.Customize) {
              return customDisplay(field.customDisplayKey);
            }
            let fieldValue = partner?.[field.key];
            if (Array.isArray(field.key) && field.key[0] === 'extensions') {
              // 动态字段
              fieldValue = get(partner, field.key);
            }
            let displayText = fieldValue || t('--');
            if (field.type === FieldType.Select) {
              displayText = renderEnumsWithString(fieldValue, field.ctrlProps.options) || t('--');
            } else if (field.type === FieldType.DatePicker) {
              displayText = dateFormatInstance.getDateString(dateFormatInstance.l10nMoment(fieldValue as string));
            } else if (field.type === FieldType.TimePicker) {
              displayText = dateFormatInstance.getDateTimeString(dateFormatInstance.l10nMoment(fieldValue as string));
            }
            return (
              <Col span={6}>
                <Form.Item label={field.label}>{displayText}</Form.Item>
              </Col>
            );
          })}
        </Row>
      </Form>
    </SectionContainer>
  );
};

export default BasicInfo;
