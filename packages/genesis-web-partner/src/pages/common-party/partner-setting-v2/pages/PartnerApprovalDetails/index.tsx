import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DownloadOutlined } from '@ant-design/icons';
import { Breadcrumb, Button, Divider, Form, Layout, Skeleton, message } from 'antd';

import { useDispatch, useLocation, useNavigate, useSearchParams } from '@umijs/max';

import { useRequest } from 'ahooks';
import clsx from 'clsx';
import qs from 'qs';

import { Icon, Input, Modal, SectionContainer, UploadFileItem } from '@zhongan/nagrand-ui';

import { MetadataService } from 'genesis-web-service';
import type { ApprovalTaskReviewRequest } from 'genesis-web-service/service-types/channel-types/package';
import { isImgFile } from 'genesis-web-shared/lib/util/fileType';

import { ChannelSchemaFieldsContext } from '@/hooks/ChannelSchemaFieldsContext';
import { useChannelPartnerSchemaFields } from '@/hooks/useGenericChannelSchemaFields';
import { usePermission } from '@/hooks/usePermissions';
import { useAuthUserInfo } from '@/hooks/useUserInfo';
import { UploadButton } from '@/pages/common-party/partner-management-approval/components/UploadButton';
import { canApproveOwnTaskDisabled, stateDisabled } from '@/pages/common-party/partner-management-approval/util';
import { getUrlByFileUniqueCode } from '@/pages/common-party/utils/util';
import { NewChannelService } from '@/services/channel/channel.service.new';
import { ModeEnum } from '@/types/common';
import { EmployeeWorkScheduleTypeTabEnum } from '@/types/common-party';

import { AccountTable } from '../../components/AccountTable';
import { AddressTable } from '../../components/AddressTable';
import { ContactPersonTable } from '../../components/ContactTable';
import { DepartmentAndDoctor } from '../../components/DepartmentAndDoctor';
import { ExperiencedFees } from '../../components/ExperiencedFeeTable';
import { ServiceAgreementTable } from '../../components/ServiceAgreementTable';
import { StaffTable } from '../../components/StaffTable/Staff';
import {
  AccountShownTypes,
  ContactShownTypes,
  ExperiencedFeeShownTypes,
  ServiceAgreementShownTypes,
  StaffShownTypes,
} from '../../constant';
import { ApproveModeContext, WorkScheduleTypeContext } from '../../hooks/useContext';
import BasicInfo from './BasicInfo';
import { PartnerSider } from './PartnerSider';
import styles from './style.scss';

export const PartnerApprovalDetails = (): JSX.Element => {
  const [t] = useTranslation(['partner', 'common']);
  const [searchParams] = useSearchParams();
  const { id } = qs.parse(searchParams.toString()) as { id: string };
  const [form] = Form.useForm();
  const [mode, setMode] = useState<'view' | 'edit'>('view');
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { state } = useLocation();
  const hasApprovalAuth = usePermission('channel.partner-approval.edit');
  const userInfo = useAuthUserInfo();

  const { schemaDefFields, schemaUsedBizDictMap } = useChannelPartnerSchemaFields();
  const [isTenantIncludesWorkType, setTenantIncludesWorkType] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [actionType, setActionType] = useState<'accept' | 'reject'>();

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'currencys',
        'country',
        'instituteType',
        'accessLevel',
        'instituteGrade',
        'language',
        'certiType',
        'organizationIdType',
        'yesNo',
        'hospitalExperiencedFeeType',
        'surgery',
        'medicalBillItem',
        'diagnosisSource',
        'doctorCertificateType',
        'companyAddressType',
        'addressModel',
        'channelCustomerType',
        'gender',
        'accountType',
        'paymentMethod',
        'channelOrgAccountType',
        'employeeStatus',
        'workingType',
        'agreementSettlementType',
        'salesAgreementStatus',
        'instituteStatus',
        'officeCity',
      ],
    });

    // 获取租户配置workScheduleType
    MetadataService.queryBizDictConfigList('workScheduleType').then(res => {
      const bizDictConfigList = res.tenantBizDictConfig?.workScheduleType?.bizDictConfigList || [];
      const isTenantConfigIncludesWorkingType = bizDictConfigList?.some(
        workScheduleType => workScheduleType?.enumItemName === EmployeeWorkScheduleTypeTabEnum.WORKING_TYPE
      );
      setTenantIncludesWorkType(isTenantConfigIncludesWorkingType);
    });
  }, []);

  const { data: { enableApproval, canApproveOwnTask } = {} } = useRequest(() =>
    MetadataService.getPartnerApprovalSwitch()
  );

  const { data: partner } = useRequest(() => NewChannelService.InstitutePackToApproveService.queryTask(+id));

  const { data: taskInfo } = useRequest(() => NewChannelService.ApprovalService.queryTask(+id));

  const handleBack = useCallback(() => {
    navigate(`/partner-management-approval`, {
      state,
    });
  }, [state]);

  const onOk = async () => {
    await form.validateFields();
    const { comment, attachments } = form.getFieldsValue();

    const requestList: ApprovalTaskReviewRequest[] = [
      {
        taskId: +id,
        comment,
        attachments,
      },
    ];
    if (actionType === 'accept') {
      await NewChannelService.ApprovalService.approve(requestList);
      message.success(t('Accept Successful'));
    } else {
      await NewChannelService.ApprovalService.reject(requestList);
      message.success(t('Reject Successful'));
    }
    setModalVisible(false);
    handleBack();
  };

  const renderEditButton = () => {
    // 没有权限
    if (!hasApprovalAuth || !enableApproval) {
      return null;
    }
    // 状态不满足
    if (!taskInfo || stateDisabled(taskInfo) || canApproveOwnTaskDisabled(taskInfo, canApproveOwnTask, userInfo)) {
      return null;
    }
    return (
      <Button size="large" onClick={() => setMode('edit')} ghost type="primary">
        {t('Edit')}
      </Button>
    );
  };

  return (
    <ApproveModeContext.Provider value={{ enableApproval: true }}>
      <WorkScheduleTypeContext.Provider value={{ isTenantIncludesWorkType }}>
        <ChannelSchemaFieldsContext.Provider
          value={{
            channelSchemaDefFields: schemaDefFields,
            schemaUsedBizDictMap: schemaUsedBizDictMap,
          }}
        >
          <Skeleton active loading={!partner}>
            <Layout className={styles.channelContent}>
              <Layout.Content style={{ overflowY: 'auto' }}>
                <Layout.Header className={styles.headerContainer}>
                  <>
                    <Button onClick={handleBack} type="link" icon={<Icon type="left" />}>
                      {t('Back to task pool')}
                    </Button>
                    <Divider type="vertical" />
                    <Breadcrumb separator={<Divider type="vertical" />} className={styles.breadCrumb}>
                      <Breadcrumb.Item className={clsx([styles.breadCrumbItems])}>
                        <Icon style={{ marginRight: 8 }} type="detail" />
                        {t('Partner Details')}
                      </Breadcrumb.Item>
                    </Breadcrumb>
                  </>
                </Layout.Header>
                <Layout.Content style={{ padding: 8 }}>
                  <BasicInfo partner={partner} />

                  <SectionContainer style={{ marginTop: 16 }}>
                    <AddressTable
                      isApprovalPage
                      initialList={partner?.addresses}
                      instituteId={partner?.id}
                      mode={ModeEnum.READ}
                      loading={false}
                      instituteType={partner?.instituteType}
                    />
                  </SectionContainer>

                  {ContactShownTypes.includes(partner?.instituteType) && (
                    <SectionContainer style={{ marginTop: 16 }}>
                      <ContactPersonTable
                        isApprovalPage
                        initialList={partner?.customers}
                        instituteId={partner?.id}
                        mode={ModeEnum.READ}
                        instituteType={partner?.instituteType}
                      />
                    </SectionContainer>
                  )}

                  {AccountShownTypes.includes(partner?.instituteType) && (
                    <SectionContainer style={{ marginTop: 16 }}>
                      <AccountTable
                        isApprovalPage
                        initialList={partner?.accounts}
                        instituteId={partner?.id}
                        mode={ModeEnum.READ}
                        instituteType={partner?.instituteType}
                      />
                    </SectionContainer>
                  )}

                  {ServiceAgreementShownTypes.includes(partner?.instituteType) && (
                    <SectionContainer style={{ marginTop: 16 }}>
                      <ServiceAgreementTable
                        isApprovalPage
                        readonly
                        instituteType={partner?.instituteType}
                        serviceAgreements={partner.serviceAgreements}
                        renew={true}
                      />
                    </SectionContainer>
                  )}
                  {StaffShownTypes.includes(partner?.instituteType) && (
                    <SectionContainer style={{ marginTop: 16 }}>
                      <StaffTable
                        isApprovalPage
                        readonly
                        instituteType={partner?.instituteType}
                        staffs={partner?.staffs}
                      />
                    </SectionContainer>
                  )}
                  {ExperiencedFeeShownTypes.includes(partner?.instituteType) && (
                    <SectionContainer style={{ marginTop: 16 }}>
                      <DepartmentAndDoctor
                        doctors={partner.doctors}
                        departments={partner.departments}
                        isApprovalPage
                        readonly
                        instituteType={partner?.instituteType}
                      />
                      <ExperiencedFees
                        experiencedFees={partner.experiencedFees}
                        isApprovalPage
                        readonly
                        instituteType={partner?.instituteType}
                      />
                    </SectionContainer>
                  )}
                </Layout.Content>
              </Layout.Content>
              <PartnerSider loading={false}>
                <div style={{ padding: 16 }}>
                  {partner?.documents?.map((item, index) => {
                    const url = getUrlByFileUniqueCode('/api/channel/v2/file/download', item.fileUniqueCode);
                    return (
                      <UploadFileItem
                        key={item.id}
                        fileName={item.fileName || ''}
                        fileUrl={url}
                        needPreview={isImgFile(item.fileName)}
                        style={{
                          marginBottom: 16,
                          display: index < 10 ? '' : 'none',
                          minWidth: 'auto',
                        }}
                        hoverInfoList={[
                          {
                            key: 'downloadBtn',
                            icon: (
                              <a href={url}>
                                <DownloadOutlined style={{ color: '#102A43' }} />
                              </a>
                            ),
                            onClick: () => {},
                          },
                        ]}
                      />
                    );
                  })}
                </div>
              </PartnerSider>
            </Layout>
            <Layout.Footer className={styles.footerContainer}>
              {mode === 'edit' ? (
                <>
                  <Button
                    type="primary"
                    size="large"
                    onClick={() => {
                      setActionType('accept');
                      setModalVisible(true);
                    }}
                  >
                    {t('Accept')}
                  </Button>
                  <Button
                    danger
                    size="large"
                    style={{ marginRight: 16 }}
                    onClick={() => {
                      setActionType('reject');
                      setModalVisible(true);
                    }}
                  >
                    {t('Reject')}
                  </Button>
                </>
              ) : (
                renderEditButton()
              )}
              <Button size="large" style={{ marginRight: 16 }} onClick={handleBack}>
                {t('Cancel')}
              </Button>
            </Layout.Footer>
          </Skeleton>
          <Modal
            title={actionType === 'accept' ? t('Accept') : t('Reject')}
            open={modalVisible}
            onOk={onOk}
            onCancel={() => {
              form.resetFields();
              setModalVisible(false);
            }}
            onClose={() => {
              form.resetFields();
              setModalVisible(false);
            }}
          >
            <div>
              <Form layout="vertical" form={form}>
                <div style={{ marginBottom: 24 }}>{t('Are you sure to {{type}} this task?', { type: actionType })}</div>
                <Form.Item label={t('Comments')} name="comment">
                  <Input.TextArea placeholder={t('Please Input')} />
                </Form.Item>
                <UploadButton form={form} />
              </Form>
            </div>
          </Modal>
        </ChannelSchemaFieldsContext.Provider>
      </WorkScheduleTypeContext.Provider>
    </ApproveModeContext.Provider>
  );
};

export default PartnerApprovalDetails;
