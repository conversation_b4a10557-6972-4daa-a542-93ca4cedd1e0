/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { useState } from 'react';

import { Button, Layout } from 'antd';
import clsx from 'clsx';

import { Icon } from '@zhongan/nagrand-ui';

import styles from './style.scss';

export const PartnerSider = ({ loading, children }: { loading: boolean; children: React.ReactNode }) => {
  const [isSiderCollapsed, setSiderCollapsed] = useState(false);

  return (
    <>
      <Button
        type="link"
        className={styles.sideMenuIcons}
        onClick={() => setSiderCollapsed(boolean => !boolean)}
        disabled={loading}
      >
        <Icon type="questionnaire-management" style={{ fontSize: 20 }} />
      </Button>
      <Layout.Sider
        className={clsx([styles.siderContainer, isSiderCollapsed && styles.siderWithoutBorder])}
        collapsedWidth={0}
        collapsible={true}
        collapsed={isSiderCollapsed}
        width={340}
        trigger={null}
        theme="light"
      >
        <div className={styles.siderHeaderContainer}>
          <div onClick={() => setSiderCollapsed(true)}>
            <Icon type="unfold-icon" style={{ fontSize: 20 }} />
          </div>
        </div>
        {children}
      </Layout.Sider>
    </>
  );
};
