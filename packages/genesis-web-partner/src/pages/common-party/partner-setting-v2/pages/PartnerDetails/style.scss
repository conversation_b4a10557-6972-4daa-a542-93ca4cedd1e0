@import '@/variables.scss';

.channel-content {
  height: calc(100vh - 73px);
  overflow-y: scroll;
  background-color: var(--white);
  .header-container {
    display: flex;
    position: sticky;
    top: 0;
    z-index: 3;
    width: 100%;
    align-items: center;
    background-color: var(--white);
    padding: 0 $gap-lg;
    height: 48px;
    line-height: 48px;
    border-bottom: 1px solid var(--default-color-bg);

    :global {
      .#{$antd-prefix}-btn-link {
        padding: 0;
        cursor: pointer;
      }
      .#{$antd-prefix}-divider-vertical {
        margin: 0 $gap-md;
        height: 1em;
        color: var(--text-color-secondary);
        top: 0;
      }
    }
  }
  .partner-main-title-container {
    margin-bottom: $gap-md;
    display: flex;
    align-items: center;

    :global {
      .nagrand-body-text.h4 {
        margin-right: $gap-md;
        display: inline-block;
      }
    }
  }
  :global {
    .nagrand-body-text.h5 {
      height: $font-size-hg;
      line-height: $font-size-hg;
      padding-left: var(--gap-xss);
      .#{$antd-prefix}-divider-vertical {
        margin-left: 0;
        width: var(--gap-xss);
        height: $font-size-hg;
        line-height: $font-size-hg;
        background-color: var(--primary-color);
        border-radius: 2px;
      }
    }
    .#{$antd-prefix}-skeleton-avatar-square {
      border-radius: var(--border-radius-big);
    }
    .#{$antd-prefix}-divider-horizontal {
      border-top: 1px solid var(--default-color-bg);
    }
    .#{$antd-prefix}-table-cell-fix-right {
      min-width: 92px;
    }
    .#{$antd-prefix}-input-textarea-show-count::after {
      color: var(--input-placeholder-color);
      position: relative;
      top: -28px;
      right: 20px;
    }
  }

  .side-menu-icons {
    cursor: pointer;
    position: fixed;
    right: 24px;
    top: 6px;
    z-index: 7;
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    border: 1px solid var(--primary-color);
    background-color: #e0f1ff;
    span {
      color: var(--primary-color);
    }
  }

  .sider-without-border {
    border-left: 0px !important;
  }

  .sider-container {
    border-left: 1px solid var(--default-color-bg);
    :global {
      .#{$antd-prefix}-layout-sider-zero-width-trigger-right {
        top: 5px;
        height: 36px;
        left: 0;
        z-index: 4;
      }
    }
    .sider-header-container {
      height: 48px;
      border-bottom: 1px solid var(--default-color-bg);
      padding: $gap-md;
      display: flex;
      align-items: center;
      span {
        cursor: pointer;
      }
    }
  }
}

.footer-container {
  height: 72px;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  padding: 0 $gap-lg;
  background-color: var(--white);
  border-top: 1px solid var(--default-color-bg);
}
