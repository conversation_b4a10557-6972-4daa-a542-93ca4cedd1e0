import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

import { useDispatch, useLocation, useNavigate, useSelector } from '@umijs/max';

import { Button, Divider, Form, Layout, Popconfirm, Skeleton, message } from 'antd';
import moment from 'moment';
import qs from 'qs';

import { Icon, Modal, StatusLabel, TextBody } from '@zhongan/nagrand-ui';

import type { InstituteDetailV2, InstituteFormDTOV2, InstituteQueryV2 } from 'genesis-web-service';
import { ChannelService, InstituteTypeEnum, MetadataService, YesOrNo } from 'genesis-web-service';
import type { InstitutePackToApproveRequest } from 'genesis-web-service/service-types/channel-types/package/index';
import { InstituteStatusEnum, YesNoEnum } from 'genesis-web-service/service-types/channel-types/package/index';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { ChannelSchemaFieldsContext } from '@/hooks/ChannelSchemaFieldsContext';
import { useChannelPartnerSchemaFields } from '@/hooks/useChannelPartnerSchemaFields';
import type { ConnectState } from '@/models/connect';
import { AccountTable } from '@/pages/common-party/partner-setting-v2/components/AccountTable';
import { AddressTable } from '@/pages/common-party/partner-setting-v2/components/AddressTable';
import { BasicInfo } from '@/pages/common-party/partner-setting-v2/components/BasicInfo';
import { ContactPersonTable } from '@/pages/common-party/partner-setting-v2/components/ContactTable';
import { DepartmentAndDoctor } from '@/pages/common-party/partner-setting-v2/components/DepartmentAndDoctor';
import { ExperiencedFees } from '@/pages/common-party/partner-setting-v2/components/ExperiencedFeeTable';
import {
  AddNewLevelButton,
  PartnerBreadCrumbNav,
  PartnerStructure,
} from '@/pages/common-party/partner-setting-v2/components/PartnerStructure';
import { RelationshipList } from '@/pages/common-party/partner-setting-v2/components/RelationshipList';
import { ServiceAgreementTable } from '@/pages/common-party/partner-setting-v2/components/ServiceAgreementTable';
import { StaffTable } from '@/pages/common-party/partner-setting-v2/components/StaffTable/Staff';
import { UploadFile } from '@/pages/common-party/partner-setting-v2/components/UploadFile';
import {
  ApproveModeContext,
  InstituteInformationContext,
  WorkScheduleTypeContext,
} from '@/pages/common-party/partner-setting-v2/hooks/useContext';
import { InstituteStatus } from '@/pages/common-party/partner-setting-v2/models/index.interface';
import { PartnerSider } from '@/pages/common-party/partner-setting-v2/pages/PartnerDetails/PartnerSider';
import { useEnumsMapping } from '@/pages/common-party/partner-setting/hooks/useEnumsMapping';
import { NewChannelService } from '@/services/channel/channel.service.new';
import { NewMetadataService } from '@/services/metadata/metadata.service.new';
import { ModeEnum } from '@/types/common';
import type { LocationQueryParam } from '@/types/common-party';
import { EmployeeWorkScheduleTypeTabEnum } from '@/types/common-party';

import {
  AccountShownTypes,
  ContactShownTypes,
  ExperiencedFeeShownTypes,
  RelationshipShownTypes,
  ServiceAgreementShownTypes,
  StaffShownTypes,
} from '../../constant';
import styles from './style.scss';

/**
 *
 * @description institute detail页入口
 */
const PartnerDetailsPage = () => {
  const [searchParams] = useSearchParams();
  const {
    id: instituteId,
    modeType,
    instituteType,
    parentId,
  } = qs.parse(searchParams.toString()) as unknown as LocationQueryParam;
  const { t } = useTranslation('partner');
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { state } = useLocation();
  const { tenantInfo } = useSelector(({ partnerManagement }: ConnectState) => ({
    tenantInfo: partnerManagement.tenantInfo,
  }));
  const [form] = Form.useForm();

  const instituteNameRef = useRef(null);
  const documentsRef = useRef(null);
  const accountRef = useRef(null);
  const addressRef = useRef(null);
  const customerRef = useRef(null);
  const serviceAgreementRef = useRef(null);
  const staffRef = useRef(null);
  const doctorRef = useRef(null);
  const departmentRef = useRef(null);
  const experiencedFeeRef = useRef(null);

  const [mode, setMode] = useState<ModeEnum>();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [isActive, setActive] = useState(true);
  const [isTenantIncludesWorkType, setTenantIncludesWorkType] = useState(false);
  const [institute, setInstitute] = useState<InstituteDetailV2>();
  const [enableApproval, setEnableApproval] = useState(false);

  const instituteStatusMapping = useEnumsMapping('instituteStatus');
  const currentInstituteI18n = instituteStatusMapping[isActive ? InstituteStatus.ACTIVE : InstituteStatus.INACTIVE];
  const aimToChangeInstituteI18n =
    instituteStatusMapping[!isActive ? InstituteStatus.ACTIVE : InstituteStatus.INACTIVE];

  const instituteTypeFormValue = Form.useWatch('instituteType', form);

  const { channelPartnerSchemaDefFields, schemaUsedBizDictMap } = useChannelPartnerSchemaFields();
  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'currencys',
        'country',
        'instituteType',
        'accessLevel',
        'instituteGrade',
        'language',
        'certiType',
        'organizationIdType',
        'yesNo',
        'hospitalExperiencedFeeType',
        'surgery',
        'medicalBillItem',
        'diagnosisSource',
        'doctorCertificateType',
        'companyAddressType',
        'addressModel',
        'channelCustomerType',
        'gender',
        'accountType',
        'paymentMethod',
        'channelOrgAccountType',
        'employeeStatus',
        'workingType',
        'agreementSettlementType',
        'salesAgreementStatus',
        'instituteStatus',
        'officeCity',
      ],
    });

    // 获取租户配置workScheduleType
    MetadataService.queryBizDictConfigList('workScheduleType').then(res => {
      const bizDictConfigList = res.tenantBizDictConfig?.workScheduleType?.bizDictConfigList || [];
      const isTenantConfigIncludesWorkingType = bizDictConfigList?.some(
        workScheduleType => workScheduleType?.enumItemName === EmployeeWorkScheduleTypeTabEnum.WORKING_TYPE
      );
      setTenantIncludesWorkType(isTenantConfigIncludesWorkingType);
    });

    return () => setTenantIncludesWorkType(false);
  }, [dispatch]);

  useEffect(() => {
    NewMetadataService.TenantConfigMgmtService.tenantConfig('partner-approval').then(res => {
      setEnableApproval(res.enableApproval);
    });
  }, []);

  useEffect(() => {
    if (modeType) {
      setMode(modeType);
    }
  }, [modeType]);

  const handleBack = useCallback(() => {
    navigate(`/partner-setting-v2/?reload=1`, {
      state,
    });
  }, [state]);

  const getInstitute = useCallback((id: number) => {
    setLoading(true);
    ChannelService.getInstituteByIdV2<InstituteDetailV2>(id)
      .then(setInstitute)
      .catch((error: Error) => message.error(error.message))
      .finally(() => setLoading(false));
  }, []);

  useEffect(() => {
    if (instituteId) {
      getInstitute(+instituteId);
    } else {
      form.resetFields();
      setInstitute({ instituteType } as InstituteDetailV2);
    }
  }, [getInstitute, instituteId]);

  useEffect(() => {
    if (institute) {
      setActive(institute?.instituteStatus === InstituteStatus.ACTIVE);
      form.setFieldsValue({
        ...institute,
        grade: ((institute?.grade as string) || undefined)?.split(','),
        setUpDate: institute?.setUpDate && moment(institute.setUpDate),
      });
    }
  }, [institute, form]);

  const packInstitute = async (isChangeStatus: boolean) => {
    const values: InstituteFormDTOV2 = await form?.validateFields();
    setSaving(true);
    const documents = documentsRef.current || [];
    const i18nInstituteNames = instituteNameRef.current || [];
    const accounts = accountRef.current || [];
    const customers = customerRef.current || [];
    const addresses = addressRef.current || [];
    const staffs = staffRef.current || [];
    const serviceAgreements = serviceAgreementRef.current?.data || [];
    const doctors = doctorRef?.current || [];
    const departments = departmentRef?.current || [];
    const experiencedFees = experiencedFeeRef.current || [];
    delete values.address;
    let instituteStatus = isActive ? InstituteStatusEnum.ACTIVE : InstituteStatusEnum.INACTIVE;

    if (isChangeStatus) {
      instituteStatus = isActive ? InstituteStatusEnum.INACTIVE : InstituteStatusEnum.ACTIVE;
    }

    const params: InstitutePackToApproveRequest = {
      parentId: +parentId,
      ...institute,
      ...values,
      addresses,
      customers,
      documents,
      accounts,
      serviceAgreements: serviceAgreements,
      doctors,
      departments,
      experiencedFees,
      i18nInstituteNames,
      grade: ((values?.grade as string[]) || undefined)?.join(','),
      setUpDate: values.setUpDate && dateFormatInstance.formatDate(values.setUpDate),
      instituteStatus: instituteStatus,
      staffs,
      onlyUpdateStatusFlag: isChangeStatus ? YesNoEnum.YES : undefined,
    };

    let req: Promise<InstituteDetailV2 | void>;
    if (!institute?.id) {
      req = NewChannelService.InstitutePackToApproveService.add(params);
    } else {
      req = NewChannelService.InstitutePackToApproveService.update(institute.id, params);
    }
    return req
      .then(() => {
        message.success(t('The modification has been submitted for review.'));
        handleBack();
      })
      .catch((error: Error) => message.error(error.message))
      .finally(() => {
        setSaving(false);
      });
  };

  const changeInstituteStatus = () => {
    Modal.confirm({
      title: currentInstituteI18n,
      icon: <Icon type="info-circle" />,
      content: t('Are you sure to change Partner status to {status}?', {
        status: aimToChangeInstituteI18n,
      }),
      okText: t('Confirm'),
      cancelText: t('Cancel'),
      onOk: () => {
        if (enableApproval) {
          packInstitute(true);
          return;
        }
        ChannelService.editInstituteV2<InstituteFormDTOV2, InstituteFormDTOV2>(institute?.id, {
          ...institute,
          instituteStatus: isActive ? InstituteStatus.INACTIVE : InstituteStatus.ACTIVE,
          onlyUpdateStatusFlag: YesOrNo.YES,
        })
          .then(res => {
            message.success(t('Change the status successfully.'));
            setActive(res?.instituteStatus === InstituteStatus.ACTIVE);
          })
          .catch(() => {
            message.error(t('Change the status failed.'));
          });
      },
    });
  };

  const handleSaveInstitute = useCallback(() => {
    form?.validateFields().then((values: InstituteFormDTOV2) => {
      setSaving(true);
      const documents = documentsRef.current || [];
      const i18nInstituteNames = instituteNameRef.current || [];
      const accountList = accountRef.current || [];
      const customerList = customerRef.current || [];
      const addressList = addressRef.current || [];
      delete values.address;
      const params: InstituteQueryV2 | InstituteDetailV2 = {
        parentId,
        ...institute,
        ...values,
        addressList,
        customerList,
        documents,
        accountList,
        i18nInstituteNames,
        grade: ((values?.grade as string[]) || undefined)?.join(','),
        setUpDate: values.setUpDate && dateFormatInstance.formatDate(values.setUpDate),
        instituteStatus: isActive ? InstituteStatus.ACTIVE : InstituteStatus.INACTIVE,
      };
      let req: Promise<InstituteDetailV2 | void>;
      if (!institute?.id) {
        req = ChannelService.addInstituteV2<InstituteQueryV2, InstituteDetailV2>(params).then(resp => {
          message.success(t('Add successfully'));
          navigate(
            `?${qs.stringify({
              id: resp?.id,
              modeType: ModeEnum.EDIT,
            })}`,
            {
              state,
            }
          );
        });
      } else {
        req = ChannelService.editInstituteV2<InstituteQueryV2, InstituteDetailV2>(institute.id, params)
          .then(setInstitute)
          .then(() => {
            message.success(t('Edited successfully'));
            if (serviceAgreementRef?.current?.saveServiceAgreement) {
              serviceAgreementRef.current.saveServiceAgreement();
            }
          });
      }
      req
        .catch((error: Error) => message.error(error.message))
        .finally(() => {
          setSaving(false);
        });
    });
  }, [form, institute, isActive, t]);

  const instituteTypeFinalValue = instituteTypeFormValue || institute?.instituteType;

  return (
    <InstituteInformationContext.Provider value={{ instituteInfo: institute }}>
      <ApproveModeContext.Provider value={{ enableApproval: enableApproval }}>
        <WorkScheduleTypeContext.Provider value={{ isTenantIncludesWorkType }}>
          <ChannelSchemaFieldsContext.Provider
            value={{
              channelSchemaDefFields: channelPartnerSchemaDefFields,
              schemaUsedBizDictMap: schemaUsedBizDictMap,
            }}
          >
            <Layout className={styles.channelContent}>
              <Layout.Content style={{ overflowY: 'auto' }}>
                <Layout.Header className={styles.headerContainer}>
                  <>
                    <Button onClick={handleBack} type="link" icon={<Icon type="left" />}>
                      {t('Back to task pool')}
                    </Button>
                    <Divider type="vertical" />
                    <PartnerBreadCrumbNav
                      id={institute?.id}
                      parentId={parentId}
                      mode={mode}
                      type={instituteTypeFinalValue}
                    />
                  </>
                </Layout.Header>
                <Layout.Content style={{ padding: 24 }}>
                  <div className={styles.partnerMainTitleContainer}>
                    <TextBody type="h4">{t('Partner Details')}</TextBody>
                    <StatusLabel needBgColor statusI18n={currentInstituteI18n} type={isActive ? 'info' : 'no-status'} />
                  </div>
                  <BasicInfo modeType={modeType} form={form} loading={loading} institute={institute} />

                  <Skeleton loading={loading} round active title={false} paragraph={{ rows: 8 }}>
                    <AddressTable
                      ref={addressRef}
                      initialList={institute?.addressList}
                      instituteId={institute?.id}
                      mode={mode}
                      loading={loading}
                      instituteType={instituteTypeFinalValue}
                    />

                    {ContactShownTypes.includes(instituteTypeFinalValue) && (
                      <ContactPersonTable
                        ref={customerRef}
                        initialList={institute?.customerList}
                        instituteId={institute?.id}
                        mode={mode}
                        instituteType={instituteTypeFinalValue}
                      />
                    )}

                    {AccountShownTypes.includes(instituteTypeFinalValue) && (
                      <AccountTable
                        ref={accountRef}
                        initialList={institute?.accountList}
                        instituteId={institute?.id}
                        mode={mode}
                        instituteType={instituteTypeFinalValue}
                      />
                    )}

                    {ServiceAgreementShownTypes.includes(instituteTypeFinalValue) && (
                      <ServiceAgreementTable
                        ref={serviceAgreementRef}
                        readonly={mode === ModeEnum.READ}
                        instituteId={institute?.id}
                        instituteType={instituteTypeFinalValue}
                        renew={true}
                      />
                    )}
                    {StaffShownTypes.includes(instituteTypeFinalValue) && (
                      <StaffTable
                        ref={staffRef}
                        readonly={mode === ModeEnum.READ}
                        instituteId={institute?.id}
                        instituteType={instituteTypeFinalValue}
                      />
                    )}

                    {ExperiencedFeeShownTypes.includes(instituteTypeFinalValue) && (
                      <>
                        <DepartmentAndDoctor
                          doctorRef={doctorRef}
                          departmentRef={departmentRef}
                          instituteId={institute?.id}
                          readonly={mode === ModeEnum.READ || !institute}
                          instituteType={instituteTypeFinalValue}
                        />
                        <ExperiencedFees
                          ref={experiencedFeeRef}
                          instituteId={institute?.id}
                          readonly={mode === ModeEnum.READ || !institute}
                          instituteType={instituteTypeFinalValue}
                        />
                      </>
                    )}
                    {/* enableApproval打开时隐藏该区块 */}
                    {RelationshipShownTypes.includes(instituteTypeFinalValue) && !enableApproval && (
                      <RelationshipList
                        channelCode={institute?.instituteCode}
                        channelName={institute?.instituteName}
                        sourceChannelId={institute?.id}
                        tenantInfo={tenantInfo}
                        type={instituteTypeFinalValue}
                        readonly={mode === ModeEnum.READ || !institute}
                        parentId={institute?.parentId}
                        mode={mode}
                        partnerCode={institute?.instituteCode}
                      />
                    )}
                  </Skeleton>
                </Layout.Content>
              </Layout.Content>
              <PartnerSider loading={loading}>
                <UploadFile
                  savedFileList={institute?.documents || []}
                  readonly={mode === ModeEnum.READ}
                  ref={documentsRef}
                />
              </PartnerSider>
            </Layout>

            <Layout.Footer className={styles.footerContainer}>
              {mode !== ModeEnum.READ && (
                <Popconfirm
                  placement="topLeft"
                  title={t('Are you sure to Submit?')}
                  onConfirm={enableApproval ? () => packInstitute(false) : handleSaveInstitute}
                  okText={t('Yes')}
                  cancelText={t('No')}
                >
                  <Button type="primary" size="large" loading={saving}>
                    {t('Save')}
                  </Button>
                </Popconfirm>
              )}

              {mode === ModeEnum.EDIT && (
                <Button size="large" style={{ marginRight: 16 }} onClick={() => changeInstituteStatus()}>
                  {aimToChangeInstituteI18n}
                </Button>
              )}

              {RelationshipShownTypes.includes(instituteTypeFinalValue) && (
                <>
                  <AddNewLevelButton
                    instituteId={institute?.id}
                    instituteType={instituteTypeFinalValue}
                    parentId={institute?.parentId}
                    level={institute?.level}
                    mode={mode}
                  />
                  <PartnerStructure id={institute?.id} mode={mode} type={instituteTypeFinalValue} />
                </>
              )}
              <Button size="large" style={{ marginRight: 16 }} onClick={() => handleBack()}>
                {t('Cancel')}
              </Button>
            </Layout.Footer>
          </ChannelSchemaFieldsContext.Provider>
        </WorkScheduleTypeContext.Provider>
      </ApproveModeContext.Provider>
    </InstituteInformationContext.Provider>
  );
};

export default PartnerDetailsPage;
