import { usePermission } from '@/hooks/usePermissions';

export const usePartnerSettingPermission = () => {
  const canEdit = usePermission('channel.partner-setting.edit');
  const canView = usePermission('channel.partner-setting.view');

  return { canEdit, canView };
};

export const usePartnerManagementPermission = () => {
  const canEdit = usePermission('channel.partner-management.edit');
  const canView = usePermission('channel.partner-management.view');

  return { canEdit, canView };
};
