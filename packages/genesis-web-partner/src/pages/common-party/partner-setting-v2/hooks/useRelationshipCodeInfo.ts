/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2022-07-04 20:04:23
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2022-07-04 20:04:23
 * @Description:
 */
import { useEffect, useState } from 'react';

import type { RelationshipDetailDTO } from 'genesis-web-service';
import { TenantOrgType } from 'genesis-web-service';

export const useRelationshipCodeInfo = (relationshipDetail: RelationshipDetailDTO) => {
  // 接口需要无值的时候传空字符串，null会被axios处理掉参数，导致接口报错
  const [agencyCode, setAgencyCode] = useState('');
  const [saleChannelCode, setSaleChannelCode] = useState('');
  useEffect(() => {
    // 在agency层级，或者配置了agency的sales channel层级，返回agencyCode
    if (relationshipDetail?.sourceChannel?.type === TenantOrgType.AGENCY) {
      setAgencyCode(relationshipDetail?.sourceChannel?.code);
    } else if (
      relationshipDetail?.sourceChannel?.type === TenantOrgType.SALE_CHANNEL &&
      relationshipDetail?.targetChannel?.type === TenantOrgType.AGENCY
    ) {
      setAgencyCode(relationshipDetail?.targetChannel?.code);
    }
    // sales channel层，返回saleChannelCode
    if (relationshipDetail?.sourceChannel?.type === TenantOrgType.SALE_CHANNEL) {
      setSaleChannelCode(relationshipDetail?.sourceChannel?.code);
    }
  }, [relationshipDetail]);
  return [agencyCode, saleChannelCode];
};
