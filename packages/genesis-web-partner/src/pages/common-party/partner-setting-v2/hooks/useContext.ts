import type { InstituteDetailV2 } from 'genesis-web-service';
import { createContext, useContext } from 'react';

export const RelationshipContext = createContext<{ level: number }>({
  level: null,
});

export const useRelationshipContext = () => useContext(RelationshipContext);

export const InstituteInformationContext = createContext<{
  instituteInfo: InstituteDetailV2;
}>({
  instituteInfo: null,
});

export const useInstituteInformationContext = () =>
  useContext(InstituteInformationContext);

// 租户配置：是否开启WorkType
export const WorkScheduleTypeContext = createContext<{
  isTenantIncludesWorkType: boolean;
}>({
  isTenantIncludesWorkType: false,
});

export const useWorkScheduleTypeContext = () =>
  useContext(WorkScheduleTypeContext);
