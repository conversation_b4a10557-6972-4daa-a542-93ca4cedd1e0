import { createContext, useContext } from 'react';

import type { InstituteDetailV2 } from 'genesis-web-service';

export const RelationshipContext = createContext<{ level: number }>({
  level: null,
});

export const useRelationshipContext = () => useContext(RelationshipContext);

export const InstituteInformationContext = createContext<{
  instituteInfo: InstituteDetailV2;
}>({
  instituteInfo: null,
});

export const useInstituteInformationContext = () => useContext(InstituteInformationContext);

// 租户配置：是否开启WorkType
export const WorkScheduleTypeContext = createContext<{
  isTenantIncludesWorkType: boolean;
}>({
  isTenantIncludesWorkType: false,
});

export const useWorkScheduleTypeContext = () => useContext(WorkScheduleTypeContext);

// 租户配置：是否开启Approve
export const ApproveModeContext = createContext<{
  enableApproval: boolean;
}>({
  enableApproval: false,
});

export const useApproveModeContext = () => useContext(ApproveModeContext);
