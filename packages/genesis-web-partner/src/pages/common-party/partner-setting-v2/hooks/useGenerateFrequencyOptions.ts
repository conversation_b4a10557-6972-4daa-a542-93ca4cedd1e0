import { useTranslation } from 'react-i18next';

/**
 * @param label 用于生成下拉选的label
 * @param length options列表长度
 * @description 用于对账结算配置处，生成Next 2/Every 2之类的下拉选option
 */
export const useGenerateFrequencyOptions = (label: string, length: number) => {
  const { t } = useTranslation('partner');

  return Array.from(new Array(length).keys()).map(key => ({
    value: (key + 1).toString(),
    label: t(`${label} {{n}}`, { n: key === 0 ? '' : key + 1 }),
  }));
};
