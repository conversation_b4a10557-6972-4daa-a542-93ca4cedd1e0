import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { FormInstance } from 'antd';
import { Col, Form, Skeleton } from 'antd';

import { useSelector } from '@umijs/max';

import type { Moment } from 'moment';
import moment from 'moment';

import { UploadImage } from 'genesis-web-component/lib/components/UploadImage';
import type { BizDictItem, InstituteDetailV2 } from 'genesis-web-service';
import {
  CustomerTypeCode,
  DownloadOrUploadType,
  InstituteTypeEnum,
  UploadTypeEnum,
  YesOrNo,
} from 'genesis-web-service';
import { security } from 'genesis-web-shared';
import { useUniqueIdentification } from 'genesis-web-shared/lib/hook/useUniqueIdentification';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import type { QueryFieldsType } from '@/components/CommonForm';
import { FieldType } from '@/components/CommonForm';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import type { ConnectState } from '@/models/connect';
import { InvestigationArea } from '@/pages/common-party/partner-setting-v2/components/InvestigationArea';
import { PhoneWithCountryCode } from '@/pages/common-party/partner-setting-v2/components/PhoneWithCountryCode';
import { getUrlByFileUniqueCode } from '@/pages/common-party/utils/util';
import { EMAIL_PATTERN, NoPureSpace_PATTERN } from '@/utils/utils';

interface InstituteCommonBasicInfoProps {
  instituteDetail: Partial<InstituteDetailV2>;
  disabled?: boolean;
  instituteTypeValue?: InstituteTypeEnum;
  form?: FormInstance;
}

const OrganizationNotRequiredList = [InstituteTypeEnum.HOSPITAL, InstituteTypeEnum.CLINIC];

export const useInstituteBasicInfoFields = ({
  instituteDetail,
  instituteTypeValue,
  disabled = true,
  form,
}: InstituteCommonBasicInfoProps) => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const { loadingEnums } = useSelector(({ loading }: ConnectState) => ({
    loadingEnums: !!loading.effects['global/getTenantBizDict'],
  }));
  const { uniqConfig: uniqueIdentifications } = useUniqueIdentification(CustomerTypeCode.organization);
  const [fileCode, setFileCode] = useState<string>();
  const gstRegisteredOrganizationValue = Form.useWatch('gstRegisteredOrganization', form);
  const instituteTypeFormValue = Form.useWatch('instituteType', form);

  useEffect(() => {
    if (instituteDetail) {
      setFileCode(instituteDetail?.logoUrl);
    }
  }, [instituteDetail, instituteTypeValue, form]);

  const commonFields: Partial<QueryFieldsType>[] = [
    {
      key: 'logoUrl',
      type: FieldType.Customize,
      customerDom: (
        <Col span={24}>
          <Form.Item key="logoUrl" name="logoUrl">
            <UploadImage
              requestUrl="/api/channel/v2/file/upload/"
              name="file"
              data={{
                type: DownloadOrUploadType.COMMON_ENTITY_FILE,
                ext: JSON.stringify({ type: UploadTypeEnum.ChannelLogo }),
              }}
              disabled={disabled}
              filePath={getUrlByFileUniqueCode('/api/channel/v2/file/download', fileCode)}
              onChange={value => {
                setFileCode((value?.fileUniqueCode as string) || '');
                form.setFieldValue('logoUrl', value?.fileUniqueCode);
              }}
              headers={{
                ...security.csrf(),
              }}
            />
          </Form.Item>
        </Col>
      ),
      customerLoadingDom: (
        <Col span={24} style={{ marginBottom: 16 }}>
          <Skeleton active avatar={{ shape: 'square', size: 118 }} paragraph={false} title={false} />
        </Col>
      ),
    },
    {
      key: 'organizationIdType',
      label: t('Organization ID Type'),
      type: FieldType.Select,
      rules: [
        {
          required:
            !OrganizationNotRequiredList.includes(instituteTypeFormValue) &&
            !!uniqueIdentifications?.organizationIdType,
          message: t('channel.common.required', {
            label: t('Organization ID Type'),
          }),
        },
      ],
      ctrlProps: {
        options: enums?.organizationIdType,
        disabled: disabled || !!instituteDetail?.organizationIdType,
        loading: loadingEnums,
      },
    },
    {
      key: 'organizationIdNo',
      label: t('Organization ID No.'),
      type: FieldType.Input,
      rules: [
        {
          required:
            !OrganizationNotRequiredList.includes(instituteTypeFormValue) && !!uniqueIdentifications.organizationIdNo,
          message: t('channel.common.required', {
            label: t('Organization ID No.'),
          }),
        },
      ],
      ctrlProps: {
        disabled: disabled || !!instituteDetail?.organizationIdNo,
      },
    },
    {
      key: 'instituteType',
      label: t('Partner Type'),
      type: FieldType.Select,
      rules: [
        {
          required: true,
          message: t('channel.common.required', {
            label: t('Partner Type'),
          }),
        },
      ],
      ctrlProps: {
        disabled: disabled || !!instituteDetail?.instituteType,
        options: enums?.instituteType,
        loading: loadingEnums,
      },
    },
    {
      key: 'instituteName',
      label: t('Partner Name'),
      type: FieldType.Input,
      rules: [
        {
          required: true,
          message: t('channel.common.required', {
            label: t('Partner Name'),
          }),
        },
      ],
      ctrlProps: {
        disabled,
      },
    },
    {
      key: 'instituteCode',
      label: t('Partner Code'),
      type: FieldType.Input,
      rules: [
        {
          required: true,
          message: t('channel.common.required', {
            label: t('Partner Code'),
          }),
        },
      ],
      ctrlProps: {
        disabled: disabled || !!instituteDetail?.instituteCode,
      },
    },
    {
      key: 'branchCode',
      label: t('Branch Code'),
      type: FieldType.Input,
      rules: [
        {
          required: !!uniqueIdentifications?.branchCode,
          message: t('channel.common.required', {
            label: t('Branch Code'),
          }),
        },
      ],
      ctrlProps: {
        maxLength: 200,
        allowClear: true,
        disabled: disabled || (!!uniqueIdentifications?.branchCode && !!instituteDetail?.branchCode),
      },
    },
    {
      key: 'email',
      label: t('Email'),
      type: FieldType.Input,
      rules: [
        {
          pattern: EMAIL_PATTERN,
          message: t('Please input correct format'),
        },
      ],
      ctrlProps: {
        disabled,
      },
    },
    {
      key: 'customizeDom',
      type: FieldType.Customize,
      customerDom: (
        <PhoneWithCountryCode
          countryCodeKey="countryCode"
          phoneKey="phoneNo"
          colSpan={16}
          label={t('Contact Number')}
          disabled={disabled}
        />
      ),
      customDisplayKey: 'Contact_Number',
    },
    {
      key: 'comments',
      label: t('Comments'),
      type: FieldType.TextArea,
      col: 24,
      rules: [
        {
          pattern: NoPureSpace_PATTERN,
          message: t('Please enter a valid character'),
        },
      ],
      ctrlProps: {
        maxLength: 100,
        disabled,
        showCount: true,
        style: {
          height: 82,
        },
      },
    },
  ];
  // InstituteTypeEnum.AssessmentCompany 没有自定义字段
  if (instituteTypeValue === InstituteTypeEnum.ExternalInsuranceCompany) {
    // insert fields
    commonFields.splice(
      commonFields.length - 1,
      0,
      ...[
        {
          key: 'country',
          label: t('Country'),
          type: FieldType.Select,
          ctrlProps: {
            options: enums?.country?.map((item: BizDictItem) => ({
              label: item.label || item.dictDesc,
              value: item.label || item.dictDesc,
            })),
            disabled: disabled,
            loading: loadingEnums,
            allowClear: true,
          },
        },
        {
          key: 'isEuMember',
          label: t('EU member'),
          type: FieldType.Select,
          ctrlProps: {
            disabled,
            options: enums?.yesNo,
          },
        },
        {
          key: 'vatNumber',
          label: t('VAT Number'),
          type: FieldType.Input,
          ctrlProps: {
            disabled,
          },
        },
        {
          key: 'sapNumber',
          label: t('BP Number in SAP'),
          type: FieldType.Input,
          ctrlProps: {
            disabled,
          },
        },
      ]
    );
  } else if (instituteTypeValue === InstituteTypeEnum.Investigator) {
    commonFields.splice(
      commonFields.length - 1,
      0,
      ...[
        {
          type: FieldType.Customize,
          customerDom: <InvestigationArea disabled={disabled} />,
          customDisplayKey: 'Investigation_Area',
        },
      ]
    );
  } else if ([InstituteTypeEnum.SERVICE_COMPANY, InstituteTypeEnum.LegalService].includes(instituteTypeValue)) {
    commonFields.splice(
      commonFields.length - 1,
      0,
      ...[
        {
          key: 'name2',
          label: t('Abbreviation'),
          type: FieldType.Input,
          rules: [],
          ctrlProps: {
            allowClear: true,
            disabled: disabled,
          },
        },
        {
          key: 'officeHour',
          label: t('Office Hour'),
          type: FieldType.Input,
          ctrlProps: {
            maxLength: 200,
            allowClear: true,
            disabled: disabled,
          },
        },
        {
          key: 'website',
          label: t('Website'),
          type: FieldType.Input,
          ctrlProps: {
            maxLength: 200,
            allowClear: true,
            disabled: disabled,
          },
        },
      ]
    );
  } else if ([InstituteTypeEnum.HOSPITAL, InstituteTypeEnum.CLINIC].includes(instituteTypeValue)) {
    commonFields.splice(
      commonFields.length - 1,
      0,
      ...[
        {
          key: 'shortInstituteName',
          label: t('Short Institute Name'),
          type: FieldType.Input,
          ctrlProps: {
            disabled: disabled,
          },
        },
        {
          key: 'isPublic',
          label: t('Public/Private'),
          type: FieldType.Select,
          ctrlProps: {
            options: enums?.accessLevel,
            disabled: disabled,
            loading: loadingEnums,
            allowClear: true,
          },
        },
        {
          key: 'grade',
          label: t('Institute Grade'),
          type: FieldType.Select,
          ctrlProps: {
            options: enums?.instituteGrade,
            disabled: disabled,
            mode: 'multiple',
            loading: loadingEnums,
            allowClear: true,
          },
        },
        {
          key: 'country',
          label: t('Country'),
          type: FieldType.Select,
          ctrlProps: {
            options: enums?.country?.map((item: BizDictItem) => ({
              label: item.label || item.dictDesc,
              value: item.label || item.dictDesc,
            })),
            disabled: disabled,
            loading: loadingEnums,
            allowClear: true,
          },
        },
        {
          key: 'setUpDate',
          label: t('Opening Date'),
          type: FieldType.DatePicker,
          col: 8,
          ctrlProps: {
            disabled: disabled,
            format: dateFormatInstance.dateFormat,
            disabledDate: (current: Moment) => current && moment(current).isAfter(moment(new Date())),
          },
        },
        {
          key: 'investigationHospital',
          label: t('Investigation Hospital'),
          type: FieldType.Select,
          col: instituteTypeValue === InstituteTypeEnum.CLINIC ? 0 : null,
          ctrlProps: {
            options: enums?.yesNo,
            disabled: disabled,
            allowClear: true,
          },
        },
        {
          key: 'panelHospital',
          label: t('Panel Hospital'),
          type: FieldType.Select,
          ctrlProps: {
            options: enums?.yesNo,
            disabled: disabled,
            allowClear: true,
          },
        },
        {
          key: 'gstRegisteredOrganization',
          label: t('GST Registered Organization'),
          type: FieldType.Select,
          ctrlProps: {
            options: enums?.yesNo,
            disabled: disabled,
            allowClear: true,
          },
        },
        {
          key: 'externalCode',
          label: t('External Code'),
          type: FieldType.Input,
          ctrlProps: {
            disabled: disabled,
          },
        },
      ]
    );
    if (gstRegisteredOrganizationValue === YesOrNo.YES) {
      commonFields.splice(commonFields?.findIndex(field => field?.key === 'gstRegisteredOrganization') + 1, 0, {
        key: 'gstRegistrationNumber',
        label: t('GST Registration Number') as string,
        type: FieldType.Input,
        preserve: false,
        ctrlProps: {
          maxLength: 64,
          disabled: disabled,
        },
      });
    } else {
      commonFields?.filter(field => field?.key !== 'gstRegistrationNumber');
    }
  }
  return commonFields;
};
