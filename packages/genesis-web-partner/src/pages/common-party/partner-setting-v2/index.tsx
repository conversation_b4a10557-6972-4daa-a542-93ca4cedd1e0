/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-10-17 14:34:43
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-11-15 16:26:58
 * @Description:
 */
import KeepAlive from 'react-activation';

import { useSearchParams } from '@umijs/max';

import { isEmpty } from 'lodash-es';
import qs from 'qs';

import { PartnerManagement as PartnerManagementPage } from '@/pages/common-party/partner-setting-v2/pages/PartnerManagement';

const PartnerManagement = () => {
  const [searchParams] = useSearchParams();
  return (
    <KeepAlive
      saveScrollPosition="screen"
      cacheKey="partner-management"
      when={() => !isEmpty(qs.parse(searchParams.toString()))}
    >
      <PartnerManagementPage />
    </KeepAlive>
  );
};

export default PartnerManagement;
