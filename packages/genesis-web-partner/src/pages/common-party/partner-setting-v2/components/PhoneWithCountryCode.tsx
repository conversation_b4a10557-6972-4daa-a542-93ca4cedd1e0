import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { NoPureSpace_PATTERN } from '@/utils/utils';
import { Select } from '@zhongan/nagrand-ui';
import { Input, Form, Col } from 'antd';
import type { BizDictItem } from 'genesis-web-service';
import type { CSSProperties } from 'react';
import { useTranslation } from 'react-i18next';

interface Props {
  countryCodeKey: string;
  phoneKey: string;
  label: string;
  disabled?: boolean;
  formStyle?: CSSProperties;
  colSpan?: number;
}

/**
 *
 * @param countryCodeKey countryCode对应Form.Item name
 * @param phoneKey phoneNo对应Form.Item name
 * @param label label
 * @param disabled 是否禁用
 * @param colSpan Col span;默认8
 * @param formStyle style
 * @description 目前legal service、institute detail页basic information模块用到；带区号的电话号码
 */
export const PhoneWithCountryCode = ({
  countryCodeKey,
  phoneKey,
  label,
  disabled,
  colSpan = 8,
  formStyle = { width: 240 },
}: Props) => {
  const { t } = useTranslation('partner');
  const countryEnums = useTenantBizDict('country') as BizDictItem[];

  return (
    <Col span={colSpan}>
      <Form.Item label={label} style={formStyle}>
        <Input.Group compact>
          <Form.Item key={countryCodeKey} name={countryCodeKey} noStyle>
            <Select
              showSearch
              disabled={disabled}
              style={{ width: '35%' }}
              optionLabelProp="label"
              dropdownMatchSelectWidth={false}
              placeholder={t('Please Select')}
              getPopupContainer={triggerElement =>
                triggerElement?.parentElement
              }
            >
              {countryEnums?.map(item => (
                <Select.Option
                  value={item.itemExtend2}
                  key={`${item.dictValueName}_${item.itemExtend2 ?? ''}`}
                  label={item.itemExtend2}
                  title={`${item.dictValueName}${item.itemExtend2 ?? ''}`}
                >
                  <p style={{ display: 'flex' }}>
                    {item.dictValueName}
                    <span style={{ flex: 1, textAlign: 'right' }}>
                      {item.itemExtend2 ?? ''}
                    </span>
                  </p>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name={phoneKey}
            key={phoneKey}
            rules={[
              {
                pattern: NoPureSpace_PATTERN,
                message: t('Please enter a valid character'),
              },
            ]}
            noStyle
          >
            <Input
              disabled={disabled}
              allowClear
              placeholder={t('Please Input')}
              style={{ width: '65%' }}
            />
          </Form.Item>
        </Input.Group>
      </Form.Item>
    </Col>
  );
};
