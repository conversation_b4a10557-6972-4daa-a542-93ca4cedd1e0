@import '@/variables.scss';

.card-list {
  position: relative;
  margin-bottom: $gap-md;
  margin-top: $gap-md;
  flex: 1;
  :global {
    .#{$antd-prefix}-col {
      margin-right: 0 !important;
    }
    .nagrand-card-layout {
      margin-left: 0px;
      margin-right: -$gap-md;
    }
  }
  .empty-card {
    margin-right: $gap-md;
    margin-bottom: $gap-md;
    min-height: 170px;
  }
  .outside-wrapper {
    overflow: hidden;
    min-width: 150px;
    position: relative;
    border-radius: $border-radius-big;
    box-shadow: 0.5px 0.5px 1.5px 0px $border-line-color-secondary;

    &:hover {
      box-shadow: 0 1px 2px -2px #00000029, 0 3px 6px #0000001f,
        0 5px 12px 4px #00000017;
      transition: 0.1s all;
      transition: box-shadow 0.3s;
      transform: scale(1.01);
      border:none
    }
    .card-container {
      position: relative;
      width: 100%;
      height: 100%;
      backface-visibility: hidden;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      background-color: $white;
    }
    .hover-container {
      width: 100%;
      height: 72px;
      bottom: 0;
      left: 0;
      position: absolute;
      opacity: 0;
      &:hover {
        opacity: 1;
        transition: 0.3s all;
      }
    }
  }
}