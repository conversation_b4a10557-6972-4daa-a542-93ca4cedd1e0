/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-11-15 14:27:24
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-11-16 14:27:30
 * @Description:
 */
import { useTranslation } from 'react-i18next';

import {
  CardActionsContainer,
  CardBodyPrimaryInfo,
  CardFooter,
  CardLayoutV2,
  CardTagList,
  CardV2,
  EditAction,
  ProfilePhoto,
  QueryBitMap,
  TagType,
  ViewAction,
} from '@zhongan/nagrand-ui';

import type { InstituteListResp } from 'genesis-web-service';

import { ModeEnum } from '@/types/common';

import { useEnumsMapping } from '../../hooks/useEnumsMapping';
import { InstituteStatus } from '../../models/index.interface';

interface CardListProps {
  data?: InstituteListResp[];
  rowKey: keyof InstituteListResp;
  loading: boolean;
  handleIconClick: (id: number, modeType: ModeEnum) => Promise<void>;
  canEdit?: boolean;
}

export const CardList = ({ data = [], rowKey, loading, handleIconClick, canEdit = false }: CardListProps) => {
  const { t } = useTranslation('partner');
  const instituteTypeMapping = useEnumsMapping('instituteType');
  const instituteStatusMapping = useEnumsMapping('instituteStatus');

  if (!data.length && !loading) {
    return <QueryBitMap queryStatus="NoContent" />;
  }

  return (
    <CardLayoutV2 loading={loading}>
      {data.map(record => (
        <CardV2
          key={record[rowKey]}
          body={
            <>
              <CardBodyPrimaryInfo
                title={t('Partner Code')}
                content={record?.instituteCode}
                right={<ProfilePhoto users={record.instituteName} />}
              />
              <CardTagList
                tagList={[
                  {
                    type: TagType.Tag,
                    tagProps: {
                      statusI18n: instituteStatusMapping[record?.instituteStatus].toUpperCase(),
                      type: record?.instituteStatus === InstituteStatus.ACTIVE ? 'success' : 'no-status',
                    },
                  },
                ]}
              />
            </>
          }
          footer={
            <CardFooter
              list={[
                {
                  label: t('Partner Type'),
                  value: instituteTypeMapping[record?.instituteType],
                },
                {
                  label: t('Partner Name'),
                  value: record?.instituteName,
                },
              ]}
            />
          }
          actions={
            <CardActionsContainer>
              <ViewAction onClick={() => handleIconClick(record[rowKey], ModeEnum.READ)} />
              <EditAction disabled={!canEdit} onClick={() => handleIconClick(record[rowKey], ModeEnum.EDIT)} />
            </CardActionsContainer>
          }
        />
      ))}
    </CardLayoutV2>
  );
};
