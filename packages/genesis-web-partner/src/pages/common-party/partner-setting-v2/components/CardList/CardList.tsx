/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-11-15 14:27:24
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-11-16 14:27:30
 * @Description:
 */
import type { ReactNode } from 'react';
import { CardLayout } from '@zhongan/nagrand-ui';
import { Card as AntdCard, Col } from 'antd';

import clsx from 'clsx';
import styles from './style.scss';

interface CardListProps<T> {
  data: T[];
  rowKey: keyof T;
  loading: boolean;
  renderCardContent: (record: T) => ReactNode;
  renderHoverContent: (record: T) => ReactNode;
  children?: ReactNode;
}

export const CardList = <T extends object>({
  data = [],
  rowKey,
  loading,
  renderCardContent,
  renderHoverContent,
  children,
}: CardListProps<T>) => {
  return (
    <div className={styles.cardList}>
      <CardLayout>
        <>
          {loading
            ? Array(12)
              .fill(0)
              .map((_, idx) => (
                <Col xs={8} sm={8} lg={6} span={6} key={idx}>
                  <AntdCard
                    className={styles.emptyCard}
                    key={idx}
                    loading={loading}
                  />
                </Col>
              ))
            : data?.map(record => (
              <Col
                xs={8}
                sm={8}
                lg={6}
                span={6}
                key={record?.[rowKey] as string}
              >
                <div
                  className={clsx([styles.emptyCard, styles.outsideWrapper])}
                >
                  <div className={styles.cardContainer}>
                    {renderCardContent(record)}
                  </div>
                  <div className={styles.hoverContainer}>
                    {renderHoverContent(record)}
                  </div>
                </div>
              </Col>
            ))}
        </>
      </CardLayout>
    </div>
  );
};
