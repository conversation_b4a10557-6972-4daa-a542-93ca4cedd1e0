import type { FC } from 'react';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Divider, Radio, Space, message } from 'antd';
import type { BizDictItem, FeeType } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { ModalConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { TextBody } from '@zhongan/nagrand-ui';
import styles from '../../style.scss';

type ExperiencedFeeHeaderProps = {
  feeTypeValue: FeeType;
  handleChange: (value: FeeType) => void;
  disabled: boolean;
  instituteId: number;
  handleFeeTypeChange: () => void;
};

export const ExperiencedFeeHeader: FC<ExperiencedFeeHeaderProps> = ({
  feeTypeValue,
  handleChange,
  disabled,
  instituteId,
  handleFeeTypeChange,
}) => {
  const { t } = useTranslation('partner');

  const hospitalExperiencedFeeType = useTenantBizDict(
    'hospitalExperiencedFeeType',
  ) as BizDictItem[];

  const clearExperiencedFee = useCallback(
    (newRadioValue: FeeType) =>
      ChannelService.deleteExperiencedFee(instituteId)
        .then(() => {
          handleChange(newRadioValue);
          handleFeeTypeChange();
        })
        .catch((error: Error) => message.error(error.message)),
    [instituteId, handleChange, handleFeeTypeChange],
  );

  const getRadioContent = useCallback(
    (text: string, value: FeeType) => (
      <ModalConfirm
        needStopPropagation
        key={value}
        icon={<ExclamationCircleOutlined />}
        title={t('Warning')}
        content={t(
          'Changing the classification will clear all the details. Confirm to change?',
        )}
        okText={t('Confirm')}
        onOk={() => clearExperiencedFee(value)}
      >
        <Radio key={value} value={value}>
          {text}
        </Radio>
      </ModalConfirm>
    ),
    [clearExperiencedFee, t],
  );

  return (
    <Space size={30} align="start">
      <p className={styles.sectionTitle}>
        <TextBody type="h5">
          <Divider type="vertical" className={styles.titleBefore} />
          {t('Experienced Fee')}
        </TextBody>
      </p>
      <Radio.Group value={feeTypeValue} disabled={disabled}>
        {hospitalExperiencedFeeType?.map((item: BizDictItem) =>
          getRadioContent(item.dictValueName, item.enumItemName as FeeType),
        )}
      </Radio.Group>
    </Space>
  );
};
