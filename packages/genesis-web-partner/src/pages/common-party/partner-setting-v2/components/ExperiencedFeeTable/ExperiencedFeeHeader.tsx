import type { FC } from 'react';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Divider, Radio, message } from 'antd';

import { TextBody } from '@zhongan/nagrand-ui';

import { ModalConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import type { BizDictItem, FeeType } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { ExclamationCircleOutlined } from '@ant-design/icons';

import { useApproveModeContext } from '../../hooks/useContext';
import styles from '../../style.scss';

type ExperiencedFeeHeaderProps = {
  feeTypeValue: FeeType;
  handleChange: (value: FeeType) => void;
  disabled: boolean;
  instituteId: number;
  handleFeeTypeChange: () => void;
};

export const ExperiencedFeeHeader: FC<ExperiencedFeeHeaderProps> = ({
  feeTypeValue,
  handleChange,
  disabled,
  instituteId,
  handleFeeTypeChange,
}) => {
  const { t } = useTranslation('partner');
  const { enableApproval } = useApproveModeContext();
  const hospitalExperiencedFeeType = useTenantBizDict('hospitalExperiencedFeeType') as BizDictItem[];

  const clearExperiencedFee = useCallback(
    (newRadioValue: FeeType) => {
      if (enableApproval) {
        handleChange(newRadioValue);
        handleFeeTypeChange();
        return Promise.resolve();
      }
      ChannelService.deleteExperiencedFee(instituteId)
        .then(() => {
          handleChange(newRadioValue);
          handleFeeTypeChange();
        })
        .catch((error: Error) => message.error(error.message));
    },
    [instituteId, handleChange, handleFeeTypeChange, enableApproval]
  );

  const getRadioContent = useCallback(
    (text: string, value: FeeType) => (
      <ModalConfirm
        needStopPropagation
        key={value}
        icon={<ExclamationCircleOutlined />}
        title={t('Warning')}
        content={t('Changing the classification will clear all the details. Confirm to change?')}
        okText={t('Confirm')}
        onOk={() => clearExperiencedFee(value)}
      >
        <Radio key={value} value={value}>
          {text}
        </Radio>
      </ModalConfirm>
    ),
    [clearExperiencedFee, t]
  );

  return (
    <div>
      <p className={styles.sectionTitle}>
        <TextBody type="h5">
          <Divider type="vertical" className={styles.titleBefore} />
          {t('Experienced Fee')}
        </TextBody>
      </p>
      <Radio.Group
        value={feeTypeValue}
        disabled={disabled}
        style={{
          marginBottom: '8px',
        }}
      >
        {hospitalExperiencedFeeType?.map((item: BizDictItem) =>
          getRadioContent(item.dictValueName, item.enumItemName as FeeType)
        )}
      </Radio.Group>
    </div>
  );
};
