import { Fragment, forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Divider, Form, Upload, message } from 'antd';
import type { UploadProps } from 'antd/es/upload';

import { useRequest } from 'ahooks';
import { set } from 'lodash-es';
import type { Moment } from 'moment';
import moment from 'moment';

import { EditableTable, Icon, Table } from '@zhongan/nagrand-ui';

import type { BizDictItem, ExperiencedFee, InstituteTypeEnum } from 'genesis-web-service';
import { ChannelService, DownloadOrUploadType, FeeType, MetadataService } from 'genesis-web-service';
import { amountFormatInstance, useL10n } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { useGetInputSearchDropdown } from '@/components/ColumnWithSearch';
import { renderEnumsWithString } from '@/components/RenderEnums';
import {
  EntityType,
  covertPartnerType2SchemaPartnerType,
  useGenericSchemaFormItemFields,
} from '@/hooks/useGenericSchemaFormItemFields';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { usePagination } from '@/pages/common-party/partner-setting/hooks/pagination';
import type { PartnerTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';
import type { ColumnEx } from '@/types/common';
import { transferDynamicFieldsToEditableTableProp } from '@/utils/utils';

import { useApproveModeContext } from '../../hooks/useContext';
import { ExperiencedFeeHeader } from './ExperiencedFeeHeader';

interface ExperiencedFeeProp {
  instituteId: number;
  readonly: boolean;
  instituteType: InstituteTypeEnum;
  isApprovalPage?: boolean;
  experiencedFees?: ExperiencedFeeExt[];
}

// 这两个name后端不会返回，是前端根据后端返回的dict value调用metadata接口获取dict value name
type ExperiencedFeeExt = ExperiencedFee & {
  diagnosisName?: string;
  surgeryName?: string;
  effectivePeriod?: Moment[];
};

/**
 *
 * @param instituteId instituteId
 * @param readonly 是否只读
 * @description 目前只用于institute detail页； institute experienced Fee模块展示，可新增、删除、修改、上传、下载
 */
export const ExperiencedFees = forwardRef(
  ({ instituteId, readonly, instituteType, isApprovalPage = false, experiencedFees }: ExperiencedFeeProp, ref) => {
    const { t } = useTranslation('partner');

    const [downloading, setDownloading] = useState(false);
    const [loading, setLoading] = useState(false);
    const [uploading, setUploading] = useState(false);
    const [experiencedFeeList, setExperiencedFeeList] = useState<ExperiencedFeeExt[]>([]);
    const [selectExperiencedFeeType, setSelectExperiencedFeeType] = useState<FeeType>(FeeType.Diagnosis);
    const [searchFeeCode, setSearchFeeCode] = useState<string | undefined>(undefined);

    const surgery = useTenantBizDict('surgery') as BizDictItem[];
    const diagnosisSource = useTenantBizDict('diagnosisSource') as BizDictItem[];
    const currencyEnums = useTenantBizDict('currencys') as BizDictItem[];
    const tenantBizDict = useTenantBizDict() as Record<string, BizDictItem[]>;
    const {
      l10n: { dateFormat },
    } = useL10n();
    const { enableApproval } = useApproveModeContext();

    const [form] = Form.useForm();

    const [pagination, setPaginationTotal] = usePagination({
      current: 1,
      pageSize: 10,
      total: 0,
    });

    useEffect(() => {
      if (isApprovalPage) {
        setSelectExperiencedFeeType(experiencedFees?.[0]?.feeType);
        setExperiencedFeeList(experiencedFees || []);
      }
    }, [isApprovalPage, experiencedFees]);

    useImperativeHandle(ref, () => {
      return experiencedFeeList;
    });

    const { formItems: staticFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'STATIC',
      category: 'EXPERIENCED_FEE',
      disabled: readonly,
      type: covertPartnerType2SchemaPartnerType(instituteType as unknown as PartnerTypeEnum),
      entityType: EntityType.PARTNER,
    });

    const { formItems: dynamicFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'DYNAMIC',
      category: 'EXPERIENCED_FEE',
      disabled: readonly,
      type: covertPartnerType2SchemaPartnerType(instituteType as unknown as PartnerTypeEnum),
      entityType: EntityType.PARTNER,
    });

    const { data: diagnosisData } = useRequest(
      async () => {
        if (!diagnosisSource) return [];
        return await MetadataService.queryBizDictBigKey(diagnosisSource[0]?.dictValue as string);
      },
      {
        refreshDeps: [diagnosisSource?.[0]?.dictValue],
      }
    );

    const { data: medicalBillItem } = useRequest(
      async () => await MetadataService.queryBizDictBigKey('medicalBillItem'),
      {
        cacheKey: 'medicalBillItem',
      }
    );

    /**
     * Sub Bill Item 需要展示 medicalBillItem 下的二级 medicalBillSubItem，
     */
    const medicalBillItemList = medicalBillItem?.reduce(
      // 二级不存在时,不需要加入,否则数组会混入值为null的数据
      (pre, cur) => (cur.childList ? pre.concat(cur.childList) : pre),
      []
    );

    const getExperiencedFees = useCallback(async () => {
      setLoading(true);
      try {
        const feesResp = await ChannelService.getExperiencedFees(instituteId, {
          pageSize: pagination.pageSize,
          pageIndex: pagination?.current - 1,
          feeCode: searchFeeCode,
        });

        setExperiencedFeeList(feesResp?.data ?? []);
        setPaginationTotal(feesResp?.totalElements || 0);
        if (feesResp?.totalElements > 0) {
          setSelectExperiencedFeeType(feesResp?.data[0].feeType);
        }
      } finally {
        setLoading(false);
      }
    }, [instituteId, pagination.current, pagination.pageSize, searchFeeCode]);

    useEffect(() => {
      if (!instituteId) {
        return;
      }
      getExperiencedFees();
    }, [instituteId, getExperiencedFees]);

    useEffect(() => {
      if (isApprovalPage) {
        return;
      }
      setExperiencedFeeList(experiencedFeeList?.filter(item => item?.id));
    }, [readonly]);

    const saveEdit = useCallback(
      async (record, editingKey: string) => {
        await form.validateFields();
        record.feeType = selectExperiencedFeeType;
        const [effectiveDate, expiryDate] = record.effectivePeriod ?? [];
        record.effectiveDate = effectiveDate;
        record.expiryDate = expiryDate;
        delete record.effectivePeriod;

        if (enableApproval) {
          record.key = Math.random();
          if (editingKey === 'add') {
            experiencedFeeList.push(record);
          } else {
            const departmentIndex = experiencedFeeList.findIndex(item => +item.id === +editingKey);

            experiencedFeeList[departmentIndex] = record;
          }
          // 过滤掉EditTable默认添加的数据
          setExperiencedFeeList([...experiencedFeeList.filter(item => item.key !== 'add')]);
          return;
        }

        setLoading(true);
        const request: Promise<void> = !record.id
          ? ChannelService.addExperiencedFee(instituteId, record)
          : ChannelService.updateExperiencedFee(instituteId, record.id, record);

        request
          .then(() => {
            message.success(t('Operated successfully'));
            getExperiencedFees();
          })
          .catch((error: Error) => message.error(error.message))
          .finally(() => setLoading(false));
      },
      [instituteId, getExperiencedFees, selectExperiencedFeeType, t, experiencedFeeList, enableApproval]
    );

    const deleteFee = useCallback(
      (index, record) => {
        if (enableApproval) {
          experiencedFeeList.splice(index, 1);

          setExperiencedFeeList([...experiencedFeeList]);
          return;
        }
        setLoading(true);
        ChannelService.deleteFee(instituteId, record.id)
          .then(() => {
            message.success(t('Delete successfully'));
            getExperiencedFees();
          })
          .catch((error: Error) => {
            message.error(error.message);
            // 删除后要重新请求数据，故不能放在finally里统一置为false
            setLoading(false);
          });
      },
      [instituteId, getExperiencedFees, t, enableApproval, experiencedFeeList]
    );

    /**
     * 下拉选列表的list，在 experiencedFees 基础基础上，在对下拉列表去重，
     * 详见 https://jira.zaouter.com/browse/GIS-80417 第4点
     */
    const options = useMemo(() => {
      let data: BizDictItem[];
      switch (selectExperiencedFeeType) {
        case FeeType.Surgery:
          data = surgery;
          break;
        case FeeType.SubBillItem:
          data = medicalBillItemList;
          break;
        default:
          data = diagnosisData;
      }

      // 允许添加同一个item
      return data;
    }, [selectExperiencedFeeType, surgery, medicalBillItem, diagnosisData]);

    const getSearchDropdown = useGetInputSearchDropdown<ExperiencedFee>((content?: string) => {
      pagination.onChange(1);
      setSearchFeeCode(content);
    });

    const columns: ColumnEx<ExperiencedFee>[] = useMemo(() => {
      const dynamicColumns = [
        {
          title: t('Diagnosis'),
          key: FeeType.Diagnosis,
          dataIndex: 'feeCode',
          editable: true,
        },
        {
          title: t('Surgery'),
          key: FeeType.Surgery,
          dataIndex: 'feeCode',
          editable: true,
        },
        {
          title: t('Sub Bill Item'),
          key: FeeType.SubBillItem,
          dataIndex: 'feeCode',
          editable: true,
          render: (text: string) =>
            text &&
            t('Name_Code', {
              name: renderEnumsWithString(text, medicalBillItemList, 'dictValue'),
              code: text,
            }),
          ...getSearchDropdown(),
        },
      ];
      const basicColumns = [
        {
          title: staticFields?.find(field => field.key === 'experiencedFee')?.label,
          editable: true,
          render: (text: string, record: ExperiencedFeeExt) =>
            amountFormatInstance.getAmountCurrencyString(record.experiencedFee, record.currency),
          fieldProps: {
            type: 'inputGroup',
            groupItems: staticFields?.filter(field => {
              field.extraProps = field.ctrlProps;
              return ['experiencedFee', 'currency'].includes(field.key);
            }),
          },
        },
        {
          title: t('Effective Period'),
          dataIndex: 'effectivePeriod',
          editable: true,
          render: (text: string, record: ExperiencedFeeExt) => {
            if (!record.effectivePeriod) {
              if (!record.effectiveDate || !record.expiryDate) {
                return;
              }
              record.effectivePeriod = [moment(record.effectiveDate), moment(record.expiryDate)];
            }
            return dateFormat.getDateRangeString(record.effectiveDate, record.expiryDate);
          },
          fieldProps: {
            type: 'dateRange',
            extraProps: {
              placeholder: [t('Effective Date'), t('Expiry Date')],
            },
          },
        },
      ];
      return [
        {
          ...dynamicColumns.find(col => {
            const fieldsOptions = options?.map(item => ({
              value: item?.dictValue,
              label:
                item.dictValue &&
                t('Name_Code', {
                  name: item.dictValueName,
                  code: item?.dictValue,
                }),
            }));
            if (col.key === selectExperiencedFeeType) {
              set(col, 'fieldProps', {
                type: 'select',
                extraProps: {
                  options: options?.map(item => ({
                    value: item?.dictValue,
                    label:
                      item.dictValue &&
                      t('Name_Code', {
                        name: item.dictValueName,
                        code: item?.dictValue,
                      }),
                  })),
                },
              });
              set(col, 'render', (text: string) => fieldsOptions?.find(option => option.value === text)?.label);
              set(col, 'formItemProps', {
                rules: [
                  {
                    required: true,
                    message: {
                      required: true,
                      message: t('channel.common.required', {
                        label: col.title,
                      }),
                    },
                  },
                ],
              });
              return true;
            }
          }),
        },
        ...basicColumns,
        ...transferDynamicFieldsToEditableTableProp(dynamicFields),
      ];
    }, [t, currencyEnums, readonly, selectExperiencedFeeType, medicalBillItemList, surgery, dynamicFields]);

    const uploadProps = useMemo(
      () =>
        getUploadPropsNew(
          `/api/channel/v2/file/upload/?id=${instituteId}&type=${DownloadOrUploadType.HOSPITAL_EXPERIENCED_FEE}&ext=${selectExperiencedFeeType}`,
          getExperiencedFees,
          setUploading
        ),
      [instituteId, getExperiencedFees, selectExperiencedFeeType]
    );

    const download = useCallback(() => {
      setDownloading(true);
      ChannelService.download({
        id: instituteId,
        type: DownloadOrUploadType.HOSPITAL_EXPERIENCED_FEE,
        ext: selectExperiencedFeeType,
      })
        .then(downloadFile)
        .finally(() => setDownloading(false));
    }, [instituteId, selectExperiencedFeeType]);

    const onChangeSelectExperiencedFeeType = useCallback(() => {
      setExperiencedFeeList([]);
      setPaginationTotal(0);
    }, []);

    return (
      <Fragment>
        {(enableApproval || instituteId) && (
          <>
            <Divider style={{ marginTop: 24, marginBottom: 24 }} />
            <ExperiencedFeeHeader
              instituteId={instituteId}
              feeTypeValue={selectExperiencedFeeType}
              handleChange={setSelectExperiencedFeeType}
              disabled={readonly}
              handleFeeTypeChange={onChangeSelectExperiencedFeeType}
            />
            {isApprovalPage ? (
              <Table dataSource={experiencedFeeList} columns={columns} scroll={{ y: 300, x: 'max-content' }} />
            ) : (
              <EditableTable
                outForm={form}
                editBtnProps={{
                  disabled: () => readonly,
                }}
                deleteBtnProps={{
                  disabled: () => readonly,
                  handleDelete: deleteFee,
                }}
                addBtnProps={{
                  disabled: readonly,
                  type: 'default',
                  visible: !readonly,
                  handleAdd: () => {
                    form.resetFields();
                  },
                }}
                rowKey={row => row.id || row.key}
                dataSource={experiencedFeeList}
                columns={columns}
                handleConfirm={saveEdit}
                scroll={{ x: 'max-content' }}
                rightSection={
                  !enableApproval && (
                    <>
                      <Upload {...(uploadProps as UploadProps)}>
                        <Button
                          icon={<Icon type="upload" />}
                          disabled={readonly}
                          style={{ marginRight: 6 }}
                          loading={uploading}
                        >
                          {t('Upload')}
                        </Button>
                      </Upload>
                      <Button icon={<Icon type="download" />} loading={downloading} onClick={download}>
                        {t('Download')}
                      </Button>
                    </>
                  )
                }
                pagination={pagination}
                setDataSource={setExperiencedFeeList}
              />
            )}
          </>
        )}
      </Fragment>
    );
  }
);
