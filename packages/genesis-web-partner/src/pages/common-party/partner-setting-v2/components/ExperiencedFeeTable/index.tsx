/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-11-08 19:48:43
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-11-23 17:25:01
 * @Description:
 */
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { Editable } from '@/pages/common-party/partner-setting/components/Editable';
import { usePagination } from '@/pages/common-party/partner-setting/hooks/pagination';
import {
  getUploadPropsNew,
  replaceSpecialChar,
} from '@/pages/common-party/utils/util';
import { useRequest } from 'ahooks';
import type { ColumnEx } from '@/types/common';
import { Icon, Select, TableActionsContainer } from '@zhongan/nagrand-ui';
import {
  Button,
  DatePicker,
  Divider,
  InputNumber,
  Modal,
  Tooltip,
  Upload,
  message,
} from 'antd';
import type { UploadProps } from 'antd/es/upload';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';
import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import type { BizDictItem, ExperiencedFee } from 'genesis-web-service';
import {
  ChannelService,
  DownloadOrUploadType,
  FeeType,
  MetadataService,
} from 'genesis-web-service';
import { differenceWith, unionBy } from 'lodash-es';
import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ExperiencedFeeHeader } from './ExperiencedFeeHeader';
import { convertStatus } from 'genesis-web-component/lib/components/StatusRender';
import styles from '../../style.scss';
import { renderEnumsWithString } from '@/components/RenderEnums';
import { amountFormatInstance, useL10n } from 'genesis-web-shared/lib/l10n';
import type { Moment } from 'moment';
import moment from 'moment';
import { useGetInputSearchDropdown } from '@/components/ColumnWithSearch';

const { RangePicker } = DatePicker;

interface ExperiencedFeeProp {
  instituteId: number;
  readonly: boolean;
}

// 这两个name后端不会返回，是前端根据后端返回的dict value调用metadata接口获取dict value name
type ExperiencedFeeExt = ExperiencedFee & {
  diagnosisName?: string;
  surgeryName?: string;
};

/**
 *
 * @param instituteId instituteId
 * @param readonly 是否只读
 * @description 目前只用于institute detail页； institute experienced Fee模块展示，可新增、删除、修改、上传、下载
 */
export const ExperiencedFees = ({
  instituteId,
  readonly,
}: ExperiencedFeeProp) => {
  const { t } = useTranslation('partner');

  const [downloading, setDownloading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [currentEditItem, setCurrentEditItem] = useState<ExperiencedFeeExt>();
  const [experiencedFees, setExperiencedFees] = useState<ExperiencedFeeExt[]>(
    [],
  );
  const [selectExperiencedFeeType, setSelectExperiencedFeeType] =
    useState<FeeType>(FeeType.Diagnosis);
  const [searchFeeCode, setSearchFeeCode] = useState<string | undefined>(
    undefined,
  );

  const surgery = useTenantBizDict('surgery') as BizDictItem[];
  const diagnosisSource = useTenantBizDict('diagnosisSource') as BizDictItem[];
  const currencyEnums = useTenantBizDict('currencys') as BizDictItem[];
  const tenantBizDict = useTenantBizDict() as Record<string, BizDictItem[]>;
  const {
    l10n: { dateFormat },
  } = useL10n();

  const [pagination, setPaginationTotal] = usePagination({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const { data: diagnosisData } = useRequest(
    async () => {
      if (!diagnosisSource) return [];
      return await MetadataService.queryBizDictBigKey(
        diagnosisSource[0]?.dictValue as string,
      );
    },
    {
      refreshDeps: [diagnosisSource?.[0]?.dictValue],
    },
  );

  const { data: medicalBillItem } = useRequest(
    async () => await MetadataService.queryBizDictBigKey('medicalBillItem'),
    {
      cacheKey: 'medicalBillItem',
    },
  );

  /**
   * Sub Bill Item 需要展示 medicalBillItem 下的二级 medicalBillSubItem，
   */
  const medicalBillItemList = medicalBillItem?.reduce(
    // 二级不存在时,不需要加入,否则数组会混入值为null的数据
    (pre, cur) => (cur.childList ? pre.concat(cur.childList) : pre),
    [],
  );

  const getExperiencedFees = useCallback(async () => {
    setLoading(true);
    try {
      const feesResp = await ChannelService.getExperiencedFees(instituteId, {
        pageSize: pagination.pageSize,
        pageIndex: pagination?.current - 1,
        feeCode: searchFeeCode,
      });

      setExperiencedFees(feesResp?.data ?? []);
      setPaginationTotal(feesResp?.totalElements || 0);
      if (feesResp?.totalElements > 0) {
        setSelectExperiencedFeeType(feesResp?.data[0].feeType);
      }
    } finally {
      setLoading(false);
    }
  }, [instituteId, pagination.current, pagination.pageSize, searchFeeCode]);

  useEffect(() => {
    if (!instituteId) {
      return;
    }
    getExperiencedFees();
  }, [instituteId, getExperiencedFees]);

  useEffect(() => {
    setCurrentEditItem(null);
    setExperiencedFees(experiencedFees?.filter(item => item?.id));
  }, [readonly]);

  useEffect(() => {
    setCurrentEditItem(null);
  }, [pagination.current, pagination.pageSize]);

  const addExperiencedFee = useCallback(() => {
    const newFee: ExperiencedFee = {
      id: undefined,
      currency: undefined,
      experiencedFee: undefined,
      instituteId,
      feeType: selectExperiencedFeeType,
      feeCode: undefined,
    };

    setCurrentEditItem(newFee);
    setExperiencedFees([newFee].concat(experiencedFees));
  }, [instituteId, experiencedFees, selectExperiencedFeeType]);

  const cancelEdit = useCallback(() => {
    // id 不存在时表示新增
    if (!currentEditItem?.id) {
      const [, ...items] = experiencedFees;
      setExperiencedFees(items);
    }
    setCurrentEditItem(null);
  }, [currentEditItem, experiencedFees]);

  const saveEdit = useCallback(() => {
    const saveAble =
      currentEditItem.feeCode &&
      currentEditItem.feeType &&
      currentEditItem.currency &&
      currentEditItem.experiencedFee;
    if (saveAble) {
      setLoading(true);
      const request: Promise<void> = !currentEditItem.id
        ? ChannelService.addExperiencedFee(instituteId, currentEditItem)
        : ChannelService.updateExperiencedFee(
          instituteId,
          currentEditItem.id,
          currentEditItem,
        );

      request
        .then(() => {
          message.success(t('Operated successfully'));
          getExperiencedFees();
          setCurrentEditItem(null);
        })
        .catch((error: Error) => message.error(error.message))
        .finally(() => setLoading(false));
    } else {
      Modal.error({
        title: t('Params are invalid'),
        content: t('Experienced Fee is required; {{name}} is required', {
          name: convertStatus(
            tenantBizDict,
            selectExperiencedFeeType,
            'hospitalExperiencedFeeType',
          ),
        }),
      });
    }
  }, [
    currentEditItem,
    instituteId,
    getExperiencedFees,
    selectExperiencedFeeType,
    tenantBizDict,
    t,
  ]);

  const deleteFee = useCallback(
    doctorId => {
      setLoading(true);
      ChannelService.deleteFee(instituteId, doctorId)
        .then(() => {
          message.success(t('Delete successfully'));
          getExperiencedFees();
        })
        .catch((error: Error) => {
          message.error(error.message);
          // 删除后要重新请求数据，故不能放在finally里统一置为false
          setLoading(false);
        });
    },
    [instituteId, getExperiencedFees, t],
  );

  const onEditClick = useCallback((event, doctor) => {
    event.preventDefault();
    setCurrentEditItem(doctor);
  }, []);

  const handleChangeEffectivePeriod = (effectivePeriod: Moment[]) => {
    const effectiveDate =
      effectivePeriod &&
      dateFormat.formatTz(moment(effectivePeriod?.[0]).startOf('day'));
    const expiryDate =
      effectivePeriod &&
      dateFormat.formatTz(
        moment(effectivePeriod?.[1]).endOf('day'),
        dateFormat.defaultTimeZone,
      );

    setCurrentEditItem({ ...currentEditItem, effectiveDate, expiryDate });
  };

  /**
   * 下拉选列表的list，在 experiencedFees 基础基础上，在对下拉列表去重，
   * 详见 https://jira.zaouter.com/browse/GIS-80417 第4点
   */
  const options = useMemo(() => {
    let data: BizDictItem[];
    switch (selectExperiencedFeeType) {
      case FeeType.Surgery:
        data = surgery;
        break;
      case FeeType.SubBillItem:
        data = medicalBillItemList;
        break;
      default:
        data = diagnosisData;
    }

    // 允许添加同一个item
    return data;
  }, [selectExperiencedFeeType, surgery, medicalBillItem, diagnosisData]);

  const handleFeeCodeChange = (feeCode: string) => {
    setCurrentEditItem({ ...currentEditItem, feeCode });
  };

  const getSearchDropdown = useGetInputSearchDropdown<ExperiencedFee>(
    (content?: string) => {
      pagination.onChange(1);
      setSearchFeeCode(content);
    },
  );

  const commonColumns: ColumnEx<ExperiencedFee>[] = useMemo(() => {
    const dynamicColumns = [
      {
        title: t('Diagnosis'),
        key: FeeType.Diagnosis,
        dataIndex: 'feeCode',
        editable: true,
        ellipsis: true,
        render: (text: string) =>
          text &&
          t('Name_Code', {
            name: renderEnumsWithString(text, diagnosisData, 'dictValue'),
            code: text,
          }),
      },
      {
        title: t('Surgery'),
        key: FeeType.Surgery,
        dataIndex: 'feeCode',
        ellipsis: true,
        editable: true,
        render: (text: string) =>
          text &&
          t('Name_Code', {
            name: renderEnumsWithString(text, surgery, 'dictValue'),
            code: text,
          }),
      },
      {
        title: t('Sub Bill Item'),
        key: FeeType.SubBillItem,
        dataIndex: 'feeCode',
        ellipsis: true,
        editable: true,
        render: (text: string) =>
          text &&
          t('Name_Code', {
            name: renderEnumsWithString(text, medicalBillItemList, 'dictValue'),
            code: text,
          }),
        ...getSearchDropdown(),
      },
    ];
    const basicColumns = [
      {
        title: t('Experienced Fee'),
        dataIndex: 'currency',
        editable: true,
        render: (text: string, record: ExperiencedFeeExt) =>
          amountFormatInstance.getAmountCurrencyString(
            record.experiencedFee,
            text,
          ),
        editChildren: (
          <Fragment>
            <Select
              style={{ width: 100, marginRight: 5 }}
              defaultValue={currentEditItem?.currency}
              onChange={(val: string) =>
                setCurrentEditItem({ ...currentEditItem, currency: val })
              }
            >
              {currencyEnums?.map(({ enumItemName, dictValue }) => (
                <Select.Option
                  key={enumItemName}
                  value={enumItemName || dictValue}
                >
                  {enumItemName || dictValue}
                </Select.Option>
              ))}
            </Select>
            <InputNumber
              style={{ width: 140 }}
              defaultValue={
                currentEditItem?.experiencedFee &&
                +currentEditItem?.experiencedFee
              }
              onChange={value =>
                !isNaN(value) &&
                setCurrentEditItem({
                  ...currentEditItem,
                  experiencedFee: value.toString(),
                })
              }
            />
          </Fragment>
        ),
      },
      {
        title: t('Effective Period'),
        dataIndex: 'effectiveDate',
        editable: true,
        render: (text: string, record: ExperiencedFee) =>
          dateFormat.getDateRangeString(text, record.expiryDate),
        editChildren: (
          <RangePicker
            defaultValue={
              currentEditItem?.effectiveDate
                ? [
                  moment(currentEditItem?.effectiveDate),
                  moment(currentEditItem?.expiryDate),
                ]
                : null
            }
            format={dateFormat?.dateFormat}
            placeholder={[t('Effective Date'), t('Expiry Date')]}
            onChange={handleChangeEffectivePeriod}
          />
        ),
      },
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        width: 114,
        render: (record: ExperiencedFee) => (
          <div className="table-actions">
            <TableActionsContainer>
              {currentEditItem?.id === record.id ? (
                <>
                  <Button
                    type="link"
                    style={{ minWidth: 15 }}
                    className={styles.btnLink}
                    icon={<Icon type="check" style={{ fontSize: 16 }} />}
                    onClick={saveEdit}
                  />
                  <Button
                    type="link"
                    style={{ minWidth: 15 }}
                    className={styles.btnLink}
                    icon={<Icon type="close" style={{ fontSize: 16 }} />}
                    onClick={cancelEdit}
                  />
                </>
              ) : (
                <>
                  <Tooltip title={t('Edit')}>
                    <Button
                      type="link"
                      icon={<Icon type="edit" style={{ fontSize: 16 }} />}
                      disabled={readonly || !!currentEditItem}
                      onClick={event => onEditClick(event, record)}
                    />
                  </Tooltip>
                  <DeleteConfirm
                    disabled={!!currentEditItem}
                    onOk={() => deleteFee(record.id)}
                  >
                    <Tooltip title={t('Delete')}>
                      <Button
                        type="link"
                        icon={<Icon type="delete" style={{ fontSize: 16 }} />}
                        disabled={readonly || !!currentEditItem}
                      />
                    </Tooltip>
                  </DeleteConfirm>
                </>
              )}
            </TableActionsContainer>
          </div>
        ),
      },
    ];
    return [
      {
        ...dynamicColumns.find(col => col.key === selectExperiencedFeeType),
        editChildren: (
          <Select
            showSearch
            allowClear
            style={{ width: 320 }}
            value={currentEditItem?.feeCode}
            optionFilterProp="label"
            options={options?.map(item => ({
              value: item?.dictValue,
              label:
                item.dictValue &&
                t('Name_Code', {
                  name: item.dictValueName,
                  code: item?.dictValue,
                }),
            }))}
            onChange={handleFeeCodeChange}
          />
        ),
      },
      ...basicColumns,
    ];
  }, [
    t,
    currentEditItem,
    currencyEnums,
    readonly,
    saveEdit,
    cancelEdit,
    onEditClick,
    deleteFee,
    selectExperiencedFeeType,
    handleFeeCodeChange,
    medicalBillItemList,
    diagnosisData,
    surgery,
  ]);

  const handleValueChange = useCallback((formValue: ExperiencedFee) => {
    setCurrentEditItem(
      replaceSpecialChar(formValue, ['feeCode', 'currency'], ''),
    );
  }, []);

  const uploadProps = useMemo(
    () =>
      getUploadPropsNew(
        `/api/channel/v2/file/upload/?id=${instituteId}&type=${DownloadOrUploadType.HOSPITAL_EXPERIENCED_FEE}&ext=${selectExperiencedFeeType}`,
        getExperiencedFees,
        setUploading,
      ),
    [instituteId, getExperiencedFees, selectExperiencedFeeType],
  );

  const download = useCallback(() => {
    setDownloading(true);
    ChannelService.download({
      id: instituteId,
      type: DownloadOrUploadType.HOSPITAL_EXPERIENCED_FEE,
      ext: selectExperiencedFeeType,
    })
      .then(downloadFile)
      .finally(() => setDownloading(false));
  }, [instituteId, selectExperiencedFeeType]);

  const onChangeSelectExperiencedFeeType = useCallback(() => {
    setExperiencedFees([]);
    setPaginationTotal(0);
  }, []);

  return (
    <Fragment>
      {instituteId && (
        <>
          <Divider style={{ marginTop: 24, marginBottom: 24 }} />
          <ExperiencedFeeHeader
            instituteId={instituteId}
            feeTypeValue={selectExperiencedFeeType}
            handleChange={setSelectExperiencedFeeType}
            disabled={readonly || !!currentEditItem}
            handleFeeTypeChange={onChangeSelectExperiencedFeeType}
          />
          {!readonly && (
            <div
              className="flex-between"
              style={{ marginTop: 11, marginBottom: 8 }}
            >
              <Button
                icon={<Icon type="add" />}
                onClick={addExperiencedFee}
                disabled={!!currentEditItem || readonly}
              >
                {t('Add New')}
              </Button>
              <div>
                <Upload {...(uploadProps as UploadProps)}>
                  <Button
                    icon={<Icon type="update" />}
                    disabled={!!currentEditItem || readonly}
                    style={{ marginRight: 6 }}
                    loading={uploading}
                  >
                    {t('Upload')}
                  </Button>
                </Upload>
                <Button
                  icon={<Icon type="download" />}
                  loading={downloading}
                  onClick={download}
                >
                  {t('Download')}
                </Button>
              </div>
            </div>
          )}

          <Editable
            scroll={{ x: 1200 }}
            loading={loading}
            commonColumns={commonColumns}
            dataSource={experiencedFees}
            currentEditItem={currentEditItem}
            handleValueChange={handleValueChange}
            pagination={pagination}
          />
        </>
      )}
    </Fragment>
  );
};
