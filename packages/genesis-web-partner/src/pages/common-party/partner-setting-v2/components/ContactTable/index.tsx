/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import {
  useState,
  useCallback,
  useEffect,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Button, Divider, Form } from 'antd';
import { useFormFields } from '@/pages/common-party/partner-setting/hooks/useFormFields';
import { useEnumsMapping } from '@/pages/common-party/partner-setting/hooks/useEnumsMapping';
import { FormTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import type { CustomerItem, ChannelTypeEnum } from 'genesis-web-service';
import { useTranslation } from 'react-i18next';
import { CommonForm } from '@/components/CommonForm/Form';
import { cloneDeep } from 'lodash-es';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { ComponentWithFallback } from 'genesis-web-component/lib/components/ComponentWithFallback';
import { ModeEnum } from '@/types/common';
import {
  Icon,
  Table,
  TableActionsContainer,
  TextBody,
} from '@zhongan/nagrand-ui';
import styles from '../../style.scss';
import CommonStyles from '../../style.scss';

interface PersonProps {
  initialList: CustomerItem[];
  channelType?: ChannelTypeEnum;
  mode: ModeEnum;
  instituteId?: number;
}

/**
 * @param initialList 初始化列表
 * @param channelType ChannelTypeEnum枚举
 * @description Contact Person信息展示
 */
export const ContactPersonTable = forwardRef(
  ({ initialList, channelType, mode, instituteId }: PersonProps, ref) => {
    const readonly = mode === ModeEnum.READ;
    const { t } = useTranslation(['partner']);
    const [form] = Form.useForm();
    const certiTypeMapping = useEnumsMapping('certiType');
    const fields = useFormFields({
      formType: FormTypeEnum.CONTACT_PERSON,
      basicInfoType: channelType,
      disabled: readonly,
    });

    const [visible, setVisible] = useState(false);
    const [editedIndex, setEditedIndex] = useState<number>();
    const [editedItem, setEditedItem] = useState<CustomerItem>();
    const [shownList, setShownList] = useState<CustomerItem[]>();

    useEffect(() => {
      setShownList([...(initialList || [])]);
    }, [initialList]);

    useImperativeHandle(ref, () => {
      return shownList;
    });

    const editClick = useCallback(
      (item: CustomerItem, index: number) => {
        setVisible(true);
        setEditedIndex(index);
        setEditedItem(item);
        form.setFieldsValue(item);
      },
      [form],
    );

    const deleteClick = useCallback(
      (index: number) => {
        const cloneList = cloneDeep(shownList || []);
        cloneList.splice(index, 1);
        setShownList(cloneList);
      },
      [shownList],
    );

    const onClose = useCallback(() => {
      setVisible(false);
      setEditedIndex(null);
      setEditedItem(null);
      form.resetFields();
    }, [form]);

    const onSubmit = useCallback(() => {
      form.validateFields().then(values => {
        const cloneList = cloneDeep(shownList || []);
        values.id = editedItem?.id;
        if (editedItem) {
          cloneList.splice(editedIndex, 1, values);
        } else {
          cloneList.push(values);
        }
        setShownList(cloneList);
        onClose();
      });
    }, [shownList, form, editedItem, editedIndex, onClose, t]);

    const columns = [
      {
        title: t('Contacts Name'),
        dataIndex: 'name',
      },
      {
        title: t('Contact Person ID Type'),
        dataIndex: 'certType',
        render: (certType?: string) => (
          <ComponentWithFallback>
            {certiTypeMapping[certType]}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Contact Person ID'),
        dataIndex: 'certNo',
      },
      {
        title: t('Telephone'),
        dataIndex: 'phone',
      },
      {
        title: t('Email'),
        dataIndex: 'email',
      },
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (_, item: CustomerItem, index: number) => (
          <TableActionsContainer>
            <Icon
              type={readonly ? 'view' : 'edit'}
              style={{ fontSize: 16 }}
              onClick={() => editClick(item, index)}
            />
            {!item?.id && (
              <Button
                disabled={readonly}
                style={{ margin: 0, padding: 0 }}
                type="text"
              >
                <Icon
                  type="delete"
                  style={{ fontSize: 16 }}
                  onClick={() => deleteClick(index)}
                />
              </Button>
            )}
          </TableActionsContainer>
        ),
      },
    ];

    return (
      <section className={styles.addressInfo}>
        {instituteId && (
          <>
            <Divider style={{ marginTop: 24, marginBottom: 24 }} />
            <p className={CommonStyles.sectionTitle}>
              <TextBody type="h5">
                <Divider type="vertical" className={styles.titleBefore} />
                {t('Contact Person')}
              </TextBody>
              <Button
                icon={<Icon type="add" />}
                disabled={readonly}
                onClick={() => editClick({} as CustomerItem, shownList?.length)}
              >
                {t('Add')}
              </Button>
            </p>
            <Table
              scroll={{ x: 'max-content' }}
              key="id"
              dataSource={shownList}
              columns={columns}
            />
            <DrawerForm
              title={t('Contact Person')}
              visible={visible}
              closable={false}
              onClose={onClose}
              onSubmit={onSubmit}
              cancelText={t('Cancel')}
              sendText={t('Submit')}
              submitBtnShow={!readonly}
            >
              <CommonForm fields={fields} form={form} />
            </DrawerForm>
          </>
        )}
      </section>
    );
  },
);
