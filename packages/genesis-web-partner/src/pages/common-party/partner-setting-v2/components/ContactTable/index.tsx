/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Divider, Form } from 'antd';

import { cloneDeep } from 'lodash-es';

import { Icon, SimpleSectionHeader, Table, TableActionsContainer } from '@zhongan/nagrand-ui';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { ChannelTypeEnum, CustomerItem, InstituteTypeEnum } from 'genesis-web-service';

import { CommonForm } from '@/components/CommonForm/Form';
import {
  EntityType,
  covertPartnerType2SchemaPartnerType,
  useGenericSchemaFormItemFields,
} from '@/hooks/useGenericSchemaFormItemFields';
import { AddNewTitle } from '@/pages/common-party/partner-setting/components/AddNewTitle';
import { useEnumsMapping } from '@/pages/common-party/partner-setting/hooks/useEnumsMapping';
import type { PartnerTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import type { SchemaProps } from '@/types/common';
import { ModeEnum } from '@/types/common';
import { transferSchemaToTableProp } from '@/utils/utils';

import { useApproveModeContext } from '../../hooks/useContext';
import { default as styles } from '../../style.scss';

interface PersonProps {
  initialList: CustomerItem[];
  channelType?: ChannelTypeEnum;
  mode: ModeEnum;
  instituteId?: number;
  instituteType: InstituteTypeEnum;
  isApprovalPage?: boolean;
}

/**
 * @param initialList 初始化列表
 * @param channelType ChannelTypeEnum枚举
 * @description Contact Person信息展示
 */
export const ContactPersonTable = forwardRef(
  ({ initialList, mode, instituteId, instituteType, isApprovalPage = false }: PersonProps, ref) => {
    const readonly = mode === ModeEnum.READ;
    const { t } = useTranslation(['partner']);
    const [form] = Form.useForm();
    const certiTypeMapping = useEnumsMapping('certiType');

    const [visible, setVisible] = useState(false);
    const [editedIndex, setEditedIndex] = useState<number>();
    const [editedItem, setEditedItem] = useState<CustomerItem>();
    const [shownList, setShownList] = useState<CustomerItem[]>();
    const { enableApproval } = useApproveModeContext();

    const { formItems: staticFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'STATIC',
      category: 'CONTACT_PERSON',
      disabled: readonly,
      type: covertPartnerType2SchemaPartnerType(instituteType as unknown as PartnerTypeEnum),
      entityType: EntityType.PARTNER,
    });

    const { formItems: dynamicFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'DYNAMIC',
      category: 'CONTACT_PERSON',
      disabled: readonly,
      type: covertPartnerType2SchemaPartnerType(instituteType as unknown as PartnerTypeEnum),
      entityType: EntityType.PARTNER,
    });

    useEffect(() => {
      setShownList([...(initialList || [])]);
    }, [initialList]);

    useImperativeHandle(ref, () => {
      return shownList;
    });

    const editClick = useCallback(
      (item: CustomerItem, index: number) => {
        setVisible(true);
        setEditedIndex(index);
        setEditedItem(item);
        form.setFieldsValue(item);
      },
      [form]
    );

    const deleteClick = useCallback(
      (index: number) => {
        const cloneList = cloneDeep(shownList || []);
        cloneList.splice(index, 1);
        setShownList(cloneList);
      },
      [shownList]
    );

    const onClose = useCallback(() => {
      setVisible(false);
      setEditedIndex(null);
      setEditedItem(null);
      form.resetFields();
    }, [form]);

    const onSubmit = useCallback(() => {
      form.validateFields().then(values => {
        const cloneList = cloneDeep(shownList || []);
        values.id = editedItem?.id;
        if (editedItem) {
          cloneList.splice(editedIndex, 1, values);
        } else {
          cloneList.push(values);
        }
        setShownList(cloneList);
        onClose();
      });
    }, [shownList, form, editedItem, editedIndex, onClose, t]);

    const columns = [
      ...transferSchemaToTableProp([...staticFields, ...dynamicFields] as SchemaProps[]),
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (_, item: CustomerItem, index: number) => (
          <TableActionsContainer>
            <Icon type={readonly ? 'view' : 'edit'} style={{ fontSize: 16 }} onClick={() => editClick(item, index)} />
            {!item?.id && !isApprovalPage && (
              <Button disabled={readonly} style={{ margin: 0, padding: 0 }} type="text">
                <Icon type="delete" style={{ fontSize: 16 }} onClick={() => deleteClick(index)} />
              </Button>
            )}
          </TableActionsContainer>
        ),
      },
    ];

    return (
      <section className={styles.addressInfo}>
        {(enableApproval || instituteId) && (
          <>
            {isApprovalPage ? (
              <SimpleSectionHeader type="h5" weight="bold" style={{ marginBottom: 16 }}>
                {t('Contact Person')}
              </SimpleSectionHeader>
            ) : (
              <>
                <Divider className="my-6" />
                <AddNewTitle
                  title={t('Contact Person')}
                  onAddClick={() => editClick({} as CustomerItem, shownList?.length)}
                  readonly={readonly}
                />
              </>
            )}
            <Table scroll={{ x: 'max-content' }} key="id" dataSource={shownList} columns={columns} />
            <DrawerForm
              title={t('Contact Person')}
              visible={visible}
              closable={false}
              onClose={onClose}
              onSubmit={onSubmit}
              cancelText={t('Cancel')}
              sendText={t('Submit')}
              submitBtnShow={!readonly}
            >
              <CommonForm fields={[...staticFields, ...dynamicFields]} form={form} />
            </DrawerForm>
          </>
        )}
      </section>
    );
  }
);
