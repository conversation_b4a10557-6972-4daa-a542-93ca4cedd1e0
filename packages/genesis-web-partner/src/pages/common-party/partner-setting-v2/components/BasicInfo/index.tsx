import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import type { FormInstance } from 'antd';
import { Form } from 'antd';

import type { Moment } from 'moment';
import moment from 'moment';

import { SimpleSectionHeader } from '@zhongan/nagrand-ui';

import type { BizDictItem, InstituteDetailV2 } from 'genesis-web-service';
import { InstituteTypeEnum } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import {
  EntityType,
  covertPartnerType2SchemaPartnerType,
  useGenericSchemaFormItemFields,
} from '@/hooks/useGenericSchemaFormItemFields';
import { InvestigationArea } from '@/pages/common-party/partner-setting-v2/components/InvestigationArea';
import { useInstituteBasicInfoFields } from '@/pages/common-party/partner-setting-v2/hooks/useFormFields';
import { ModeEnum } from '@/types/common';
import { EMAIL_PATTERN, NoPureSpace_PATTERN } from '@/utils/utils';

export const BasicInfo = ({
  modeType,
  institute,
  loading,
  form,
}: {
  modeType: ModeEnum;
  institute: InstituteDetailV2;
  loading: boolean;
  form: FormInstance;
}) => {
  const { t } = useTranslation('partner');
  const instituteTypeValue = Form.useWatch('instituteType', form);

  const fields = useInstituteBasicInfoFields({
    instituteDetail: institute,
    disabled: modeType === ModeEnum.READ,
    instituteTypeValue,
    form,
  });
  const { formItems: staticFields } = useGenericSchemaFormItemFields({
    staticOrDynamic: 'STATIC',
    category: 'BASE',
    disabled: modeType === ModeEnum.READ,
    type: covertPartnerType2SchemaPartnerType(instituteTypeValue ?? InstituteTypeEnum.HOSPITAL),
    entityType: EntityType.PARTNER,
  });

  const processedStaticFields = useMemo(() => {
    staticFields.forEach(field => {
      const props = field.ctrlProps ?? {};

      switch (field.key) {
        case 'email':
          field.rule = [
            {
              pattern: EMAIL_PATTERN,
              message: t('Please input correct format'),
            },
          ];
          break;
        case 'comments':
          field.type = FieldType.TextArea;
          field.rule = [
            {
              pattern: NoPureSpace_PATTERN,
              message: t('Please enter a valid character'),
            },
          ];
          field.ctrlProps = {
            ...field.ctrlProps,
            maxLength: 100,
            showCount: true,
          };
          field.col = 24;
          break;
        case 'areaList':
          field.type = FieldType.Customize;
          field.customerDom = <InvestigationArea disabled={props.disabled} />;
          break;
        case 'setUpDate':
          props.format = dateFormatInstance.dateFormat;
          props.disabledDate = (current: Moment) => current && moment(current).isAfter(moment(new Date()));
          break;
        case 'countryCode':
          props.options = props.options?.map((option: BizDictItem) => ({
            ...option,
            label: (
              <span className="flex justify-between">
                {option.label}
                <span>{option.itemExtend2}</span>
              </span>
            ),
          }));
          props.filterOption = (input: string, option: BizDictItem) =>
            (option?.itemExtend2 ?? '').toLowerCase().includes(input.toLowerCase());
          break;
      }
    });
    return [...staticFields];
  }, [staticFields]);

  const { formItems: dynamicFields } = useGenericSchemaFormItemFields({
    staticOrDynamic: 'DYNAMIC',
    category: 'BASE',
    disabled: modeType === ModeEnum.READ,
    type: covertPartnerType2SchemaPartnerType(instituteTypeValue),
    entityType: EntityType.PARTNER,
  });

  return (
    <>
      <SimpleSectionHeader type="h5" weight="bold">
        {t('Basic Information')}
      </SimpleSectionHeader>
      <CommonForm
        style={{ marginTop: 24 }}
        fields={[fields[0], ...processedStaticFields, ...dynamicFields]}
        form={form}
        loading={loading}
      />
    </>
  );
};
