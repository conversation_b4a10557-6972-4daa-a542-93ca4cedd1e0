/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import type { FC, ReactNode } from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { FormInstance } from 'antd';
import { Input } from 'antd';
import { Col, Form, Row, Switch } from 'antd';

import { Select } from '@zhongan/nagrand-ui';

import {
  ChannelDataSourceTypeEnum,
  ExtractPeriodTypeEnum,
  ExtractionType,
  RelationshipConfigTypeEnum,
  SettlementFrequency,
  SettlementTimeTypeEnum,
  YesOrNo,
} from 'genesis-web-service';
import type { BizDictItem, SettlementConfigDTO } from 'genesis-web-service';

import { renderEnumsWithString } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { useTimezoneList } from '@/hooks/useTenantConfig';
import { QuestionTooltip } from '@/pages/common-party/partner-setting-v2/components/RelationshipList';
import { useRelationshipContext } from '@/pages/common-party/partner-setting-v2/hooks/useContext';
import { useGenerateFrequencyOptions } from '@/pages/common-party/partner-setting-v2/hooks/useGenerateFrequencyOptions';

import { MonthlyInput } from './MonthlyInput';
import { PeriodTypeSelect } from './PeriodTypeSelect';
import styles from './index.scss';

// 设置ExtractionDay默认值(Next)
const DefaultExtractionDay = '1';
const DefaultDurationStartDay = '1';

interface BatchConfigFormProps {
  batchConfig?: Pick<
    SettlementConfigDTO,
    | 'frequencyValue'
    | 'frequency'
    | 'reconciliationOrSettlementTimePeriod'
    | 'extractDate'
    | 'extractWeekOrMonth'
    | 'extractPeriodType'
    | 'configId'
    | 'containSubChannelFlag'
    | 'settlementTimeType'
  >;
  type: RelationshipConfigTypeEnum;
  form: FormInstance;
  readonly: boolean;
  isMonthlyDuringMode: boolean;
  defaultDuringLength: number;
  defaultDuringStarts: (number | undefined)[];
  dataSourceType: string;
  isAgencySouceChannel?: boolean;
}

/**
 *
 * 对账结算中共用的Reconciliation Recurrence配置，包含五个输入项和一个非常恶心的信息提示
 * 抽取规则：start from模式：在指定extraction date抽取一周/一个月数据，during模式：在指定extraction date抽取指定区间数据
 * 抽取时间不得小于结算开始时间
 * extraction date是按照结算开始时间算的当月/下月（周），例：monthly start from模式，是抽取period时间往后一个月的数据，所以如果选择了next month，则是period时间下个月的几号开始抽，因此开始抽取时间得大于等于period时间，否则会漏抽
 * 每次切换period date和extraction type，都需要清空extraction date
 *
 * type：是否包含对账结算的配置，没有业务含义，只是用来判断展示的文本
 * form：表单实例
 * batchConfig：Reconciliation Recurrence 在对账结算表单中的六个公共属性
 * 以下三个属性都为月抽during模式设置既存值/初始值
 * isMonthlyDuringMode：用于判断既有数据是否为月抽During模式
 * defaultDuringLength：用于设置月抽During模式有多少区间段
 * defaultDuringStarts：用于设置月抽During模式各区间段起始日期
 * isAgencySouceChannel: agency 类型下隐藏2nd level channel 开关 https://jira.zaouter.com/browse/GIS-93954
 */

export const BatchConfigForm: FC<BatchConfigFormProps> = ({
  form,
  batchConfig,
  type,
  readonly,
  isMonthlyDuringMode,
  defaultDuringLength,
  defaultDuringStarts,
  dataSourceType,
  isAgencySouceChannel = false,
}) => {
  const { t } = useTranslation('partner');
  const weekdayEnums = useTenantBizDict('weekday') as BizDictItem[];
  const channelSettlementFrequencyEnum = useTenantBizDict('channelSettlementFrequency') as BizDictItem[];
  const { level } = useRelationshipContext();
  const timeZoneList = useTimezoneList();
  const timeTypeList = useTenantBizDict('settlementTimeType') as BizDictItem[];
  const frequencyOptions = useGenerateFrequencyOptions('Every', 7);
  const extractionOptions = useGenerateFrequencyOptions('Next', 31);

  // agency 类型下刨除 ReconciliationCompletionTime 选项
  const timeTypeOptions = useMemo(() => {
    return isAgencySouceChannel
      ? timeTypeList?.filter(time => time?.value !== SettlementTimeTypeEnum.ReconciliationCompletionTime)
      : timeTypeList;
  }, [isAgencySouceChannel]);

  const { setFieldsValue } = form;

  const [durationExtractionDayDom, setDurationExtractionDayDom] = useState<ReactNode>();

  const isReconciliation = type === RelationshipConfigTypeEnum.RECONCILIATION;
  const isSettlement = type === RelationshipConfigTypeEnum.SETTLEMENT;

  const frequencyForm = Form.useWatch('frequency', form);
  const frequencyValueForm = Form.useWatch('frequencyValue', form);
  const periodValue = Form.useWatch('reconciliationOrSettlementTimePeriod', form);
  const extractionType = Form.useWatch('extractWeekOrMonth', form);

  const timeZoneLabel = isReconciliation ? t('Reconciliation Time Zone') : t('Settlement Time Zone');
  const periodLabel = isReconciliation ? t('Reconciliation Period') : t('Settlement Period');

  // 当frequency为月时，抽取方式是区间抽取时，用于存储区间个数。
  const [startsLength, setStartsLength] = useState(defaultDuringLength);
  // 当frequency为月时，用于存储抽取方式(区间抽取还是x号开始抽取)
  const [isMonthlyDuring, setIsMonthlyDuring] = useState(isMonthlyDuringMode);

  useEffect(() => {
    setIsMonthlyDuring(isMonthlyDuringMode);
  }, [isMonthlyDuringMode]);

  useEffect(() => {
    setStartsLength(defaultDuringLength);
  }, [defaultDuringLength]);

  const weekExtractionTypeOptions = useMemo(
    () => [
      {
        label: t('of the Next Week'),
        value: ExtractionType.Next,
      },
      {
        label: t('of the Next 2 Week'),
        value: ExtractionType.Next_2,
      },
    ],
    []
  );

  // 获取week情况下，extract date下拉options
  const weekExtractionDateOptions = useMemo(() => {
    return weekdayEnums?.map(weekday => ({
      value: weekday.dictValue,
      label: weekday.dictValueName,
      disabled: extractionType === ExtractionType.Next && weekday.dictValue <= periodValue, // next week模式时抽取时间不得小于结算开始时间
    }));
  }, [weekdayEnums, extractionType, periodValue]);

  const onFrequencyAndValueChange = useCallback(() => {
    let dom: ReactNode = null;
    switch (frequencyForm) {
      // 根据不同抽取周期渲染不同的表单 GIS-64504
      case SettlementFrequency.Day:
        // 设置extractDate默认值为Next
        form.setFieldValue('extractDate', DefaultExtractionDay);
        dom = (
          <>
            <Col span={8} offset={1}>
              {/* 占位 不收集数据 */}
              <Input disabled value={t('--')} style={{ width: 240 }} />
            </Col>
            <Col span={8}>
              <Input.Group compact>
                <Form.Item
                  name="extractDate"
                  rules={[
                    {
                      required: true,
                      message: t('Please Select'),
                    },
                  ]}
                  noStyle
                >
                  <Select options={extractionOptions} disabled style={{ width: 100 }} />
                </Form.Item>
                <Input disabled value={t('Day(s)')} style={{ width: 60 }} />
              </Input.Group>
              {/* </Form.Item> */}
            </Col>
          </>
        );
        break;
      case SettlementFrequency.Week:
        dom = (
          <>
            <Col span={8} offset={1}>
              <Input.Group compact>
                <PeriodTypeSelect readonly disabled={true} />
                <Form.Item
                  name="reconciliationOrSettlementTimePeriod"
                  rules={[
                    {
                      required: true,
                      message: t('Please Select'),
                    },
                  ]}
                >
                  <Select
                    showSearch
                    className={styles.durationStartDateSelect}
                    disabled={readonly}
                    placeholder={t('Please Select')}
                    getPopupContainer={triggerElement => triggerElement?.parentElement}
                    onChange={() => {
                      form.resetFields(['extractDate']);
                    }}
                  >
                    {weekdayEnums?.map(dateOption => (
                      <Select.Option key={dateOption.dictValue} value={dateOption.dictValue}>
                        {dateOption.dictValueName}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Input.Group>
            </Col>
            <Col span={8}>
              <Input.Group compact>
                <Form.Item
                  name="extractDate"
                  rules={[
                    {
                      required: true,
                      message: t('Please Select'),
                    },
                  ]}
                >
                  <Select
                    className={styles.extractionDateSelect}
                    showSearch
                    disabled={readonly}
                    options={weekExtractionDateOptions}
                    getPopupContainer={triggerElement => triggerElement?.parentElement}
                    style={{ width: '40%' }}
                    placeholder={t('Please select')}
                  />
                </Form.Item>
                <Form.Item
                  name="extractWeekOrMonth"
                  rules={[
                    {
                      required: true,
                      message: t('Please Select'),
                    },
                  ]}
                  noStyle
                >
                  <Select
                    disabled={readonly}
                    options={weekExtractionTypeOptions}
                    defaultValue={ExtractionType.Current}
                    getPopupContainer={triggerElement => triggerElement?.parentElement}
                    style={{ width: '60%' }}
                    onChange={() => {
                      form.resetFields(['extractDate']);
                    }}
                  />
                </Form.Item>
              </Input.Group>
            </Col>
          </>
        );
        break;
      case SettlementFrequency.Month:
        dom = (
          <MonthlyInput
            defaults={defaultDuringStarts}
            length={startsLength}
            form={form}
            disabled={readonly}
            onChange={setStartsLength}
            isDuring={isMonthlyDuring}
            onTypeChange={setIsMonthlyDuring}
            isStatic={frequencyValueForm !== '1'}
          />
        );
        break;
      default:
        break;
    }
    setDurationExtractionDayDom(dom);
  }, [
    frequencyForm,
    form,
    frequencyValueForm,
    readonly,
    startsLength,
    isMonthlyDuring,
    extractionOptions,
    weekdayEnums,
    weekExtractionDateOptions,
    defaultDuringStarts,
    setStartsLength,
    setIsMonthlyDuring,
    setDurationExtractionDayDom,
    t,
  ]);

  const handleFrequencyChange = useCallback(() => {
    // frequency一变更，证明不再是During模式，所以During模式下存储数据全部清空重置。
    setIsMonthlyDuring(false);
    setStartsLength(1);
    // frequency变更时，reconciliationOrSettlementTimePeriod, extractDate清空、extractWeekOrMonth设为默认值Next。
    setFieldsValue({
      reconciliationOrSettlementTimePeriod: undefined,
      extractDate: undefined,
      durationStart0: DefaultDurationStartDay,
      durationStart1: undefined,
      durationStart2: undefined,
      durationStart3: undefined,
      extractWeekOrMonth: ExtractionType.Next,
    });
  }, [setIsMonthlyDuring, setStartsLength, setFieldsValue]);

  const handleFrequencyValueChange = useCallback(() => {
    // frequencyValue一变更，证明不再是During模式，所以During模式下存储数据全部清空重置。
    setIsMonthlyDuring(false);
    setStartsLength(1);
    setFieldsValue({
      durationStart0: DefaultDurationStartDay,
      durationStart1: undefined,
      durationStart2: undefined,
      durationStart3: undefined,
      reconciliationOrSettlementTimePeriod: undefined,
      extractDate: undefined,
      extractWeekOrMonth: ExtractionType.Next,
    });
  }, [setIsMonthlyDuring, setStartsLength, setFieldsValue]);

  useEffect(() => {
    onFrequencyAndValueChange();
  }, [frequencyForm, frequencyValueForm, startsLength, isMonthlyDuring, periodValue, extractionType]);

  return (
    <>
      <Row>
        <Col span={8}>
          <Form.Item
            name="timezone"
            label={timeZoneLabel}
            rules={[
              {
                required: true,
                message: t('channel.common.required', {
                  label: timeZoneLabel,
                }),
              },
            ]}
          >
            <Select
              className={styles.standardTdWidth}
              showSearch
              allowClear
              disabled={readonly}
              placeholder={t('Please Select')}
              getPopupContainer={triggerElement => triggerElement?.parentElement}
              options={timeZoneList.map(zoneOption => ({
                value: zoneOption.value,
                label: zoneOption.name,
              }))}
            />
          </Form.Item>
        </Col>
        {isSettlement && (
          <Col span={8}>
            <Form.Item
              name="settlementTimeType"
              label={t('Settlement Time Type')}
              rules={[
                {
                  required: true,
                  message: t('channel.common.required', {
                    label: t('Settlement Time Type'),
                  }),
                },
              ]}
            >
              <Select
                className={styles.standardTdWidth}
                showSearch
                allowClear
                key={dataSourceType}
                disabled={dataSourceType === ChannelDataSourceTypeEnum.SOURCE_DATA_DETAIL || readonly}
                placeholder={t('Please Select')}
                getPopupContainer={triggerElement => triggerElement?.parentElement}
                options={timeTypeOptions}
              />
            </Form.Item>
          </Col>
        )}
      </Row>
      <Row className={styles.layoutMarginBottomRow} />
      <Row style={{ marginBottom: 8 }}>
        <Col span={6}>{t('Frequency')}</Col>
        <Col span={8} offset={1}>
          {periodLabel}
        </Col>
        <Col span={8}>{t('Extraction Date')}</Col>
      </Row>
      <Row className={styles.frequencyContainer}>
        <Col span={6} className={styles.durationFrequencyInputContainer}>
          <Input.Group compact>
            <Form.Item
              name="frequencyValue"
              rules={[
                {
                  required: true,
                  message: t('Please Select'),
                },
              ]}
              noStyle
            >
              <Select
                disabled={readonly}
                className={styles.durationFrequencyValueSelect}
                getPopupContainer={triggerElement => triggerElement?.parentElement}
                onChange={handleFrequencyValueChange}
              >
                {frequencyOptions.map(frequencyOption => (
                  <Select.Option key={frequencyOption.value} value={frequencyOption.value}>
                    {frequencyOption.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item
              name="frequency"
              rules={[
                {
                  required: true,
                  message: t('Please Select'),
                },
              ]}
              noStyle
            >
              <Select
                className={!readonly ? styles.durationFrequencySelect : null}
                showSearch
                disabled={readonly}
                placeholder={t('Please Select')}
                onChange={handleFrequencyChange}
                getPopupContainer={triggerElement => triggerElement?.parentElement}
              >
                {Object.values(SettlementFrequency)
                  .filter(
                    // 年抽不考虑。
                    frequencyEnum => ![SettlementFrequency.Season, SettlementFrequency.Year].includes(frequencyEnum)
                  )
                  .map(key => (
                    <Select.Option key={key} value={key}>
                      {`${renderEnumsWithString(key, channelSettlementFrequencyEnum)}(s)`}
                    </Select.Option>
                  ))}
              </Select>
            </Form.Item>
          </Input.Group>
        </Col>
        {durationExtractionDayDom}
      </Row>
      {frequencyForm === SettlementFrequency.Month && (
        <Row className={styles.monthlyPeriodTips}>
          <Col span={6} />
          <Col span={8} offset={1}>
            {t('End of month (default value: 31st)')}
          </Col>
        </Row>
      )}
      <Row>
        <Col span={8} className={styles.includeAllCol}>
          <span>
            {t('Whether including all historical data')}
            <QuestionTooltip
              tooltip={t('If turn on the switch, then all historical un-settled data will be included.')}
            />
          </span>
          <Form.Item name="extractPeriodType" style={{ marginBottom: 0 }} valuePropName="checked">
            <Switch disabled={readonly} />
          </Form.Item>
        </Col>
      </Row>
      {level == 1 && ( // 一级渠道 且 非agency 类型下才展示
        <Row>
          <Col span={8} className={styles.includeAllCol}>
            {!isAgencySouceChannel && (
              <span>
                {t('Whether to Include 2nd level Channel')}
                <QuestionTooltip tooltip={t('If the switch turns on, sub-channel data will be reconciled together.')} />
              </span>
            )}
            <Form.Item
              name="containSubChannelFlag"
              style={{ marginBottom: 0 }}
              // agency 类型下直接提交默认值No到接口
              hidden={isAgencySouceChannel}
              valuePropName="checked"
            >
              <Switch disabled={readonly} />
            </Form.Item>
          </Col>
        </Row>
      )}
    </>
  );
};
