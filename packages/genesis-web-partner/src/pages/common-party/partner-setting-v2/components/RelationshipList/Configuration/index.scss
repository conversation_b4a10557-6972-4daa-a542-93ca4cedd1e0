@import '../../../../../../variables.scss';

$no-left-border-radius: 0 var(--border-radius-base) var(--border-radius-base) 0;
$no-right-border-radius: var(--border-radius-base) 0 0 var(--border-radius-base);

.reconciliation-detail-drawer {
  overflow-y: scroll;
  h3 {
    margin-top: 16px;
    margin-bottom: 12px;
  }
  :global {
    .#{$antd-prefix}-drawer-title {
      font-size: 20px;
      font-weight: 700;
    }
    .#{$antd-prefix}-drawer-body {
      padding: 8px 16px 80px 24px;
      margin-bottom: 34px;
    }
    .#{$antd-prefix}-form {
      margin-bottom: 24px;
    }
    .#{$antd-prefix}-row .#{$antd-prefix}-form-item {
      margin-bottom: 24px;
    }
    .#{$antd-prefix}-form-item-has-error {
      .#{$antd-prefix}-checkbox-group {
        border-color: var(--error-color);
      }
    }
  }
  .vertical-radio {
    > label {
      display: block;
      margin-bottom: 6px;
    }

    margin-bottom: 12px;
  }
  .button-footer {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background-color: var(--white);
    border-top: 1px solid var(--border-default);
  }

  .check-box-container {
    border: 1px solid var(--border-default);
    border-radius: var(--border-radius-base);
    padding: 4px 16px;
    margin-top: 6px;
    display: flex;
    flex-wrap: wrap;
    :global {
      .#{$antd-prefix}-checkbox-group-item {
        margin: 4px 0;
        width: 290px;
        display: flex;
        .#{$antd-prefix}-checkbox {
          padding-top: 3px;
        }
        > span:nth-last-child(1) {
          display: inline-block;
          flex: 1;
        }
      }
      .#{$antd-prefix}-checkbox-checked::after {
        border: 0;
      }
    }
  }
  .select-all-btn {
    border: none;
    text-align: left;
    padding-top: 6px;
    padding-left: 12px;
    &:hover {
      color: var(--primary-color);
    }
  }
  .duration-start-date-input-disabled {
    width: 280px !important;
  }
                                                                                                                                                                                                                                                                .duration-start-date-select {

    :global {
      .#{$antd-prefix}-select-selector {
        border-radius: $no-left-border-radius !important;
      }
    }
  }
  .duration-frequency-input-container {
    display: flex;
    align-items: flex-start;

    .duration-frequency-value-select {
      width: 120px !important;

      :global {
        .#{$antd-prefix}-select-selector {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }
      }
    }

    .duration-frequency-select {
      width: 120px !important;
      :global {
        .#{$antd-prefix}-select-selector {
          border-radius: $no-left-border-radius;
          background: $input-addon-bg;
        }
      }
    }
  }
  .layout-flex-col {
    display: flex;
  }
  .layout-flex-margin-col {
    display: flex;
    margin-left: 8px;
  }
  .layout-flex-double-margin-col {
    display: flex;
    margin-left: 16px;
  }
  .layout-full-width-row {
    width: 100%;
  }
  .layout-full-width-margin-row {
    width: 100%;
    margin: 8px;
  }
  .layout-margin-bottom-row {
    margin-bottom: -4px;
  }
    .extraction-date-select {

    :global {
      .#{$antd-prefix}-select-selector {
        border-radius: $no-right-border-radius !important;
      }
    }
  }
  .extraction-date-unit {
    width: 60px;
    border: 1px solid var(--border-color-base);
    color: var(--text-color);
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
    border-radius: $no-left-border-radius;
    background: $input-addon-bg;
  }
  .monthly-during-collection-container {
    display: flex;
    width: 100%;
    padding: 8px;
    margin-left: -8px;

    &:first-child {
      margin-top: -8px;
    }
    &:hover {
      background: var(--table-row-hover-bg);
      border-radius: var(--border-radius-base);
    }
    .monthly-action-container {
      display: flex;
      & > div:nth-of-type(2) {
        margin-left: 18px;
      }
      .monthly-action-svg {
        align-self: flex-start;
        margin: 5px;
        cursor: pointer;
      }
    }

                .component-duration-input-container {

      :global {
        .#{$antd-prefix}-input-number {
          border-radius: 0 !important;
          width: 70px !important;
          background: var(--white);
        }

        .#{$antd-prefix}-input-number-disabled {
          color: var(--text-color);
        }

        .#{$antd-prefix}-input-number-affix-wrapper-status-error {
          border: 1px solid var(--error-color);
        }

        .#{$antd-prefix}-input-number-affix-wrapper {
          display: flex;
          border-left: 0;
          padding: 0;
          background: var(--white);

          .#{$antd-prefix}-input-number-prefix {
            color: var(--text-color);
            font-weight: 500;
            font-size: 14px;
            line-height: 22px;
            margin: 0 3px;
          }

          .#{$antd-prefix}-input-number {
            border-radius: var(--border-radius-base) !important;
          }
        }
      }

      input {
        text-align: center;
      }
    }

    .component-duration-input-container-disabled {
      @extend .component-duration-input-container;

      :global {
        .#{$antd-prefix}-input-number,
        .#{$antd-prefix}-input-number-affix-wrapper,
        .#{$antd-prefix}-input-number-prefix {
          color: var(--disabled-color) !important;
          background: var(--disabled-bg) !important;
        }
      }
    }
  }
  .component-period-type-wording {
    width: 120px;
    height: 32px;
    color: var(--text-color);
    padding: 0 8px;
    line-height: 32px;
    background: $input-addon-bg;
    border: 1px solid var(--border-color-base);
    border-radius: $no-right-border-radius;
  }
  .component-period-type-select {
    width: 110px !important;
    height: 32px;

    :global {
      .#{$antd-prefix}-select-selector {
        background-color: $input-addon-bg !important;
        border-radius: $no-right-border-radius !important;
      }
    }
  }
  .monthly-period-tips {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    display: flex;
    align-items: center;
    color: var(--disabled-color);
    margin-bottom: 4px;
  }
  .frequency-container {
    :global {
      .#{$antd-prefix}-form-item {
        margin-bottom: 0;
      }
      .#{$antd-prefix}-input-group-compact {
        display: flex;
      }
      .#{$antd-prefix}-select-arrow {
        top: 16px;
      }
    }
  }
}
.reconciliation-config-table {
  :global {
    .#{$antd-prefix}-table {
      .#{$antd-prefix}-table-row {
        a {
          color: var(--primary-color);
        }
      }
      th {
        max-width: 240px;
      }
      th:nth-last-child(1) {
        width: 90px;
        min-width: 0;
      }
      .#{$antd-prefix}-btn-link,
      .#{$antd-prefix}-btn-link:active,
      .#{$antd-prefix}-btn-link:focus {
        min-width: 0;
        padding: 0;
        border: none;
        background-color: transparent;
        border-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none;
      }
    }
  }
}
.standard-td-width {
  width: 240px !important;
}

.include-all-col {
  display: flex;
  align-items: center;
  margin-top: 16px;
}
