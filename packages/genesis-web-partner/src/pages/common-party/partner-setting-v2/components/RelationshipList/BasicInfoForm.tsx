/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { FormInstance } from 'antd';
import { Checkbox, Col, Divider, Form, Input, Radio, Row } from 'antd';
import type { RadioChangeEvent } from 'antd/es';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';
import type { OptionProps } from 'antd/lib/select';
import moment from 'moment';

import { DatePicker, Select } from '@zhongan/nagrand-ui';

import { TenantOrgType } from 'genesis-web-service';
import type { BizDictItem, RelationshipDetailDTO } from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { RenderEnums } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';

import styles from './index.scss';

interface BasicInfoFormProps {
  basicInfoDetail: RelationshipDetailDTO;
  sourceChannelType: TenantOrgType;
  agencyList: OptionProps[];
  form: FormInstance<RelationshipDetailDTO>;
  readonly: boolean;
}

/**
 * basicInfoDetail：最新的relationshipDetail
 * sourceChannelType： channel的type，和父组件的业务含义相同
 */

enum EffectiveDateType {
  DetermineSection = 'DETERMINE_SECTION',
  LongTerm = 'LONG_TERM',
}

enum ConfigurationOptions {
  BillingSender = 'BILLING_SENDER',
  DataReceiver = 'DATA_RECEIVER',
}

export const BasicInfoDetail: React.FC<BasicInfoFormProps> = ({
  form,
  basicInfoDetail,
  sourceChannelType,
  agencyList,
  readonly,
}) => {
  const { setFieldsValue, resetFields } = form;
  const { t } = useTranslation('partner');
  const {
    l10n: { dateFormat },
  } = useL10n();
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  const [isReconciliationConfigChecked, setReconciliationConfigChecked] = useState(false);

  const [isSettlementConfigChecked, setSettlementConfigChecked] = useState(false);

  const [isEffectiveDateVisible, setEffectiveDateVisible] = useState(true);

  useEffect(() => {
    resetFields();
    setSettlementConfigChecked(!!basicInfoDetail?.settlementConfig);
    setReconciliationConfigChecked(!!basicInfoDetail?.reconciliationConfig);
    if (basicInfoDetail?.effectiveDateType) {
      setEffectiveDateVisible(basicInfoDetail?.effectiveDateType === EffectiveDateType.DetermineSection);
    }
    setFieldsValue({
      ...basicInfoDetail,
      effectiveDateType: basicInfoDetail?.effectiveDateType ?? EffectiveDateType.DetermineSection,
      effectiveDate: basicInfoDetail?.startDate
        ? [moment(basicInfoDetail?.startDate), moment(basicInfoDetail?.endDate)]
        : null,
    });
  }, [basicInfoDetail, resetFields, setFieldsValue]);

  const onReconciliationConfigChange = useCallback(
    (event: CheckboxChangeEvent) => {
      setReconciliationConfigChecked(event.target.checked);
      if (!event.target.checked) {
        resetFields(['reconciliationConfig']);
      }
    },
    [resetFields]
  );

  const onSettlementConfigChange = useCallback(
    (event: CheckboxChangeEvent) => {
      setSettlementConfigChecked(event.target.checked);
      if (!event.target.checked) {
        resetFields(['settlementConfig']);
      }
    },
    [resetFields]
  );

  return (
    <>
      <h3 style={{ paddingTop: 14, fontWeight: 600 }}>{t('Relationship Basic Info')}</h3>
      <Form form={form} layout="vertical">
        <Row>
          <Col span={8}>
            <Form.Item
              name="code"
              label={t('Relationship Code')}
              rules={[
                {
                  required: true,
                  message: t('channel.common.required', {
                    label: t('Relationship Code'),
                  }),
                },
              ]}
            >
              <Input
                disabled={!!basicInfoDetail?.channelRelationId || readonly}
                className={styles.standardTdWidth}
                placeholder={t('Please Input')}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="name"
              label={t('Relationship Name')}
              rules={[
                {
                  required: true,
                  message: t('channel.common.required', {
                    label: t('Relationship Name'),
                  }),
                },
              ]}
            >
              <Input disabled={readonly} className={styles.standardTdWidth} placeholder={t('Please Input')} />
            </Form.Item>
          </Col>
          {/* // https://jira.zaouter.com/browse/GIS-93703 隐藏Belonging Agency字段 */}
          {/* {sourceChannelType === TenantOrgType.SALE_CHANNEL && (
            <Col span={8}>
              <Form.Item name="targetChannelId" label={t('Belonging Agency')}>
                <Select
                  className={styles.standardTdWidth}
                  showSearch
                  allowClear
                  disabled={readonly}
                >
                  {agencyList?.map(agency => (
                    <Select.Option key={agency.value} value={agency.value}>
                      {agency.label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          )} */}
        </Row>
        <Row>
          <Col span={24}>
            <Form.Item
              name="effectiveDateType"
              label={t('Period of Validity')}
              rules={[
                {
                  required: true,
                  message: t('channel.common.required', {
                    label: t('Period of Validity'),
                  }),
                },
              ]}
            >
              <Radio.Group
                disabled={readonly}
                onChange={(event: RadioChangeEvent) =>
                  setEffectiveDateVisible(event?.target?.value === EffectiveDateType.DetermineSection)
                }
              >
                <Radio value={EffectiveDateType.DetermineSection} style={{ display: 'block' }}>
                  <RenderEnums
                    keyName={EffectiveDateType.DetermineSection}
                    enums={enums?.channelRelationEffectiveDateType}
                  />
                </Radio>
                {isEffectiveDateVisible && (
                  <Form.Item
                    name="effectiveDate"
                    style={{ marginLeft: 24, marginBottom: 14 }}
                    rules={[
                      {
                        required: isEffectiveDateVisible,
                        message: t('Please Select'),
                      },
                    ]}
                  >
                    <DatePicker.RangePicker
                      disabled={readonly}
                      style={{ marginTop: 8 }}
                      format={dateFormat?.dateFormat}
                    />
                  </Form.Item>
                )}
                <Radio
                  disabled={readonly}
                  value={EffectiveDateType.LongTerm}
                  style={{ display: 'block', marginTop: 8 }}
                >
                  <RenderEnums keyName={EffectiveDateType.LongTerm} enums={enums?.channelRelationEffectiveDateType} />
                </Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
        <Divider dashed style={{ marginTop: 0 }} />
        <p style={{ marginBottom: 12 }}>{t('Configuration')}</p>
        {sourceChannelType !== TenantOrgType.AGENCY && (
          <>
            <Row>
              <Checkbox
                disabled={readonly}
                checked={isReconciliationConfigChecked}
                onChange={onReconciliationConfigChange}
              >
                {t('Reconciliation')}
              </Checkbox>
            </Row>
            <Form.Item
              name="reconciliationConfig"
              style={{ marginBottom: 12, marginLeft: 24 }}
              rules={[
                {
                  required: isReconciliationConfigChecked,
                  message: t('channel.common.required', {
                    label: t('Reconciliation'),
                  }),
                },
              ]}
            >
              <Select
                disabled={!isReconciliationConfigChecked || readonly}
                placeholder={t('Please Select')}
                className={styles.standardTdWidth}
                style={{ marginTop: 8 }}
              >
                <Select.Option value={ConfigurationOptions.DataReceiver}>
                  <RenderEnums keyName={ConfigurationOptions.DataReceiver} enums={enums?.reconciliationConfig} />
                </Select.Option>
              </Select>
            </Form.Item>
          </>
        )}

        <Row>
          <Checkbox disabled={readonly} checked={isSettlementConfigChecked} onChange={onSettlementConfigChange}>
            {t('Settlement')}
          </Checkbox>
        </Row>
        <Form.Item
          name="settlementConfig"
          style={{ marginLeft: 24 }}
          rules={[
            {
              required: isSettlementConfigChecked,
              message: t('channel.common.required', {
                label: t('Settlement'),
              }),
            },
          ]}
        >
          <Select
            disabled={!isSettlementConfigChecked || readonly}
            placeholder={t('Please Select')}
            className={styles.standardTdWidth}
            style={{ marginTop: 8 }}
          >
            <Select.Option value={ConfigurationOptions.BillingSender}>
              <RenderEnums keyName={ConfigurationOptions.BillingSender} enums={enums?.reconciliationConfig} />
            </Select.Option>
          </Select>
        </Form.Item>
      </Form>
    </>
  );
};
