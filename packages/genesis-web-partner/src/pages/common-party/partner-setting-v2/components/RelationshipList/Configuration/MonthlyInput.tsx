import { PlusOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { FormInstance } from 'antd';
import { Col, Form, Row, Input } from 'antd';
import { useCallback, useEffect, useState, useMemo } from 'react';

import { DurationInput } from './DurationInput';
import { PeriodTypeSelect } from './PeriodTypeSelect';
import { DeleteOutline } from '@/components/Icons';
import { ExtractionType } from 'genesis-web-service';

import styles from './index.scss';
import { Select } from '@zhongan/nagrand-ui';

// 设置ExtractionDay区间结尾日常量
const ExtractionDayDuringEnd = 31;

interface ComponentProps {
  form: FormInstance<any>;
  length: number;
  defaults: (number | undefined)[];
  disabled?: boolean;
  onChange: (value: number) => void;
  isDuring: boolean;
  onTypeChange: (value: boolean) => void;
  isStatic: boolean;
}

/**
 * BatchConfigForm中的月抽模式使用
 * StartFrom模式：单月或隔月，从x日开始，不需要特殊处理
 * During模式：单月抽取，以区间的形式，以1开头，31结尾。
 *  存了各区间的起始值，提交时会通过起始值计算出结尾值，最后拼成"1~5,6~15,16~31"形式的字符串。
 *
 * length：区间数
 * defaults：默认各区间起始日数组
 * form：从上层传来的表单
 * disabled：是否为只读模式
 * extractionOptions：选择抽取日的下拉列表（Next）
 * onChange：用于变更区间数目
 * isDuring：标记月抽模式During/StartFrom，true为During模式
 * onTypeChange：变更月抽模式
 * isStatic：用于标记是否为隔月抽，这种抽取只有StartFrom模式
 */
export const MonthlyInput = (props: ComponentProps) => {
  const {
    length,
    defaults,
    form,
    disabled,
    onChange,
    isDuring,
    onTypeChange,
    isStatic,
  } = props;
  const { t } = useTranslation('partner');
  // 设置during模式时，区间起始值数组用于显示，区间结尾值由下一区间的起始值减1计算得出。
  const [startValues, setStartValues] =
    useState<(number | undefined)[]>(defaults);

  const frequencyValueForm = Form.useWatch('frequencyValue', form);
  const periodValue = Form.useWatch(
    'reconciliationOrSettlementTimePeriod',
    form,
  );
  const extractWeekOrMonth = Form.useWatch('extractWeekOrMonth');
  const extractWeekOrMonth0 = Form.useWatch('extractWeekOrMonth0');
  const extractWeekOrMonth1 = Form.useWatch('extractWeekOrMonth1');
  const extractWeekOrMonth2 = Form.useWatch('extractWeekOrMonth2');
  const extractWeekOrMonth3 = Form.useWatch('extractWeekOrMonth3');

  useEffect(() => {
    setStartValues(defaults);
  }, [defaults]);

  const selectedExtractionTypes = useMemo(
    () => ({
      extractWeekOrMonth,
      extractWeekOrMonth0,
      extractWeekOrMonth1,
      extractWeekOrMonth2,
      extractWeekOrMonth3,
    }),
    [
      extractWeekOrMonth,
      extractWeekOrMonth0,
      extractWeekOrMonth1,
      extractWeekOrMonth2,
      extractWeekOrMonth3,
    ],
  );

  const getMonthExtractionTypeOptions = useCallback(
    (endData: number) => [
      {
        label: t('of the Current Month'),
        value: ExtractionType.Current,
        disabled: frequencyValueForm !== '1' || endData === 31 || !isDuring, // 选择every X month 或者选择最后一天 或者start from模式时，current配置禁用
      },
      {
        label: t('of the Next Month'),
        value: ExtractionType.Next,
      },
      {
        label: t('of the Next 2 Month'),
        value: ExtractionType.Next_2,
      },
    ],
    [frequencyValueForm, isDuring],
  );

  const defaultMonthDateOptions = useMemo(() => {
    const formatDay = (day: number) => {
      switch (day) {
        case 1:
          return t('1st');
        case 2:
          return t('2nd');
        case 3:
          return t('3rd');
        case 21:
          return t('21st');
        case 22:
          return t('22nd');
        case 23:
          return t('23rd');
        case 31:
          return t('31st');
        default:
          return t('(n)th', { n: day });
      }
    };

    return new Array(31).fill(0).map((_, index) => ({
      label: formatDay(index + 1),
      value: index + 1,
    }));
  }, []);

  const handleChange = useCallback(
    (value: number | undefined, index: number) => {
      const starts = [...startValues];
      starts[index] = value;
      setStartValues(starts);
      form.resetFields([`extractDate${index - 1}`]); // 修改start date时，清除上一条数据的extractDate
    },
    [startValues, setStartValues],
  );

  const handleModeChange = (value: boolean) => {
    onTypeChange(value);
    if (!value) {
      /**
       * 切换为Start From模式时：
       * 1.设置exactionDay默认值;
       * 2.清空during区间的值(durationStart + key字段 * 4);
       * 3.设置during区间条数为1(onChange);
       * 4.设置during区间起始值数组为初始值[1, undefined, undefined, undefined];
       */
      form.setFieldsValue({
        extractDate: undefined,
        extractDate0: undefined,
        durationStart0: '1',
        durationStart1: undefined,
        durationStart2: undefined,
        durationStart3: undefined,
        extractWeekOrMonth: ExtractionType.Next,
      });
      onChange(1);
      setStartValues([1, undefined, undefined, undefined]);
    } else {
      /**
       * 切换为During模式时：
       * 1.清空frequencyValue2值
       *  (由于during使用的是duration + key的字段，所以需要清空frequencyValue2值，
       *  防止再切换为Start From模式时，会回填缓存的frequencyValue2值)
       * 2.设置exactionDay默认值;
       */
      form.setFieldsValue({
        reconciliationOrSettlementTimePeriod: undefined,
        durationStart0: '1',
        extractDate0: undefined,
        extractWeekOrMonth0: ExtractionType.Next,
      });
    }
  };

  const handleReduce = useCallback(() => {
    // 最少一个区间
    if (length !== 1) {
      // 将删除的区间起始值重置，以防表单缓存导致的再追加新区间时，将已删除的区间值带入新区间。
      form.setFieldValue(`durationStart${length - 1}`, undefined);
      // 同步用于显示的起始值数组。
      const starts = [...startValues];
      starts[length - 1] = undefined;
      setStartValues(starts);
      onChange(length - 1);
    }
    form.setFieldValue(`extractWeekOrMonth${length - 2}`, ExtractionType.Next); // 最后一条的extraction type要重置
  }, [length, startValues, form, setStartValues, onChange]);

  const handleAdd = useCallback(() => {
    // 最多4个区间，或者某一区间起始日是31，那么不允许再新增区间
    if (length === 4 || startValues.includes(ExtractionDayDuringEnd)) {
      return;
    }
    // during增加一个区间时设置exaction Date type默认值
    form.setFieldValue(`extractWeekOrMonth${length}`, ExtractionType.Next);
    const starts = [...startValues];
    starts[length] = undefined;
    setStartValues(starts);
    onChange(length + 1);
  }, [length, startValues, form, setStartValues, onChange]);

  useEffect(() => {
    // 如果某一区间起始日是31，该区间之后的被删除。
    const monthEndIndex = startValues.indexOf(ExtractionDayDuringEnd);
    if (monthEndIndex > 0) {
      onChange(monthEndIndex + 1);
    }
  }, [startValues]);

  const getEndDate = useCallback(
    (key: number) => {
      if (key + 1 === length) {
        // 最后一个区间，结尾值固定显示31
        return ExtractionDayDuringEnd;
      }
      // 由于是1-31的闭区间，其他区间结尾值等于下一区间的起始值减一
      return startValues[key + 1]
        ? (startValues[key + 1] as number) - 1
        : undefined;
    },
    [length, ExtractionDayDuringEnd, startValues],
  );

  const getMonthExtractionDateOptions = useCallback(
    (currExtractionType: ExtractionType, endDateValue: number) => {
      if (
        (currExtractionType === ExtractionType.Current && endDateValue) ||
        (!isDuring && currExtractionType === ExtractionType.Next)
      ) {
        // current配置或者start from模式下的next month配置，只能选大于等于end date的date
        return defaultMonthDateOptions?.map(date => ({
          ...date,
          disabled: date.value < endDateValue,
        }));
      }
      return defaultMonthDateOptions;
    },
    [defaultMonthDateOptions, isDuring],
  );

  return !isDuring ? (
    <>
      <Col span={8} offset={1} className={styles.layoutFlexCol}>
        <Input.Group compact>
          {isStatic ? (
            // 隔多月(frequencyValue >= Every 2)抽取的时候，只能选择第一个月几号开始抽，不能分区间抽取。
            <PeriodTypeSelect readonly disabled={disabled} />
          ) : (
            // 单月抽取的时候，可以分区间抽取，也可以选从几号开始抽。
            <PeriodTypeSelect
              readonly={false}
              onChange={handleModeChange}
              isDuring={isDuring}
              disabled={disabled}
            />
          )}
          <Form.Item
            name="reconciliationOrSettlementTimePeriod"
            rules={[
              {
                required: true,
                message: t('Please Select'),
              },
            ]}
          >
            <Select
              showSearch
              className={styles.durationStartDateSelect}
              disabled={disabled}
              placeholder={t('00')}
              getPopupContainer={triggerElement =>
                triggerElement?.parentElement
              }
              options={defaultMonthDateOptions}
              onChange={() => form.resetFields(['extractDate'])}
            />
          </Form.Item>
        </Input.Group>
      </Col>
      <Col span={8}>
        <Input.Group compact>
          <Form.Item
            name="extractDate"
            rules={[
              {
                required: true,
                message: t('Please Select'),
              },
            ]}
          >
            <Select
              className={styles.extractionDateSelect}
              showSearch
              disabled={disabled}
              options={getMonthExtractionDateOptions(
                selectedExtractionTypes?.extractWeekOrMonth,
                periodValue,
              )}
              getPopupContainer={triggerElement =>
                triggerElement?.parentElement
              }
              style={{ width: '40%' }}
              placeholder={t('Please select')}
            />
          </Form.Item>
          <Form.Item
            name="extractWeekOrMonth"
            rules={[
              {
                required: true,
                message: t('Please Select'),
              },
            ]}
            noStyle
          >
            <Select
              disabled={disabled}
              options={getMonthExtractionTypeOptions(periodValue)}
              getPopupContainer={triggerElement =>
                triggerElement?.parentElement
              }
              style={{ width: '60%' }}
              onChange={() => {
                form.resetFields(['extractDate']);
              }}
            />
          </Form.Item>
        </Input.Group>
      </Col>
    </>
  ) : (
    <>
      <Col span={17} offset={1}>
        {Array.from(new Array(length).keys()).map(key => (
          <Row key={key} className={styles.monthlyDuringCollectionContainer}>
            <Col span={11}>
              <Input.Group compact>
                <PeriodTypeSelect
                  readonly={false}
                  onChange={handleModeChange}
                  isDuring={isDuring}
                  disabled={disabled}
                />
                <DurationInput
                  index={key}
                  name={`durationStart${key}`}
                  handleChange={handleChange}
                  readonly={disabled}
                  // 1号位置禁止自定义输入
                  disabled={key === 0 || disabled}
                  // 除第一行外，其余行区间起始日不得小于上一行的起始日
                  min={key === 0 ? 1 : (startValues[key - 1] ?? 0) + 1}
                  // 除最后一行的区间结尾日为31外，其余行区间结尾日不得大于下一行的结尾日
                  endData={getEndDate(key)}
                />
              </Input.Group>
            </Col>
            <Col span={10} offset={1} style={{ left: -11 }}>
              <Input.Group compact>
                <Form.Item
                  name={`extractDate${key}`}
                  rules={[
                    {
                      required: true,
                      message: t('Please Select'),
                    },
                  ]}
                  shouldUpdate
                >
                  <Select
                    className={styles.extractionDateSelect}
                    showSearch
                    disabled={disabled}
                    getPopupContainer={triggerElement =>
                      triggerElement?.parentElement
                    }
                    options={getMonthExtractionDateOptions(
                      selectedExtractionTypes[
                        `extractWeekOrMonth${key}`
                      ] as ExtractionType,
                      getEndDate(key),
                    )}
                    style={{ width: '40%' }}
                    placeholder={t('Please select')}
                  />
                </Form.Item>
                <Form.Item
                  name={`extractWeekOrMonth${key}`}
                  rules={[
                    {
                      required: true,
                      message: t('Please Select'),
                    },
                  ]}
                  noStyle
                >
                  <Select
                    disabled={disabled}
                    options={getMonthExtractionTypeOptions(getEndDate(key))}
                    getPopupContainer={triggerElement =>
                      triggerElement?.parentElement
                    }
                    style={{ width: '60%' }}
                    onChange={() => {
                      form.resetFields([`extractDate${key}`]);
                    }}
                  />
                </Form.Item>
              </Input.Group>
            </Col>
            {/** 只有一行时没有删除， 有4行(最多四行)或任一行起始日是31时，不能再追加新行 */}
            {key === length - 1 && !disabled && (
              <Col span={1} className={styles.monthlyActionContainer}>
                {length !== 4 &&
                  !startValues.includes(ExtractionDayDuringEnd) && (
                  <div
                    onClick={handleAdd}
                    className={styles.monthlyActionSvg}
                  >
                    <PlusOutlined />
                  </div>
                )}
                {length !== 1 && (
                  <div
                    onClick={handleReduce}
                    className={styles.monthlyActionSvg}
                  >
                    <DeleteOutline />
                  </div>
                )}
              </Col>
            )}
          </Row>
        ))}
      </Col>
    </>
  );
};
