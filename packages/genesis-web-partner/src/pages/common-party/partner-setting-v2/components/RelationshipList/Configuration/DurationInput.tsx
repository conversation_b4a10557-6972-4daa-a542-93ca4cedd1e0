import { Form, Input, InputNumber } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import style from './index.scss';

interface InputProps {
  name: string;
  index: number;
  min: number;
  endData?: number;
  disabled?: boolean;
  readonly?: boolean;
  value?: number;
  onChange?: (value?: number) => void;
  handleChange: (value: number | undefined, index: number) => void;
}

/**
 * MonthlyInput中的月抽During模式使用。区间输入的自定义antd form组件。
 *
 * name：form item属性，即表单的key
 * index：标记区间的key值0～3
 * min：最小值，不小于上一区间的起始值+1 || 2
 * endData：最大值，不大于下一区间的起始值+1 || 31
 * disabled：是否禁止输入
 * readonly：是否为只读模式，区别于disabled
 * value：form item传递来的值，自动绑定无需传递，即表单值
 * onChange：form item传递来的change函数，自动绑定无需传递，用于变更表单值
 * handleChange：用于存储区间起始日，来计算上一行的结尾日和做一些逻辑check
 */
const InnerDurationInput = (props: InputProps) => {
  const {
    index,
    value,
    endData,
    disabled,
    min,
    readonly,
    onChange,
    handleChange,
  } = props;
  const [startData, setStartData] = useState(value);
  const { t } = useTranslation('partner');

  useEffect(() => {
    if (startData !== value) {
      setStartData(value);
    }
  }, [value]);

  useEffect(() => {
    if (startData !== value) {
      onChange?.(startData);
      handleChange(startData, index);
    }
  }, [startData]);

  return (
    <Input.Group
      compact
      className={
        readonly
          ? style.componentDurationInputContainerDisabled
          : style.componentDurationInputContainer
      }
    >
      <InputNumber
        disabled={disabled}
        value={startData}
        precision={0}
        min={min}
        max={endData}
        placeholder={t('00')}
        onChange={setStartData}
      />
      <InputNumber
        prefix={t('to')}
        value={endData}
        placeholder={t('t - 1')}
        // 只用于显示 不做数据收集。
        disabled
      />
    </Input.Group>
  );
};

export const DurationInput = (props: InputProps) => {
  return (
    <Form.Item
      name={props.name}
      rules={[{ required: true, message: 'Please Select' }]}
    >
      <InnerDurationInput {...props} />
    </Form.Item>
  );
};
