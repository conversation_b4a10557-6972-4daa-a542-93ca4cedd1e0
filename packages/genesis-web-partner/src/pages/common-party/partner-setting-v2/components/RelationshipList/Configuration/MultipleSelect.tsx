/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import type { FormInstance } from 'antd';
import { Form } from 'antd';
import type { SelectProps } from 'antd/es/select';

import { Select } from '@zhongan/nagrand-ui';

import type {
  BizDictItem,
  ReconciliationConfigDTO,
  RelationshipConfigDTO,
  SettlementConfigDTO,
} from 'genesis-web-service';

/**
 * initialValue：form中的initialvalue
 * formKey：字段的key，会用作formitem的name
 * options：下拉项
 * form：表单实例
 * required：必填
 */

interface MultipleSelectProps extends SelectProps {
  initialValue?: RelationshipConfigDTO;
  formKey: keyof ReconciliationConfigDTO | keyof SettlementConfigDTO;
  options: BizDictItem[];
  form: FormInstance;
  label: string;
  required?: boolean;
}

export const MultipleSelect: FC<MultipleSelectProps> = ({
  initialValue,
  form,
  formKey,
  options,
  label,
  required = false,
  ...rest
}) => {
  const { t } = useTranslation('partner');

  return (
    <Form.Item
      label={label || formKey}
      name={formKey}
      rules={[
        {
          required,
          message: t('channel.common.required', {
            label,
          }),
        },
      ]}
    >
      <Select
        allowClear
        mode="multiple"
        placeholder={t('Please Select')}
        getPopupContainer={triggerElement => triggerElement?.parentElement}
        {...rest}
      >
        {options?.map(bizDict => (
          <Select.Option key={bizDict?.value ?? bizDict?.enumItemName} value={bizDict?.value ?? bizDict?.enumItemName}>
            {bizDict?.label ?? bizDict?.dictValueName}
          </Select.Option>
        ))}
      </Select>
    </Form.Item>
  );
};
