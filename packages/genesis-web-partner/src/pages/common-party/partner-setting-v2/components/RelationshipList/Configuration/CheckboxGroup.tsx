/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import type { FC, ReactNode } from 'react';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { FormInstance } from 'antd';
import { Checkbox, Col, Form, Row } from 'antd';
import type { CheckboxGroupProps as AntdCheckboxGroupProps, CheckboxValueType } from 'antd/es/checkbox/Group';

import type { BizDictItem, RelationshipConfigDTO } from 'genesis-web-service';

import styles from './index.scss';

/**
 * initialValue：form中的initialvalue
 * formKey：字段的key，会用作formitem的name
 * localOptions：下拉项
 * form：表单实例
 * required：必填
 * toolTip：label需要有额外的提示
 */

interface CheckboxGroupProps extends AntdCheckboxGroupProps {
  label: string;
  formKey: keyof RelationshipConfigDTO;
  localOptions: BizDictItem[];
  form: FormInstance;
  initialValue?: RelationshipConfigDTO;
  required?: boolean;
  toolTip?: ReactNode;
  readonly?: boolean;
}

export const CheckboxGroup: FC<CheckboxGroupProps> = ({
  form,
  label,
  formKey,
  localOptions,
  initialValue,
  required = false,
  toolTip,
  readonly,
}) => {
  const { t } = useTranslation('partner');
  const { setFieldsValue } = form;
  const [isAllSelected, setAllSelected] = useState(
    (initialValue?.[formKey] as string[])?.length === localOptions?.length
  );
  const [indeterminate, setIndeterminate] = useState(
    (initialValue?.[formKey] as string[])?.length &&
      (initialValue?.[formKey] as string[])?.length !== localOptions?.length
  );
  const [value, setValue] = useState(initialValue?.[formKey] as string[]);

  const formattedOptions = useMemo(
    () =>
      localOptions?.map(bizDict => ({
        label: bizDict.dictValueName,
        value: bizDict.enumItemName,
      })),
    [localOptions]
  );

  const colSpan = useMemo(() => {
    return Math.min((formattedOptions?.length ?? 0) * 8, 24);
  }, [formattedOptions?.length]);

  const onSelectAllChange = useCallback(() => {
    setIndeterminate(false);
    if (!isAllSelected) {
      setFieldsValue({
        [formKey]: formattedOptions?.map(option => option?.value),
      });
      setValue(formattedOptions?.map(option => option?.value) as string[]);
    } else {
      setFieldsValue({ [formKey]: [] });
      setValue([]);
    }
    setAllSelected(!isAllSelected);
  }, [formKey, formattedOptions, isAllSelected, setFieldsValue]);

  const onCheckBoxChange = useCallback(
    (checkedValue: CheckboxValueType[]) => {
      setValue(checkedValue as string[]);
      setFieldsValue({
        [formKey]: checkedValue,
      });
      if (checkedValue?.length) {
        setIndeterminate(checkedValue?.length !== formattedOptions?.length);
        setAllSelected(checkedValue?.length === formattedOptions?.length);
      } else {
        setIndeterminate(false);
      }
    },
    [formKey, setFieldsValue, formattedOptions?.length]
  );

  const formLabel = useMemo(
    () => (
      <span>
        {label ?? formKey}
        {toolTip}
      </span>
    ),
    [label, formKey, toolTip]
  );

  return (
    <Row>
      <Col span={colSpan}>
        <Form.Item
          label={formLabel}
          name={formKey}
          rules={[
            {
              required,
              message: t('channel.common.required', {
                label,
              }),
            },
          ]}
        >
          <Checkbox
            disabled={readonly}
            checked={isAllSelected}
            onChange={onSelectAllChange}
            indeterminate={indeterminate}
          >
            {t('Select All')}
          </Checkbox>
          <Checkbox.Group
            disabled={readonly}
            value={value}
            className={styles.checkBoxContainer}
            onChange={onCheckBoxChange}
            options={formattedOptions}
          />
        </Form.Item>
      </Col>
    </Row>
  );
};
