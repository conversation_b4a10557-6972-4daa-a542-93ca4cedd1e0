/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import type { FC } from 'react';
import { useCallback } from 'react';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Col, Drawer, Form, Input, Radio, Row, message } from 'antd';
import { useForm } from 'antd/es/form/Form';

import { useRequest } from 'ahooks';
import { uniqWith } from 'lodash-es';
import type { Moment } from 'moment';
import moment from 'moment';

import { DatePicker, Select } from '@zhongan/nagrand-ui';

import {
  ChannelDataSourceTypeEnum,
  ChannelService,
  ExtractPeriodTypeEnum,
  ReconciliationMethodEnum,
  RelationshipConfigTypeEnum,
  SettlementFrequency,
  YesOrNo,
} from 'genesis-web-service';
import type {
  BizDictItem,
  GoodsDetailDTO,
  ReconciliationConfigDTO,
  RelationshipConfigDTO,
  RelationshipDetailDTO,
} from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { RenderEnums } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { useTenantTimezone } from '@/hooks/useTenantConfig';
import { QuestionTooltip } from '@/pages/common-party/partner-setting-v2/components/RelationshipList';
import { BatchConfigForm } from '@/pages/common-party/partner-setting-v2/components/RelationshipList/Configuration/BatchConfigForm';
import { CheckboxGroup } from '@/pages/common-party/partner-setting-v2/components/RelationshipList/Configuration/CheckboxGroup';
import { MultipleSelect } from '@/pages/common-party/partner-setting-v2/components/RelationshipList/Configuration/MultipleSelect';
import { useRelationshipCodeInfo } from '@/pages/common-party/partner-setting-v2/hooks/useRelationshipCodeInfo';

import styles from './index.scss';

const ReconciliationSupportedFileType = 'CSV';

const PolicyLevelDefaultValue = 'POLICY';
// 当frequency为月，抽取方式是区间抽取时，用于存储区间起始日。数组长度4位固定，首位为1不变
const MonthlyModeDefaultStarts = [1, undefined, undefined, undefined];

interface ReconciliationDetailDrawerProps {
  goodList: GoodsDetailDTO[];
  insuranceCompanyCode: string;
  relationshipDetail: RelationshipDetailDTO;
  visible: boolean;
  onClose: (needRefresh: boolean) => void;
  reconciliationConfig?: ReconciliationConfigDTO;
  readonly: boolean;
  partnerCode: string;
}

interface ReconciliationConfigDTOForm
  extends Omit<ReconciliationConfigDTO, 'extractPeriodType' | 'containSubChannelFlag'> {
  extractPeriodType: boolean;
  containSubChannelFlag: boolean;
  effectiveDateRange: Moment[];
}

/**
 *
 * 对账详情抽屉
 * goodsList：可选的商品信息
 * insuranceCompanyCode：从父页面中拿来的信息，只在提交阶段用到，不参与到这个页面的逻辑
 * relationshipDetail：relationship的信息，主要用来取channel id和code
 * reconciliationConfig：对账的详细信息
 */

export const ReconciliationDetailDrawer: FC<ReconciliationDetailDrawerProps> = ({
  visible,
  onClose,
  reconciliationConfig,
  relationshipDetail,
  insuranceCompanyCode,
  goodList,
  readonly,
  partnerCode,
}) => {
  const { t } = useTranslation('partner');
  const {
    l10n: { dateFormat },
  } = useL10n();
  const [form] = useForm<ReconciliationConfigDTOForm>();
  const { validateFields, resetFields, setFieldsValue } = form;
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const tenantTimezone = useTenantTimezone();

  const [needRefresh, setNeedRefresh] = useState(false);
  const [agencyCode, saleChannelCode] = useRelationshipCodeInfo(relationshipDetail);

  // 用于标记是否为月抽 During区间模式。
  const [isMonthlyDuringMode, setIsMonthlyDuringMode] = useState(false);
  // 用于标记月抽 During区间模式时的区间数。
  const [monthlyDuringLength, setMonthlyDuringLength] = useState(1);
  // 用于标记月抽 During区间模式时，各个区间的起始日。
  const [monthlyDuringStarts, setMonthlyDuringStarts] = useState<(number | undefined)[]>(MonthlyModeDefaultStarts);

  const dataSourceTypeOptions = useMemo(() => {
    const dataSourceTypeList = [ChannelDataSourceTypeEnum?.PAYMENT, ChannelDataSourceTypeEnum?.COLLECTION_TRANSACTION];
    return enums?.channelDataSourceType?.filter(sourceType =>
      dataSourceTypeList.includes(sourceType?.enumItemName as ChannelDataSourceTypeEnum)
    );
  }, [enums?.channelDataSourceType]);

  const methodCategoryOptions = useMemo(() => {
    return enums?.reconciliationSettlementMethodCategory?.map(method => ({
      label: method?.dictValueName,
      value: method?.enumItemName,
      disabled: method?.enumItemName === ReconciliationMethodEnum.API,
    }));
  }, [enums?.reconciliationSettlementMethodCategory]);

  const goodsCodeOptions = useMemo(() => {
    return goodList?.map(good => ({
      label: good?.goodsCode,
      value: good?.goodsCode,
    })) as BizDictItem[];
  }, [goodList]);

  // 新建&更新ReconciliationConfig
  const { run: submitReconciliationConfig, loading } = useRequest(
    (params: RelationshipConfigDTO) =>
      reconciliationConfig?.configId
        ? ChannelService.updateReconciliationSettlementConfig(params)
        : ChannelService.addReconciliationSettlementConfig(params),
    {
      manual: true,
      onError: error => message.error(error?.message),
      onSuccess: () => {
        message.success(t('Success'));
        onClose(true);
      },
    }
  );

  const onBasicInfoSubmit = useCallback(() => {
    validateFields().then(values => {
      const { effectiveDateRange, extractPeriodType, containSubChannelFlag, durationStart0, ...rest } = values;
      const effectiveDate = dateFormat.formatTz(effectiveDateRange?.[0]);
      const expiryDate = dateFormat.formatTz(effectiveDateRange?.[1]);
      let frequencyValue2 = '';
      let extractDate = '';
      let extractWeekOrMonth = '';
      /**
       * 如果durationStart0存在，说明选取的是月抽的During模式，frequencyValue2和extractDate按以下要求拼接
       *
       * frequencyValue2来自于durationStart[0~3],最终保存为时间段，各段时间为闭区间。
       * 例：durationStart0=1, durationStart1=10, durationStart2=20
       * frequencyValue2 -> "1~9,10~19,20~31"
       * 拼接规则为"start0~start1-1,start1~start2-1,start2~start3-1,start3~31"
       * 任一start[key]不存在, 则上一个start直接拼接～31，结束。
       *
       * extractDate来自于extractDate[0~3], 以逗号拼接成字符串即可。
       * 例：extractDate -> '2,1,3'
       */
      if (durationStart0) {
        let cyclicFlag = true;
        let key = 0;
        while (cyclicFlag) {
          const currentDurationKey = `durationStart${key}` as keyof ReconciliationConfigDTO;
          const nextDurationKey = `durationStart${key + 1}` as keyof ReconciliationConfigDTO;
          const currentExtractKey = `extractDate${key}` as keyof ReconciliationConfigDTO;

          const currExtractionTypeKey = `extractWeekOrMonth${key}` as keyof ReconciliationConfigDTO;
          delete rest[currentDurationKey];
          delete rest[currentExtractKey];
          delete rest[currExtractionTypeKey];
          if (values[nextDurationKey]) {
            frequencyValue2 += `${values[currentDurationKey]}~${values[nextDurationKey] - 1},`;
            extractDate += `${values[currentExtractKey]},`;
            extractWeekOrMonth += `${values[currExtractionTypeKey]},`;
            key += 1;
          } else {
            frequencyValue2 += `${values[currentDurationKey]}~31`;
            extractDate += `${values[currentExtractKey]}`;
            extractWeekOrMonth += `${values[currExtractionTypeKey]}`;
            cyclicFlag = false;
          }
        }
      } else {
        // durationStart0不存在，说明是正常模式，直接赋值即可。
        frequencyValue2 = values.reconciliationOrSettlementTimePeriod;
        extractDate = values.extractDate;
        extractWeekOrMonth = values.extractWeekOrMonth;
      }
      const params: RelationshipConfigDTO = {
        // form value
        effectiveDate,
        expiryDate,
        extractPeriodType: extractPeriodType ? ExtractPeriodTypeEnum.NON_FIXED_TIME : ExtractPeriodTypeEnum.FIXED_TIME,
        containSubChannelFlag: containSubChannelFlag ? YesOrNo.YES : YesOrNo.NO,
        ...rest,
        reconciliationOrSettlementTimePeriod: frequencyValue2,
        extractDate,
        extractWeekOrMonth,

        // 表单里不存在，上个页面带来的字段
        configId: reconciliationConfig?.configId,
        channelRelationId: relationshipDetail?.channelRelationId,
        relationshipCode: relationshipDetail?.code,
        configType: RelationshipConfigTypeEnum.RECONCILIATION,
        agencyCode,
        saleChannelCode,
        insuranceCompanyCode,
        partnerCode,
      };
      submitReconciliationConfig(params);
    });
  }, [
    reconciliationConfig,
    agencyCode,
    saleChannelCode,
    insuranceCompanyCode,
    relationshipDetail,
    validateFields,
    submitReconciliationConfig,
    dateFormat,
  ]);

  useEffect(
    () => () => {
      setNeedRefresh(false);
    },
    [visible]
  );

  useEffect(() => {
    resetFields();
    // 如果是月抽区间模式，frequencyValue2示例"1~10,11~19,20~31", 正则匹配为['1~10,', '11~19,', '20~31']
    // 没匹配到返回null，说明不是月抽的区间抽取模式
    const durationList = reconciliationConfig?.reconciliationOrSettlementTimePeriod?.match(/(\d{1,2}(~\d{1,2}),?)+?/g);
    let durationDefaults: Record<string, number> = {};
    let extractDefaults: Record<string, string> = {};
    let extractTypeDefaults: Record<string, string> = {};
    // 设置是否为月抽的区间抽取模式
    setIsMonthlyDuringMode(!!durationList);
    // 设置月抽的区间抽取模式的区间数量
    setMonthlyDuringLength(durationList?.length ?? 1);
    if (durationList) {
      // 如果是月抽区间模式，需要设置表单durationStart[0~3]初始值
      durationDefaults = durationList.reduce(
        (preDurations, currentDuration, currentIndex) => ({
          ...preDurations,
          // 示例['1~10,', '11~19,', '20~31']，取前边的数字作为区间起始值。
          [`durationStart${currentIndex}`]: parseInt(currentDuration.replace(/~\d{1,2},?/, '')),
        }),
        {} as Record<string, number>
      );
      const { durationStart0, durationStart1, durationStart2, durationStart3 } = durationDefaults;
      // 设置区间起始值数组，用于既存数据区间显示，没值默认undefined
      setMonthlyDuringStarts([durationStart0, durationStart1, durationStart2, durationStart3]);
      // 如果是月抽区间模式，需要设置表单extractDate[0~3]初始值
      // 示例 "2,4,6"
      extractDefaults = reconciliationConfig?.extractDate?.split(',').reduce(
        (preExtracts, currentExtract, currentIndex) => ({
          ...preExtracts,
          [`extractDate${currentIndex}`]: currentExtract,
        }),
        {} as Record<string, string>
      );

      extractTypeDefaults = reconciliationConfig?.extractWeekOrMonth?.split(',').reduce(
        (preExtracts, currentExtract, currentIndex) => ({
          ...preExtracts,
          [`extractWeekOrMonth${currentIndex}`]: currentExtract,
        }),
        {} as Record<string, string>
      );
    } else {
      // 如果不是月抽的区间抽取模式，设置区间起始数组为默认值
      setMonthlyDuringStarts(MonthlyModeDefaultStarts);
    }
    setFieldsValue({
      ...reconciliationConfig,
      ...durationDefaults,
      ...extractDefaults,
      ...extractTypeDefaults,
      // 设置新增config时的初始值，为每天抽取的日抽。
      frequency: reconciliationConfig?.frequency ?? SettlementFrequency.Day,
      frequencyValue: reconciliationConfig?.frequencyValue ?? '1',
      // 如果是月抽During Mode，则弃用reconciliationOrSettlementTimePeriod字段，最后submit时，会手动拼成reconciliationOrSettlementTimePeriod
      reconciliationOrSettlementTimePeriod: durationList
        ? undefined
        : reconciliationConfig?.reconciliationOrSettlementTimePeriod,
      extractWeekOrMonth: durationList ? undefined : reconciliationConfig?.extractWeekOrMonth,
      // 设置新增config时的初始值，初始值为租户设置的系统默认时区。
      timezone: reconciliationConfig?.timezone ?? tenantTimezone,
      effectiveDateRange: reconciliationConfig?.effectiveDate
        ? [moment(reconciliationConfig?.effectiveDate), moment(reconciliationConfig?.expiryDate)]
        : null,
      policyLevel: reconciliationConfig?.policyLevel ?? PolicyLevelDefaultValue,
      methodCategory: reconciliationConfig?.methodCategory ?? ReconciliationMethodEnum.FILE,
      extractPeriodType: reconciliationConfig?.extractPeriodType === ExtractPeriodTypeEnum.NON_FIXED_TIME,
      containSubChannelFlag: reconciliationConfig?.containSubChannelFlag === YesOrNo.YES,
    });
  }, [reconciliationConfig]);

  return (
    <Drawer
      rootClassName={styles.reconciliationDetailDrawer}
      open={visible}
      title={t('Reconciliation Configuration')}
      onClose={() => onClose(needRefresh)}
      width={1000}
      destroyOnClose
      closable={false}
      maskClosable={false}
    >
      <Form form={form} layout="vertical">
        <h3>{t('Basic Info')}</h3>
        <Form.Item
          name="code"
          label={t('Reconciliation Code')}
          rules={[
            {
              required: true,
              message: t('channel.common.required', {
                label: t('Reconciliation Code'),
              }),
            },
          ]}
        >
          <Input
            disabled={!!reconciliationConfig?.configId || readonly}
            className={styles.standardTdWidth}
            placeholder={t('Please input')}
          />
        </Form.Item>
        <Form.Item
          name="effectiveDateRange"
          label={t('Effective Date')}
          rules={[
            {
              required: true,
              message: t('channel.common.required', {
                label: t('Effective Date'),
              }),
            },
          ]}
        >
          <DatePicker.RangePicker
            disabled={readonly}
            style={{ marginTop: 8, width: 320 }}
            format={dateFormat.dateFormat}
            getPopupContainer={triggerElement => triggerElement?.parentElement}
          />
        </Form.Item>
        <h3>{t('Reconciliation Detail')}</h3>
        <Row>
          <Col span={6}>
            <Form.Item
              name="isIncrement"
              label={t('Re-generation Option')}
              rules={[
                {
                  required: true,
                  message: t('channel.common.required', {
                    label: t('Re-generation Option'),
                  }),
                },
              ]}
            >
              <Select
                showSearch
                allowClear
                disabled={readonly}
                placeholder={t('Please select')}
                getPopupContainer={triggerElement => triggerElement?.parentElement}
              >
                <Select.Option key={YesOrNo.YES} value={YesOrNo.YES}>
                  {t('Partial Re-generation')}
                </Select.Option>
                <Select.Option key={YesOrNo.NO} value={YesOrNo.NO}>
                  {t('Full Re-generation')}
                </Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={6} offset={2}>
            <MultipleSelect
              label={t('Payment/Collection')}
              formKey="dataSourceTypeList"
              initialValue={reconciliationConfig}
              form={form}
              required
              // 需要对选项做一下过滤
              options={dataSourceTypeOptions}
              disabled={readonly}
            />
          </Col>
        </Row>
        <Row>
          <Col span={6}>
            <MultipleSelect
              label={t('Payment/Collection Method')}
              formKey="payMethodList"
              initialValue={reconciliationConfig}
              form={form}
              required
              options={uniqWith(
                [...(enums?.collectionPaymentMethod ?? []), ...(enums?.paymentPaymentMethod ?? [])],
                (method1, method2) => method1?.value === method2?.value
              )}
              disabled={readonly}
            />
          </Col>
          <Col span={6} offset={2}>
            <MultipleSelect
              label={t('Reconciliation Goods Code')}
              formKey="goodsCodeList"
              initialValue={reconciliationConfig}
              form={form}
              options={goodsCodeOptions}
              disabled={readonly}
            />
          </Col>
        </Row>
        <Row>
          <Col span={6}>
            <Form.Item
              name="policyLevel"
              label={t('Reconciliation Policy Dimension')}
              rules={[
                {
                  required: true,
                  message: t('channel.common.required', {
                    label: t('Reconciliation Policy Dimension'),
                  }),
                },
              ]}
            >
              <Select
                className={styles.standardTdWidth}
                showSearch
                allowClear
                disabled={readonly}
                placeholder={t('Please select')}
                getPopupContainer={triggerElement => triggerElement?.parentElement}
              >
                {enums?.policyLevel?.map(policy => (
                  <Select.Option key={policy.dictValueName} value={policy.enumItemName}>
                    {policy.dictValueName}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6} offset={2}>
            <MultipleSelect
              label={t('Currency')}
              formKey="currencyList"
              initialValue={reconciliationConfig}
              form={form}
              required
              options={enums?.currencys}
              disabled={readonly}
            />
          </Col>
        </Row>
        <CheckboxGroup
          form={form}
          initialValue={reconciliationConfig}
          readonly={readonly}
          required
          label={t('Transaction Business Type')}
          formKey="transactionTypeList"
          localOptions={enums?.reconciliationSettlementTransType}
        />
        <CheckboxGroup
          form={form}
          initialValue={reconciliationConfig}
          readonly={readonly}
          required
          label={t('Reconciliation Fields')}
          toolTip={
            <QuestionTooltip
              tooltip={t(
                'To config the reconciliation fields, only when all selected fields were matched, the reconciled record is marked as Matched.'
              )}
            />
          }
          formKey="matchFieldList"
          localOptions={enums?.reconciliationField
            .filter(item => item.itemExtend2)
            .sort((a, b) => Number(a.itemExtend2) - Number(b.itemExtend2))}
        />
        <CheckboxGroup
          form={form}
          initialValue={reconciliationConfig}
          readonly={readonly}
          toolTip={
            <QuestionTooltip
              tooltip={t('To config the columns that displayed in downloaded reconciliation result file.')}
            />
          }
          label={t('Reconciliation Result Display')}
          formKey="searchDetailDisplayFieldList"
          localOptions={enums?.reconciliationField
            .filter(item => item.itemExtend3)
            .sort((a, b) => Number(a.itemExtend3) - Number(b.itemExtend3))}
        />
        <h3>{t('Reconciliation Method & File')}</h3>
        <Form.Item label={t('Reconciliation Method')} name="methodCategory" style={{ marginBottom: 0 }}>
          <Radio.Group className={styles.verticalRadio} options={methodCategoryOptions} disabled={readonly} />
        </Form.Item>
        <Row>
          <Col span={8}>
            <Form.Item
              name="fileFormat"
              label={t('Reconciliation File Format')}
              rules={[
                {
                  required: true,
                  message: t('channel.common.required', {
                    label: t('Reconciliation File Format'),
                  }),
                },
              ]}
            >
              <Select
                className={styles.standardTdWidth}
                showSearch
                allowClear
                disabled={readonly}
                placeholder={t('Please select')}
                getPopupContainer={triggerElement => triggerElement?.parentElement}
              >
                <Select.Option value={ReconciliationSupportedFileType}>
                  <RenderEnums
                    keyName={ReconciliationSupportedFileType}
                    enums={enums?.reconciliationSettlementFileFormat}
                  />
                </Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="filePath" label={t('File Path')}>
              <Input disabled={readonly} className={styles.standardTdWidth} placeholder={t('Please input')} />
            </Form.Item>
          </Col>
        </Row>
        <CheckboxGroup
          label={t('Unique Combination Fields')}
          formKey="uniqueCombinationFieldList"
          initialValue={reconciliationConfig}
          readonly={readonly}
          form={form}
          toolTip={
            <QuestionTooltip
              tooltip={t(
                'To config the unique key to directing one same business transaction within provided reconciliation file and local data source.'
              )}
            />
          }
          localOptions={enums?.reconciliationUniqueCombinationField?.sort(
            (field1, field2) => Number(field1?.itemExtend2 ?? 0) - Number(field2?.itemExtend2 ?? 0)
          )}
        />
        <h3>{t('Reconciliation Recurrence')}</h3>
        <BatchConfigForm
          form={form}
          batchConfig={reconciliationConfig}
          type={RelationshipConfigTypeEnum.RECONCILIATION}
          readonly={readonly}
          isMonthlyDuringMode={isMonthlyDuringMode}
          defaultDuringLength={monthlyDuringLength}
          defaultDuringStarts={monthlyDuringStarts}
        />
      </Form>
      <div className={styles.buttonFooter}>
        {!readonly && (
          <Button type="primary" size="large" style={{ marginRight: 32 }} loading={loading} onClick={onBasicInfoSubmit}>
            {t('Save')}
          </Button>
        )}
        <Button size="large" style={{ marginRight: 16 }} onClick={() => onClose(needRefresh)}>
          {t('Cancel')}
        </Button>
      </div>
    </Drawer>
  );
};
