/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

import type { FC } from 'react';
import { useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { PlusOutlined } from '@ant-design/icons';
import { Button, message, Divider } from 'antd';
import {
  ChannelService,
  RelationshipConfigTypeEnum,
  TenantOrgType,
} from 'genesis-web-service';
import type {
  ReconciliationConfigDTO,
  ChannelDataType,
  RelationshipDetailDTO,
  SettlementConfigDTO,
} from 'genesis-web-service';
import { useRelationshipCodeInfo } from '@/pages/common-party/partner-setting-v2/hooks/useRelationshipCodeInfo';
import { useRequest } from 'ahooks';

import {
  useReconciliationColumns,
  useSettlementColumns,
} from '@/pages/common-party/partner-setting-v2/components/RelationshipList/Configuration/columns';
import { ReconciliationDetailDrawer } from '@/pages/common-party/partner-setting-v2/components/RelationshipList/Configuration/ReconciliationDetailDrawer';
import { SettlementDetailDrawer } from '@/pages/common-party/partner-setting-v2/components/RelationshipList/Configuration/SettlementDetailDrawer';
import styles from './index.scss';

import { TableTotal } from 'genesis-web-component/lib/components/TableTotal';
import { Table } from '@zhongan/nagrand-ui';

message.config({
  maxCount: 1,
});

interface ConfigurationProps {
  basicInfo: RelationshipDetailDTO;
  channelRelationId: number;
  tenantInfo: ChannelDataType;
  readonly: boolean;
  partnerCode: string;
  sourceChannelType: TenantOrgType;
}

/**
 * basicInfo：父组件传过来的relationship详情
 * channelRelationId：basicInfo里的channelRelationId
 * tenantInfo：主公司信息
 *
 * 这个页面包含了对账结算的table和详情抽屉
 */

export const Configuration: FC<ConfigurationProps> = ({
  basicInfo,
  channelRelationId,
  tenantInfo,
  readonly,
  partnerCode,
  sourceChannelType,
}) => {
  const { t } = useTranslation('partner');
  const [agencyCode, saleChannelCode] = useRelationshipCodeInfo(basicInfo);

  const [isReconciliationVisible, setReconciliationVisible] = useState(false);
  const [currentReconciliation, setCurrentReconciliation] =
    useState<ReconciliationConfigDTO>();
  const [hasReconciliationEditAuth, setReconciliationEditAuth] =
    useState(false);

  const [isSettlementVisible, setSettlementVisible] = useState(false);
  const [currentSettlement, setCurrentSettlement] =
    useState<SettlementConfigDTO>();
  const [hasSettlementEditAuth, setSettlementEditAuth] = useState(false);

  // ----- reconciliation props start -----
  // 获取ReconciliationConfig
  const {
    data: reconciliationData,
    loading: isReconciliationLoading,
    refresh: refreshReconciliationList,
    run: getReconciliationList,
  } = useRequest(
    () =>
      ChannelService.getReconciliationSettlementList({
        relationId: channelRelationId,
        agencyCode,
        channelCode: saleChannelCode,
        configType: RelationshipConfigTypeEnum.RECONCILIATION,
      }),
    {
      onError: error => message.error(error?.message),
      manual: true,
    },
  );

  // 获取goodslist
  const { data: goodList, run: getGoodList } = useRequest(
    () =>
      ChannelService.getGoodsList(
        basicInfo?.sourceChannel?.code,
        tenantInfo?.code,
      ),
    {
      onError: error => message.error(error?.message),
      manual: true,
    },
  );

  // 删除ReconciliationConfig
  const { run: deleteReconciliationSettlementConfig } = useRequest(
    (configId: number) =>
      ChannelService.deleteReconciliationSettlementConfig(configId),
    {
      manual: true,
      onError: error => message.error(error?.message),
      onSuccess: () => {
        message.success(t('Delete successfully'));
        refreshReconciliationList();
        setCurrentReconciliation(null);
      },
    },
  );

  const openReconciliationDrawer = useCallback(
    (record?: ReconciliationConfigDTO) => {
      setCurrentReconciliation(record);
      setReconciliationVisible(true);
    },
    [],
  );

  const onReconciliationDrawerClose = useCallback(
    (needRefresh: boolean) => {
      setCurrentReconciliation(null);
      setReconciliationVisible(false);
      if (needRefresh) {
        refreshReconciliationList();
      }
    },
    [refreshReconciliationList],
  );

  const reconciliationColumns = useReconciliationColumns(
    openReconciliationDrawer,
    deleteReconciliationSettlementConfig,
    hasReconciliationEditAuth && !readonly,
  );
  // ----- reconciliation props end -----

  // ----- settlement props start -----
  const {
    data: settlementData,
    loading: isSettlementLoading,
    refresh: refreshSettlementList,
    run: getSettlementList,
  } = useRequest(
    () =>
      ChannelService.getReconciliationSettlementList({
        relationId: channelRelationId,
        agencyCode,
        channelCode: saleChannelCode,
        configType: RelationshipConfigTypeEnum.SETTLEMENT,
      }),
    {
      onError: error => message.error(error?.message),
      manual: true,
    },
  );

  const openSettlementDrawer = useCallback((record?: SettlementConfigDTO) => {
    setCurrentSettlement(record);
    setSettlementVisible(true);
  }, []);

  const onSettlementDrawerClose = useCallback(
    (needRefresh: boolean) => {
      setCurrentSettlement(null);
      setSettlementVisible(false);
      if (needRefresh) {
        refreshSettlementList();
      }
    },
    [refreshSettlementList],
  );

  const settlementColumns = useSettlementColumns(
    openSettlementDrawer,
    deleteReconciliationSettlementConfig,
    hasSettlementEditAuth && !readonly,
  );

  // ----- settlement props end -----

  useEffect(() => {
    if (channelRelationId) {
      getReconciliationList();
      getSettlementList();
    }
  }, [
    channelRelationId,
    getGoodList,
    getReconciliationList,
    getSettlementList,
    saleChannelCode,
    agencyCode,
  ]);

  useEffect(() => {
    if (basicInfo?.sourceChannel?.code && tenantInfo?.code) {
      getGoodList();
    }
  }, [basicInfo?.sourceChannel?.code, getGoodList, tenantInfo?.code]);

  useEffect(() => {
    // 配置了reconciliationConfig的才可以编辑reconciliation
    setReconciliationEditAuth(!!basicInfo?.reconciliationConfig);
  }, [basicInfo?.reconciliationConfig]);

  useEffect(() => {
    // settlement同reconciliationConfig
    setSettlementEditAuth(!!basicInfo?.settlementConfig);
  }, [basicInfo?.settlementConfig]);

  return (
    <>
      {/* https://jira.zaouter.com/browse/GIS-93703 隐藏reconciliation */}
      {sourceChannelType !== TenantOrgType.AGENCY && (
        <>
          <h3 style={{ paddingTop: 24 }}>{t('Reconciliation')}</h3>
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            disabled={!hasReconciliationEditAuth || readonly}
            block
            onClick={() => openReconciliationDrawer()}
          >
            {t('add')}
          </Button>
          <Table
            columns={reconciliationColumns}
            loading={isReconciliationLoading}
            dataSource={reconciliationData as ReconciliationConfigDTO[]}
            scroll={{ x: 'max-content' }}
            style={{ paddingTop: 12 }}
            rowKey="configId"
            className={styles.reconciliationConfigTable}
          />
          <TableTotal total={reconciliationData?.length ?? 0} />
          <Divider dashed />
        </>
      )}
      <h3 style={{ paddingTop: 24 }}>{t('Settlement')}</h3>
      <Button
        icon={<PlusOutlined />}
        type="dashed"
        disabled={!hasSettlementEditAuth || readonly}
        block
        onClick={() => openSettlementDrawer()}
      >
        {t('add')}
      </Button>
      <Table
        columns={settlementColumns}
        loading={isSettlementLoading}
        dataSource={settlementData as SettlementConfigDTO[]}
        scroll={{ x: 'max-content' }}
        style={{ paddingTop: 12 }}
        rowKey="configId"
        className={styles.reconciliationConfigTable}
      />
      <TableTotal total={settlementData?.length ?? 0} />
      <Divider dashed />
      <ReconciliationDetailDrawer
        goodList={goodList}
        reconciliationConfig={currentReconciliation}
        relationshipDetail={basicInfo}
        visible={isReconciliationVisible}
        insuranceCompanyCode={tenantInfo?.code}
        onClose={onReconciliationDrawerClose}
        readonly={readonly}
        partnerCode={partnerCode}
      />
      <SettlementDetailDrawer
        sourceChannelType={sourceChannelType}
        settlementConfig={currentSettlement}
        reconciliationList={reconciliationData}
        relationshipDetail={basicInfo}
        visible={isSettlementVisible}
        insuranceCompanyCode={tenantInfo?.code}
        onClose={onSettlementDrawerClose}
        readonly={readonly}
        partnerCode={partnerCode}
      />
    </>
  );
};
