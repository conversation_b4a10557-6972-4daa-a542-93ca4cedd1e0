import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Select } from '@zhongan/nagrand-ui';

import style from './index.scss';

interface SelectProps {
  readonly: boolean;
  disabled?: boolean;
  isDuring?: boolean;
  onChange?: (value: boolean) => void;
}

enum PeriodType {
  StartFrom = 'POINT',
  During = 'DURATION',
}

/**
 * BatchConfigForm中使用。标记StartFrom/During模式的下拉选。
 *
 * readonly：隔月抽和周抽只有Start From模式，为true说明是这两个模式
 * disabled：标记是否为只读模式
 * isDuring：标记是否为During模式
 * onChange：变更模式
 */
export const PeriodTypeSelect = (props: SelectProps) => {
  const { readonly, isDuring, disabled, onChange } = props;
  const { t } = useTranslation('partner');
  const [innerValue, setInnerValue] = useState(PeriodType.StartFrom);

  useEffect(() => {
    setInnerValue(isDuring ? PeriodType.During : PeriodType.StartFrom);
  }, [isDuring]);

  const selectOptions = [
    { value: PeriodType.StartFrom, label: t('Start from') },
    { value: PeriodType.During, label: t('During from') },
  ];

  if (readonly) {
    return <div className={!disabled ? style.componentPeriodTypeWording : null}>{t('Start from')}</div>;
  }

  return (
    <Select
      options={selectOptions}
      onChange={value => onChange?.(value === PeriodType.During)}
      className={!disabled ? style.componentPeriodTypeSelect : null}
      value={innerValue}
      disabled={disabled}
    />
  );
};
