@import '../../../../../variables.scss';

.section-title {
  font-weight: 700;
  margin-bottom: 1em;
}
.question-circle {
  color: var(--text-disabled-color);
  margin-left: 6px;
  margin-right: 6px;
  line-height: 22px;
}
.relationship-row-container {
  font-size: $font-size-root;
  color: var(--text-disabled-color);
  :global {
    .#{$antd-prefix}-col-1 {
      display: flex;
      flex-direction: row-reverse;
    }
    .#{$antd-prefix}-btn-link,
    .#{$antd-prefix}-btn-link:active,
    .#{$antd-prefix}-btn-link:focus {
      min-width: 0;
      padding: 0;
      border: none;
      background-color: transparent;
      border-color: transparent;
      -webkit-box-shadow: none;
      box-shadow: none;
    }
    .#{$antd-prefix}-badge-status-text {
      font-size: 16px;
      color: var(--text-color);
    }
    .#{$antd-prefix}-dropdown-menu-item {
      padding: 0;
    }
    p {
      font-weight: 500;
      font-size: 16px;
      margin-bottom: 6px;
      color: var(--text-color);
      a {
        color: var(--primary-color);
      }
    }
  }
  .relationshipRow {
    margin-top: 16px;
    border: 1px var(--border-default) solid;
    border-radius: var(--border-radius-base);
    padding: 16px 24px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
}
.relationship-row-container:hover {
  border-color: var(--primary-color);
  transition: all 0.5s;
}
.relationship-menu {
  :global {
    .#{$antd-prefix}-btn-link,
    .#{$antd-prefix}-btn-link:active,
    .#{$antd-prefix}-btn-link:focus {
      border: none;
    }
    .#{$antd-prefix}-dropdown-menu-item {
      padding: 0;
    }
  }
}
.relationship-detail-drawer {
  :global {
    .#{$antd-prefix}-drawer-title {
      font-size: 20px;
      font-weight: 700;
    }
    .#{$antd-prefix}-drawer-body {
      margin-bottom: 100px;
    }
    .#{$antd-prefix}-tabs-nav {
      width: 100%;
      .#{$antd-prefix}-tabs-tab {
        padding: 14px 16px;
        margin-right: 32px;
        width: 300px;
      }
      .#{$antd-prefix}-tabs-tab-active {
        p > span {
          background-color: var(--primary-color);
          border: 1px solid var(--primary-color);
          color: var(--white);
          transition: 0.3s;
        }
      }
      .#{$antd-prefix}-tabs-ink-bar {
        background-color: var(--primary-color);
      }
      .#{$antd-prefix}-tabs-ink-bar.#{$antd-prefix}-tabs-ink-bar-animated {
        height: 1px;
      }
    }
    .#{$antd-prefix}-row .#{$antd-prefix}-form-item {
      margin-bottom: 14px;
    }
  }
  .relation-header-section {
    height: 100px;
    background-color: var(--border-light);
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 24px;
    margin-bottom: 10px;
    > div {
      width: 40%;
      > p {
        margin-top: 6px;
        margin-bottom: 0;
      }
    }
  }
  .tab-number {
    display: inline-block;
    font-size: 12px;
    height: 26px;
    width: 26px;
    margin-right: 8px;
    border: 1px solid var(--border-default);
    border-radius: 50%;
    text-align: center;
    line-height: 24px;
    color: var(--text-color);
  }
  .timezone-picker {
    margin-top: 8px;
    padding-left: 24px;
    margin-bottom: 14px;
    :global {
      .#{$antd-prefix}-legacy-form-explain {
        color: var(--error-color);
      }
    }
  }
  .button-footer {
    z-index: 2;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background-color: var(--white);
    border-top: 1px solid var(--border-default);
  }
}

.standard-td-width {
  width: 240px !important;
}
