/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import type { FC } from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Col, Drawer, Form, Input, Radio, Row, Tooltip, message } from 'antd';
import { useForm } from 'antd/es/form/Form';

import { useRequest } from 'ahooks';
import { uniqWith } from 'lodash-es';
import type { Moment } from 'moment';
import moment from 'moment';

import { DatePicker, Select } from '@zhongan/nagrand-ui';

import {
  ChannelDataSourceTypeEnum,
  ChannelService,
  ExtractPeriodTypeEnum,
  ReconciliationMethodEnum,
  RelationshipConfigTypeEnum,
  SettlementFrequency,
  SettlementTimeTypeEnum,
  TenantOrgType,
  YesOrNo,
} from 'genesis-web-service';
import type {
  BizDictItem,
  RelationshipConfigDTO,
  RelationshipDetailDTO,
  SettlementConfigDTO,
} from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { RenderEnums } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { useTenantTimezone } from '@/hooks/useTenantConfig';
import { BatchConfigForm } from '@/pages/common-party/partner-setting-v2/components/RelationshipList/Configuration/BatchConfigForm';
import { CheckboxGroup } from '@/pages/common-party/partner-setting-v2/components/RelationshipList/Configuration/CheckboxGroup';
import { MultipleSelect } from '@/pages/common-party/partner-setting-v2/components/RelationshipList/Configuration/MultipleSelect';
import { useRelationshipCodeInfo } from '@/pages/common-party/partner-setting-v2/hooks/useRelationshipCodeInfo';

import styles from './index.scss';

const SettlementSupportedFileType = 'PDF';
// 当frequency为月，抽取方式是区间抽取时，用于存储区间起始日。数组长度4位固定，首位为1不变
const MonthlyModeDefaultStarts = [1, undefined, undefined, undefined];
const SettlementCommissionOnlyScope = 'COMMISSION_ONLY';
const SettlementGroupBySalesPlatform = 'SALES_PLATFORM';

interface SettlementDetailFormProps {
  reconciliationList: RelationshipConfigDTO[];
  insuranceCompanyCode: string;
  relationshipDetail: RelationshipDetailDTO;
  visible: boolean;
  onClose: (needRefresh: boolean) => void;
  settlementConfig?: SettlementConfigDTO;
  readonly: boolean;
  partnerCode: string;
  sourceChannelType: TenantOrgType;
}

interface SettlementConfigDTOForm
  extends Omit<SettlementConfigDTO, 'dataSourceTypeList' | 'extractPeriodType' | 'containSubChannelFlag'> {
  dataSourceTypeList: string;
  extractPeriodType: boolean;
  containSubChannelFlag: boolean;
  effectiveDateRange: Moment[];
}

/**
 *
 * 结算详情抽屉
 * reconciliationList：可关联的对账信息
 * insuranceCompanyCode：从父页面中拿来的信息，只在提交阶段用到，不参与到这个页面的逻辑
 * relationshipDetail：relationship的信息，主要用来取channel id和code
 * settlementConfig：结算的详细信息
 */

export const SettlementDetailDrawer: FC<SettlementDetailFormProps> = ({
  reconciliationList,
  visible,
  onClose,
  settlementConfig,
  relationshipDetail,
  insuranceCompanyCode,
  readonly,
  partnerCode,
  sourceChannelType,
}) => {
  const { t } = useTranslation('partner');
  const {
    l10n: { dateFormat },
  } = useL10n();
  const [form] = useForm<SettlementConfigDTOForm>();
  const { validateFields, resetFields, setFieldsValue } = form;
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const settlementSourcesOptions = [
    ChannelDataSourceTypeEnum?.RECONCILIATION,
    ChannelDataSourceTypeEnum?.SOURCE_DATA_DETAIL,
  ];
  const [needRefresh, setNeedRefresh] = useState(false);
  const [isReconciliationCodeSelectDisabled, setReconciliationCodeSelectDisabled] = useState(false);
  const [dataSourceType, setDataSourceType] = useState(settlementConfig?.dataSourceTypeList?.[0] || '');
  const [agencyCode, saleChannelCode] = useRelationshipCodeInfo(relationshipDetail);
  const tenantTimezone = useTenantTimezone();
  const settlementScopeTypeSelected = Form.useWatch('settlementScopeType', form);

  // 用于标记是否为月抽 During区间模式。
  const [isMonthlyDuringMode, setIsMonthlyDuringMode] = useState(false);
  // 用于标记月抽 During区间模式时的区间数。
  const [monthlyDuringLength, setMonthlyDuringLength] = useState(1);
  // 用于标记月抽 During区间模式时，各个区间的起始日。
  const [monthlyDuringStarts, setMonthlyDuringStarts] = useState<(number | undefined)[]>(MonthlyModeDefaultStarts);
  const isAgencySouceChannel = useMemo(() => sourceChannelType === TenantOrgType.AGENCY, [sourceChannelType]);

  // 新建&更新SettlementConfig
  const { run: submitSettlementConfig, loading } = useRequest(
    (params: SettlementConfigDTO) =>
      settlementConfig?.configId
        ? ChannelService.updateReconciliationSettlementConfig(params)
        : ChannelService.addReconciliationSettlementConfig(params),
    {
      manual: true,
      onError: error => message.error(error?.message),
      onSuccess: () => {
        onClose(true);
        message.success(t('Success'));
      },
    }
  );

  const methodCategoryOptions = useMemo(() => {
    return enums?.reconciliationSettlementMethodCategory?.map(method => ({
      label: method?.dictValueName,
      value: method?.enumItemName,
      disabled: method?.enumItemName === ReconciliationMethodEnum.API,
    }));
  }, [enums?.reconciliationSettlementMethodCategory]);

  const reconciliationOptions = useMemo(() => {
    return reconciliationList?.map(reconciliation => ({
      label: reconciliation?.code,
      value: reconciliation?.code,
    })) as BizDictItem[];
  }, [reconciliationList]);

  const onSettlementConfigSubmit = useCallback(() => {
    validateFields().then(values => {
      const {
        effectiveDateRange,
        dataSourceTypeList,
        settlementScopeType,
        durationStart0,
        extractPeriodType,
        containSubChannelFlag,
        ...rest
      } = values;
      const effectiveDate = dateFormat.formatTz(effectiveDateRange?.[0]);
      const expiryDate = dateFormat.formatTz(effectiveDateRange?.[1]);

      let frequencyValue2 = '';
      let extractDate = '';
      let extractWeekOrMonth = '';
      /**
       * 如果durationStart0存在，说明选取的是月抽的During模式，frequencyValue2和extractDate按以下要求拼接
       *
       * frequencyValue2来自于durationStart[0~3],最终保存为时间段，各段时间为闭区间。
       * 例：durationStart0=1, durationStart1=10, durationStart2=20
       * frequencyValue2 -> "1~9,10~19,20~31"
       * 拼接规则为"start0~start1-1,start1~start2-1,start2~start3-1,start3~31"
       * 任一start[key]不存在, 则上一个start直接拼接～31，结束。
       *
       * extractDate来自于extractDate[0~3], 以逗号拼接成字符串即可。
       * 例：extractDate -> '2,1,3'
       */
      if (durationStart0) {
        let cyclicFlag = true;
        let key = 0;
        while (cyclicFlag) {
          const currentDurationKey = `durationStart${key}` as keyof SettlementConfigDTO;
          const nextDurationKey = `durationStart${key + 1}` as keyof SettlementConfigDTO;
          const currentExtractKey = `extractDate${key}` as keyof SettlementConfigDTO;

          const currExtractionTypeKey = `extractWeekOrMonth${key}` as keyof ReconciliationConfigDTO;
          delete rest[currentDurationKey];
          delete rest[currentExtractKey];
          if (values[nextDurationKey]) {
            frequencyValue2 += `${values[currentDurationKey]}~${values[nextDurationKey] - 1},`;
            extractDate += `${values[currentExtractKey]},`;
            key += 1;
            extractWeekOrMonth += `${values[currExtractionTypeKey]},`;
          } else {
            frequencyValue2 += `${values[currentDurationKey]}~31`;
            extractDate += `${values[currentExtractKey]}`;
            extractWeekOrMonth += `${values[currExtractionTypeKey]}`;
            cyclicFlag = false;
          }
        }
      } else {
        // durationStart0不存在，说明是正常模式，直接赋值即可。
        frequencyValue2 = values.reconciliationOrSettlementTimePeriod;
        extractDate = values.extractDate;
        extractWeekOrMonth = values.extractWeekOrMonth;
      }

      const params: SettlementConfigDTO = {
        // form value
        effectiveDate,
        expiryDate,
        extractPeriodType: extractPeriodType ? ExtractPeriodTypeEnum.NON_FIXED_TIME : ExtractPeriodTypeEnum.FIXED_TIME,
        containSubChannelFlag: containSubChannelFlag ? YesOrNo.YES : YesOrNo.NO,
        // AGENCY type下隐藏选项 赋默认值
        dataSourceTypeList: isAgencySouceChannel
          ? [ChannelDataSourceTypeEnum.SOURCE_DATA_DETAIL]
          : [dataSourceTypeList],
        settlementScopeType: isAgencySouceChannel ? SettlementCommissionOnlyScope : settlementScopeType,
        ...rest,
        reconciliationOrSettlementTimePeriod: frequencyValue2,
        extractDate,
        extractWeekOrMonth,

        // 表单里不存在，上个页面带来的字段
        configId: settlementConfig?.configId,
        channelRelationId: relationshipDetail?.channelRelationId,
        relationshipCode: relationshipDetail?.code,
        configType: RelationshipConfigTypeEnum.SETTLEMENT,
        agencyCode,
        saleChannelCode,
        insuranceCompanyCode,
        partnerCode,
      };
      submitSettlementConfig(params);
    });
  }, [
    settlementConfig,
    agencyCode,
    saleChannelCode,
    insuranceCompanyCode,
    relationshipDetail,
    validateFields,
    dateFormat,
    submitSettlementConfig,
  ]);

  const onSettlementSourcesChange = useCallback(
    (value: string) => {
      setReconciliationCodeSelectDisabled(value !== ChannelDataSourceTypeEnum.RECONCILIATION);
      setDataSourceType(value);
      if (value === ChannelDataSourceTypeEnum.SOURCE_DATA_DETAIL) {
        form.setFieldValue('settlementTimeType', SettlementTimeTypeEnum?.BusinessOccurrenceTime);
      }
      if (value === ChannelDataSourceTypeEnum.RECONCILIATION) {
        form.setFieldValue('settlementTimeType', SettlementTimeTypeEnum?.ReconciliationCompletionTime);
      }
      setFieldsValue({ reconCodeList: [] });
    },
    [setFieldsValue]
  );

  useEffect(
    () => () => {
      setNeedRefresh(false);
    },
    [visible]
  );

  useEffect(() => {
    resetFields();
    // 如果是月抽区间模式，frequencyValue2示例"1~10,11~19,20~31", 正则匹配为['1~10,', '11~19,', '20~31']
    // 没匹配到返回null，说明不是月抽的区间抽取模式
    const durationList = settlementConfig?.reconciliationOrSettlementTimePeriod?.match(/(\d{1,2}(~\d{1,2}),?)+?/g);
    let durationDefaults: Record<string, number> = {};
    let extractDefaults: Record<string, string> = {};
    let extractTypeDefaults: Record<string, string> = {};
    // 设置是否为月抽的区间抽取模式
    setIsMonthlyDuringMode(!!durationList);
    // 设置月抽的区间抽取模式的区间数量
    setMonthlyDuringLength(durationList?.length ?? 1);
    if (durationList) {
      // 如果是月抽区间模式，需要设置表单durationStart[0~3]初始值
      durationDefaults = durationList.reduce(
        (preDurations, currentDuration, currentIndex) => ({
          ...preDurations,
          // 示例['1~10,', '11~19,', '20~31']，取前边的数字作为区间起始值。
          [`durationStart${currentIndex}`]: parseInt(currentDuration.replace(/~\d{1,2},?/, '')),
        }),
        {} as Record<string, number>
      );
      const { durationStart0, durationStart1, durationStart2, durationStart3 } = durationDefaults;
      // 设置区间起始值数组，用于既存数据区间显示，没值默认undefined
      setMonthlyDuringStarts([durationStart0, durationStart1, durationStart2, durationStart3]);
      // 如果是月抽区间模式，需要设置表单extractDate[0~3]初始值
      // 示例 "2,4,6"
      extractDefaults = settlementConfig?.extractDate?.split(',').reduce(
        (preExtracts, currentExtract, currentIndex) => ({
          ...preExtracts,
          [`extractDate${currentIndex}`]: currentExtract,
        }),
        {} as Record<string, string>
      );

      extractTypeDefaults = settlementConfig?.extractWeekOrMonth?.split(',').reduce(
        (preExtracts, currentExtract, currentIndex) => ({
          ...preExtracts,
          [`extractWeekOrMonth${currentIndex}`]: currentExtract,
        }),
        {} as Record<string, string>
      );
    } else {
      // 如果不是月抽的区间抽取模式，设置区间起始数组为默认值
      setMonthlyDuringStarts(MonthlyModeDefaultStarts);
    }
    setReconciliationCodeSelectDisabled(
      settlementConfig?.dataSourceTypeList?.[0] !== ChannelDataSourceTypeEnum.RECONCILIATION
    );
    setFieldsValue({
      ...settlementConfig,
      ...durationDefaults,
      ...extractDefaults,
      ...extractTypeDefaults,
      // 设置新增config时的初始值，为每天抽取的日抽。
      frequency: settlementConfig?.frequency ?? SettlementFrequency.Day,
      frequencyValue: settlementConfig?.frequencyValue ?? '1',
      // 如果是月抽During Mode，则弃用frequencyValue2字段，最后submit时，会手动拼成frequencyValue2
      reconciliationOrSettlementTimePeriod: durationList
        ? undefined
        : settlementConfig?.reconciliationOrSettlementTimePeriod,
      extractWeekOrMonth: durationList ? undefined : settlementConfig?.extractWeekOrMonth,
      // 设置新增config时的初始值，初始值为租户设置的系统默认时区。
      timezone: settlementConfig?.timezone ?? tenantTimezone,
      effectiveDateRange: settlementConfig?.effectiveDate
        ? [moment(settlementConfig?.effectiveDate), moment(settlementConfig?.expiryDate)]
        : null,
      methodCategory: settlementConfig?.methodCategory ?? ReconciliationMethodEnum.FILE,
      dataSourceTypeList: settlementConfig?.dataSourceTypeList?.[0],
      generateDebitNoteDetailFlag: settlementConfig?.generateDebitNoteDetailFlag ?? YesOrNo.NO,
      // agency type时, 设置settlementTimeType默认值为BusinessOccurrenceTime
      settlementTimeType:
        (settlementConfig?.generateDebitNoteDetailFlag ?? isAgencySouceChannel)
          ? SettlementTimeTypeEnum.BusinessOccurrenceTime
          : undefined,
      extractPeriodType: settlementConfig?.extractPeriodType === ExtractPeriodTypeEnum.NON_FIXED_TIME,
      containSubChannelFlag: settlementConfig?.containSubChannelFlag === YesOrNo.YES,
    });
  }, [settlementConfig]);

  const groupByOptions = useMemo(() => {
    return enums?.settlementGroupValue?.filter(option =>
      isAgencySouceChannel
        ? option?.value === SettlementGroupBySalesPlatform
        : option?.value !== SettlementGroupBySalesPlatform
    );
  }, [enums, isAgencySouceChannel]);

  return (
    <Drawer
      rootClassName={styles.reconciliationDetailDrawer}
      open={visible}
      title={t('Settlement Configuration')}
      onClose={() => onClose(needRefresh)}
      width={1000}
      destroyOnClose
      closable={false}
      maskClosable={false}
    >
      <Form form={form} layout="vertical">
        <h3>{t('Basic Info')}</h3>
        <Form.Item
          name="code"
          label={t('Setting Code')}
          rules={[
            {
              required: true,
              message: t('channel.common.required', {
                label: t('Setting Code'),
              }),
            },
          ]}
        >
          <Input
            disabled={!!settlementConfig?.configId || readonly}
            className={styles.standardTdWidth}
            placeholder={t('Please input')}
          />
        </Form.Item>
        <Form.Item
          name="effectiveDateRange"
          label={t('Effective Date')}
          rules={[
            {
              required: true,
              message: t('channel.common.required', {
                label: t('Effective Date'),
              }),
            },
          ]}
        >
          <DatePicker.RangePicker
            disabled={readonly}
            style={{ marginTop: 8, width: 320 }}
            format={dateFormat.dateFormat}
            getPopupContainer={triggerElement => triggerElement?.parentElement}
          />
        </Form.Item>
        {/* https://jira.zaouter.com/browse/GIS-93703 Agency type下隐藏字段 */}
        {!isAgencySouceChannel && (
          <>
            <h3>{t('Settlement Option')}</h3>
            <Row>
              <Col span={6}>
                <Form.Item
                  name="dataSourceTypeList"
                  rules={[
                    {
                      required: true,
                      message: t('channel.common.required', {
                        label: t('Settlement Sources'),
                      }),
                    },
                  ]}
                  label={
                    <span>
                      {t('Settlement Sources')}
                      <Tooltip
                        title={
                          <>
                            {t('Source Data: data that do not need reconciliation.')}
                            <br />
                            {t('Reconciliation Data: data that already reconciled.')}
                          </>
                        }
                      >
                        <QuestionCircleOutlined className={styles.questionCircle} />
                      </Tooltip>
                    </span>
                  }
                >
                  <Select
                    showSearch
                    allowClear
                    disabled={readonly}
                    placeholder={t('Please select')}
                    onChange={onSettlementSourcesChange}
                    getPopupContainer={triggerElement => triggerElement?.parentElement}
                  >
                    {settlementSourcesOptions.map(sourceType => (
                      <Select.Option value={sourceType} key={sourceType}>
                        <RenderEnums keyName={sourceType} enums={enums?.channelDataSourceType} />
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </>
        )}
        {!isAgencySouceChannel && (
          <Row>
            <Col>
              <Form.Item
                name="settlementScopeType"
                label={t('Settlement Scope')}
                rules={[
                  {
                    required: true,
                    message: t('channel.common.required', {
                      label: t('Settlement Scope'),
                    }),
                  },
                ]}
              >
                <Radio.Group disabled={readonly}>
                  {enums?.channelSettlementScopeType?.map(scopeType => (
                    <Radio key={scopeType?.enumItemName} value={scopeType?.enumItemName}>
                      {scopeType?.dictValueName}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>
        )}
        {!isAgencySouceChannel && (
          <Row>
            <Col span={14}>
              <MultipleSelect
                label={t('Select Reconciliation')}
                formKey="reconCodeList"
                initialValue={settlementConfig}
                disabled={isReconciliationCodeSelectDisabled || readonly}
                form={form}
                required={!isReconciliationCodeSelectDisabled}
                options={reconciliationOptions}
              />
            </Col>
          </Row>
        )}
        <Row>
          <Col span={6}>
            <MultipleSelect
              label={t('Payment/Collection Method')}
              formKey="payMethodList"
              initialValue={settlementConfig}
              form={form}
              required
              options={uniqWith(
                [...(enums?.collectionPaymentMethod ?? []), ...(enums?.paymentPaymentMethod ?? [])],
                (method1, method2) => method1?.value === method2?.value
              )}
              disabled={readonly}
            />
          </Col>
          <Col span={6} offset={2}>
            <MultipleSelect
              label={t('Settlement Currency')}
              formKey="currencyList"
              initialValue={settlementConfig}
              form={form}
              required
              options={enums?.currencys}
              disabled={readonly}
            />
          </Col>
        </Row>
        {settlementScopeTypeSelected !== SettlementCommissionOnlyScope && (
          <CheckboxGroup
            form={form}
            initialValue={settlementConfig}
            readonly={readonly}
            required
            label={t('Transaction Business Type')}
            formKey="transactionTypeList"
            localOptions={enums?.reconciliationSettlementTransType}
          />
        )}
        <CheckboxGroup
          form={form}
          initialValue={settlementConfig}
          readonly={readonly}
          label={t('Group By Selection')}
          formKey="groupValueList"
          localOptions={groupByOptions}
        />
        <h3>{t('Settlement Method Category')}</h3>
        <Form.Item name="methodCategory" style={{ marginBottom: 0 }}>
          <Radio.Group className={styles.verticalRadio} options={methodCategoryOptions} disabled={readonly} />
        </Form.Item>
        <Row>
          <Col span={8}>
            <Form.Item
              name="fileFormat"
              label={t('Settlement File Format')}
              rules={[
                {
                  required: true,
                  message: t('channel.common.required', {
                    label: t('Settlement File Format'),
                  }),
                },
              ]}
            >
              <Select
                className={styles.standardTdWidth}
                showSearch
                allowClear
                disabled={readonly}
                placeholder={t('Please select')}
                getPopupContainer={triggerElement => triggerElement?.parentElement}
              >
                <Select.Option value={SettlementSupportedFileType}>
                  <RenderEnums
                    keyName={SettlementSupportedFileType}
                    enums={enums?.reconciliationSettlementFileFormat}
                  />
                </Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="filePath" label={t('File Path')}>
              <Input disabled={readonly} className={styles.standardTdWidth} placeholder={t('Please input')} />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col>
            <Form.Item name="generateDebitNoteDetailFlag" label={t('Whether to generate debit note details list?')}>
              <Radio.Group disabled={readonly}>
                {enums?.yesNo?.map(({ enumItemName, dictValueName }) => (
                  <Radio key={enumItemName} value={enumItemName}>
                    {dictValueName}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
        <h3>{t('Settlement Recurrence')}</h3>
        <BatchConfigForm
          form={form}
          batchConfig={settlementConfig}
          type={RelationshipConfigTypeEnum.SETTLEMENT}
          readonly={readonly}
          isMonthlyDuringMode={isMonthlyDuringMode}
          defaultDuringLength={monthlyDuringLength}
          defaultDuringStarts={monthlyDuringStarts}
          dataSourceType={dataSourceType || settlementConfig?.dataSourceTypeList?.[0]}
          isAgencySouceChannel={isAgencySouceChannel}
        />
      </Form>
      <div className={styles.buttonFooter}>
        {!readonly && (
          <Button
            type="primary"
            size="large"
            style={{ marginRight: 32 }}
            loading={loading}
            onClick={onSettlementConfigSubmit}
          >
            {t('Save')}
          </Button>
        )}
        <Button size="large" style={{ marginRight: 16 }} onClick={() => onClose(needRefresh)}>
          {t('Cancel')}
        </Button>
      </div>
    </Drawer>
  );
};
