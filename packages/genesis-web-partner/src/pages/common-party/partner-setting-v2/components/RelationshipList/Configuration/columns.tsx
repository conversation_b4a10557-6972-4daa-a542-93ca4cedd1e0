import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Modal, Tooltip, Typography } from 'antd';
import type { ColumnProps } from 'antd/es/table';

import { DeleteAction } from '@zhongan/nagrand-ui';

import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import type { BizDictItem, ReconciliationConfigDTO, SettlementConfigDTO } from 'genesis-web-service';
import { ReconciliationFrequencyEnum, RegenerationOptionEnum } from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { RenderEnums, renderEnumsWithString } from '@/components/RenderEnums';
import { useTenantBizDict, useTenantBizDictMap } from '@/hooks/useTenantBizDict';

import modalStyles from '../../../style.scss';
import styles from './index.scss';

/**
 *
 * @param openReconciliationDrawer 打开reconciliation详情抽屉
 * @param deleteReconciliationSettlementConfig 删除
 * @param hasReconciliationEditAuth 是否有编辑权限
 * @description 生成reconciliation表单columns
 */
export const useReconciliationColumns = (
  openReconciliationDrawer: (record: ReconciliationConfigDTO) => void,
  deleteReconciliationSettlementConfig: (configId: number) => void,
  hasReconciliationEditAuth: boolean
): ColumnProps<ReconciliationConfigDTO>[] => {
  const {
    l10n: { dateFormat },
  } = useL10n();
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const bizDictMap = useTenantBizDictMap() || {};
  const weekdayMap = useTenantBizDictMap('dictValue')?.weekday;
  const channelDataSourceTypeMap = bizDictMap.channelDataSourceType;
  const payMethodMap = {
    ...(bizDictMap?.collectionPaymentMethod ?? {}),
    ...(bizDictMap?.paymentPaymentMethod ?? {}),
  };
  const reconciliationSettlementTransTypeMap = bizDictMap.reconciliationSettlementTransType;
  const reconciliationFieldMap = bizDictMap.reconciliationField;
  const { t } = useTranslation('partner');

  const onReconciliationDelete = useCallback(
    (configId: number) => {
      Modal.confirm({
        cancelText: t('Cancel'),
        okText: t('Confirm'),
        title: t('Delete'),
        content: t('Are you sure to delete?'),
        className: modalStyles.deleteModal,
        onOk: () => deleteReconciliationSettlementConfig(configId),
      });
    },
    [deleteReconciliationSettlementConfig, t]
  );

  return useMemo(
    () => [
      {
        title: t('Reconciliation Code'),
        dataIndex: 'code',
        fixed: 'left',
        render: (code, record) => <a onClick={() => openReconciliationDrawer(record)}>{code}</a>,
      },
      {
        title: t('Effective Date'),
        render: (_, record) => (
          <ComponentWithFallback>
            {t('separate with -', {
              first: dateFormat.getDateString(record?.effectiveDate),
              second: dateFormat.getDateString(record?.expiryDate),
            })}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Re-generation Option'),
        dataIndex: 'isIncrement',
        render: (text: keyof typeof RegenerationOptionEnum) => (
          <ComponentWithFallback>{t(RegenerationOptionEnum[text])}</ComponentWithFallback>
        ),
      },
      {
        title: t('Payment/Collection'),
        dataIndex: 'dataSourceTypeList',
        render: (dataSourceTypeList: string[]) => (
          <ComponentWithFallback>
            {dataSourceTypeList?.length &&
              dataSourceTypeList
                ?.map(dataSourceType => channelDataSourceTypeMap?.[dataSourceType]?.dictValueName || dataSourceType)
                ?.join(', ')}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Payment/Collection Method'),
        dataIndex: 'payMethodList',
        render: (payMethodList: string[]) => {
          const textContent = payMethodList
            ?.map(payMethod => payMethodMap?.[payMethod]?.dictValueName || payMethod)
            ?.join(', ');
          return (
            <ComponentWithFallback>
              {payMethodList?.length && (
                <Tooltip overlay={textContent}>
                  <Typography.Text ellipsis className={styles.standardTdWidth}>
                    {textContent}
                  </Typography.Text>
                </Tooltip>
              )}
            </ComponentWithFallback>
          );
        },
      },
      {
        title: t('Reconciliation Policy Dimension'),
        dataIndex: 'policyLevel',
        render: (policyLevel: string) => (
          <ComponentWithFallback>
            {policyLevel && <RenderEnums keyName={policyLevel} enums={enums?.policyLevel} />}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Transaction Business Type'),
        dataIndex: 'transactionTypeList',
        render: (transactionTypeList: string[]) => {
          const textContent = transactionTypeList
            ?.map(
              transactionType =>
                reconciliationSettlementTransTypeMap?.[transactionType]?.dictValueName || transactionType
            )
            ?.join(', ');
          return (
            <ComponentWithFallback>
              {transactionTypeList?.length && (
                <Tooltip overlay={textContent}>
                  <Typography.Text ellipsis className={styles.standardTdWidth}>
                    {textContent}
                  </Typography.Text>
                </Tooltip>
              )}
            </ComponentWithFallback>
          );
        },
      },
      {
        title: t('Reconciliation Fields'),
        dataIndex: 'matchFieldList',
        render: (matchFieldList: string[]) => {
          const textContent = matchFieldList
            ?.map(matchField => reconciliationFieldMap?.[matchField]?.dictValueName || matchField)
            ?.join(', ');
          return (
            <ComponentWithFallback>
              {matchFieldList?.length && (
                <Tooltip overlay={textContent}>
                  <Typography.Text ellipsis className={styles.standardTdWidth}>
                    {textContent}
                  </Typography.Text>
                </Tooltip>
              )}
            </ComponentWithFallback>
          );
        },
      },
      {
        title: t('Reconciliation Result Display'),
        dataIndex: 'searchDetailDisplayFieldList',
        render: (searchDetailDisplayFieldList: string[]) => {
          const textContent = searchDetailDisplayFieldList
            ?.map(
              searchDetailDisplayField =>
                reconciliationFieldMap?.[searchDetailDisplayField]?.dictValueName || searchDetailDisplayField
            )
            ?.join(', ');
          return (
            <ComponentWithFallback>
              {searchDetailDisplayFieldList?.length && (
                <Tooltip overlay={textContent}>
                  <Typography.Text ellipsis className={styles.standardTdWidth}>
                    {textContent}
                  </Typography.Text>
                </Tooltip>
              )}
            </ComponentWithFallback>
          );
        },
      },
      {
        title: t('Reconciliation Method'),
        dataIndex: 'methodCategory',
        render: (methodCategory: string) => (
          <ComponentWithFallback>
            <RenderEnums keyName={methodCategory} enums={enums?.reconciliationSettlementMethodCategory} />
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Reconciliation File Format'),
        dataIndex: 'fileFormat',
        render: (fileFormat: string) => (
          <ComponentWithFallback>
            <RenderEnums keyName={fileFormat} enums={enums?.reconciliationSettlementFileFormat} />
          </ComponentWithFallback>
        ),
      },
      {
        title: t('File Path'),
        dataIndex: 'filePath',
        render: (filePath: string) => <ComponentWithFallback>{filePath}</ComponentWithFallback>,
      },
      {
        title: t('Extraction Duration'),
        dataIndex: 'frequencyValue',
        render: (frequencyValue: string) => <ComponentWithFallback>{frequencyValue}</ComponentWithFallback>,
      },
      {
        title: t('Reconciliation Frequency'),
        dataIndex: 'frequency',
        render: (frequency: string) => (
          <ComponentWithFallback>
            <RenderEnums keyName={frequency} enums={enums?.channelSettlementFrequency} />
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Reconciliation Recurrence Date'),
        dataIndex: 'reconciliationOrSettlementTimePeriod',
        render: (frequencyValue2: string, { frequency }) => (
          <ComponentWithFallback>
            {
              // enums?.weekday结构和其他enum不一样，需要手动取一下国际化
              ReconciliationFrequencyEnum[frequency as keyof typeof ReconciliationFrequencyEnum] ===
              ReconciliationFrequencyEnum.WEEK
                ? weekdayMap?.[frequencyValue2]?.dictValueName
                : frequencyValue2
            }
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Extraction Schedule Date'),
        dataIndex: 'extractDate',
        render: (extractDate: string, { frequency }) => (
          <ComponentWithFallback>
            {ReconciliationFrequencyEnum[frequency as keyof typeof ReconciliationFrequencyEnum] ===
            ReconciliationFrequencyEnum.WEEK
              ? weekdayMap?.[extractDate]?.dictValueName
              : extractDate}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Actions'),
        width: 90,
        fixed: 'right',
        align: 'right',
        render: (_, record) => (
          <DeleteAction
            disabled={!hasReconciliationEditAuth}
            onClick={() => onReconciliationDelete(record?.configId)}
          />
        ),
      },
    ],
    [
      dateFormat,
      enums,
      hasReconciliationEditAuth,
      weekdayMap,
      channelDataSourceTypeMap,
      reconciliationSettlementTransTypeMap,
      reconciliationFieldMap,
      openReconciliationDrawer,
      t,
    ]
  );
};

/**
 *
 * @param openSettlementDrawer 打开settlement详情抽屉
 * @param deleteReconciliationSettlementConfig 删除
 * @param hasSettlementEditAuth 是否有编辑权限
 * @description 生成settlement表单columns
 */
export const useSettlementColumns = (
  openSettlementDrawer: (record: SettlementConfigDTO) => void,
  deleteReconciliationSettlementConfig: (number: number) => void,
  hasSettlementEditAuth: boolean
): ColumnProps<SettlementConfigDTO>[] => {
  const {
    l10n: { dateFormat },
  } = useL10n();
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const bizDictMap = useTenantBizDictMap() || {};
  const weekdayMap = useTenantBizDictMap('dictValue')?.weekday;
  const channelDataSourceTypeMap = bizDictMap.channelDataSourceType;
  const payMethodMap = {
    ...(bizDictMap?.collectionPaymentMethod ?? {}),
    ...(bizDictMap?.paymentPaymentMethod ?? {}),
  };
  const reconciliationSettlementTransTypeMap = bizDictMap.reconciliationSettlementTransType;
  const settlementGroupValueMap = bizDictMap.settlementGroupValue;
  const { t } = useTranslation('partner');

  const onSettlementDelete = useCallback(
    (configId: number) => {
      Modal.confirm({
        cancelText: t('Cancel'),
        okText: t('Confirm'),
        title: t('Delete'),
        content: t('Are you sure to delete?'),
        className: modalStyles.deleteModal,
        onOk: () => deleteReconciliationSettlementConfig(configId),
      });
    },
    [deleteReconciliationSettlementConfig, t]
  );

  return useMemo(
    () => [
      {
        title: t('Setting Code'),
        dataIndex: 'code',
        fixed: 'left',
        render: (code, record) => <a onClick={() => openSettlementDrawer(record)}>{code}</a>,
      },
      {
        title: t('Effective Date'),
        render: (_, record) => (
          <ComponentWithFallback>
            {t('separate with -', {
              first: dateFormat.getDateString(record?.effectiveDate),
              second: dateFormat.getDateString(record?.expiryDate),
            })}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Settlement Sources'),
        dataIndex: 'dataSourceTypeList',
        render: (dataSourceTypeList: string[]) => (
          <ComponentWithFallback>
            {dataSourceTypeList?.length &&
              dataSourceTypeList
                ?.map(dataSourceType => channelDataSourceTypeMap?.[dataSourceType]?.dictValueName || dataSourceType)
                ?.join(', ')}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Settlement Scope'),
        dataIndex: 'settlementScopeType',
        render: (settlementScopeType: string) => (
          <ComponentWithFallback>
            <RenderEnums keyName={settlementScopeType} enums={enums?.channelSettlementScopeType} />
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Selected Reconciliation Code'),
        dataIndex: 'reconCodeList',
        render: (reconCodeList: string[]) => (
          <ComponentWithFallback>
            {reconCodeList?.length && (
              <Tooltip overlay={reconCodeList?.join(', ')}>
                <Typography.Text ellipsis className={styles.standardTdWidth}>
                  {reconCodeList?.join(', ')}
                </Typography.Text>
              </Tooltip>
            )}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Payment/Collection Method'),
        dataIndex: 'payMethodList',
        render: (payMethodList: string[]) => {
          const textContent = payMethodList
            ?.map(payMethod => payMethodMap?.[payMethod]?.dictValueName || payMethod)
            ?.join(', ');
          return (
            <ComponentWithFallback>
              {payMethodList?.length && (
                <Tooltip overlay={textContent}>
                  <Typography.Text ellipsis className={styles.standardTdWidth}>
                    {textContent}
                  </Typography.Text>
                </Tooltip>
              )}
            </ComponentWithFallback>
          );
        },
      },
      {
        title: t('Settlement Currency'),
        dataIndex: 'currencyList',
        render: (currencyList: string[]) => (
          <ComponentWithFallback>
            {currencyList?.map(currency => renderEnumsWithString(currency, enums?.currencys)).join(', ')}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Transaction Business Type'),
        dataIndex: 'transactionTypeList',
        render: (transactionTypeList: string[]) => {
          const textContent = transactionTypeList
            ?.map(
              transactionType =>
                reconciliationSettlementTransTypeMap?.[transactionType]?.dictValueName || transactionType
            )
            ?.join(', ');
          return (
            <ComponentWithFallback>
              {transactionTypeList?.length && (
                <Tooltip overlay={textContent}>
                  <Typography.Text ellipsis className={styles.standardTdWidth}>
                    {textContent}
                  </Typography.Text>
                </Tooltip>
              )}
            </ComponentWithFallback>
          );
        },
      },
      {
        title: t('Group By Selection'),
        dataIndex: 'groupValueList',
        render: (groupValueList: string[]) => (
          <ComponentWithFallback>
            {groupValueList?.length &&
              groupValueList
                ?.map(groupValue => settlementGroupValueMap?.[groupValue]?.dictValueName || groupValue)
                ?.join(', ')}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Settlement Method Category'),
        dataIndex: 'methodCategory',
        render: (methodCategory: string) => (
          <ComponentWithFallback>
            <RenderEnums keyName={methodCategory} enums={enums?.reconciliationSettlementMethodCategory} />
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Settlement File Format'),
        dataIndex: 'fileFormat',
        render: (fileFormat: string) => (
          <ComponentWithFallback>
            <RenderEnums keyName={fileFormat} enums={enums?.reconciliationSettlementFileFormat} />
          </ComponentWithFallback>
        ),
      },
      {
        title: t('File Path'),
        dataIndex: 'filePath',
        render: (filePath: string) => <ComponentWithFallback>{filePath}</ComponentWithFallback>,
      },
      {
        title: t('Extraction Duration'),
        dataIndex: 'frequencyValue',
        render: (frequencyValue: string) => <ComponentWithFallback>{frequencyValue}</ComponentWithFallback>,
      },
      {
        title: t('Settlement Frequency'),
        dataIndex: 'frequency',
        render: (frequency: string) => (
          <ComponentWithFallback>
            <RenderEnums keyName={frequency} enums={enums?.channelSettlementFrequency} />
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Settlement Recurrence Date'),
        dataIndex: 'reconciliationOrSettlementTimePeriod',
        render: (frequencyValue2: string, { frequency }) => (
          <ComponentWithFallback>
            {
              // enums?.weekday结构和其他enum不一样，需要手动取一下国际化
              ReconciliationFrequencyEnum[frequency as keyof typeof ReconciliationFrequencyEnum] ===
              ReconciliationFrequencyEnum.WEEK
                ? weekdayMap?.[frequencyValue2]?.dictValueName
                : frequencyValue2
            }
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Extraction Schedule Date'),
        dataIndex: 'extractDate',
        render: (extractDate: string, { frequency }) => (
          <ComponentWithFallback>
            {ReconciliationFrequencyEnum[frequency as keyof typeof ReconciliationFrequencyEnum] ===
            ReconciliationFrequencyEnum.WEEK
              ? weekdayMap?.[extractDate]?.dictValueName
              : extractDate}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Actions'),
        width: 90,
        fixed: 'right',
        align: 'right',
        render: (_, record) => (
          <DeleteAction disabled={!hasSettlementEditAuth} onClick={() => onSettlementDelete(record?.configId)} />
        ),
      },
    ],
    [
      dateFormat,
      enums,
      hasSettlementEditAuth,
      weekdayMap,
      channelDataSourceTypeMap,
      reconciliationSettlementTransTypeMap,
      settlementGroupValueMap,
      openSettlementDrawer,
      t,
    ]
  );
};
