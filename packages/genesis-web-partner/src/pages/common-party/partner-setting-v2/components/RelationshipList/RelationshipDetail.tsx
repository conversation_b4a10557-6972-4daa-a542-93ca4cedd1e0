/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

import { Drawer, Button, Tabs, Form, message } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TenantOrgType, ChannelService } from 'genesis-web-service';
import type {
  RelationshipDetailDTO,
  ChannelDataType,
} from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';
import { usePermission } from '@/hooks/usePermissions';
import type { OptionProps } from 'antd/lib/select';

import type { Moment } from 'moment';

import { BasicInfoDetail } from '@/pages/common-party/partner-setting-v2/components/RelationshipList/BasicInfoForm';
import { Configuration as RelationshipConfiguration } from '@/pages/common-party/partner-setting-v2/components/RelationshipList/Configuration';
import styles from './index.scss';

enum RelationshipTabKey {
  BasicInfo = 'BASIC_INFO',
  Configuration = 'CONFIGURATION',
}

/**
 * RelationshipDetailProps
 * sourceChannelType，sourceChannelId，channelCode，channelName，tenantInfo来自于列表页的信息
 *
 * relationshipDetail： relationship的详细信息
 * agencyList：当type===SALE_CHANNEL允许用户关联agency
 *
 */

interface RelationshipDetailProps {
  sourceChannelType: TenantOrgType;
  sourceChannelId: number;
  channelCode: string;
  channelName: string;
  relationshipDetail: RelationshipDetailDTO;
  visible: boolean;
  tenantInfo: ChannelDataType;
  agencyList: OptionProps[];
  readonly?: boolean;
  onClose: (refreshList: boolean) => void;
  partnerCode: string;
}

export const RelationshipDetail: React.FC<RelationshipDetailProps> = ({
  sourceChannelType,
  visible,
  onClose,
  sourceChannelId,
  relationshipDetail,
  channelCode,
  channelName,
  agencyList,
  tenantInfo,
  readonly,
  partnerCode,
}) => {
  const [form] = Form.useForm();
  const { t } = useTranslation('partner');
  const {
    l10n: { dateFormat },
  } = useL10n();
  const [currenTabKey, setCurrenTabKey] = useState(
    RelationshipTabKey.BasicInfo,
  );
  const [loading, setLoading] = useState(false);
  const [refreshList, setRefreshList] = useState(false);
  const [titleLabels, setTitleLabels] = useState({
    codeLabel: t('Channel Code'),
    nameLabel: t('Channel Name'),
  });
  const [channelRelationInfo, setChannelRelationInfo] =
    useState<RelationshipDetailDTO>();
  const hasEditAuth = !!usePermission('channel.relationship.edit');

  useEffect(() => {
    setChannelRelationInfo(relationshipDetail);
  }, [relationshipDetail]);

  useEffect(() => {
    setTitleLabels({
      codeLabel:
        sourceChannelType === TenantOrgType.SALE_CHANNEL
          ? t('Channel Code')
          : t('Agency Code'),
      nameLabel:
        sourceChannelType === TenantOrgType.SALE_CHANNEL
          ? t('Channel Name')
          : t('Agency Name'),
    });
  }, [sourceChannelType, t]);

  useEffect(() => {
    setCurrenTabKey(RelationshipTabKey.BasicInfo);
    setRefreshList(false);
  }, [visible]);

  const onBasicInfoSubmit = useCallback(() => {
    form.validateFields().then(values => {
      setLoading(true);
      const { effectiveDate, ...rest } = values;
      const startDate = dateFormat.formatTz((effectiveDate as Moment[])?.[0]);
      const endDate = dateFormat.formatTz((effectiveDate as Moment[])?.[1]);
      const params: RelationshipDetailDTO = {
        channelRelationId: channelRelationInfo?.channelRelationId,
        startDate,
        endDate,
        sourceChannelId,
        ...rest,
      };
      const request = channelRelationInfo?.channelRelationId
        ? ChannelService.updateRelationship(params)
        : ChannelService.addRelationship(params);
      request
        .then(res => {
          message.success(t('success'));
          ChannelService.getRelationshipById(res).then(relation =>
            setChannelRelationInfo(relation),
          );
          setCurrenTabKey(RelationshipTabKey.Configuration);
          setRefreshList(true);
        })
        .catch((error: Error) => message.error(error.message))
        .finally(() => setLoading(false));
    });
  }, [
    channelRelationInfo?.channelRelationId,
    dateFormat,
    form,
    sourceChannelId,
    t,
  ]);

  return (
    <Drawer
      rootClassName={styles.relationshipDetailDrawer}
      open={visible}
      closable={false}
      maskClosable={false}
      destroyOnClose
      title={t('Relationship Setting')}
      onClose={() => onClose(refreshList)}
      width={1000}
    >
      <div className={styles.relationHeaderSection}>
        <div>
          <p>{titleLabels.codeLabel}</p>
          <p>{channelCode}</p>
        </div>
        <div>
          <p>{titleLabels.nameLabel}</p>
          <p>{channelName}</p>
        </div>
      </div>
      <Tabs
        activeKey={currenTabKey}
        onChange={activeKey => setCurrenTabKey(activeKey as RelationshipTabKey)}
        items={[
          {
            key: RelationshipTabKey.BasicInfo,
            label: (
              <p style={{ margin: 0, textAlign: 'left' }}>
                <span className={styles.tabNumber}>1</span>
                {t('Basic Info')}
              </p>
            ),
            children: (
              <BasicInfoDetail
                sourceChannelType={sourceChannelType}
                basicInfoDetail={channelRelationInfo}
                agencyList={agencyList}
                form={form}
                readonly={readonly}
              />
            ),
          },
          {
            key: RelationshipTabKey.Configuration,
            label: (
              <p style={{ margin: 0, textAlign: 'left' }}>
                <span className={styles.tabNumber}>2</span>
                {sourceChannelType === TenantOrgType.AGENCY
                  ? t('Settlement Configuration')
                  : t('Reconciliation&Settlement Configuration')}
              </p>
            ),
            children: (
              <RelationshipConfiguration
                sourceChannelType={sourceChannelType}
                tenantInfo={tenantInfo}
                basicInfo={channelRelationInfo}
                channelRelationId={channelRelationInfo?.channelRelationId}
                readonly={readonly}
                partnerCode={partnerCode}
              />
            ),
          },
        ]}
      />
      <div className={styles.buttonFooter}>
        {currenTabKey === RelationshipTabKey.BasicInfo && !readonly && (
          <Button
            type="primary"
            size="large"
            style={{ marginRight: 32 }}
            loading={loading}
            disabled={!hasEditAuth}
            onClick={onBasicInfoSubmit}
          >
            {t('Next')}
          </Button>
        )}
        <Button
          size="large"
          onClick={() => onClose(refreshList)}
          style={{ marginRight: 16 }}
        >
          {t('Cancel')}
        </Button>
      </div>
    </Drawer>
  );
};
