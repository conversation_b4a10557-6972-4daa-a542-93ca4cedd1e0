/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

import { RelationshipDetail } from '@/pages/common-party/partner-setting-v2/components/RelationshipList/RelationshipDetail';
import { RenderEnums } from '@/components/RenderEnums';
import { TableActionEllipsis } from '@/components/TableActionEllipsis';
import { usePermission } from '@/hooks/usePermissions';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { QuestionCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import {
  Divider,
  Button,
  Row,
  Col,
  Skeleton,
  message,
  Tooltip,
  Modal,
} from 'antd';
import type { OptionProps } from 'antd/lib/select';
import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import { ChannelService, TenantOrgType } from 'genesis-web-service';
import type {
  RelationshipDetailDTO,
  ChannelDataType,
  BizDictItem,
  InstituteTypeEnum,
} from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from '@umijs/max';
import { RelationshipContext } from '@/pages/common-party/partner-setting-v2/hooks/useContext';
import { ModeEnum } from '@/types/common';
import { NoData } from 'genesis-web-component/lib/components/NoData';
import commonStyles from '../../style.scss';
import styles from './index.scss';
import modalStyles from '../../style.scss';

message.config({
  maxCount: 1,
});

export const QuestionTooltip: React.FC<{ tooltip: string }> = ({ tooltip }) =>
  useMemo(
    () => (
      <Tooltip title={tooltip}>
        <QuestionCircleOutlined className={styles.questionCircle} />
      </Tooltip>
    ),
    [tooltip],
  );

interface RelationshipListProps {
  sourceChannelId: number;
  type?: TenantOrgType | InstituteTypeEnum;
  channelCode: string;
  channelName: string;
  tenantInfo: ChannelDataType;
  readonly?: boolean;
  mode?: ModeEnum;
  partnerCode: string;
  parentId?: string;
}

/**
 * RelationshipList
 * sourceChannelId: channel的id
 * type: channel的type AGENCY｜SALE_CHANNEL｜SERVICE etc
 * tenantInfo: 主公司的信息，在旧partner取的是/api/channel/channel-backend/queryChannel返回值的第一个信息
 * parentId: 当前channel层级是否第一层级，用于判断Reconciliation/Settlement Configuration是否展示“是否包含二级渠道”开关
 */

export const RelationshipList: React.FC<RelationshipListProps> = ({
  sourceChannelId,
  type,
  channelCode,
  channelName,
  tenantInfo,
  readonly,
  mode,
  partnerCode,
  parentId,
}) => {
  const { t } = useTranslation('partner');
  const dispatch = useDispatch();
  const {
    l10n: { dateFormat },
  } = useL10n();
  const reconciliationSettlementConfigTypeEnum = useTenantBizDict(
    'reconciliationSettlementConfigType',
  ) as BizDictItem[];
  const channelRelationEffectiveDateTypeEnum = useTenantBizDict(
    'channelRelationEffectiveDateType',
  ) as BizDictItem[];
  const hasEditAuth = !!usePermission('channel.relationship.edit');

  const [isRelationshipDetailVisible, setRelationshipDetailVisible] =
    useState(false);
  const [currentRelationshipInfo, setCurrentRelationshipInfo] =
    useState<RelationshipDetailDTO>();

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'reconciliationSettlementConfigType',
        'channelRelationEffectiveDateType',
        'channelDataSourceType',
        'policyLevel',
        'reconciliationSettlementTransType',
        'reconciliationField',
        'reconciliationSettlementMethodCategory',
        'reconciliationSettlementFileFormat',
        'weekday',
        'settlementGroupValue',
        'currencys',
        'reconciliationConfig',
        'channelSettlementScopeType',
        'channelSettlementFrequency',
        'reconciliationUniqueCombinationField',
        'yesNo',
        'settlementTimeType',
        'paymentPaymentMethod',
        'collectionPaymentMethod',
      ],
    });
  }, [dispatch]);

  // 获取agency列表
  const { data: channelList } = useRequest(
    () =>
      ChannelService.getChannelList({
        pageIndex: 0,
        pageSize: 1000,
        type: TenantOrgType.AGENCY,
      }),
    {
      onError: error => message.error(error?.message),
      ready: type === TenantOrgType.SALE_CHANNEL,
      refreshDeps: [type],
    },
  );

  const agencyListOptions = useMemo(() => {
    const selectOptions: OptionProps[] = [];
    channelList?.data?.forEach(agency => {
      const { id, name, code } = agency;
      selectOptions.push({
        label: t('separate with -', {
          first: code,
          second: name,
        }),
        value: id,
      });
    });
    return selectOptions;
  }, [channelList]);

  const {
    data: relationshipList,
    loading,
    refresh: refreshReconciliationList,
  } = useRequest(
    () => {
      if (!sourceChannelId) return Promise.resolve([]);
      return ChannelService.getRelationshipList(sourceChannelId);
    },
    {
      onError: error => message.error(error?.message),
      refreshDeps: [sourceChannelId],
    },
  );

  const deleteRelationship = useCallback(
    (channelRelationId: number) => {
      Modal.confirm({
        cancelText: t('Cancel'),
        okText: t('Confirm'),
        title: t('Delete'),
        content: t('Are you sure to delete?'),
        className: modalStyles.deleteModal,
        onOk: () => {
          message.loading(t('Loading...'), 10);
          ChannelService.deleteRelationship(channelRelationId)
            .then(() => {
              message.success(t('Delete successfully'));
              refreshReconciliationList();
            })
            .catch((error: Error) => {
              message.error(error.message);
            });
        },
      });
    },
    [refreshReconciliationList, t],
  );

  const editRelationship = useCallback(
    (relationInfo: RelationshipDetailDTO) => {
      setCurrentRelationshipInfo(relationInfo);
      setRelationshipDetailVisible(true);
    },
    [],
  );

  const onDrawerClose = useCallback(
    (refreshList: boolean) => {
      setRelationshipDetailVisible(false);
      setCurrentRelationshipInfo(null);
      if (refreshList) {
        refreshReconciliationList();
      }
    },
    [refreshReconciliationList],
  );

  return (
    <RelationshipContext.Provider value={{ level: parentId ? null : 1 }}>
      {sourceChannelId && (
        <>
          <Divider dashed />
          <h4 className={styles.sectionTitle}>{t('Relationship List')}</h4>
          {readonly && mode === ModeEnum.ADD ? (
            <NoData
              disabled={true}
              inTable={false}
              className={commonStyles.noData}
            />
          ) : (
            <>
              <Button
                type="dashed"
                icon={<PlusOutlined />}
                block
                disabled={!hasEditAuth || readonly}
                onClick={() => editRelationship({} as RelationshipDetailDTO)}
              >
                {t('add')}
              </Button>
              <div className={styles.relationshipRowContainer}>
                <Skeleton loading={loading}>
                  {relationshipList?.map(
                    (relationship: RelationshipDetailDTO) => (
                      <Row
                        className={styles.relationshipRow}
                        key={relationship?.channelRelationId}
                      >
                        <Col style={{ flex: 1 }}>
                          <p>
                            <a onClick={() => editRelationship(relationship)}>
                              {relationship?.name}
                            </a>
                          </p>
                          <ComponentWithFallback>
                            {relationship?.code}
                          </ComponentWithFallback>
                        </Col>
                        <Col style={{ flex: 1 }}>
                          <p>{t('Period of Validity')}</p>
                          <ComponentWithFallback>
                            {relationship?.startDate ? (
                              t('separate with -', {
                                first: dateFormat.getDateString(
                                  relationship?.startDate,
                                ),
                                second: dateFormat.getDateString(
                                  relationship?.endDate,
                                ),
                              })
                            ) : (
                              <RenderEnums
                                keyName={relationship?.effectiveDateType}
                                enums={channelRelationEffectiveDateTypeEnum}
                              />
                            )}
                          </ComponentWithFallback>
                        </Col>
                        <Col style={{ flex: 1 }}>
                          <p>
                            {t('Reconciliation& Settlement')}
                            <QuestionTooltip
                              tooltip={t(
                                'Reconciliation& Settlement Configuration',
                              )}
                            />
                          </p>
                          <ComponentWithFallback>
                            {relationship?.existConfigType && (
                              <RenderEnums
                                keyName={relationship?.existConfigType}
                                enums={reconciliationSettlementConfigTypeEnum}
                              />
                            )}
                          </ComponentWithFallback>
                        </Col>
                        {
                          // https://jira.zaouter.com/browse/GIS-93703 隐藏Belonging Agency字段
                          // 当前为SALE_CHANNEL层级并且配置了所属的AGENCY，展示AGENCY信息
                          // type === TenantOrgType.SALE_CHANNEL &&
                          //   relationship?.targetChannel?.type ===
                          //     TenantOrgType.AGENCY && (
                          //     <Col style={{ flex: 1 }}>
                          //       <p>{t('Belonging Agency')}</p>
                          //       <ComponentWithFallback>
                          //         {t('separate with -', {
                          //           first: relationship?.targetChannel?.code,
                          //           second: relationship?.targetChannel?.name,
                          //         })}
                          //       </ComponentWithFallback>
                          //     </Col>
                          //   )
                        }
                        <Col span={1} style={{ marginLeft: 8 }}>
                          <TableActionEllipsis
                            menuItems={[
                              {
                                title: t('delete'),
                                key: 'delete',
                                disabled: !hasEditAuth || readonly,
                                onClick: () =>
                                  deleteRelationship(
                                    relationship?.channelRelationId,
                                  ),
                              },
                            ]}
                          />
                        </Col>
                      </Row>
                    ),
                  )}
                </Skeleton>
              </div>
              <RelationshipDetail
                sourceChannelId={sourceChannelId}
                sourceChannelType={type}
                channelCode={channelCode}
                channelName={channelName}
                relationshipDetail={currentRelationshipInfo}
                agencyList={agencyListOptions}
                onClose={onDrawerClose}
                visible={isRelationshipDetailVisible}
                tenantInfo={tenantInfo}
                readonly={readonly}
                partnerCode={partnerCode}
              />
            </>
          )}
        </>
      )}
    </RelationshipContext.Provider>
  );
};
