/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { QuestionCircleOutlined } from '@ant-design/icons';
import { Modal, Tooltip, message } from 'antd';
import type { OptionProps } from 'antd/lib/select';

import { useDispatch } from '@umijs/max';

import { useRequest } from 'ahooks';

import { DeleteAction, EditAction, Table, ViewAction } from '@zhongan/nagrand-ui';

import type { BizDictItem, ChannelDataType, InstituteTypeEnum, RelationshipDetailDTO } from 'genesis-web-service';
import { ChannelService, TenantOrgType } from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { RenderEnums } from '@/components/RenderEnums';
import { usePermission } from '@/hooks/usePermissions';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { RelationshipDetail } from '@/pages/common-party/partner-setting-v2/components/RelationshipList/RelationshipDetail';
import { RelationshipContext } from '@/pages/common-party/partner-setting-v2/hooks/useContext';
import { AddNewTitle } from '@/pages/common-party/partner-setting/components/AddNewTitle';

import { default as modalStyles } from '../../style.scss';
import styles from './index.scss';

message.config({
  maxCount: 1,
});

export const QuestionTooltip: React.FC<{ tooltip: string }> = ({ tooltip }) =>
  useMemo(
    () => (
      <Tooltip title={tooltip}>
        <QuestionCircleOutlined className={styles.questionCircle} />
      </Tooltip>
    ),
    [tooltip]
  );

interface RelationshipListProps {
  sourceChannelId: number;
  type?: TenantOrgType | InstituteTypeEnum;
  channelCode: string;
  channelName: string;
  tenantInfo: ChannelDataType;
  partnerCode: string;
  parentId?: string;
  disabled: boolean;
}

/**
 * RelationshipList
 * sourceChannelId: channel的id
 * type: channel的type AGENCY｜SALE_CHANNEL｜SERVICE etc
 * tenantInfo: 主公司的信息，在旧partner取的是/api/channel/channel-backend/queryChannel返回值的第一个信息
 * parentId: 当前channel层级是否第一层级，用于判断Reconciliation/Settlement Configuration是否展示“是否包含二级渠道”开关
 */

export const RelationshipList: React.FC<RelationshipListProps> = ({
  sourceChannelId,
  type,
  channelCode,
  channelName,
  tenantInfo,
  partnerCode,
  parentId,
  disabled,
}) => {
  const { t } = useTranslation('partner');
  const dispatch = useDispatch();
  const {
    l10n: { dateFormat },
  } = useL10n();
  const reconciliationSettlementConfigTypeEnum = useTenantBizDict(
    'reconciliationSettlementConfigType'
  ) as BizDictItem[];
  const channelRelationEffectiveDateTypeEnum = useTenantBizDict('channelRelationEffectiveDateType') as BizDictItem[];
  const hasEditAuth = !!usePermission('channel.relationship.edit');

  const [isRelationshipDetailVisible, setRelationshipDetailVisible] = useState(false);
  const [currentRelationshipInfo, setCurrentRelationshipInfo] = useState<RelationshipDetailDTO>();
  const [readonly, setReadonly] = useState(false);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'reconciliationSettlementConfigType',
        'channelRelationEffectiveDateType',
        'channelDataSourceType',
        'policyLevel',
        'reconciliationSettlementTransType',
        'reconciliationField',
        'reconciliationSettlementMethodCategory',
        'reconciliationSettlementFileFormat',
        'weekday',
        'settlementGroupValue',
        'currencys',
        'reconciliationConfig',
        'channelSettlementScopeType',
        'channelSettlementFrequency',
        'reconciliationUniqueCombinationField',
        'yesNo',
        'settlementTimeType',
        'paymentPaymentMethod',
        'collectionPaymentMethod',
      ],
    });
  }, [dispatch]);

  // 获取agency列表
  const { data: channelList } = useRequest(
    () =>
      ChannelService.getChannelList({
        pageIndex: 0,
        pageSize: 1000,
        type: TenantOrgType.AGENCY,
      }),
    {
      onError: error => message.error(error?.message),
      ready: type === TenantOrgType.SALE_CHANNEL,
      refreshDeps: [type],
    }
  );

  const agencyListOptions = useMemo(() => {
    const selectOptions: OptionProps[] = [];
    channelList?.data?.forEach(agency => {
      const { id, name, code } = agency;
      selectOptions.push({
        label: t('separate with -', {
          first: code,
          second: name,
        }),
        value: id,
      } as unknown as OptionProps);
    });
    return selectOptions;
  }, [channelList]);

  const {
    data: relationshipList,
    loading,
    refresh: refreshReconciliationList,
  } = useRequest(
    () => {
      if (!sourceChannelId) return Promise.resolve([]);
      return ChannelService.getRelationshipList(sourceChannelId);
    },
    {
      onError: error => message.error(error?.message),
      refreshDeps: [sourceChannelId],
    }
  );

  const deleteRelationship = useCallback(
    (channelRelationId: number) => {
      Modal.confirm({
        cancelText: t('Cancel'),
        okText: t('Confirm'),
        title: t('Delete'),
        content: t('Are you sure to delete?'),
        className: modalStyles.deleteModal,
        onOk: () => {
          message.loading(t('Loading...'), 10);
          ChannelService.deleteRelationship(channelRelationId)
            .then(() => {
              message.success(t('Delete successfully'));
              refreshReconciliationList();
            })
            .catch((error: Error) => {
              message.error(error.message);
            });
        },
      });
    },
    [refreshReconciliationList, t]
  );

  const editRelationship = useCallback((relationInfo: RelationshipDetailDTO) => {
    setCurrentRelationshipInfo(relationInfo);
    setRelationshipDetailVisible(true);
  }, []);

  const onDrawerClose = useCallback(
    (refreshList: boolean) => {
      setRelationshipDetailVisible(false);
      setCurrentRelationshipInfo(null);
      if (refreshList) {
        refreshReconciliationList();
      }
    },
    [refreshReconciliationList]
  );

  return (
    <RelationshipContext.Provider value={{ level: parentId ? null : 1 }}>
      {sourceChannelId && (
        <div>
          <AddNewTitle
            title={t('Relationship List')}
            onAddClick={() => {
              editRelationship({} as RelationshipDetailDTO);
              setReadonly(false);
            }}
            readonly={!hasEditAuth || disabled}
          />
          <Table
            columns={[
              {
                title: t('Relationship Name'),
                dataIndex: 'name',
              },
              {
                title: t('Relationship Code'),
                dataIndex: 'code',
              },
              {
                title: t('Period of Validity'),
                dataIndex: 'startDate',
                render: (text, record) => {
                  if (text) {
                    return t('separate with -', {
                      first: dateFormat.getDateString(record?.startDate),
                      second: dateFormat.getDateString(record?.endDate),
                    });
                  }
                  return (
                    <RenderEnums keyName={record?.effectiveDateType} enums={channelRelationEffectiveDateTypeEnum} />
                  );
                },
              },
              {
                title: (
                  <>
                    {t('Reconciliation& Settlement')}
                    <QuestionTooltip tooltip={t('Reconciliation& Settlement Configuration')} />
                  </>
                ),
                dataIndex: 'existConfigType',
                render: text => text && <RenderEnums keyName={text} enums={reconciliationSettlementConfigTypeEnum} />,
              },
              {
                title: t('Action'),
                render: (_, record) => (
                  <>
                    <ViewAction
                      onClick={() => {
                        editRelationship(record);
                        setReadonly(true);
                      }}
                    />
                    <EditAction
                      onClick={() => {
                        editRelationship(record);
                        setReadonly(false);
                      }}
                      disabled={disabled}
                    />
                    <DeleteAction
                      deleteConfirmContent={t('Are you sure to delete?')}
                      onClick={() => deleteRelationship(record?.channelRelationId)}
                      disabled={disabled}
                    />
                  </>
                ),
              },
            ]}
            dataSource={relationshipList}
            pagination={false}
            scroll={{ x: 'max-content' }}
          />
          <RelationshipDetail
            sourceChannelId={sourceChannelId}
            sourceChannelType={type}
            channelCode={channelCode}
            channelName={channelName}
            relationshipDetail={currentRelationshipInfo}
            agencyList={agencyListOptions}
            onClose={onDrawerClose}
            visible={isRelationshipDetailVisible}
            tenantInfo={tenantInfo}
            readonly={readonly}
            partnerCode={partnerCode}
          />
        </div>
      )}
    </RelationshipContext.Provider>
  );
};
