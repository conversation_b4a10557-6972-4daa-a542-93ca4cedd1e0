@import '@/variables.scss';

.conditional-search-container {
  width: 560px;
  display: flex;
  position: relative;
  :global {
    .#{$antd-prefix}-btn-primary {
      height: 40px;
      margin-left: -1px;
      border-radius: 0 $border-radius-base $border-radius-base 0;
      &:hover {
        background-color: $menu-item-hover-color;
      }
    }
    .#{$antd-prefix}-select-selection-search-input {
      border-radius: $border-radius-base 0 0 $border-radius-base;
      height: 40px;
      .#{$antd-prefix}-input-suffix {
        position: relative;
        right: 21px;
        top: 1px;
      }
    }
  }
  .conditional-panel-trigger {
    position: absolute;
    z-index: 6;
    height: 20px;
    width: 20px;
    top: 10px;
    padding: 0px;
    right: 111px;
  }

  .clear-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
  }
}

.conditional-panel-container {
  padding: $gap-sm;
  width: 845px;
  font-weight: 500;
  .conditional-panel-header {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $gap-md;
  }
  .conditional-panel-footer {
    display: flex;
    width: 100%;
    justify-content: end;
    > button {
      margin-left: $gap-md;
      font-weight: 500;
    }
  }
}
.setting-panel-header {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  padding: $gap-sm $gap-md;
  box-shadow: 0 0.5px 0.5px 0px $border-line-color-secondary;
}
.setting-panel-container {
  width: 780px;
  display: flex;
  padding: $gap-xs 0;
  .checkbox-container {
    flex: 1;
    :global {
      .#{$antd-prefix}-input-affix-wrapper {
        width: 360px;
        margin-bottom: $gap-md;
        border-width: 0.5px;
        .anticon {
          color: $divider-color;
        }
      }
    }
    .checkbox-group {
      :global {
        .#{$antd-prefix}-checkbox-wrapper {
          margin-bottom: $gap-md;
        }
        .#{$antd-prefix}-checkbox-disabled {
          border: none !important;
          .#{$antd-prefix}-checkbox-inner {
            border-color: transparent !important;
          }
        }
      }
    }
  }

  .droppable-container {
    flex: 1;
    height: 340px;
    padding: $gap-xs;
    padding-left: $gap-lg;
    position: relative;
    overflow-y: auto;
    box-shadow: -0.5px 0px 0px 0px $border-line-color-secondary;
  }
  .is-droppable-container-working {
    // placeholder
  }
  .draggable-list-items {
    display: flex;
    align-items: center;
    width: 100%;
    height: 32px;
    padding: $gap-xss;
    border-radius: $gap-xss;
    background-color: $form-bg;
    margin-bottom: $gap-xs;
    :global {
      .anticon {
        margin-right: $gap-xs;
        font-size: $font-size-lg;
      }
    }
    > div {
      flex: 1;
    }
    .delete-item {
      cursor: pointer;
    }
    &:hover {
      box-shadow: 0 1px 2px -2px #00000029, 0 3px 6px #0000001f,
        0 5px 12px 4px #00000017;
      transition: all 0.3s;
    }
  }
  .is-draggable-list-items-active {
    box-shadow: 0 1px 2px -2px #00000029, 0 3px 6px #0000001f,
      0 5px 12px 4px #00000017;
    transform: scale(1.01);
    transition: all 0.3s;
  }
  .draggable-item-disabled {
  }
}
.setting-panel-footer {
  box-shadow: 0 -0.5px 0px 0px $border-line-color-secondary;
  padding-top: $gap-lg;
  display: flex;
  flex-direction: row-reverse;
  > button {
    margin-left: $gap-md;
  }
}
.history-panel-container {
  width: 528px;
  padding: $gap-xs;
  :global {
    .#{$antd-prefix}-select-item {
      border-radius: $checkbox-border-radius;
      padding: 0;
      background-color: $white;
    }
    .#{$antd-prefix}-row {
      display: flex;
      padding: $gap-xss;
      border-radius: $border-radius-medium;
      cursor: pointer;
      align-items: center;
      > .anticon {
        color: $default-color;
        opacity: 0;
      }
      .#{$antd-prefix}-typography {
        color: $label;
        flex: 1;
        > .anticon {
          margin-left: $gap-xs;
          margin-right: $gap-xs;
        }
      }
      &:hover {
        background-color: $form-bg;
        transition: 0.3s all;
        > .anticon {
          color: $default-color;
          opacity: 1;
        }
      }
    }
  }
}
.history-list-popover {
  top: 55px !important;
}

.row-dragging {
  display: flex;
  z-index: 1999 !important;
  align-items: center;
  width: 100%;
  height: 32px;
  padding: $gap-xss;
  border-radius: $gap-xss;
  background-color: $form-bg;
  margin-bottom: $gap-xs;
  :global {
    .anticon {
      margin-right: $gap-xs;
      font-size: $font-size-lg;
    }
  }
  > div {
    flex: 1;
  }
  .delete-item {
    cursor: pointer;
  }
  .drag-visible {
    opacity: 1;
    visibility: visible;
  }
}
