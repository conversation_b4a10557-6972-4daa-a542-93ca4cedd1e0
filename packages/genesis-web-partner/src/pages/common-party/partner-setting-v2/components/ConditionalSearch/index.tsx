import type { MouseEvent, ReactElement } from 'react';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { FormInstance } from 'antd';
import {
  Input,
  Button,
  Popover,
  Row,
  Typography,
  AutoComplete,
  Divider,
} from 'antd';
import { zipObject } from 'lodash-es';

import { TextBody, Icon as NagrandIcon, Modal } from '@zhongan/nagrand-ui';
import type { QueryFieldsType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';

import { SettingPanel } from './SettingPanel';
import style from './style.scss';

import SettingSvg from '@/assets/svg/Setting.svg';
import ConditionSearchSvg from '@/assets/svg/ConditionSearch.svg';
import Icon from '@ant-design/icons';
import { lessVars } from '@zhongan/nagrand-ui';

export interface ConditionalSearchFieldType extends Partial<QueryFieldsType> {
  immutable?: boolean;
}
export interface ConditionalSearchProps {
  fields: ConditionalSearchFieldType[];
  onSearch: (searchValue?: Record<string, any>) => void;
  form: FormInstance;
  // 主搜索框的搜索key，可能会对应多个fields的key
  mainSearchFieldKey?: string[];
  loading?: boolean;
}

export const ConditionalSearch = ({
  fields,
  onSearch,
  form,
  loading,
  mainSearchFieldKey,
}: ConditionalSearchProps) => {
  const { t } = useTranslation('partner');
  const [isConditionPanelVisible, setConditionPanelVisible] = useState(false);
  const [isSettingPanelVisible, setSettingPanelVisible] = useState(false);
  const [isHistoryPanelVisible, setHistoryPanelVisible] = useState(false);
  const [mainSearchInputValue, setMainSearchInputValue] = useState<string>();
  const [searchHistory, setSearchHistory] =
    useState<{ label: ReactElement; value: string }[]>();
  const [searchHistoryFlag, setSearchHistoryFlag] = useState(1); // 用来控制删除历史记录后更新历史记录

  const [filteredFields, setFilteredFields] = useState<
    ConditionalSearchFieldType[]
  >([]);
  const [searchedValues, setSearchedValues] =
    useState<Record<string, string>>();

  useEffect(() => {
    setFilteredFields(fields?.filter(field => field?.immutable) ?? []);
  }, [fields]);

  const deleteSearchHistory = (deleteIndex: number) => {
    const storedSearchHistory = [
      ...((JSON.parse(
        localStorage.getItem('PartnerManagementSearchHistory'),
      ) as string[]) || []),
    ];
    storedSearchHistory?.splice(deleteIndex, 1);
    localStorage.setItem(
      'PartnerManagementSearchHistory',
      JSON.stringify([...storedSearchHistory]),
    );
    setSearchHistoryFlag(old => old + 1);
  };

  const onMainSearchPress = useCallback(
    (searchValue: string) => {
      const param = {
        ...form.getFieldsValue(),
        ...zipObject(
          mainSearchFieldKey?.map(key => key),
          mainSearchFieldKey?.map(() => searchValue),
        ),
      };
      const searchHistoryFromLS =
        (JSON.parse(
          localStorage.getItem('PartnerManagementSearchHistory'),
        ) as string[]) || [];
      if (searchValue && !searchHistoryFromLS?.includes(searchValue)) {
        localStorage.setItem(
          'PartnerManagementSearchHistory',
          JSON.stringify([searchValue, ...searchHistoryFromLS?.slice(0, 4)]),
        );
      }
      onSearch?.(param);
      setConditionPanelVisible(false);
      setHistoryPanelVisible(false);
      setSearchHistoryFlag(old => old + 1);
    },
    [form],
  );

  const onDeleteSearchHistory = useCallback(
    (index: number, event: MouseEvent) => {
      deleteSearchHistory(index);
      event.stopPropagation();
    },
    [deleteSearchHistory],
  );

  useEffect(() => {
    if (isHistoryPanelVisible) {
      const storedSearchHistory = [
        ...((JSON.parse(
          localStorage.getItem('PartnerManagementSearchHistory'),
        ) as string[]) || []),
      ];
      setSearchHistory(
        storedSearchHistory.map((text, index) => ({
          label: (
            <Row key={text}>
              <Typography.Text
                onClick={() => {
                  setMainSearchInputValue(text);
                  setTimeout(() => onMainSearchPress(text));
                }}
              >
                <NagrandIcon type="time" />
                {text}
              </Typography.Text>
              <NagrandIcon
                type="close"
                onClick={(event: MouseEvent) =>
                  onDeleteSearchHistory(index, event)
                }
              />
            </Row>
          ),
          value: text,
        })),
      );
    }
  }, [
    isHistoryPanelVisible,
    searchHistoryFlag,
    localStorage.getItem('PartnerManagementSearchHistory'),
  ]);

  useEffect(() => {
    // 打开condition panel时，保留上次搜索内容
    if (isConditionPanelVisible) {
      form.setFieldsValue(searchedValues);
    }
  }, [isConditionPanelVisible]);

  const onSearchCondition = useCallback(() => {
    onSearch?.(form.getFieldsValue());
    setSearchedValues(form.getFieldsValue());
    setConditionPanelVisible(false);
  }, [form, onSearch]);

  const handleCloseSettingModal = () => {
    setFilteredFields(filteredFields);
    setSettingPanelVisible(false);
  };

  const renderConditionalPanel = () => {
    return (
      <div className={style.conditionalPanelContainer}>
        <Row className={style.conditionalPanelHeader}>
          <TextBody type="h5" weight="bold">
            {t('Condition Query')}
          </TextBody>
          <Icon
            onClick={() => setSettingPanelVisible(true)}
            component={ConditionSearchSvg}
            style={{ fontSize: 36, color: 'transparent', cursor: 'pointer' }}
          />
        </Row>
        <CommonForm fields={filteredFields} form={form} gutter={50} />
        <Row className={style.conditionalPanelFooter}>
          <Button onClick={() => form.resetFields()}>{t('Clear')}</Button>
          <Button type="primary" onClick={onSearchCondition}>
            {t('Search')}
          </Button>
        </Row>

        <Modal
          title={
            <TextBody type="h5" weight="bold">
              {t('Customized Query Conditions Setting')}
            </TextBody>
          }
          open={isSettingPanelVisible}
          footer={null}
          onCancel={handleCloseSettingModal}
          width={840}
          // 层级高于popover,覆盖显示
          zIndex={1031}
        >
          <SettingPanel
            fullFields={fields as ConditionalSearchFieldType[]}
            sortedFields={filteredFields as ConditionalSearchFieldType[]}
            setFilteredFields={value => {
              setFilteredFields(value);
              setSettingPanelVisible(false);
            }}
          />
        </Modal>
      </div>
    );
  };

  return (
    <div className={style.conditionalSearchContainer}>
      <AutoComplete
        popupClassName={style.historyPanelContainer}
        backfill
        notFoundContent={null}
        popupMatchSelectWidth={560}
        onDropdownVisibleChange={setHistoryPanelVisible}
        style={{ width: 560 }}
        options={searchHistory}
        getPopupContainer={() => document.body}
      >
        <Input
          size="middle"
          value={mainSearchInputValue}
          onChange={event => setMainSearchInputValue(event?.target.value)}
          allowClear={{
            clearIcon: (
              <div className={style.clearIcon}>
                <NagrandIcon
                  type="close"
                  style={{
                    fontSize: lessVars['@font-size-lg'],
                    color: lessVars['@text-color-tertiary'],
                  }}
                />
                <Divider
                  type="vertical"
                  style={{ height: 15, color: lessVars['@disabled-color'] }}
                />
              </div>
            ),
          }}
          placeholder={t('Please Input')}
        />
      </AutoComplete>
      <Popover
        trigger="click"
        content={renderConditionalPanel}
        open={isConditionPanelVisible}
        onOpenChange={visible => {
          // 加锁检查,modal打开时不触发popover的关闭事件
          if (!isSettingPanelVisible) {
            setConditionPanelVisible(visible);
          }
        }}
        getPopupContainer={() =>
          document.getElementsByClassName('partner-global')[0] as HTMLElement
        }
      >
        <Button
          size="middle"
          type="text"
          className={style.conditionalPanelTrigger}
          onClick={event => {
            event?.stopPropagation();
            event?.preventDefault();
            setConditionPanelVisible(true);
            form.setFieldsValue({
              ...zipObject(
                mainSearchFieldKey?.map(key => key),
                mainSearchFieldKey?.map(() => mainSearchInputValue),
              ),
            });
          }}
        >
          <Icon component={SettingSvg} style={{ fontSize: 20 }} />
        </Button>
      </Popover>
      <Button
        type="primary"
        icon={<NagrandIcon type="search" />}
        className={style.mainSearchButton}
        loading={loading}
        onClick={() => onMainSearchPress(mainSearchInputValue)}
      >
        {t('Search')}
      </Button>
    </div>
  );
};
