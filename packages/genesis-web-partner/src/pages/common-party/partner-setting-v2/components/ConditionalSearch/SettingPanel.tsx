import { useState, useRef } from 'react';
import { Col, Input, Checkbox, Row, Typography, Button } from 'antd';
import { Icon, TextBody } from '@zhongan/nagrand-ui';
import React from 'react';
import { HolderOutlined } from '@ant-design/icons';
import clsx from 'clsx';
import type { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { unionBy } from 'lodash-es';
import { useTranslation } from 'react-i18next';
import {
  SortableContainer,
  SortableElement,
  SortableHandle,
} from 'react-sortable-hoc';
import arrayMove from 'array-move';

import type { ConditionalSearchFieldType } from './index';
import style from './style.scss';

export const SettingPanel = ({
  fullFields,
  sortedFields,
  setFilteredFields,
}: {
  fullFields: ConditionalSearchFieldType[];
  sortedFields: ConditionalSearchFieldType[];
  setFilteredFields: React.Dispatch<
    React.SetStateAction<ConditionalSearchFieldType[]>
  >;
}) => {
  const { t } = useTranslation('partner');
  // internal sorted fields
  const [sortedFieldsList, setSortedFieldsList] =
    useState<ConditionalSearchFieldType[]>(sortedFields);
  const containerBody = useRef<any>(null);

  const onSettingCheckboxChange = (selectedFields: CheckboxValueType[]) => {
    const clonedSortedFields = [...sortedFieldsList, ...fullFields];
    setSortedFieldsList(
      unionBy(
        clonedSortedFields?.filter(field =>
          selectedFields.includes(field?.key),
        ),
        'key',
      ),
    );
  };

  const onDargItemDelete = (key: string) => {
    onSettingCheckboxChange(
      sortedFieldsList
        ?.map(field => field?.key)
        ?.filter(fieldKey => fieldKey !== key),
    );
  };

  const DragHandle = SortableHandle(() => (
    <HolderOutlined style={{ cursor: 'pointer' }} />
  ));

  const SortItem = SortableElement(
    ({ record }: { record: ConditionalSearchFieldType }) => (
      <Typography.Text
        disabled={record?.immutable}
        className={clsx([
          style.draggableListItems,
          record?.immutable && style.draggableItemDisabled,
        ])}
      >
        <DragHandle />
        <div>{record?.label || record?.key}</div>
        {!record?.immutable && (
          <Icon
            type="close"
            className={style.deleteItem}
            onClick={() => onDargItemDelete(record?.key)}
          />
        )}
      </Typography.Text>
    ),
  );

  const SortContainer = SortableContainer(
    (props: React.HTMLAttributes<HTMLTableSectionElement>) => (
      <div {...props} />
    ),
  );

  const onSortEnd = ({
    oldIndex,
    newIndex,
  }: {
    oldIndex: number;
    newIndex: number;
  }) => setSortedFieldsList(arrayMove(sortedFieldsList, oldIndex, newIndex));

  return (
    <>
      <div className={style.settingPanelContainer}>
        <div className={style.checkboxContainer}>
          <Input placeholder={t('Search')} prefix={<Icon type="search" />} />
          <Checkbox.Group
            className={style.checkboxGroup}
            value={sortedFieldsList?.map(sortedField => sortedField?.key)}
            onChange={onSettingCheckboxChange}
          >
            <Row>
              {fullFields?.map(field => (
                <Col key={field?.key} span={24}>
                  <Checkbox
                    disabled={field?.immutable || false}
                    value={field?.key}
                  >
                    {field?.label || field?.key}
                  </Checkbox>
                </Col>
              ))}
            </Row>
          </Checkbox.Group>
        </div>
        <div className={style.droppableContainer}>
          <p>
            {t('Search Criteria {{n}}', { n: sortedFieldsList?.length || 0 })}
          </p>
          <SortContainer
            ref={containerBody}
            helperClass={style['row-dragging']}
            onSortEnd={onSortEnd}
            useDragHandle
          >
            {sortedFieldsList.map((item, index) => (
              <SortItem
                disabled={item?.immutable}
                record={item}
                index={index}
              />
            ))}
          </SortContainer>
        </div>
      </div>
      <Row className={style.settingPanelFooter}>
        <Button
          type="primary"
          onClick={() => setFilteredFields(sortedFieldsList)}
        >
          {t('Confirm')}
        </Button>
        <Button onClick={() => setFilteredFields(sortedFields)}>
          {t('Cancel')}
        </Button>
      </Row>
    </>
  );
};
