import { useTranslation } from 'react-i18next';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { StatusTag } from '@/components/StatusTag';
import { RenderEnums, renderEnumsWithString } from '@/components/RenderEnums';
import { TagsShape, TagsType } from '@/utils/constants';
import type { BizDictItem, InstituteStaff } from 'genesis-web-service';
import { EmployeeStatus, InstituteTypeEnum } from 'genesis-web-service';
import { useTenantBizDictMap } from '@/hooks/useTenantBizDict';
import { useMemo } from 'react';
import type { TableColumnProps } from 'antd';
import { useAddressConfig } from 'genesis-web-component/lib/components/Address';
import { transferSchemaToTableProp } from '@/utils/utils';
import { useAddressI18nList } from '@/hooks/useAddressI18nList';

export const useStaffColumns = (
  instituteType: InstituteTypeEnum,
  data?: InstituteStaff[],
) => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const { workingType } = useTenantBizDictMap();
  // 处理address & zipCode 表格表头展示
  const { formItems } = useAddressConfig();

  const addressI18nList = useAddressI18nList(data);
  const addressAndZipCodeTableColumns = useMemo(() => {
    if (!formItems?.length) {
      return [];
    }
    return transferSchemaToTableProp(formItems, addressI18nList);
  }, [formItems, addressI18nList]);

  const commonStaffColumns: TableColumnProps<InstituteStaff>[] = [
    {
      title: t('Staff Name'),
      dataIndex: 'staffName',
    },
    {
      title: t('Staff Code'),
      dataIndex: 'staffCode',
    },
    {
      title: t('Email'),
      dataIndex: 'email',
    },
    {
      title: t('Country Code'),
      dataIndex: 'countryCode',
    },
    {
      title: t('Phone Number'),
      dataIndex: 'phoneNo',
    },
    {
      title: t('Remark'),
      dataIndex: 'remark',
    },
  ];
  if (instituteType === InstituteTypeEnum.AssessmentCompany) {
    commonStaffColumns?.splice(
      2,
      0,
      ...[
        {
          title: t('Status'),
          dataIndex: 'status',
          render: (status: EmployeeStatus) => {
            const tagType =
              status === EmployeeStatus.OnLeave
                ? TagsType.Default
                : TagsType.Active;
            return (
              <StatusTag
                shape={TagsShape.Round}
                type={tagType}
                hasborder={false}
              >
                <RenderEnums enums={enums?.employeeStatus} keyName={status} />
              </StatusTag>
            );
          },
        },
        {
          title: t('Workload Rate'),
          dataIndex: 'workloadRate',
          render: (workloadRate: number) =>
            workloadRate >= 0 && `${workloadRate}%`,
        },
        {
          title: t('Office City'),
          dataIndex: 'officeCity',
          render: (officeCity: string) => {
            const officeCities = officeCity?.split(',');
            const officeCityNames = officeCities?.map(item =>
              renderEnumsWithString(item, enums?.officeCity),
            );
            return officeCityNames?.join('，');
          },
        },
        {
          title: t('Third-party assessment company user name'),
          width: 240,
          dataIndex: 'companyUserName',
        },
        ...addressAndZipCodeTableColumns,
      ],
    );
  }
  if (
    [
      InstituteTypeEnum.AssessmentCompany,
      InstituteTypeEnum.Investigator,
    ].includes(instituteType)
  ) {
    commonStaffColumns?.splice(
      commonStaffColumns?.length - 1,
      0,
      ...[
        {
          title: t('Working Type'),
          dataIndex: 'workingTypes',
          render: (workingTypes: string[]) => {
            const workingTypesI18n: string[] = [];
            workingTypes?.forEach(workingTypesItem => {
              workingTypesI18n.push(
                workingType?.[workingTypesItem]?.dictValueName,
              );
            });
            return workingTypesI18n.join(',');
          },
        },
        {
          title: t('Contract Service'),
          dataIndex: 'contractService',
          render: (contractService: string) => (
            <RenderEnums keyName={contractService} enums={enums?.yesNo} />
          ),
        },
      ],
    );
  }
  if (instituteType === InstituteTypeEnum.ExternalInsuranceCompany) {
    commonStaffColumns?.splice(
      commonStaffColumns?.length - 1,
      0,
      ...addressAndZipCodeTableColumns,
    );
  }
  return commonStaffColumns;
};
