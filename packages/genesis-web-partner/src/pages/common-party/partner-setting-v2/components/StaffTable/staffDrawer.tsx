import { useCallback, useMemo, useState, useEffect } from 'react';
import { Form, message } from 'antd';
import { useTranslation } from 'react-i18next';
import type { InstituteStaff, InstituteTypeEnum } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';
import { CommonForm } from '@/components/CommonForm/Form';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { useStaffFormFields } from './useStaffFormFields';
import { useWorkScheduleTypeContext } from '@/pages/common-party/partner-setting-v2/hooks/useContext';
import { WorkScheduleManagementEnabledInstituteList } from './Staff';

interface Props {
  visible: boolean;
  readonly: boolean;
  instituteId: number;
  editedStaff: InstituteStaff;
  onClose: (updated: boolean) => void;
  instituteType: string;
}
export const StaffDrawer = ({
  visible,
  readonly,
  instituteId,
  instituteType,
  editedStaff,
  onClose,
}: Props) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const { isTenantIncludesWorkType } = useWorkScheduleTypeContext();
  const isWorkScheduleManagementEnabled =
    WorkScheduleManagementEnabledInstituteList.includes(
      instituteType as InstituteTypeEnum,
    );
  const [submitting, setSubmitting] = useState(false);
  const [addressCascaderValue, setAddressCascaderValue] =
    useState<Record<string, string>>();

  const fields = useStaffFormFields({
    disabled: readonly,
    staff: editedStaff,
    instituteType: instituteType as InstituteTypeEnum,
    setAddressCascaderValue,
    form,
  });

  useEffect(() => {
    if (editedStaff) {
      form.setFieldsValue({
        ...editedStaff,
        officeCity: editedStaff.officeCity
          ? editedStaff.officeCity.split(',')
          : undefined,
      });
    }
  }, [editedStaff]);

  const combinedFields = useMemo(() => {
    // 如果config center配置了WORKING_TYPE就不在employee详情展示workingTypes
    return [
      ...(isTenantIncludesWorkType && isWorkScheduleManagementEnabled
        ? [...fields.filter(field => field?.key !== 'workingTypes')]
        : [...fields]),
    ];
  }, [fields, readonly]);

  const onCloseDrawer = useCallback((updated?: boolean) => {
    form.resetFields();
    onClose(updated);
  }, []);

  const onSubmit = useCallback(() => {
    form.validateFields().then(values => {
      setSubmitting(true);
      let submitReq: Promise<InstituteStaff>;
      if (editedStaff) {
        submitReq = ChannelService.updateInstituteStaff(
          instituteId,
          editedStaff.id,
          {
            ...values,
            ...addressCascaderValue,
            officeCity: values?.officeCity?.join(','),
          },
        );
      } else {
        submitReq = ChannelService.addInstituteStaff(instituteId, {
          instituteId: instituteId,
          ...values,
          ...addressCascaderValue,
          officeCity: values?.officeCity?.join(','),
          instituteType,
        });
      }
      submitReq
        .then(() => {
          message.success(t('Save successfully'));
          onCloseDrawer(true);
        })
        .catch((error: Error) => message.error(error?.message))
        .finally(() => setSubmitting(false));
    });
  }, [instituteId, editedStaff, addressCascaderValue, onCloseDrawer]);

  return (
    <DrawerForm
      title={t('Staff')}
      visible={visible}
      closable={false}
      maskClosable={false}
      width={752}
      onClose={onCloseDrawer}
      onSubmit={onSubmit}
      cancelText={t('Cancel')}
      sendText={t('Confirm')}
      submitBtnShow={!readonly}
      submitBtnProps={{ loading: submitting }}
    >
      <CommonForm form={form} fields={combinedFields} />
    </DrawerForm>
  );
};
