import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, message } from 'antd';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { InstituteStaff, InstituteTypeEnum } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';

import { CommonForm } from '@/components/CommonForm/Form';
import {
  useApproveModeContext,
  useWorkScheduleTypeContext,
} from '@/pages/common-party/partner-setting-v2/hooks/useContext';

import { WorkScheduleManagementEnabledInstituteList } from './Staff';
import { useStaffFormFields } from './useStaffFormFields';

interface Props {
  visible: boolean;
  readonly: boolean;
  instituteId: number;
  editedStaff: InstituteStaff;
  onClose: (updated: boolean) => void;
  onSubmit: (values: any) => void;
  instituteType: string;
  dynamicFields: Record<string, any>[];
}
export const StaffDrawer = ({
  visible,
  readonly,
  instituteId,
  instituteType,
  editedStaff,
  onClose,
  onSubmit,
  dynamicFields,
}: Props) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const { isTenantIncludesWorkType } = useWorkScheduleTypeContext();
  const isWorkScheduleManagementEnabled = WorkScheduleManagementEnabledInstituteList.includes(
    instituteType as InstituteTypeEnum
  );
  const [submitting, setSubmitting] = useState(false);
  const [addressCascaderValue, setAddressCascaderValue] = useState<Record<string, string>>();
  const { enableApproval } = useApproveModeContext();

  const fields = useStaffFormFields({
    disabled: readonly,
    staff: editedStaff,
    instituteType: instituteType as InstituteTypeEnum,
    setAddressCascaderValue,
    form,
  });

  useEffect(() => {
    if (editedStaff) {
      form.setFieldsValue({
        ...editedStaff,
        officeCity: editedStaff.officeCity ? editedStaff.officeCity.split(',') : undefined,
      });
    }
  }, [editedStaff]);

  const combinedFields = useMemo(() => {
    // 如果config center配置了WORKING_TYPE就不在employee详情展示workingTypes
    return [
      ...(isTenantIncludesWorkType && isWorkScheduleManagementEnabled
        ? [...fields.filter(field => field?.key !== 'workingTypes')]
        : [...fields]),
    ];
  }, [fields, readonly]);

  const onCloseDrawer = useCallback((updated?: boolean) => {
    form.resetFields();
    onClose(updated);
  }, []);

  const submit = useCallback(() => {
    form.validateFields().then(values => {
      if (enableApproval) {
        onSubmit({
          ...values,
          ...addressCascaderValue,
          status: 'ON_DUTY',
          officeCity: values?.officeCity?.join(','),
          instituteType,
        });
        // 更新成功不需要再调查询接口，所以传false
        onCloseDrawer(false);
        return;
      }
      setSubmitting(true);
      let submitReq: Promise<InstituteStaff>;
      if (editedStaff) {
        submitReq = ChannelService.updateInstituteStaff(instituteId, editedStaff.id, {
          ...values,
          ...addressCascaderValue,
          officeCity: values?.officeCity?.join(','),
        });
      } else {
        submitReq = ChannelService.addInstituteStaff(instituteId, {
          instituteId: instituteId,
          ...values,
          ...addressCascaderValue,
          officeCity: values?.officeCity?.join(','),
          instituteType,
        });
      }
      submitReq
        .then(() => {
          message.success(t('Save successfully'));
          onCloseDrawer(true);
        })
        .catch((error: Error) => message.error(error?.message))
        .finally(() => setSubmitting(false));
    });
  }, [instituteId, editedStaff, addressCascaderValue, onCloseDrawer, onSubmit]);

  return (
    <DrawerForm
      title={t('Staff')}
      visible={visible}
      closable={false}
      maskClosable={false}
      width={1024}
      onClose={onCloseDrawer}
      onSubmit={submit}
      cancelText={t('Cancel')}
      sendText={t('Confirm')}
      submitBtnShow={!readonly}
      submitBtnProps={{ loading: submitting }}
    >
      <CommonForm form={form} fields={[...combinedFields, ...dynamicFields]} />
    </DrawerForm>
  );
};
