import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, message } from 'antd';

import { cloneDeep } from 'lodash-es';

import { AddressComponent } from 'genesis-web-component/lib/components/AddressComponent';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { BizDictItem, FactorsType, InstituteStaff } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';

import { Account } from '@/components/Account';
import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import { useApproveModeContext } from '@/pages/common-party/partner-setting-v2/hooks/useContext';

interface Props {
  visible: boolean;
  readonly: boolean;
  instituteId: number;
  editedStaff: InstituteStaff;
  onClose: (updated: boolean) => void;
  onSubmit: (values: any) => void;
  instituteType: string;
  dynamicFields: Record<string, any>[];
  staticFields: Record<string, any>[];
}
export const StaffDrawer = ({
  visible,
  readonly,
  instituteId,
  instituteType,
  editedStaff,
  onClose,
  onSubmit,
  dynamicFields,
  staticFields,
}: Props) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  const [addressCascaderValue, setAddressCascaderValue] = useState<Record<string, string>>();
  const { enableApproval } = useApproveModeContext();

  const combinedFields = useMemo(() => {
    const [clonedAddressStaticFields, clonedNotAddressStaticFields] = staticFields.reduce(
      (acc, field) => {
        const isAddressField = field.key?.includes('address') || field.key === 'zipCode';
        acc[isAddressField ? 0 : 1].push(cloneDeep(field));
        return acc;
      },
      [[], []]
    ) as [Record<string, any>[], Record<string, any>[]];

    clonedNotAddressStaticFields?.splice(9, 0, {
      type: FieldType.Customize,
      customerDom: (
        <AddressComponent
          disabled={readonly}
          onCascaderValueChange={setAddressCascaderValue}
          form={form}
          cascaderCol={24}
          colProps={{ span: 12 }}
          inputWidth="100%"
          selfCustomFields={
            clonedAddressStaticFields.map(field => {
              const { key, label, ctrlProps, rules } = field;

              return {
                factorCode: key,
                factorName: label,
                isRequired: rules?.length ? 1 : 0,
                options: ctrlProps?.options,
              };
            }) as FactorsType[]
          }
        />
      ),
    });
    clonedNotAddressStaticFields?.splice(-2, 1, {
      type: FieldType.Customize,
      customerDom: (
        <Account initialValue={editedStaff} propForm={form} disabled={readonly} relyOn="staffName" name="Graphene" />
      ),
    });

    return clonedNotAddressStaticFields?.map(field => {
      const props = field.ctrlProps ?? {};
      switch (field.key) {
        case 'countryCode':
          props.options = props.options?.map((option: BizDictItem) => ({
            ...option,
            label: (
              <span className="flex justify-between">
                {option.label}
                <span>{option.itemExtend2}</span>
              </span>
            ),
          }));
          props.filterOption = (input: string, option: BizDictItem) =>
            (option?.itemExtend2 ?? '').toLowerCase().includes(input.toLowerCase());
          break;
        case 'workloadRate':
          props.addonAfter = '%';
          break;
      }
      field.col = 12;
      props.style = { width: '100%' };
      return field;
    });
  }, [staticFields, readonly, form, editedStaff]);

  const onCloseDrawer = useCallback((updated?: boolean) => {
    form.resetFields();
    onClose(updated);
  }, []);

  const submit = useCallback(() => {
    form.validateFields().then(values => {
      if (enableApproval) {
        onSubmit({
          ...values,
          ...addressCascaderValue,
          status: 'ON_DUTY',
          instituteType,
        });
        // 更新成功不需要再调查询接口，所以传false
        onCloseDrawer(false);
        return;
      }
      setSubmitting(true);
      let submitReq: Promise<InstituteStaff>;
      if (editedStaff) {
        submitReq = ChannelService.updateInstituteStaff(instituteId, editedStaff.id, {
          ...values,
          ...addressCascaderValue,
        });
      } else {
        submitReq = ChannelService.addInstituteStaff(instituteId, {
          instituteId: instituteId,
          ...values,
          ...addressCascaderValue,
          instituteType,
        });
      }
      submitReq
        .then(() => {
          message.success(t('Save successfully'));
          onCloseDrawer(true);
        })
        .catch((error: Error) => message.error(error?.message))
        .finally(() => setSubmitting(false));
    });
  }, [instituteId, editedStaff, addressCascaderValue, onCloseDrawer, onSubmit]);

  return (
    <DrawerForm
      title={t('Staff')}
      visible={visible}
      closable={false}
      maskClosable={false}
      width={1024}
      onClose={onCloseDrawer}
      onSubmit={submit}
      cancelText={t('Cancel')}
      sendText={t('Confirm')}
      submitBtnShow={!readonly}
      submitBtnProps={{ loading: submitting }}
    >
      <CommonForm
        form={form}
        initialValues={editedStaff}
        fields={[...combinedFields, ...dynamicFields]}
        clearOnDestroy
      />
    </DrawerForm>
  );
};
