import type { QueryFieldsType } from '@/components/CommonForm';
import { FieldType } from '@/components/CommonForm';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { EMAIL_PATTERN } from '@/utils/utils';
import type { InstituteStaff, BizDictItem } from 'genesis-web-service';
import { InstituteTypeEnum } from 'genesis-web-service';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { PhoneWithCountryCode } from '@/pages/common-party/partner-setting-v2/components/PhoneWithCountryCode';
import { AddressComponent } from 'genesis-web-component/lib/components/AddressComponent';
import type { FormInstance } from 'antd';
import { Account } from '@/components/Account';
import { AddressFormConfig } from '@/pages/common-party/utils/constants';

export const useStaffFormFields = ({
  disabled,
  staff,
  instituteType,
  setAddressCascaderValue,
  form,
}: {
  disabled: boolean;
  staff: InstituteStaff;
  instituteType: InstituteTypeEnum;
  setAddressCascaderValue: (result: Record<string, string>) => void;
  form: FormInstance;
}): Partial<QueryFieldsType>[] => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  const commonStaffFields = useMemo(
    () => [
      {
        key: 'staffName',
        label: t('Staff Name'),
        type: FieldType.Input,
        col: 12,
        ctrlProps: {
          allowClear: true,
          disabled,
          style: { width: '100%' },
        },
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Staff Name'),
            }),
          },
        ],
      },
      {
        key: 'staffCode',
        label: t('Staff Code'),
        type: FieldType.Input,
        col: 12,
        ctrlProps: {
          allowClear: true,
          disabled: disabled || !!staff?.staffCode,
          style: { width: '100%' },
        },
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Staff Code'),
            }),
          },
        ],
      },
      {
        key: 'email',
        label: t('Email'),
        col: 12,
        rules: [
          {
            pattern: EMAIL_PATTERN,
            message: t('Please enter a valid character'),
          },
        ],
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        type: FieldType.Customize,
        key: 'customizeDom',
        customerDom: (
          <PhoneWithCountryCode
            countryCodeKey="countryCode"
            phoneKey="phoneNo"
            label={t('Phone No.')}
            disabled={disabled}
            colSpan={12}
            formStyle={{ width: '100%' }}
          />
        ),
      },
      {
        key: 'remark',
        label: t('Remark'),
        type: FieldType.TextArea,
        col: 16,
        ctrlProps: {
          disabled,
        },
      },
    ],
    [disabled, staff],
  );
  if (instituteType === InstituteTypeEnum.AssessmentCompany) {
    commonStaffFields?.splice(
      commonStaffFields?.length - 1,
      0,
      ...[
        {
          key: 'workingTypes',
          label: t('Working Type'),
          col: 12,
          type: FieldType.Select,
          ctrlProps: {
            mode: 'multiple',
            options: enums?.workingType,
            disabled,
            style: { width: '100%' },
            allowClear: true,
            showArrow: true,
          },
        },
        {
          key: 'contractService',
          label: t('Contract Service'),
          col: 12,
          type: FieldType.Select,
          ctrlProps: {
            options: enums?.yesNo,
            disabled,
            style: { width: '100%' },
            allowClear: true,
          },
        },
        {
          key: 'workloadRate',
          label: t('Workload Rate'),
          col: 12,
          type: FieldType.InputNumber,
          ctrlProps: {
            min: 0,
            disabled,
            addonAfter: '%',
            style: { width: '100%' },
          },
        },
        {
          key: 'officeCity',
          label: t('Office City'),
          col: 12,
          type: FieldType.Select,
          ctrlProps: {
            options: enums?.officeCity,
            mode: 'multiple',
            showArrow: true,
            disabled,
            style: { width: '100%' },
            allowClear: true,
          },
        },
        {
          key: 'companyUserName',
          label: t('Third-party assessment company user name'),
          col: 12,
          ctrlProps: {
            disabled,
            style: { width: '100%' },
            allowClear: true,
          },
        },
        {
          type: FieldType.Customize,
          customerDom: (
            <AddressComponent
              disabled={disabled}
              initialValue={staff ?? {}}
              onCascaderValueChange={setAddressCascaderValue}
              form={form}
              cascaderCol={24}
              colProps={{ span: 12 }}
              inputWidth="100%"
            />
          ),
        },
        {
          type: FieldType.Customize,
          customerDom: (
            <Account
              initialValue={staff ?? {}}
              propForm={form}
              disabled={disabled}
              relyOn="staffName"
              name="Graphene"
            />
          ),
        },
      ],
    );
  }
  if (instituteType === InstituteTypeEnum.Investigator) {
    commonStaffFields?.splice(
      commonStaffFields?.length - 1,
      0,
      ...[
        {
          key: 'workingTypes',
          label: t('Working Type'),
          col: 12,
          type: FieldType.Select,
          ctrlProps: {
            mode: 'multiple',
            options: enums?.workingType,
            disabled,
            style: { width: '100%' },
            allowClear: true,
            showArrow: true,
          },
        },
        {
          key: 'contractService',
          label: t('Contract Service'),
          col: 12,
          type: FieldType.Select,
          ctrlProps: {
            options: enums?.yesNo,
            disabled,
            style: { width: '100%' },
            allowClear: true,
          },
        },
        {
          key: 'staffAccount',
          type: FieldType.Customize,
          customerDom: (
            <Account
              initialValue={staff ?? {}}
              propForm={form}
              disabled={disabled}
              relyOn="staffName"
              name="Graphene"
            />
          ),
        },
      ],
    );
  }

  if (instituteType === InstituteTypeEnum.ExternalInsuranceCompany) {
    commonStaffFields?.splice(commonStaffFields?.length - 1, 0, {
      key: 'AddressComponent',
      type: FieldType.Customize,
      customerDom: (
        <AddressComponent
          disabled={disabled}
          initialValue={staff ?? {}}
          onCascaderValueChange={setAddressCascaderValue}
          form={form}
          cascaderCol={24}
          colProps={{ span: 12 }}
          inputWidth="100%"
        />
      ),
    });
  }
  return commonStaffFields;
};
