/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { UploadProps } from 'antd';
import { Button, Divider, Dropdown, Upload, message } from 'antd';

import { useRequest } from 'ahooks';
import { cloneDeep } from 'lodash-es';
import type { Moment } from 'moment';

import type { ColumnsType } from '@zhongan/nagrand-ui';
import {
  CommonIconAction,
  DeleteAction,
  EditAction,
  Icon,
  SimpleSectionHeader,
  StatusTag,
  Table,
  TableActionsContainer,
  ViewAction,
} from '@zhongan/nagrand-ui';

import type { AddressItem, BizDictItem, InstituteStaff } from 'genesis-web-service';
import { ChannelService, DownloadOrUploadType, InstituteTypeEnum, YearlyWorkScheduleStatus } from 'genesis-web-service';
import type { InstituteStaffScheduleRequest } from 'genesis-web-service/service-types/channel-types/package';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { WorkSchedule } from '@/components/Icons';
import { PersonalWorkScheduleDrawer } from '@/components/PersonalWorkScheduleDrawer';
import { WorkScheduleManagementDrawer } from '@/components/WorkScheduleMgmtDrawer';
import { useAddressI18nList } from '@/hooks/useAddressI18nList';
import {
  EntityType,
  covertPartnerType2SchemaPartnerType,
  useGenericSchemaFormItemFields,
} from '@/hooks/useGenericSchemaFormItemFields';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { useApproveModeContext } from '@/pages/common-party/partner-setting-v2/hooks/useContext';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import type { PartnerTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';
import type { SchemaProps } from '@/types/common';
import type { WorkScheduleType } from '@/types/common-party';
import { CustomerType, DefaultTablePagination } from '@/utils/constants';
import { transferSchemaToTableProp } from '@/utils/utils';

import { StaffDrawer } from './staffDrawer';
import styles from './style.scss';

export const WorkScheduleManagementEnabledInstituteList = [
  InstituteTypeEnum.AssessmentCompany,
  InstituteTypeEnum.Investigator,
];

const StaffFileKeyMapping: Record<string, string> = {
  [InstituteTypeEnum.AssessmentCompany]: DownloadOrUploadType.AssessmentCompanyStaff,
  [InstituteTypeEnum.Investigator]: DownloadOrUploadType.InvestigatorStaff,
  [InstituteTypeEnum.ExternalInsuranceCompany]: DownloadOrUploadType.ExternalInsuranceCompanyStaff,
};

interface Props {
  readonly: boolean;
  instituteId: number;
  instituteType: InstituteTypeEnum;
  isApprovalPage?: boolean;
  staffs?: InstituteStaff[];
}

export const StaffTable = forwardRef(
  ({ readonly, instituteId, instituteType, isApprovalPage = false, staffs }: Props, ref) => {
    const { t } = useTranslation('partner');

    const [pagination, setPagination] = useState(DefaultTablePagination);
    const [visible, setVisible] = useState(false);
    const [uploading, setUploading] = useState(false);
    const [downloading, setDownloading] = useState(false);
    const [editedStaff, setEditedStaff] = useState<InstituteStaff>();
    const [staffCode, setStaffCode] = useState<string>();
    const [selectedScheduleYear, setSelectedScheduleYear] = useState<string>();
    const [personalScheduleVisible, setPersonalScheduleVisible] = useState(false);
    const { canEdit, canView } = usePartnerSettingPermission();
    const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
    const { enableApproval } = useApproveModeContext();
    const isWorkScheduleManagementEnabled = WorkScheduleManagementEnabledInstituteList.includes(instituteType);
    const [staffList, setStaffList] = useState<InstituteStaff[]>([]);

    const { formItems: staticFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'STATIC',
      category: 'STAFF',
      disabled: readonly,
      type: covertPartnerType2SchemaPartnerType(instituteType as unknown as PartnerTypeEnum),
      entityType: EntityType.PARTNER,
    });

    const { formItems: dynamicFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'DYNAMIC',
      category: 'STAFF',
      disabled: readonly,
      type: covertPartnerType2SchemaPartnerType(instituteType as unknown as PartnerTypeEnum),
      entityType: EntityType.PARTNER,
    });

    useEffect(() => {
      if (!isApprovalPage) {
        return;
      }

      setStaffList(staffs);
    }, [staffs, isApprovalPage]);

    const {
      data: staffResp,
      loading,
      run: queryInstituteStaffs,
    } = useRequest(
      () => {
        if (instituteId) {
          return ChannelService.queryInstituteStaffs(instituteId, {
            pageIndex: pagination.current - 1,
            pageSize: pagination.pageSize,
            instituteType,
          }).then(res => {
            if (enableApproval) {
              setStaffList(res.data);
            }
            return res;
          });
        }
      },
      {
        refreshDeps: [pagination, instituteType, instituteId],
      }
    );
    const tableData = enableApproval ? staffList : staffResp?.data;

    const addressI18nList = useAddressI18nList(tableData as unknown as AddressItem[], CustomerType.COMPANY);

    useImperativeHandle(ref, () => {
      return staffList;
    });

    const onEdit = useCallback(staff => {
      setVisible(true);
      setEditedStaff(staff);
    }, []);

    const onClickPersonalWorkSchedule = useCallback((code: string) => {
      setStaffCode(code);
      setPersonalScheduleVisible(true);
    }, []);

    const onDelete = useCallback(
      (staffId: number) => {
        if (enableApproval) {
          const staffIndex = staffList.findIndex(item => item.id === staffId);

          staffList.splice(staffIndex, 1);

          setStaffList([...staffList]);
          return;
        }
        return ChannelService.deleteInstituteStaff(instituteId, staffId)
          .then(() => {
            message.success(t('Delete successfully'));
            setPagination({ ...DefaultTablePagination });
          })
          .catch((error: Error) => {
            message.error(error?.message);
          });
      },
      [instituteId, staffList, enableApproval]
    );

    const onClose = useCallback(
      (updated: boolean) => {
        setVisible(false);
        setEditedStaff(undefined);
        if (updated) {
          if (editedStaff) {
            // 编辑成功
            queryInstituteStaffs();
          } else {
            // 新增成功回到第一页
            setPagination({ ...DefaultTablePagination });
          }
        }
      },
      [queryInstituteStaffs, editedStaff, enableApproval]
    );

    const onDownload = useCallback(() => {
      setDownloading(true);
      ChannelService.download<{
        id: number;
        type: string;
      }>({
        id: instituteId,
        type: StaffFileKeyMapping[instituteType],
      })
        .then(downloadFile)
        .catch((error: Error) => message.error(error?.message || t('Download failed')))
        .finally(() => setDownloading(false));
    }, [instituteId, instituteType]);

    const uploadProps = useMemo(
      () =>
        getUploadPropsNew(
          `/api/channel/v2/file/upload/?id=${instituteId}&type=${StaffFileKeyMapping[instituteType]}`,
          () => setPagination({ ...DefaultTablePagination }),
          setUploading
        ),
      [instituteId, instituteType]
    );

    const combineColumns: ColumnsType<InstituteStaff> = useMemo(() => {
      return [
        ...transferSchemaToTableProp([...staticFields, ...dynamicFields] as SchemaProps[], addressI18nList),
        {
          title: t('Actions'),
          key: 'actions',
          fixed: 'right',
          align: 'right',
          render: (staff: InstituteStaff) => (
            <TableActionsContainer>
              {readonly ? (
                <ViewAction onClick={() => onEdit(staff)} />
              ) : (
                <>
                  <DeleteAction onClick={() => onDelete(staff.id)} doubleConfirmType="modal" />
                  <EditAction onClick={() => onEdit(staff)} />
                </>
              )}
              {isWorkScheduleManagementEnabled && (
                <CommonIconAction
                  tooltipTitle={t('Personal Work Schedule')}
                  disabled={!canView && !canEdit}
                  icon={<WorkSchedule />}
                  onClick={() => onClickPersonalWorkSchedule(staff.staffCode)}
                />
              )}
            </TableActionsContainer>
          ),
        },
      ];
    }, [enums, readonly, canView, canEdit, onEdit, onDelete, onClickPersonalWorkSchedule]);

    // 获取所有年份维护的排班表状态
    const { data: allYearHistory, run: queryAllYearsEmployeeHistory } = useRequest(
      () => {
        if (instituteId && isWorkScheduleManagementEnabled) {
          return ChannelService.queryAllYearsStaffHistory(instituteId, instituteType);
        }
      },
      {
        refreshDeps: [instituteId],
      }
    );

    const scheduleMenuItems = useMemo(
      () =>
        allYearHistory?.map(history => {
          return {
            label: (
              <div className="flex items-center justify-between">
                {history.year}
                <StatusTag
                  statusI18n={t(history.status)}
                  type={history.status === YearlyWorkScheduleStatus.Done ? 'no-status' : 'info'}
                />
              </div>
            ),
            key: history.year,
          };
        }),
      [allYearHistory]
    );

    // 关闭总表drawer
    const onCloseWorkScheduleDrawer = useCallback(
      (updated: boolean) => {
        setSelectedScheduleYear(undefined);
        if (updated) {
          setPagination({ ...DefaultTablePagination });
          queryAllYearsEmployeeHistory();
        }
      },
      [queryAllYearsEmployeeHistory]
    );

    // 关闭个人排班表drawer
    const onClosePersonalScheduleDrawer = useCallback(
      (updated: boolean) => {
        setPersonalScheduleVisible(false);
        if (updated) {
          queryInstituteStaffs();
        }
      },
      [queryInstituteStaffs]
    );

    return (
      <>
        {(enableApproval || instituteId) && (
          <>
            {isApprovalPage ? (
              <SimpleSectionHeader type="h5" weight="bold" style={{ marginTop: 8, marginBottom: 16 }}>
                {t('Staff')}
              </SimpleSectionHeader>
            ) : (
              <>
                <Divider className="my-6" />
                <SimpleSectionHeader type="h5" weight="bold">
                  {t('Staff')}
                </SimpleSectionHeader>
                {!readonly && (
                  <div className={styles.staffActionsContainer}>
                    <Button icon={<Icon type="add" />} onClick={() => setVisible(true)} disabled={readonly}>
                      {t('Add New')}
                    </Button>
                    {isWorkScheduleManagementEnabled && (
                      <Dropdown
                        menu={{
                          items: scheduleMenuItems,
                          onClick: ({ key: year }) => setSelectedScheduleYear(year),
                        }}
                        trigger={['click']}
                        overlayStyle={{ width: 292 }}
                        disabled={!allYearHistory?.length}
                        getPopupContainer={triggerNode => triggerNode?.parentElement}
                      >
                        <Button icon={<WorkSchedule />} className="margin-left-16">
                          {t('Work Schedule Management')}
                        </Button>
                      </Dropdown>
                    )}
                    {!enableApproval && (
                      <div>
                        <Upload {...(uploadProps as UploadProps)}>
                          <Button
                            icon={<Icon type="upload" />}
                            disabled={readonly}
                            loading={uploading}
                            style={{ marginRight: 6 }}
                          >
                            {t('Upload')}
                          </Button>
                        </Upload>
                        <Button icon={<Icon type="download" />} onClick={onDownload} loading={downloading}>
                          {t('Download')}
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
            <Table
              dataSource={tableData}
              columns={combineColumns}
              loading={loading}
              rowKey={row => row.id || row.key}
              scroll={{ x: 'max-content' }}
              bordered={false}
              style={{ marginBottom: 16 }}
              pagination={
                enableApproval
                  ? { size: 'small' }
                  : {
                      size: 'small',
                      total: staffResp?.totalElements || 0,
                      current: pagination.current,
                      pageSize: pagination.pageSize,
                      onChange: (current: number, pageSize: number) =>
                        setPagination(old => ({
                          ...old,
                          current,
                          pageSize,
                        })),
                    }
              }
            />
            <StaffDrawer
              visible={visible}
              readonly={readonly}
              editedStaff={editedStaff}
              instituteId={instituteId}
              instituteType={instituteType}
              onClose={onClose}
              onSubmit={values => {
                if (editedStaff) {
                  const staffIndex = staffList.findIndex(item => item === editedStaff);

                  staffList[staffIndex] = { ...values };
                  setEditedStaff(undefined);

                  setStaffList([...staffList]);
                } else {
                  values.key = Math.random();
                  staffList.push(values);
                  setStaffList([...staffList]);
                }
              }}
              dynamicFields={dynamicFields}
              staticFields={staticFields}
            />
            {isWorkScheduleManagementEnabled && (
              <>
                <WorkScheduleManagementDrawer
                  year={selectedScheduleYear}
                  id={instituteId}
                  visible={!!selectedScheduleYear}
                  readonly={readonly}
                  type={instituteType as unknown as WorkScheduleType}
                  onClose={onCloseWorkScheduleDrawer}
                />
                <PersonalWorkScheduleDrawer
                  isApprovalPage={isApprovalPage}
                  personCode={staffCode}
                  editRecord={staffList.find(item => item.staffCode === staffCode)}
                  onSubmit={(selectedDate: Moment, values: InstituteStaffScheduleRequest) => {
                    const changedMonth = selectedDate.format('YYYY-MM');
                    const editRecordIndex = staffList.findIndex(item => item.staffCode === staffCode);
                    if (editRecordIndex < 0) {
                      return;
                    }
                    const editRecord = cloneDeep(staffList[editRecordIndex]);

                    if (editRecord.staffSchedule) {
                      // 每次配置Schedule都是一个月一配置，编辑的时候，先删掉之前配置月份的数据，然后存上最新配置的数据
                      editRecord.staffSchedule.schedules = editRecord.staffSchedule.schedules.filter(
                        item => !item.dateStr.startsWith(changedMonth)
                      );

                      editRecord.staffSchedule.schedules.push(...values.schedules);
                    } else {
                      editRecord.staffSchedule = values;
                    }
                    staffList[editRecordIndex] = editRecord;
                    setStaffList([...staffList]);
                  }}
                  visible={personalScheduleVisible}
                  readonly={readonly}
                  type={instituteType as unknown as WorkScheduleType}
                  onClose={onClosePersonalScheduleDrawer}
                />
              </>
            )}
          </>
        )}
      </>
    );
  }
);
