/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-11-08 19:28:24
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-11-16 11:15:45
 * @Description:
 */
import { useCallback, useMemo, useState } from 'react';
import { Button, message, Upload, Tooltip, Dropdown, Divider } from 'antd';
import {
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import type { TableColumnProps, UploadProps } from 'antd';
import { useRequest } from 'ahooks';
import { useTranslation } from 'react-i18next';
import type { InstituteStaff, BizDictItem } from 'genesis-web-service';
import {
  ChannelService,
  YearlyWorkScheduleStatus,
  DownloadOrUploadType,
  InstituteTypeEnum,
} from 'genesis-web-service';
import { NoData } from 'genesis-web-component/lib/components/NoData';
import {
  DeleteOutline,
  ViewSquareOutline,
  EditOutline,
  WorkSchedule,
} from '@/components/Icons';
import { PaginationComponent } from '@/components/Pagination';
import { StatusTag } from '@/components/StatusTag';
import { DefaultTablePagination, TagsShape, TagsType } from '@/utils/constants';
import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import { WorkScheduleManagementDrawer } from '@/components/WorkScheduleMgmtDrawer';
import { PersonalWorkScheduleDrawer } from '@/components/PersonalWorkScheduleDrawer';
import { StaffDrawer } from './staffDrawer';

import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import type { WorkScheduleType } from '@/types/common-party';
import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import { renderEnumsWithString } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { useWorkScheduleTypeContext } from '@/pages/common-party/partner-setting-v2/hooks/useContext';
import { Table, TableActionsContainer, TextBody } from '@zhongan/nagrand-ui';
import commonStyles from '../../style.scss';
import styles from './style.scss';
import { useStaffColumns } from './useStaffColumns';

export const WorkScheduleManagementEnabledInstituteList = [
  InstituteTypeEnum.AssessmentCompany,
  InstituteTypeEnum.Investigator,
];

const StaffFileKeyMapping: Record<string, string> = {
  [InstituteTypeEnum.AssessmentCompany]:
    DownloadOrUploadType.AssessmentCompanyStaff,
  [InstituteTypeEnum.Investigator]: DownloadOrUploadType.InvestigatorStaff,
  [InstituteTypeEnum.ExternalInsuranceCompany]:
    DownloadOrUploadType.ExternalInsuranceCompanyStaff,
};

interface Props {
  readonly: boolean;
  instituteId: number;
  instituteType: InstituteTypeEnum;
}

export const StaffTable = ({ readonly, instituteId, instituteType }: Props) => {
  const { t } = useTranslation('partner');

  const [pagination, setPagination] = useState(DefaultTablePagination);
  const [visible, setVisible] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [editedStaff, setEditedStaff] = useState<InstituteStaff>();
  const [staffCode, setStaffCode] = useState<string>();
  const [selectedScheduleYear, setSelectedScheduleYear] = useState<string>();
  const [personalScheduleVisible, setPersonalScheduleVisible] = useState(false);
  const { canEdit, canView } = usePartnerSettingPermission();
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const { isTenantIncludesWorkType } = useWorkScheduleTypeContext();
  const isWorkScheduleManagementEnabled =
    WorkScheduleManagementEnabledInstituteList.includes(instituteType);

  const {
    data: staffResp,
    loading,
    run: queryInstituteStaffs,
  } = useRequest(
    () => {
      if (instituteId) {
        return ChannelService.queryInstituteStaffs(instituteId, {
          pageIndex: pagination.current - 1,
          pageSize: pagination.pageSize,
          instituteType,
        });
      }
    },
    {
      refreshDeps: [pagination, instituteType, instituteId],
    },
  );

  const onEdit = useCallback(staff => {
    setVisible(true);
    setEditedStaff(staff);
  }, []);

  const onClickPersonalWorkSchedule = useCallback((code: string) => {
    setStaffCode(code);
    setPersonalScheduleVisible(true);
  }, []);

  const onDelete = useCallback(
    (staffId: number) => {
      return ChannelService.deleteInstituteStaff(instituteId, staffId)
        .then(() => {
          message.success(t('Delete successfully'));
          setPagination({ ...DefaultTablePagination });
        })
        .catch((error: Error) => {
          message.error(error?.message);
        });
    },
    [instituteId],
  );

  const onClose = useCallback(
    (updated: boolean) => {
      setVisible(false);
      setEditedStaff(undefined);
      if (updated) {
        if (editedStaff) {
          // 编辑成功
          queryInstituteStaffs();
        } else {
          // 新增成功回到第一页
          setPagination({ ...DefaultTablePagination });
        }
      }
    },
    [queryInstituteStaffs, editedStaff],
  );

  const onDownload = useCallback(() => {
    setDownloading(true);
    ChannelService.download<{
      id: number;
      type: string;
    }>({
      id: instituteId,
      type: StaffFileKeyMapping[instituteType],
    })
      .then(downloadFile)
      .catch((error: Error) =>
        message.error(error?.message || t('Download failed')),
      )
      .finally(() => setDownloading(false));
  }, [instituteId, instituteType]);

  const uploadProps = useMemo(
    () =>
      getUploadPropsNew(
        `/api/channel/v2/file/upload/?id=${instituteId}&type=${StaffFileKeyMapping[instituteType]}`,
        () => setPagination({ ...DefaultTablePagination }),
        setUploading,
      ),
    [instituteId, instituteType],
  );

  const columns = useStaffColumns(instituteType, staffResp?.data);

  const combineColumns: TableColumnProps<InstituteStaff>[] = useMemo(() => {
    const clonedColumns =
      isTenantIncludesWorkType && isWorkScheduleManagementEnabled
        ? [
          ...columns?.map(column => {
            const clonedColumn = { ...column };
            if (column?.dataIndex === 'workingTypes') {
              clonedColumn.dataIndex = 'scheduleWorkingTypes';
              clonedColumn.render = (scheduleWorkingTypes: string[]) => (
                <ComponentWithFallback>
                  {scheduleWorkingTypes?.length &&
                      scheduleWorkingTypes
                        ?.map(workingType =>
                          renderEnumsWithString(
                            workingType,
                            enums?.workingType,
                          ),
                        )
                        ?.join(', ')}
                </ComponentWithFallback>
              );
            }
            return clonedColumn;
          }),
        ]
        : [...columns];
    return [
      ...clonedColumns,
      {
        title: t('Actions'),
        key: 'actions',
        fixed: 'right',
        align: 'right',
        render: (staff: InstituteStaff) => (
          <div className="table-actions">
            <TableActionsContainer>
              {readonly ? (
                <Tooltip title={t('View')}>
                  <Button
                    icon={<ViewSquareOutline />}
                    type="link"
                    onClick={() => onEdit(staff)}
                  />
                </Tooltip>
              ) : (
                <>
                  <DeleteConfirm onOk={() => onDelete(staff.id)}>
                    <Tooltip title={t('Delete')}>
                      <Button icon={<DeleteOutline />} type="link" />
                    </Tooltip>
                  </DeleteConfirm>
                  <Tooltip title={t('Edit')}>
                    <Button
                      icon={<EditOutline />}
                      type="link"
                      onClick={() => onEdit(staff)}
                    />
                  </Tooltip>
                </>
              )}
              {isWorkScheduleManagementEnabled && (
                <Tooltip title={t('Personal Work Schedule')}>
                  <Button
                    disabled={!canView && !canEdit}
                    type="link"
                    icon={<WorkSchedule />}
                    onClick={() => onClickPersonalWorkSchedule(staff.staffCode)}
                  />
                </Tooltip>
              )}
            </TableActionsContainer>
          </div>
        ),
      },
    ];
  }, [
    enums,
    columns,
    readonly,
    canView,
    canEdit,
    onEdit,
    onDelete,
    onClickPersonalWorkSchedule,
  ]);

  // 获取所有年份维护的排班表状态
  const { data: allYearHistory, run: queryAllYearsEmployeeHistory } =
    useRequest(
      () => {
        if (instituteId && isWorkScheduleManagementEnabled) {
          return ChannelService.queryAllYearsStaffHistory(
            instituteId,
            instituteType,
          );
        }
      },
      {
        refreshDeps: [instituteId],
      },
    );

  const scheduleMenuItems = useMemo(
    () =>
      allYearHistory?.map(history => {
        const type =
          history.status === YearlyWorkScheduleStatus.Done
            ? TagsType.Default
            : TagsType.Regular;

        return {
          label: (
            <div className={styles.rosterMenuItem}>
              {history.year}
              <StatusTag
                hasDot={false}
                hasborder={false}
                shape={TagsShape.Round}
                color={
                  history.status === YearlyWorkScheduleStatus.Done
                    ? commonStyles.primaryDisabledColor
                    : null
                }
                type={type}
              >
                {t(history.status)}
              </StatusTag>
            </div>
          ),
          key: history.year,
        };
      }),
    [allYearHistory],
  );

  // 关闭总表drawer
  const onCloseWorkScheduleDrawer = useCallback(
    (updated: boolean) => {
      setSelectedScheduleYear(undefined);
      if (updated) {
        setPagination({ ...DefaultTablePagination });
        queryAllYearsEmployeeHistory();
      }
    },
    [queryAllYearsEmployeeHistory],
  );

  // 关闭个人排班表drawer
  const onClosePersonalScheduleDrawer = useCallback(
    (updated: boolean) => {
      setPersonalScheduleVisible(false);
      if (updated) {
        queryInstituteStaffs();
      }
    },
    [queryInstituteStaffs],
  );

  return (
    <>
      {instituteId && (
        <>
          <Divider style={{ marginTop: 24, marginBottom: 24 }} />
          <p className={commonStyles.sectionTitle}>
            <TextBody type="h5">
              <Divider type="vertical" className={styles.titleBefore} />
              {t('Staff')}
            </TextBody>
          </p>
          {!readonly && (
            <div
              className={styles.staffActionsContainer}
              style={{ marginTop: 16, marginBottom: 16 }}
            >
              <Button
                icon={<PlusOutlined />}
                onClick={() => setVisible(true)}
                disabled={readonly}
              >
                {t('Add New')}
              </Button>
              {isWorkScheduleManagementEnabled && (
                <Dropdown
                  menu={{
                    items: scheduleMenuItems,
                    onClick: ({ key: year }) => setSelectedScheduleYear(year),
                  }}
                  trigger={['click']}
                  overlayStyle={{ width: 292 }}
                  disabled={!allYearHistory?.length}
                  getPopupContainer={triggerNode => triggerNode?.parentElement}
                >
                  <Button icon={<WorkSchedule />} className="margin-left-16">
                    {t('Work Schedule Management')}
                  </Button>
                </Dropdown>
              )}
              <div>
                <Upload {...(uploadProps as UploadProps)}>
                  <Button
                    icon={<UploadOutlined />}
                    disabled={readonly}
                    loading={uploading}
                    style={{ marginRight: 6 }}
                  >
                    {t('Upload')}
                  </Button>
                </Upload>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={onDownload}
                  loading={downloading}
                >
                  {t('Download')}
                </Button>
              </div>
            </div>
          )}
          <Table
            dataSource={staffResp?.data}
            columns={combineColumns}
            loading={loading}
            rowKey="id"
            scroll={{ x: 'max-content' }}
            bordered={false}
            style={{ marginBottom: 16 }}
            pagination={false}
          />
          <PaginationComponent
            size="small"
            className="margin-top-16 margin-bottom-16"
            total={staffResp?.totalElements || 0}
            pagination={pagination}
            handlePaginationChange={(current: number, pageSize: number) =>
              setPagination(old => ({
                ...old,
                current,
                pageSize,
              }))
            }
          />
          <StaffDrawer
            visible={visible}
            readonly={readonly}
            editedStaff={editedStaff}
            instituteId={instituteId}
            instituteType={instituteType}
            onClose={onClose}
          />
          {isWorkScheduleManagementEnabled && (
            <>
              <WorkScheduleManagementDrawer
                year={selectedScheduleYear}
                id={instituteId}
                visible={!!selectedScheduleYear}
                readonly={readonly}
                type={instituteType as WorkScheduleType}
                onClose={onCloseWorkScheduleDrawer}
              />
              <PersonalWorkScheduleDrawer
                personCode={staffCode}
                visible={personalScheduleVisible}
                readonly={readonly}
                type={instituteType as WorkScheduleType}
                onClose={onClosePersonalScheduleDrawer}
              />
            </>
          )}
        </>
      )}
    </>
  );
};
