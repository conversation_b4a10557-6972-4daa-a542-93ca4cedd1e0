import type { FC } from 'react';
import { Col, Form, Input } from 'antd';
import { useTranslation } from 'react-i18next';

import { useAddressModelIsDropdown } from '@/pages/common-party/partner-setting/hooks/useFormFields';
import { SelectWithBizDictPageLevel } from 'genesis-web-component/lib/components/index';

export const InvestigationArea: FC<{ disabled: boolean }> = ({ disabled }) => {
  const { t } = useTranslation(['partner']);

  const addressModelIsDropdown = useAddressModelIsDropdown();

  return (
    <Col span={8}>
      <Form.Item
        name="areaList"
        label={t('Investigation Area')}
        style={{ width: 240 }}
      >
        {addressModelIsDropdown ? (
          <SelectWithBizDictPageLevel
            bizDictKey="address1"
            mode="multiple"
            disabled={disabled}
          />
        ) : (
          <Input disabled={disabled} />
        )}
      </Form.Item>
    </Col>
  );
};
