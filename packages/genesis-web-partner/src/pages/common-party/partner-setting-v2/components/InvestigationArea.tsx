import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Input } from 'antd';

import { SelectWithBizDictPageLevel } from 'genesis-web-component/lib/components/index';

import { useAddressModelIsDropdown } from '@/pages/common-party/partner-setting/hooks/useFormFields';

const ArrayValueInput = ({
  value,
  onChange,
  disabled,
}: {
  value?: string[] | undefined;
  onChange?: (value: string[] | undefined) => void;
  disabled: boolean;
}) => {
  return (
    <Input
      value={value?.[0]}
      onChange={evt => {
        const changeValue = evt.target.value;
        if (changeValue) {
          onChange?.([changeValue]);
          return;
        }
        onChange?.(undefined);
      }}
      disabled={disabled}
    />
  );
};

export const InvestigationArea: FC<{ disabled: boolean }> = ({ disabled }) => {
  const { t } = useTranslation(['partner']);

  const addressModelIsDropdown = useAddressModelIsDropdown();

  return (
    <Col span={8}>
      <Form.Item name="areaList" label={t('Investigation Area')} style={{ width: 240 }}>
        {addressModelIsDropdown ? (
          <SelectWithBizDictPageLevel bizDictKey="address1" mode="multiple" disabled={disabled} />
        ) : (
          <ArrayValueInput disabled={disabled} />
        )}
      </Form.Item>
    </Col>
  );
};