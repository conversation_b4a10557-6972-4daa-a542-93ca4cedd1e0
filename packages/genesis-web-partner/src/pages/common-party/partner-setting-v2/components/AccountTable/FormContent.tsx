/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-11-08 17:37:02
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-11-08 17:37:02
 * @Description:
 */
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Input } from 'antd';
import type { FormInstance } from 'antd/lib/form';

import { Select } from '@zhongan/nagrand-ui';

import { PaymentMethodForm } from 'genesis-web-component/lib/components/PaymentMethodFormV4';
import type { AccountItem, BizDictItem } from 'genesis-web-service';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';

const FormItemStyle = {
  width: '240px',
  marginRight: '100px',
};

interface Props {
  form: FormInstance;
  readonly?: boolean;
  initialValues: AccountItem;
}

type PaymentMethod = AccountItem & {
  paymentMethod: string;
  cardHolderName: string;
};

/**
 *
 * @param form form实例
 * @param readonly 是否只读
 * @param initialValues 初始值
 *
 */
export const FormContent = ({ form, initialValues, readonly }: Props) => {
  const { t } = useTranslation('partner');
  const channelOrgAccountTypes = useTenantBizDict('channelOrgAccountType') as BizDictItem[];
  const [paymentMethodData, setPaymentMethodData] = useState<PaymentMethod>();

  const channelOrgAccountTypeOptions = useMemo(
    () =>
      channelOrgAccountTypes?.map(item => ({
        label: item.label || item.dictDesc,
        value: item.value,
      })),
    [channelOrgAccountTypes]
  );

  useEffect(() => {
    form.setFieldsValue(initialValues);
    setPaymentMethodData({
      ...initialValues,
      paymentMethod: initialValues?.payMethod,
      cardHolderName: initialValues?.userName,
    });
  }, [form, initialValues]);

  return useMemo(
    () => (
      <Form layout="vertical" form={form}>
        <PaymentMethodForm
          form={form}
          readonly={readonly}
          initialValues={paymentMethodData}
          payMethodLabel={t('Payment Method')}
        />
        <div style={{ display: 'flex' }}>
          <Form.Item name="useType" label={t('Account Usage')}>
            <Select
              placeholder={t('Please select')}
              options={channelOrgAccountTypeOptions}
              allowClear
              showSearch
              disabled={readonly}
              style={FormItemStyle}
              getPopupContainer={triggerNode => triggerNode.parentNode}
            />
          </Form.Item>
          <Form.Item name="financialCode" label={t('SWIFT Code')}>
            <Input disabled={readonly} placeholder={t('Please input')} style={FormItemStyle} />
          </Form.Item>
        </div>
      </Form>
    ),
    [channelOrgAccountTypeOptions, form, paymentMethodData, readonly, t]
  );
};
