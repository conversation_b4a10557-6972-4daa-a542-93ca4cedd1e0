/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import {
  useCallback,
  useState,
  useEffect,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Button, Divider, Form } from 'antd';
import { useEnumsMapping } from '@/pages/common-party/partner-setting/hooks/useEnumsMapping';
import { MetadataService, type AccountItem, type BizDictItem } from 'genesis-web-service';
import { useTranslation } from 'react-i18next';
import { cloneDeep, keyBy } from 'lodash-es';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { FormContent } from './FormContent';
import { ModeEnum } from '@/types/common';
import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import {
  Icon,
  Table,
  TableActionsContainer,
  TextBody,
} from '@zhongan/nagrand-ui';
import styles from '../../style.scss';
import CommonStyles from '../../style.scss';

interface Props {
  initialList: AccountItem[];
  instituteId?: number;
  mode: ModeEnum;
}

/**
 * @param initialList 初始化列表
 * @param readonly 是否只读
 * @description Account Information信息展示
 */
export const AccountTable = forwardRef(
  ({ initialList, mode, instituteId }: Props, ref) => {
    const readonly = mode === ModeEnum.READ;
    const { t } = useTranslation(['partner']);
    const [form] = Form.useForm();
    const channelOrgAccountTypeMapping = useEnumsMapping(
      'channelOrgAccountType',
    );
    const [paymentMethodBizdicts, setPaymentMethodBizdicts] = useState<BizDictItem[]>([]);

    const paymentMethodMapping = useMemo(() => keyBy(paymentMethodBizdicts, 'enumItemName'), [paymentMethodBizdicts])

    const [visible, setVisible] = useState(false);
    const [editedIndex, setEditedIndex] = useState<number>();
    const [editedItem, setEditedItem] = useState<AccountItem>();
    const [shownList, setShownList] = useState<AccountItem[]>();

    useEffect(() => {
      setShownList([...(initialList || [])]);
    }, [initialList]);

    useEffect(() => {
      MetadataService.getPaymentMethodsBizDict().then(res => {
        setPaymentMethodBizdicts(res);
      })
    }, []);

    useImperativeHandle(ref, () => {
      return shownList;
    });

    const editClick = useCallback((item: AccountItem, index: number) => {
      setVisible(true);
      setEditedIndex(index);
      setEditedItem(item);
      form.setFieldsValue(item);
    }, []);

    const deleteClick = useCallback(
      (index: number) => {
        const cloneList = cloneDeep(shownList || []);
        cloneList.splice(index, 1);
        setShownList(cloneList);
      },
      [shownList],
    );

    /* ----- 抽屉相关 func----- */
    const onClose = useCallback(() => {
      setVisible(false);
      setEditedIndex(null);
      setEditedItem(null);
      form.resetFields();
    }, [form]);

    const onSubmit = useCallback(() => {
      form.validateFields().then(values => {
        const { data: paymentMethodFormValue, ...restFormValue } = values ?? {};
        const {
          cardHolderName,
          paymentMethod,
          ...resetPaymentMethodFormValue
        } = paymentMethodFormValue ?? {};
        const result = {
          ...restFormValue,
          ...resetPaymentMethodFormValue,
          userName: cardHolderName,
          payMethod: paymentMethod,
          id: editedItem?.id,
        };
        const cloneList = cloneDeep(shownList || []);
        if (editedItem) {
          cloneList.splice(editedIndex, 1, result);
        } else {
          cloneList.push(result);
        }
        setShownList(cloneList);
        onClose();
      });
    }, [shownList, form, editedItem, editedIndex, onClose]);

    const columns = [
      {
        title: t('Payment Method / Account Type'),
        dataIndex: 'accountType',
        render: (_, item: AccountItem) => {
          const paymethodBizdict = paymentMethodMapping[item?.payMethod];
          const accountTypeBizdict = paymethodBizdict?.childList?.find(bizdict => bizdict.enumItemName === item.accountType);
          return `${paymethodBizdict?.dictValueName} / ${accountTypeBizdict?.dictValueName || t('--')}`
        },
      },
      {
        title: t('Account Usage'),
        dataIndex: 'useType',
        render: (useType?: string) => (
          <ComponentWithFallback>
            {channelOrgAccountTypeMapping[useType]}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('SWIFT Code'),
        dataIndex: 'financialCode',
      },
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (_, item: AccountItem, index: number) => (
          <TableActionsContainer>
            <Icon
              type={readonly ? 'view' : 'edit'}
              style={{ fontSize: 16 }}
              onClick={() => editClick(item, index)}
            />
            {!item?.id && (
              <Button
                disabled={readonly}
                style={{ margin: 0, padding: 0 }}
                type="text"
              >
                <Icon
                  type="delete"
                  style={{ fontSize: 16 }}
                  onClick={() => deleteClick(index)}
                />
              </Button>
            )}
          </TableActionsContainer>
        ),
      },
    ];

    return (
      <section className={styles.addressInfo}>
        {instituteId && (
          <>
            <Divider style={{ marginTop: 24, marginBottom: 24 }} />
            <p className={CommonStyles.sectionTitle}>
              <TextBody type="h5">
                <Divider type="vertical" className={styles.titleBefore} />
                {t('Account')}
              </TextBody>
              <Button
                icon={<Icon type="add" />}
                disabled={readonly}
                onClick={() => editClick({} as AccountItem, shownList?.length)}
              >
                {t('Add')}
              </Button>
            </p>
            <Table
              key="id"
              scroll={{ x: 'max-content' }}
              dataSource={shownList}
              columns={columns}
            />
            <DrawerForm
              title={t('Account')}
              visible={visible}
              closable={false}
              onClose={onClose}
              onSubmit={onSubmit}
              bodyStyle={{ width: 1070 }}
              cancelText={t('Cancel')}
              sendText={t('Submit')}
              submitBtnShow={!readonly}
            >
              <FormContent
                form={form}
                initialValues={editedItem}
                readonly={readonly}
              />
            </DrawerForm>
          </>
        )}
      </section>
    );
  },
);
