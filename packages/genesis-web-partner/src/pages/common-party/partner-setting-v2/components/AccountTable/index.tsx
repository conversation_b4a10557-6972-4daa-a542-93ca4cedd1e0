/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Divider, Form } from 'antd';

import { cloneDeep, keyBy } from 'lodash-es';

import { Icon, SimpleSectionHeader, Table, TableActionsContainer } from '@zhongan/nagrand-ui';

import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { AccountItem, BizDictItem, InstituteTypeEnum } from 'genesis-web-service';
import { MetadataService } from 'genesis-web-service';

import { CommonForm } from '@/components/CommonForm/Form';
import {
  EntityType,
  covertPartnerType2SchemaPartnerType,
  useGenericSchemaFormItemFields,
} from '@/hooks/useGenericSchemaFormItemFields';
import { AddNewTitle } from '@/pages/common-party/partner-setting/components/AddNewTitle';
import { useEnumsMapping } from '@/pages/common-party/partner-setting/hooks/useEnumsMapping';
import type { PartnerTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import type { SchemaProps } from '@/types/common';
import { ModeEnum } from '@/types/common';
import { transferSchemaToTableProp } from '@/utils/utils';

import { useApproveModeContext } from '../../hooks/useContext';
import { default as styles } from '../../style.scss';
import { FormContent } from './FormContent';

interface Props {
  initialList: AccountItem[];
  instituteId?: number;
  mode: ModeEnum;
  instituteType: InstituteTypeEnum;
  isApprovalPage?: boolean;
}

/**
 * @param initialList 初始化列表
 * @param readonly 是否只读
 * @description Account Information信息展示
 */
export const AccountTable = forwardRef(
  ({ initialList, mode, instituteId, instituteType, isApprovalPage = false }: Props, ref) => {
    const readonly = mode === ModeEnum.READ;
    const { t } = useTranslation(['partner']);
    const [form] = Form.useForm();
    const channelOrgAccountTypeMapping = useEnumsMapping('channelOrgAccountType');
    const [paymentMethodBizdicts, setPaymentMethodBizdicts] = useState<BizDictItem[]>([]);

    const paymentMethodMapping = useMemo(() => keyBy(paymentMethodBizdicts, 'enumItemName'), [paymentMethodBizdicts]);

    const [visible, setVisible] = useState(false);
    const [editedIndex, setEditedIndex] = useState<number>();
    const [editedItem, setEditedItem] = useState<AccountItem>();
    const [shownList, setShownList] = useState<AccountItem[]>();
    const { enableApproval } = useApproveModeContext();

    const { formItems: dynamicFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'DYNAMIC',
      category: 'ACCOUNT',
      disabled: readonly,
      type: covertPartnerType2SchemaPartnerType(instituteType as unknown as PartnerTypeEnum),
      entityType: EntityType.PARTNER,
    });

    useEffect(() => {
      setShownList([...(initialList || [])]);
    }, [initialList]);

    useEffect(() => {
      MetadataService.getPaymentMethodsBizDict().then(res => {
        setPaymentMethodBizdicts(res);
      });
    }, []);

    useImperativeHandle(ref, () => {
      return shownList;
    });

    const editClick = useCallback((item: AccountItem, index: number) => {
      setVisible(true);
      setEditedIndex(index);
      setEditedItem(item);
      form.setFieldsValue(item);
    }, []);

    const deleteClick = useCallback(
      (index: number) => {
        const cloneList = cloneDeep(shownList || []);
        cloneList.splice(index, 1);
        setShownList(cloneList);
      },
      [shownList]
    );

    /* ----- 抽屉相关 func----- */
    const onClose = useCallback(() => {
      setVisible(false);
      setEditedIndex(null);
      setEditedItem(null);
      form.resetFields();
    }, [form]);

    const onSubmit = useCallback(() => {
      form.validateFields().then(values => {
        const { data: paymentMethodFormValue, ...restFormValue } = values ?? {};
        const { cardHolderName, paymentMethod, ...resetPaymentMethodFormValue } = paymentMethodFormValue ?? {};
        const result = {
          ...restFormValue,
          ...resetPaymentMethodFormValue,
          userName: cardHolderName,
          payMethod: paymentMethod,
          id: editedItem?.id,
        };
        const cloneList = cloneDeep(shownList || []);
        if (editedItem) {
          cloneList.splice(editedIndex, 1, result);
        } else {
          cloneList.push(result);
        }
        setShownList(cloneList);
        onClose();
      });
    }, [shownList, form, editedItem, editedIndex, onClose]);

    const columns = [
      {
        title: t('Payment Method / Account Type'),
        dataIndex: 'accountType',
        render: (_, item: AccountItem) => {
          const paymethodBizdict = paymentMethodMapping[item?.payMethod];
          const accountTypeBizdict = paymethodBizdict?.childList?.find(
            bizdict => bizdict.enumItemName === item.accountType
          );
          return `${paymethodBizdict?.dictValueName} / ${accountTypeBizdict?.dictValueName || t('--')}`;
        },
      },
      {
        title: t('Account Usage'),
        dataIndex: 'useType',
        render: (useType?: string) => (
          <ComponentWithFallback>{channelOrgAccountTypeMapping[useType]}</ComponentWithFallback>
        ),
      },
      {
        title: t('SWIFT Code'),
        dataIndex: 'financialCode',
      },
      ...transferSchemaToTableProp(dynamicFields as SchemaProps[]),
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (_, item: AccountItem, index: number) => (
          <TableActionsContainer>
            <Icon type={readonly ? 'view' : 'edit'} style={{ fontSize: 16 }} onClick={() => editClick(item, index)} />
            {!item?.id && !isApprovalPage && (
              <Button disabled={readonly} style={{ margin: 0, padding: 0 }} type="text">
                <Icon type="delete" style={{ fontSize: 16 }} onClick={() => deleteClick(index)} />
              </Button>
            )}
          </TableActionsContainer>
        ),
      },
    ];

    return (
      <section className={styles.addressInfo}>
        {(enableApproval || instituteId) && (
          <>
            {isApprovalPage ? (
              <SimpleSectionHeader type="h5" weight="bold" style={{ marginBottom: 16 }}>
                {t('Account')}
              </SimpleSectionHeader>
            ) : (
              <>
                <Divider className="my-6" />
                <AddNewTitle
                  title={t('Account')}
                  onAddClick={() => editClick({} as AccountItem, shownList?.length)}
                  readonly={readonly}
                />
              </>
            )}
            <Table key="id" scroll={{ x: 'max-content' }} dataSource={shownList} columns={columns} />
            <DrawerForm
              title={t('Account')}
              visible={visible}
              closable={false}
              onClose={onClose}
              onSubmit={onSubmit}
              bodyStyle={{ width: 1070 }}
              cancelText={t('Cancel')}
              sendText={t('Submit')}
              submitBtnShow={!readonly}
            >
              <FormContent form={form} initialValues={editedItem} readonly={readonly} />
              <CommonForm fields={dynamicFields} form={form} />
            </DrawerForm>
          </>
        )}
      </section>
    );
  }
);
