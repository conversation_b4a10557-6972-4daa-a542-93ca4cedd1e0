/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Divider, Form } from 'antd';

import { cloneDeep, omit } from 'lodash-es';

import { Icon, SimpleSectionHeader, Table, TableActionsContainer } from '@zhongan/nagrand-ui';

import { AddressComponent } from 'genesis-web-component/lib/components/AddressComponent';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { AddressItem, FactorsType, InstituteTypeEnum } from 'genesis-web-service';

import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import { useAddressI18nList } from '@/hooks/useAddressI18nList';
import {
  EntityType,
  covertPartnerType2SchemaPartnerType,
  useGenericSchemaFormItemFields,
} from '@/hooks/useGenericSchemaFormItemFields';
import { AddNewTitle } from '@/pages/common-party/partner-setting/components/AddNewTitle';
import type { PartnerTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { ModeEnum } from '@/types/common';
import { CustomerType } from '@/utils/constants';
import { transferSchemaToTableProp } from '@/utils/utils';

import { useApproveModeContext } from '../../hooks/useContext';
import { default as styles } from '../../style.scss';

interface Props {
  initialList: AddressItem[];
  mode: ModeEnum;
  instituteId?: number;
  loading?: boolean;
  instituteType: InstituteTypeEnum;
  isApprovalPage?: boolean;
}

/**
 * @param initialList 初始化列表
 * @param readonly 是否只读
 * @description Address信息展示
 */
export const AddressTable = forwardRef(
  ({ initialList, mode, instituteId, loading, instituteType, isApprovalPage = false }: Props, ref) => {
    const readonly = mode === ModeEnum.READ;
    const { t } = useTranslation(['partner']);
    const [form] = Form.useForm();

    const [visible, setVisible] = useState(false);
    const [editedIndex, setEditedIndex] = useState<number>();
    const [editedItem, setEditedItem] = useState<AddressItem>();
    const [shownList, setShownList] = useState<AddressItem[]>();
    const [addressResult, setAddressResult] = useState<Record<string, string>>();
    const { enableApproval } = useApproveModeContext();

    const addressI18nList = useAddressI18nList(shownList, CustomerType.COMPANY);

    const { formItems: dynamicFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'DYNAMIC',
      category: 'ADDRESS',
      disabled: readonly,
      type: covertPartnerType2SchemaPartnerType(instituteType as unknown as PartnerTypeEnum),
      entityType: EntityType.PARTNER,
    });

    const { formItems: staticFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'STATIC',
      category: 'ADDRESS',
      disabled: readonly,
      type: covertPartnerType2SchemaPartnerType(instituteType as unknown as PartnerTypeEnum),
      fieldKeyPrefix: '',
      entityType: EntityType.PARTNER,
    });

    const addressAndZipCodeTableColumns = useMemo(() => {
      if (!staticFields?.length) {
        return [];
      }
      return transferSchemaToTableProp([...staticFields, ...dynamicFields], addressI18nList);
    }, [staticFields, addressI18nList, dynamicFields]);

    const fields = [
      staticFields.find(field => field.key === 'addressType'),
      {
        type: FieldType.Customize,
        customerDom: (
          <AddressComponent
            disabled={readonly}
            initialValue={(editedItem as unknown as Record<string, string>) ?? {}}
            onCascaderValueChange={setAddressResult}
            form={form}
            cascaderCol={24}
            colProps={{ span: 12 }}
            inputWidth="100%"
            selfCustomFields={
              staticFields
                ?.filter(field => field.key !== 'addressType')
                .map(field => {
                  const { key, label, ctrlProps, rules } = field;

                  return {
                    factorCode: key,
                    factorName: label,
                    isRequired: rules?.length ? 1 : 0,
                    options: ctrlProps?.options,
                  };
                }) as FactorsType[]
            }
          />
        ),
      },
    ];

    useEffect(() => {
      setShownList([...(initialList || [])]);
    }, [initialList]);

    useImperativeHandle(ref, () => {
      return shownList;
    });

    const editClick = useCallback(
      (item: AddressItem, index: number) => {
        setVisible(true);
        setEditedIndex(index);
        setEditedItem(item);
        form.setFieldsValue(item);
      },
      [form]
    );

    const deleteClick = useCallback(
      (index: number) => {
        const cloneList = cloneDeep(shownList || []);
        cloneList.splice(index, 1);
        setShownList(cloneList);
      },
      [shownList]
    );

    const onClose = useCallback(() => {
      setVisible(false);
      setEditedIndex(null);
      setEditedItem(null);
      form.resetFields();
    }, [form]);

    const onSubmit = useCallback(() => {
      form.validateFields().then(values => {
        const cloneList = cloneDeep(shownList || []);
        values.id = editedItem?.id;
        const valuesWithAddress = {
          ...omit(values, 'address'),
          ...addressResult,
        };

        if (editedItem) {
          cloneList.splice(editedIndex, 1, valuesWithAddress);
        } else {
          cloneList.push(valuesWithAddress);
        }
        setShownList(cloneList);
        onClose();
      });
    }, [shownList, form, editedItem, editedIndex, onClose, t, addressResult]);

    const columns = [
      ...addressAndZipCodeTableColumns,
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (_, item: AddressItem, index: number) => (
          <TableActionsContainer>
            <Icon type={readonly ? 'view' : 'edit'} style={{ fontSize: 16 }} onClick={() => editClick(item, index)} />
            {!item?.id && !isApprovalPage && (
              <Button disabled={readonly} style={{ margin: 0, padding: 0 }} type="text">
                <Icon type="delete" style={{ fontSize: 16 }} onClick={() => deleteClick(index)} />
              </Button>
            )}
          </TableActionsContainer>
        ),
      },
    ];

    return (
      <section className={styles.addressInfo}>
        {(enableApproval || instituteId) && (
          <>
            {isApprovalPage ? (
              <SimpleSectionHeader type="h5" weight="bold" style={{ marginBottom: 16 }}>
                {t('Address')}
              </SimpleSectionHeader>
            ) : (
              <>
                <Divider className="my-6" />
                <AddNewTitle
                  title={t('Address')}
                  onAddClick={() => editClick({} as AddressItem, shownList?.length)}
                  readonly={readonly}
                />
              </>
            )}
            <Table scroll={{ x: 'max-content' }} key="id" dataSource={shownList} loading={loading} columns={columns} />
            <DrawerForm
              title={t('Address')}
              visible={visible}
              closable={false}
              onClose={onClose}
              onSubmit={onSubmit}
              cancelText={t('Cancel')}
              sendText={t('Submit')}
              submitBtnShow={!readonly}
            >
              <CommonForm fields={[...fields, ...dynamicFields]} form={form} />
            </DrawerForm>
          </>
        )}
      </section>
    );
  }
);
