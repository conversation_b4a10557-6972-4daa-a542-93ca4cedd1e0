/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import {
  useState,
  useCallback,
  useEffect,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Button, Divider, Form } from 'antd';
import { useFormFields } from '@/pages/common-party/partner-setting/hooks/useFormFields';
import { useEnumsMapping } from '@/pages/common-party/partner-setting/hooks/useEnumsMapping';
import { FormTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import type { AddressItem } from 'genesis-web-service';
import { useTranslation } from 'react-i18next';
import { CommonForm } from '@/components/CommonForm/Form';
import { cloneDeep, omit } from 'lodash-es';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { ModeEnum } from '@/types/common';
import {
  Icon,
  Table,
  TableActionsContainer,
  TextBody,
} from '@zhongan/nagrand-ui';
import { NoData } from 'genesis-web-component/lib/components/NoData';
import styles from '../../style.scss';
import CommonStyles from '../../style.scss';
import {
  useAddressConfig,
  useGetAddressI18nInfo,
} from 'genesis-web-component/lib/components';
import { transferSchemaToTableProp } from '@/utils/utils';
import { CustomerType } from '@/utils/constants';
import { useAddressI18nList } from '@/hooks/useAddressI18nList';

interface Props {
  initialList: AddressItem[];
  mode: ModeEnum;
  instituteId?: number;
  loading?: boolean;
}

/**
 * @param initialList 初始化列表
 * @param readonly 是否只读
 * @description Address信息展示
 */
export const AddressTable = forwardRef(
  ({ initialList, mode, instituteId, loading }: Props, ref) => {
    const readonly = mode === ModeEnum.READ;
    const [form] = Form.useForm();
    const { t } = useTranslation(['partner']);
    const companyAddressTypesMapping = useEnumsMapping('companyAddressType');
    const { formItems } = useAddressConfig(CustomerType.COMPANY);

    const [visible, setVisible] = useState(false);
    const [editedIndex, setEditedIndex] = useState<number>();
    const [editedItem, setEditedItem] = useState<AddressItem>();
    const [shownList, setShownList] = useState<AddressItem[]>();
    const [addressResult, setAddressResult] =
      useState<Record<string, string>>();

    const addressI18nList = useAddressI18nList(shownList, CustomerType.COMPANY);

    const addressAndZipCodeTableColumns = useMemo(() => {
      if (!formItems?.length) {
        return [];
      }
      return transferSchemaToTableProp(formItems, addressI18nList);
    }, [formItems, addressI18nList]);

    const fields = useFormFields({
      formType: FormTypeEnum.ADDRESS,
      disabled: readonly,
      setAddressResult,
      form,
      editedItem,
    });

    useEffect(() => {
      setShownList([...(initialList || [])]);
    }, [initialList]);

    useImperativeHandle(ref, () => {
      return shownList;
    });

    const editClick = useCallback(
      (item: AddressItem, index: number) => {
        setVisible(true);
        setEditedIndex(index);
        setEditedItem(item);
        form.setFieldsValue(item);
      },
      [form],
    );

    const deleteClick = useCallback(
      (index: number) => {
        const cloneList = cloneDeep(shownList || []);
        cloneList.splice(index, 1);
        setShownList(cloneList);
      },
      [shownList],
    );

    const onClose = useCallback(() => {
      setVisible(false);
      setEditedIndex(null);
      setEditedItem(null);
      form.resetFields();
    }, [form]);

    const onSubmit = useCallback(() => {
      form.validateFields().then(values => {
        const cloneList = cloneDeep(shownList || []);
        values.id = editedItem?.id;
        const valuesWithAddress = {
          ...omit(values, 'address'),
          ...addressResult,
        };

        if (editedItem) {
          cloneList.splice(editedIndex, 1, valuesWithAddress);
        } else {
          cloneList.push(valuesWithAddress);
        }

        setShownList(cloneList);
        onClose();
      });
    }, [shownList, form, editedItem, editedIndex, onClose, t, addressResult]);

    const columns = [
      {
        title: t('Address Type'),
        dataIndex: 'addressType',
        render: (_, item: AddressItem) => {
          return companyAddressTypesMapping[item.addressType];
        },
      },
      ...addressAndZipCodeTableColumns,
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (_, item: AddressItem, index: number) => (
          <TableActionsContainer>
            <Icon
              type={readonly ? 'view' : 'edit'}
              style={{ fontSize: 16 }}
              onClick={() => editClick(item, index)}
            />
            {!item?.id && (
              <Button
                disabled={readonly}
                style={{ margin: 0, padding: 0 }}
                type="text"
              >
                <Icon
                  type="delete"
                  style={{ fontSize: 16 }}
                  onClick={() => deleteClick(index)}
                />
              </Button>
            )}
          </TableActionsContainer>
        ),
      },
    ];

    return (
      <section className={styles.addressInfo}>
        {instituteId && (
          <>
            <Divider style={{ marginTop: 10, marginBottom: 24 }} />
            <p className={CommonStyles.sectionTitle}>
              <TextBody type="h5">
                <Divider type="vertical" className={styles.titleBefore} />
                {t('Address')}
              </TextBody>
              <Button
                icon={<Icon type="add" />}
                disabled={readonly}
                onClick={() => editClick({} as AddressItem, shownList?.length)}
              >
                {t('Add')}
              </Button>
            </p>
            <Table
              scroll={{ x: 'max-content' }}
              key="id"
              dataSource={shownList}
              loading={loading}
              columns={columns}
            />
            <DrawerForm
              title={t('Address')}
              visible={visible}
              closable={false}
              onClose={onClose}
              onSubmit={onSubmit}
              cancelText={t('Cancel')}
              sendText={t('Submit')}
              submitBtnShow={!readonly}
            >
              <CommonForm fields={fields} form={form} />
            </DrawerForm>
          </>
        )}
      </section>
    );
  },
);
