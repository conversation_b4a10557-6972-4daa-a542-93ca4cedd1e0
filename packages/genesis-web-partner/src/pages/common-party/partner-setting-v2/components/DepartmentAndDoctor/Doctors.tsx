import { Button, Upload, Form, message, Tooltip } from 'antd';
import {
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import type { UploadProps } from 'antd/lib/upload';
import type { ColumnProps } from 'antd/lib/table';
import type { PaginationProps } from 'antd/lib/pagination';
import { useTranslation } from 'react-i18next';
import { useCallback, useMemo, useState } from 'react';
import { ChannelService, DownloadOrUploadType } from 'genesis-web-service';
import type { Doctor, BizDictItem } from 'genesis-web-service';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import { CommonForm } from '@/components/CommonForm/Form';
import { RenderEnums } from '@/components/RenderEnums';
import { NoData } from 'genesis-web-component/lib/components/NoData';
import {
  ViewSquareOutline,
  EditOutline,
  DeleteOutline,
} from '@/components/Icons';
import { useDoctorFormFields } from '@/pages/common-party/partner-setting/hooks/useFormFields';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';
import { useAntdTable } from 'ahooks';
import { PaginationComponent } from '@/components/Pagination';
import { Table, TableActionsContainer } from '@zhongan/nagrand-ui';

interface DoctorsProp {
  instituteId: number;
  readonly: boolean;
}

export const Doctors = ({ instituteId, readonly }: DoctorsProp) => {
  const { t } = useTranslation('partner');
  const certiTypeEnums = useTenantBizDict('certiType') as BizDictItem[];
  const doctorCertificateTypeEnums = useTenantBizDict(
    'doctorCertificateType',
  ) as BizDictItem[];
  const [form] = Form.useForm();

  const formFields = useDoctorFormFields({ disabled: readonly });
  const [visible, setVisible] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [editedDoctor, setEditedDoctor] = useState<Doctor>();

  const getTableData = useCallback(
    async ({ current, pageSize }: PaginationProps) => {
      if (instituteId) {
        const doctors = await ChannelService.getDoctors(instituteId, {
          pageIndex: current - 1,
          pageSize: pageSize,
        });
        return {
          total: doctors?.totalElements,
          list: doctors?.data,
        };
      } else {
        return {
          total: 0,
          list: [],
        };
      }
    },
    [instituteId],
  );

  const { tableProps, search } = useAntdTable(getTableData, {
    onError: error => message.error(error?.message),
    refreshDeps: [instituteId],
  });

  const uploadProps = useMemo(
    () =>
      getUploadPropsNew(
        `/api/channel/v2/file/upload/?type=${DownloadOrUploadType.HOSPITAL_DOCTOR}&id=${instituteId}`,
        () => search.submit(),
        setUploading,
      ),
    [instituteId, search],
  );

  const download = useCallback(() => {
    setDownloading(true);
    ChannelService.download({
      type: DownloadOrUploadType.HOSPITAL_DOCTOR,
      id: instituteId,
    })
      .then(downloadFile)
      .finally(() => setDownloading(false));
  }, [instituteId]);

  const onEdit = useCallback((doctor: Doctor) => {
    setVisible(true);
    setEditedDoctor(doctor);
    form.setFieldsValue(doctor);
  }, []);

  const onDelete = useCallback(
    (doctorId: number) => {
      return ChannelService.deleteDoctor(instituteId, doctorId)
        .then(() => {
          message.success(t('Delete successfully'));
          search.submit();
        })
        .catch((error: Error) => {
          message.error(error?.message);
        });
    },
    [instituteId, search],
  );

  const columns: ColumnProps<Doctor>[] = useMemo(
    () => [
      {
        title: t('Doctor Name'),
        dataIndex: 'doctorName',
        fixed: 'left',
      },
      {
        title: t('Title'),
        dataIndex: 'title',
      },
      {
        title: t('Department'),
        dataIndex: 'department',
      },
      {
        title: t('Doctor ID type'),
        dataIndex: 'idType',
        render: (idType: string) => (
          <RenderEnums enums={certiTypeEnums} keyName={idType} />
        ),
      },
      {
        title: t('ID No'),
        dataIndex: 'idNo',
      },
      {
        title: t('Phone No.'),
        dataIndex: 'phoneNo',
      },
      {
        title: t('E-mail'),
        dataIndex: 'email',
      },
      {
        title: t('Certificate Type'),
        dataIndex: 'certificateType',
        render: (certificateType: string) => (
          <RenderEnums
            enums={doctorCertificateTypeEnums}
            keyName={certificateType}
          />
        ),
      },
      {
        title: t('Certificate No'),
        dataIndex: 'certificateNo',
      },
      {
        title: t('Actions'),
        align: 'right',
        fixed: 'right',
        width: 114,
        render: (doctor: Doctor) => (
          <div className="table-actions">
            <TableActionsContainer>
              <DeleteConfirm
                onOk={() => onDelete(doctor.id)}
                disabled={readonly}
              >
                <Tooltip title={t('Delete')}>
                  <Button
                    icon={<DeleteOutline />}
                    disabled={readonly}
                    type="link"
                  />
                </Tooltip>
              </DeleteConfirm>
              {readonly ? (
                <Tooltip title={t('View')}>
                  <Button
                    icon={<ViewSquareOutline />}
                    type="link"
                    onClick={() => onEdit(doctor)}
                  />
                </Tooltip>
              ) : (
                <Tooltip title={t('Edit')}>
                  <Button
                    icon={<EditOutline />}
                    type="link"
                    onClick={() => onEdit(doctor)}
                  />
                </Tooltip>
              )}
            </TableActionsContainer>
          </div>
        ),
      },
    ],
    [certiTypeEnums, readonly, onDelete, onEdit, doctorCertificateTypeEnums],
  );

  const onClose = useCallback(() => {
    setVisible(false);
    setEditedDoctor(null);
    form.resetFields();
  }, []);

  const onSubmit = useCallback(() => {
    form.validateFields().then(values => {
      setSubmitting(true);
      let submitReq: Promise<void>;
      if (editedDoctor) {
        submitReq = ChannelService.updateDoctor(
          instituteId,
          editedDoctor.id,
          values,
        ).then(() => {
          message.success(t('Edited successfully'));
          onClose();
          search.submit();
        });
      } else {
        submitReq = ChannelService.addDoctor(instituteId, values).then(() => {
          message.success(t('Add successfully'));
          onClose();
          search.submit();
        });
      }
      submitReq
        .catch((error: Error) => message.error(error?.message))
        .finally(() => setSubmitting(false));
    });
  }, [instituteId, editedDoctor, onClose, search]);

  return (
    <div>
      {!readonly && (
        <div className="flex-between" style={{ marginTop: 16 }}>
          <Button
            icon={<PlusOutlined />}
            onClick={() => setVisible(true)}
            disabled={readonly || tableProps?.loading}
          >
            {t('Add New')}
          </Button>
          <div>
            <Upload {...(uploadProps as UploadProps)}>
              <Button
                icon={<UploadOutlined />}
                disabled={readonly || tableProps?.loading}
                loading={uploading}
                style={{ marginRight: 6 }}
              >
                {t('Upload')}
              </Button>
            </Upload>
            <Button
              icon={<DownloadOutlined />}
              onClick={download}
              loading={downloading}
              disabled={readonly || tableProps?.loading}
            >
              {t('Download')}
            </Button>
          </div>
        </div>
      )}
      <Table
        columns={columns}
        rowKey="id"
        scroll={{ x: 'max-content' }}
        bordered={false}
        style={{ marginTop: 16, marginBottom: 16 }}
        {...tableProps}
        pagination={false}
      />
      <PaginationComponent
        size="small"
        total={tableProps?.pagination?.total}
        pagination={tableProps?.pagination}
        handlePaginationChange={(current, pageSize) =>
          tableProps?.onChange({ ...tableProps?.pagination, current, pageSize })
        }
      />

      <DrawerForm
        title={t('Doctor')}
        visible={visible}
        closable={false}
        maskClosable={false}
        onClose={onClose}
        onSubmit={onSubmit}
        cancelText={t('Cancel')}
        sendText={t('Confirm')}
        submitBtnShow={!readonly}
        submitBtnProps={{ loading: submitting }}
      >
        <CommonForm form={form} fields={formFields} />
      </DrawerForm>
    </div>
  );
};
