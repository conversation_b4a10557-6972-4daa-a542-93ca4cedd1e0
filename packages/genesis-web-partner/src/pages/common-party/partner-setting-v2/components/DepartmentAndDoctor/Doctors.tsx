import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DownloadOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Form, Upload, message } from 'antd';
import type { PaginationProps } from 'antd/lib/pagination';
import type { ColumnProps } from 'antd/lib/table';
import type { UploadProps } from 'antd/lib/upload';

import { useAntdTable } from 'ahooks';

import { DeleteAction, EditAction, Table, TableActionsContainer, ViewAction } from '@zhongan/nagrand-ui';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { BizDictItem, Doctor, InstituteTypeEnum } from 'genesis-web-service';
import { ChannelService, DownloadOrUploadType } from 'genesis-web-service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { CommonForm } from '@/components/CommonForm/Form';
import {
  EntityType,
  covertPartnerType2SchemaPartnerType,
  useGenericSchemaFormItemFields,
} from '@/hooks/useGenericSchemaFormItemFields';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import type { PartnerTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';
import type { SchemaProps } from '@/types/common';
import { transferSchemaToTableProp } from '@/utils/utils';

import { useApproveModeContext } from '../../hooks/useContext';

interface DoctorsProp {
  instituteId: number;
  readonly: boolean;
  instituteType: InstituteTypeEnum;
  doctors?: Doctor[];
  isApprovalPage?: boolean;
}

export const Doctors = forwardRef(
  ({ instituteId, readonly, instituteType, doctors, isApprovalPage = false }: DoctorsProp, ref) => {
    const { t } = useTranslation('partner');
    const certiTypeEnums = useTenantBizDict('certiType') as BizDictItem[];
    const doctorCertificateTypeEnums = useTenantBizDict('doctorCertificateType') as BizDictItem[];
    const [form] = Form.useForm();

    const [visible, setVisible] = useState(false);
    const [uploading, setUploading] = useState(false);
    const [downloading, setDownloading] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [editedDoctor, setEditedDoctor] = useState<Doctor>();
    const { enableApproval } = useApproveModeContext();
    const [doctorList, setDoctorList] = useState<Doctor[]>([]);

    useEffect(() => {
      if (isApprovalPage) {
        setDoctorList(doctors);
      }
    }, [isApprovalPage, doctors]);

    const { formItems: staticFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'STATIC',
      category: 'DOCTOR',
      disabled: readonly,
      type: covertPartnerType2SchemaPartnerType(instituteType as unknown as PartnerTypeEnum),
      entityType: EntityType.PARTNER,
    });

    const { formItems: dynamicFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'DYNAMIC',
      category: 'DOCTOR',
      disabled: readonly,
      type: covertPartnerType2SchemaPartnerType(instituteType as unknown as PartnerTypeEnum),
      entityType: EntityType.PARTNER,
    });

    useImperativeHandle(ref, () => {
      return doctorList;
    });

    const getTableData = useCallback(
      async ({ current, pageSize }: PaginationProps) => {
        if (instituteId) {
          const doctorRes = await ChannelService.getDoctors(instituteId, {
            pageIndex: current - 1,
            pageSize: pageSize,
          });
          if (enableApproval) {
            setDoctorList(doctorRes?.data ?? []);
          }
          return {
            total: doctorRes?.totalElements,
            list: doctorRes?.data,
          };
        } else {
          return {
            total: 0,
            list: [],
          };
        }
      },
      [instituteId]
    );

    const { tableProps, search } = useAntdTable(getTableData, {
      onError: error => message.error(error?.message),
      refreshDeps: [instituteId],
    });

    const { tableProps: approvalTableProps } = useAntdTable(
      async () => {
        return {
          list: doctorList,
          total: doctorList.length,
        };
      },
      {
        refreshDeps: [doctorList],
      }
    );

    const uploadProps = useMemo(
      () =>
        getUploadPropsNew(
          `/api/channel/v2/file/upload/?type=${DownloadOrUploadType.HOSPITAL_DOCTOR}&id=${instituteId}`,
          () => search.submit(),
          setUploading
        ),
      [instituteId, search]
    );

    const download = useCallback(() => {
      setDownloading(true);
      ChannelService.download({
        type: DownloadOrUploadType.HOSPITAL_DOCTOR,
        id: instituteId,
      })
        .then(downloadFile)
        .finally(() => setDownloading(false));
    }, [instituteId]);

    const onEdit = useCallback((doctor: Doctor) => {
      setVisible(true);
      setEditedDoctor(doctor);
      form.setFieldsValue(doctor);
    }, []);

    const onDelete = useCallback(
      (doctorId: number) => {
        if (enableApproval) {
          const staffIndex = doctorList.findIndex(item => item.id === doctorId);

          doctorList.splice(staffIndex, 1);

          setDoctorList([...doctorList]);
          return;
        }
        return ChannelService.deleteDoctor(instituteId, doctorId)
          .then(() => {
            message.success(t('Delete successfully'));
            search.submit();
          })
          .catch((error: Error) => {
            message.error(error?.message);
          });
      },
      [instituteId, search, enableApproval, doctorList]
    );

    const columns: ColumnProps<Doctor>[] = useMemo(
      () => [
        ...transferSchemaToTableProp([...staticFields, ...dynamicFields] as SchemaProps[]),
        {
          title: t('Actions'),
          align: 'right',
          fixed: 'right',
          width: 114,
          render: (doctor: Doctor) => (
            <TableActionsContainer>
              <DeleteAction disabled={readonly} onClick={() => onDelete(doctor.id)} doubleConfirmType="modal" />
              {readonly ? <ViewAction onClick={() => onEdit(doctor)} /> : <EditAction onClick={() => onEdit(doctor)} />}
            </TableActionsContainer>
          ),
        },
      ],
      [certiTypeEnums, readonly, onDelete, onEdit, doctorCertificateTypeEnums]
    );

    const onClose = useCallback(() => {
      setVisible(false);
      setEditedDoctor(null);
      form.resetFields();
    }, []);

    const onSubmit = useCallback(() => {
      form.validateFields().then(values => {
        if (enableApproval) {
          if (editedDoctor) {
            const staffIndex = doctorList.findIndex(item => item === editedDoctor);

            doctorList[staffIndex] = { ...values };
            setEditedDoctor(undefined);

            setDoctorList([...doctorList]);
          } else {
            doctorList.push(values);
            setDoctorList([...doctorList]);
          }
          onClose();
          return;
        }
        setSubmitting(true);
        let submitReq: Promise<void>;
        if (editedDoctor) {
          submitReq = ChannelService.updateDoctor(instituteId, editedDoctor.id, values).then(() => {
            message.success(t('Edited successfully'));
            onClose();
            search.submit();
          });
        } else {
          submitReq = ChannelService.addDoctor(instituteId, values).then(() => {
            message.success(t('Add successfully'));
            onClose();
            search.submit();
          });
        }
        submitReq.catch((error: Error) => message.error(error?.message)).finally(() => setSubmitting(false));
      });
    }, [instituteId, editedDoctor, onClose, search]);

    // 根据approval来切换数据源
    const finalTableProps = enableApproval ? approvalTableProps : tableProps;

    return (
      <div>
        {!readonly && !isApprovalPage && (
          <div className="flex-between" style={{ marginTop: 16 }}>
            <Button
              icon={<PlusOutlined />}
              onClick={() => setVisible(true)}
              disabled={readonly || finalTableProps?.loading}
            >
              {t('Add New')}
            </Button>
            {!enableApproval && (
              <div>
                <Upload {...(uploadProps as UploadProps)}>
                  <Button
                    icon={<UploadOutlined />}
                    disabled={readonly || finalTableProps?.loading}
                    loading={uploading}
                    style={{ marginRight: 6 }}
                  >
                    {t('Upload')}
                  </Button>
                </Upload>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={download}
                  loading={downloading}
                  disabled={readonly || finalTableProps?.loading}
                >
                  {t('Download')}
                </Button>
              </div>
            )}
          </div>
        )}
        <Table
          columns={columns}
          rowKey="id"
          scroll={{ x: 'max-content' }}
          bordered={false}
          style={{ marginTop: 16, marginBottom: 16 }}
          {...finalTableProps}
          pagination={{
            size: 'small',
            total: finalTableProps?.pagination?.total,
            current: finalTableProps?.pagination?.current,
            pageSize: finalTableProps?.pagination?.pageSize,
            onChange: (current, pageSize) =>
              finalTableProps?.onChange({ ...finalTableProps?.pagination, current, pageSize }),
          }}
        />

        <DrawerForm
          title={t('Doctor')}
          visible={visible}
          closable={false}
          maskClosable={false}
          onClose={onClose}
          onSubmit={onSubmit}
          cancelText={t('Cancel')}
          sendText={t('Confirm')}
          submitBtnShow={!readonly}
          submitBtnProps={{ loading: submitting }}
        >
          <CommonForm form={form} fields={[...staticFields, ...dynamicFields]} />
        </DrawerForm>
      </div>
    );
  }
);
