import { forwardRef, useCallback, useEffect, useImperative<PERSON>andle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DownloadOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Form, Tooltip, Upload, message } from 'antd';
import type { PaginationProps } from 'antd/lib/pagination';
import type { ColumnProps } from 'antd/lib/table';
import type { UploadProps } from 'antd/lib/upload';

import { useAntdTable } from 'ahooks';

import { Table, TableActionsContainer } from '@zhongan/nagrand-ui';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import type { BizDictItem, Doctor } from 'genesis-web-service';
import { ChannelService, DownloadOrUploadType } from 'genesis-web-service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { CommonForm } from '@/components/CommonForm/Form';
import { DeleteOutline, EditOutline, ViewSquareOutline } from '@/components/Icons';
import { PaginationComponent } from '@/components/Pagination';
import { RenderEnums } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { useDoctorFormFields } from '@/pages/common-party/partner-setting/hooks/useFormFields';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';


import { useApproveModeContext } from '../../hooks/useContext';

interface DoctorsProp {
  instituteId: number;
  readonly: boolean;
  doctors?: Doctor[];
  isApprovalPage?: boolean;
}

export const Doctors = forwardRef(
  ({ instituteId, readonly, doctors, isApprovalPage = false }: DoctorsProp, ref) => {
    const { t } = useTranslation('partner');
    const certiTypeEnums = useTenantBizDict('certiType') as BizDictItem[];
    const doctorCertificateTypeEnums = useTenantBizDict('doctorCertificateType') as BizDictItem[];
    const [form] = Form.useForm();

    const formFields = useDoctorFormFields({ disabled: readonly });
    const [visible, setVisible] = useState(false);
    const [uploading, setUploading] = useState(false);
    const [downloading, setDownloading] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [editedDoctor, setEditedDoctor] = useState<Doctor>();
    const { enableApproval } = useApproveModeContext();
    const [doctorList, setDoctorList] = useState<Doctor[]>([]);

    useEffect(() => {
      if (isApprovalPage) {
        setDoctorList(doctors);
      }
    }, [isApprovalPage, doctors]);

    useImperativeHandle(ref, () => {
      return doctorList;
    });

    const getTableData = useCallback(
      async ({ current, pageSize }: PaginationProps) => {
        if (instituteId) {
          const doctorRes = await ChannelService.getDoctors(instituteId, {
            pageIndex: current - 1,
            pageSize: pageSize,
          });
          if (enableApproval) {
            setDoctorList(doctorRes?.data);
          }
          return {
            total: doctorRes?.totalElements,
            list: doctorRes?.data,
          };
        } else {
          return {
            total: 0,
            list: [],
          };
        }
      },
      [instituteId]
    );

    const { tableProps, search } = useAntdTable(getTableData, {
      onError: error => message.error(error?.message),
      refreshDeps: [instituteId],
    });

    const { tableProps: approvalTableProps } = useAntdTable(
      async () => {
        return {
          list: doctorList,
          total: doctorList.length,
        };
      },
      {
        refreshDeps: [doctorList],
      }
    );

    const uploadProps = useMemo(
      () =>
        getUploadPropsNew(
          `/api/channel/v2/file/upload/?type=${DownloadOrUploadType.HOSPITAL_DOCTOR}&id=${instituteId}`,
          () => search.submit(),
          setUploading
        ),
      [instituteId, search]
    );

    const download = useCallback(() => {
      setDownloading(true);
      ChannelService.download({
        type: DownloadOrUploadType.HOSPITAL_DOCTOR,
        id: instituteId,
      })
        .then(downloadFile)
        .finally(() => setDownloading(false));
    }, [instituteId]);

    const onEdit = useCallback((doctor: Doctor) => {
      setVisible(true);
      setEditedDoctor(doctor);
      form.setFieldsValue(doctor);
    }, []);

    const onDelete = useCallback(
      (doctorId: number) => {
        if (enableApproval) {
          const staffIndex = doctorList.findIndex(item => item.id === doctorId);

          doctorList.splice(staffIndex, 1);

          setDoctorList([...doctorList]);
          return;
        }
        return ChannelService.deleteDoctor(instituteId, doctorId)
          .then(() => {
            message.success(t('Delete successfully'));
            search.submit();
          })
          .catch((error: Error) => {
            message.error(error?.message);
          });
      },
      [instituteId, search, enableApproval, doctorList]
    );

    const columns: ColumnProps<Doctor>[] = useMemo(
      () => [
        {
          title: t('Doctor Name'),
          dataIndex: 'doctorName',
          fixed: 'left',
        },
        {
          title: t('Title'),
          dataIndex: 'title',
        },
        {
          title: t('Department'),
          dataIndex: 'department',
        },
        {
          title: t('Doctor ID type'),
          dataIndex: 'idType',
          render: (idType: string) => <RenderEnums enums={certiTypeEnums} keyName={idType} />,
        },
        {
          title: t('ID No'),
          dataIndex: 'idNo',
        },
        {
          title: t('Phone No.'),
          dataIndex: 'phoneNo',
        },
        {
          title: t('E-mail'),
          dataIndex: 'email',
        },
        {
          title: t('Certificate Type'),
          dataIndex: 'certificateType',
          render: (certificateType: string) => (
            <RenderEnums enums={doctorCertificateTypeEnums} keyName={certificateType} />
          ),
        },
        {
          title: t('Certificate No'),
          dataIndex: 'certificateNo',
        },
        {
          title: t('Actions'),
          align: 'right',
          fixed: 'right',
          width: 114,
          render: (doctor: Doctor) => (
            <div className="table-actions">
              <TableActionsContainer>
                <DeleteConfirm onOk={() => onDelete(doctor.id)} disabled={readonly}>
                  <Tooltip title={t('Delete')}>
                    <Button icon={<DeleteOutline />} disabled={readonly} type="link" />
                  </Tooltip>
                </DeleteConfirm>
                {readonly ? (
                  <Tooltip title={t('View')}>
                    <Button icon={<ViewSquareOutline />} type="link" onClick={() => onEdit(doctor)} />
                  </Tooltip>
                ) : (
                  <Tooltip title={t('Edit')}>
                    <Button icon={<EditOutline />} type="link" onClick={() => onEdit(doctor)} />
                  </Tooltip>
                )}
              </TableActionsContainer>
            </div>
          ),
        },
      ],
      [certiTypeEnums, readonly, onDelete, onEdit, doctorCertificateTypeEnums]
    );

    const onClose = useCallback(() => {
      setVisible(false);
      setEditedDoctor(null);
      form.resetFields();
    }, []);

    const onSubmit = useCallback(() => {
      form.validateFields().then(values => {
        if (enableApproval) {
          if (editedDoctor) {
            const staffIndex = doctorList.findIndex(item => item === editedDoctor);

            doctorList[staffIndex] = { ...values };
            setEditedDoctor(undefined);

            setDoctorList([...doctorList]);
          } else {
            doctorList.push(values);
            setDoctorList([...doctorList]);
          }
          onClose();
          return;
        }
        setSubmitting(true);
        let submitReq: Promise<void>;
        if (editedDoctor) {
          submitReq = ChannelService.updateDoctor(instituteId, editedDoctor.id, values).then(() => {
            message.success(t('Edited successfully'));
            onClose();
            search.submit();
          });
        } else {
          submitReq = ChannelService.addDoctor(instituteId, values).then(() => {
            message.success(t('Add successfully'));
            onClose();
            search.submit();
          });
        }
        submitReq.catch((error: Error) => message.error(error?.message)).finally(() => setSubmitting(false));
      });
    }, [instituteId, editedDoctor, onClose, search]);

    // 根据approval来切换数据源
    const finalTableProps = enableApproval ? approvalTableProps : tableProps;

    return (
      <div>
        {!readonly && !isApprovalPage && (
          <div className="flex-between" style={{ marginTop: 16 }}>
            <Button
              icon={<PlusOutlined />}
              onClick={() => setVisible(true)}
              disabled={readonly || finalTableProps?.loading}
            >
              {t('Add New')}
            </Button>
            {!enableApproval && (
              <div>
                <Upload {...(uploadProps as UploadProps)}>
                  <Button
                    icon={<UploadOutlined />}
                    disabled={readonly || finalTableProps?.loading}
                    loading={uploading}
                    style={{ marginRight: 6 }}
                  >
                    {t('Upload')}
                  </Button>
                </Upload>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={download}
                  loading={downloading}
                  disabled={readonly || finalTableProps?.loading}
                >
                  {t('Download')}
                </Button>
              </div>
            )}
          </div>
        )}
        <Table
          columns={columns}
          rowKey="id"
          scroll={{ x: 'max-content' }}
          bordered={false}
          style={{ marginTop: 16, marginBottom: 16 }}
          {...finalTableProps}
          pagination={false}
        />
        <PaginationComponent
          size="small"
          total={finalTableProps?.pagination?.total}
          pagination={finalTableProps?.pagination}
          handlePaginationChange={(current, pageSize) =>
            finalTableProps?.onChange({ ...finalTableProps?.pagination, current, pageSize })
          }
        />

        <DrawerForm
          title={t('Doctor')}
          visible={visible}
          closable={false}
          maskClosable={false}
          onClose={onClose}
          onSubmit={onSubmit}
          cancelText={t('Cancel')}
          sendText={t('Confirm')}
          submitBtnShow={!readonly}
          submitBtnProps={{ loading: submitting }}
        >
          <CommonForm form={form} fields={formFields} />
        </DrawerForm>
      </div>
    );
  }
);