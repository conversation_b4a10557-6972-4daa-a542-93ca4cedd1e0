import type { Ref } from 'react';
import { forwardRef, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Divider, Radio, message } from 'antd';

import { SimpleSectionHeader } from '@zhongan/nagrand-ui';

import { ModalConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import type { Doctor, InstituteTypeEnum } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';

import { Departments } from '@/pages/common-party/partner-setting-v2/components/DepartmentAndDoctor/Departments';
import { Doctors } from '@/pages/common-party/partner-setting-v2/components/DepartmentAndDoctor/Doctors';

import { useApproveModeContext } from '../../hooks/useContext';

interface DepartmentAndDoctorProps {
  instituteId: number;
  readonly: boolean;
  instituteType: InstituteTypeEnum;
  isApprovalPage?: boolean;
  doctors?: Doctor[];
  departments?: [];
  doctorRef: Ref<unknown>;
  departmentRef: Ref<unknown>;
}

enum RadioType {
  Doctor = 'Doctor',
  Department = 'Department',
}

export const DepartmentAndDoctor = forwardRef(
  (
    {
      instituteId,
      readonly,
      instituteType,
      isApprovalPage = false,
      doctors,
      departments,
      doctorRef,
      departmentRef,
    }: DepartmentAndDoctorProps,
    ref
  ) => {
    const { t } = useTranslation('partner');
    const [radioValue, setRadioValue] = useState<RadioType>();
    const { enableApproval } = useApproveModeContext();

    useEffect(() => {
      if (!instituteId) {
        if (isApprovalPage) {
          if (departments?.length > 0) {
            setRadioValue(RadioType.Department);
          } else {
            setRadioValue(RadioType.Doctor);
          }
          return;
        }
        if (enableApproval) {
          setRadioValue(RadioType.Doctor);
        }
        return;
      }
      ChannelService.getDepartments(instituteId, {
        pageIndex: 0,
        pageSize: 1,
      })
        .then(({ totalElements }) => {
          if (totalElements > 0) {
            setRadioValue(RadioType.Department);
            return;
          }

          setRadioValue(RadioType.Doctor);
        })
        .catch(error => message.error(error.message));
    }, [instituteId]);

    const clearDepartment = useCallback(
      (newRadioValue: RadioType) => {
        if (enableApproval) {
          setRadioValue(newRadioValue);
          return Promise.resolve();
        }
        ChannelService.clearDepartment(instituteId)
          .then(() => setRadioValue(newRadioValue))
          .catch(error => message.error(error.message));
      },
      [instituteId, enableApproval]
    );

    const getRadioContent = useCallback(
      (text: string, value: RadioType) => (
        <ModalConfirm
          needStopPropagation
          icon={<ExclamationCircleOutlined />}
          title={t('Warning')}
          content={t('Changing the classification will clear all the details. Confirm to change?')}
          okText={t('Confirm')}
          onOk={() => clearDepartment(value)}
        >
          <Radio value={value}>{text}</Radio>
        </ModalConfirm>
      ),
      [clearDepartment, t]
    );

    return (
      <>
        {(enableApproval || instituteId) && (
          <>
            <Divider className="my-6" />
            <SimpleSectionHeader type="h5" weight="bold">
              {t('Department & Doctor')}
            </SimpleSectionHeader>
            <Radio.Group disabled={readonly || (!radioValue && !enableApproval)} value={radioValue} className="mt-2">
              {getRadioContent(t('Doctor'), RadioType.Doctor)}
              {getRadioContent(t('Department'), RadioType.Department)}
            </Radio.Group>
            {radioValue === RadioType.Doctor && (
              <Doctors
                isApprovalPage={isApprovalPage}
                doctors={doctors}
                ref={doctorRef}
                instituteId={instituteId}
                readonly={readonly}
                instituteType={instituteType}
              />
            )}
            {radioValue === RadioType.Department && (
              <Departments
                isApprovalPage={isApprovalPage}
                departments={departments}
                ref={departmentRef}
                instituteId={instituteId}
                readonly={readonly}
                instituteType={instituteType}
              />
            )}
          </>
        )}
      </>
    );
  }
);
