import { useState, useCallback, useEffect } from 'react';
import { Divider, Radio, message } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { ChannelService } from 'genesis-web-service';
import { Doctors } from '@/pages/common-party/partner-setting-v2/components/DepartmentAndDoctor/Doctors';
import { Departments } from '@/pages/common-party/partner-setting-v2/components/DepartmentAndDoctor/Departments';
import { ModalConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import { TextBody } from '@zhongan/nagrand-ui';
import styles from '../../style.scss';

interface DepartmentAndDoctorProps {
  instituteId: number;
  readonly: boolean;
}

enum RadioType {
  Doctor = 'Doctor',
  Department = 'Department',
}

export const DepartmentAndDoctor = ({
  instituteId,
  readonly,
}: DepartmentAndDoctorProps) => {
  const { t } = useTranslation('partner');
  const [radioValue, setRadioValue] = useState<RadioType>();

  useEffect(() => {
    if (!instituteId) {
      return;
    }
    ChannelService.getDepartments(instituteId, {
      pageIndex: 0,
      pageSize: 1,
    })
      .then(({ totalElements }) => {
        if (totalElements > 0) {
          setRadioValue(RadioType.Department);
          return;
        }

        setRadioValue(RadioType.Doctor);
      })
      .catch(error => message.error(error.message));
  }, [instituteId]);

  const clearDepartment = useCallback(
    (newRadioValue: RadioType) =>
      ChannelService.clearDepartment(instituteId)
        .then(() => setRadioValue(newRadioValue))
        .catch(error => message.error(error.message)),
    [instituteId],
  );

  const getRadioContent = useCallback(
    (text: string, value: RadioType) => (
      <ModalConfirm
        needStopPropagation
        icon={<ExclamationCircleOutlined />}
        title={t('Warning')}
        content={t(
          'Changing the classification will clear all the details. Confirm to change?',
        )}
        okText={t('Confirm')}
        onOk={() => clearDepartment(value)}
      >
        <Radio value={value}>{text}</Radio>
      </ModalConfirm>
    ),
    [clearDepartment, t],
  );

  return (
    <>
      {instituteId && (
        <>
          <Divider style={{ marginTop: 24, marginBottom: 24 }} />
          <p className={styles.sectionTitle}>
            <TextBody type="h5">
              <Divider type="vertical" className={styles.titleBefore} />
              {t('Department & Doctor')}
            </TextBody>
          </p>
          <Radio.Group disabled={readonly || !radioValue} value={radioValue}>
            {getRadioContent(t('Doctor'), RadioType.Doctor)}
            {getRadioContent(t('Department'), RadioType.Department)}
          </Radio.Group>
          {radioValue === RadioType.Doctor && (
            <Doctors instituteId={instituteId} readonly={readonly} />
          )}
          {radioValue === RadioType.Department && (
            <Departments instituteId={instituteId} readonly={readonly} />
          )}
        </>
      )}
    </>
  );
};
