import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, Upload, message } from 'antd';
import type { UploadProps } from 'antd/es/upload';

import { EditableTable, Icon, Table } from '@zhongan/nagrand-ui';

import type { CommonRespWithPagination, Department, InstituteTypeEnum } from 'genesis-web-service';
import { ChannelService, DownloadOrUploadType } from 'genesis-web-service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { usePagination } from '@/pages/common-party/partner-setting/hooks/pagination';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';
import type { ColumnEx } from '@/types/common';

import { useApproveModeContext } from '../../hooks/useContext';

interface DepartmentsProps {
  instituteId: number;
  readonly: boolean;
  instituteType: InstituteTypeEnum;
  departments?: Department[];
  isApprovalPage?: boolean;
}

export const Departments = forwardRef(
  ({ instituteId, readonly, instituteType, isApprovalPage = false, departments }: DepartmentsProps, ref) => {
    const { t } = useTranslation('partner');
    const [form] = Form.useForm();
    const [downloading, setDownloading] = useState(false);
    const [loading, setLoading] = useState(false);
    const [uploading, setUploading] = useState(false);
    const [pagination, setPaginationTotal] = usePagination({
      current: 1,
      pageSize: 10,
      total: 0,
    });
    const [departmentList, setDepartmentList] = useState<Department[]>([]);
    const { enableApproval } = useApproveModeContext();

    useEffect(() => {
      if (isApprovalPage) {
        setDepartmentList(departments);
      }
    }, [isApprovalPage, departments]);

    useImperativeHandle(ref, () => {
      return departmentList;
    });

    const getDepartments = useCallback(async () => {
      setLoading(true);
      try {
        const departmentsResp = await ChannelService.getDepartments(instituteId, {
          pageSize: pagination.pageSize,
          pageIndex: pagination.current - 1,
        });

        setLoading(false);
        setDepartmentList(departmentsResp?.data ?? []);
        setPaginationTotal(departmentsResp?.totalElements || 0);
      } catch {
        setLoading(false);
      }
    }, [instituteId, pagination.current, pagination.pageSize]);

    useEffect(() => {
      if (!instituteId) {
        return;
      }
      getDepartments();
    }, [instituteId, getDepartments]);

    const saveEdit = useCallback(
      (current, editingKey: string) => {
        form
          .validateFields()
          .then(department => {
            const newDepartment = {
              ...current,
              ...department,
            };
            if (enableApproval) {
              newDepartment.key = Math.random();
              if (editingKey === 'add') {
                departmentList.push(newDepartment);
              } else {
                const departmentIndex = departmentList.findIndex(item => +item.key === +editingKey);

                departmentList[departmentIndex] = newDepartment;
              }
              // 过滤掉EditTable默认添加的数据
              setDepartmentList([...departmentList.filter(item => item.key !== 'add')]);
              return;
            }
            setLoading(true);
            const request: Promise<CommonRespWithPagination<Department>> = !current.id
              ? ChannelService.addDepartment(instituteId, newDepartment)
              : ChannelService.updateDepartment(instituteId, newDepartment.id, newDepartment);

            request
              .then(() => {
                message.success(t('Operated successfully'));
                getDepartments();
              })
              .catch((error: Error) => message.error(error.message))
              .finally(() => setLoading(false));
          })
          .catch(() => {});
      },
      [form, instituteId, t, getDepartments, departmentList]
    );

    const deleteDepartment = useCallback(
      (index, item) => {
        if (enableApproval) {
          departmentList.splice(index, 1);

          setDepartmentList([...departmentList]);
          return;
        }
        setLoading(true);
        ChannelService.deleteDepartment(instituteId, item.id)
          .then(() => {
            message.success(t('Delete successfully'));
            getDepartments();
          })
          .catch((error: Error) => {
            message.error(error.message);
            // 删除后要重新请求数据，故不能放在finally里统一置为false
            setLoading(false);
          });
      },
      [instituteId, getDepartments, t, departmentList]
    );

    const columns: ColumnEx<Department>[] = useMemo(
      () => [
        {
          title: t('Department Name'),
          dataIndex: 'departmentName',
          fieldProps: {
            type: 'input',
          },
          editable: true,
          formItemProps: {
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Department Name'),
                }),
              },
            ],
          },
        },
        {
          title: t('Description'),
          dataIndex: 'departmentDesc',
          editable: true,
          fieldProps: {
            type: 'input',
          },
        },
      ],
      [t, readonly]
    );

    const uploadProps = useMemo(
      () =>
        getUploadPropsNew(
          `/api/channel/v2/file/upload/?id=${instituteId}&type=${DownloadOrUploadType.HospitalDepartment}`,
          getDepartments,
          setUploading
        ),
      [instituteId, getDepartments]
    );

    const download = useCallback(() => {
      setDownloading(true);
      ChannelService.download({
        id: instituteId,
        type: DownloadOrUploadType.HospitalDepartment,
      })
        .then(downloadFile)
        .finally(() => setDownloading(false));
    }, [instituteId]);

    if (isApprovalPage) {
      return <Table dataSource={departmentList} columns={columns} scroll={{ y: 300, x: 'max-content' }} />;
    }

    return (
      <EditableTable
        editBtnProps={{
          disabled: () => readonly,
        }}
        deleteBtnProps={{
          disabled: () => readonly,
          handleDelete: deleteDepartment,
        }}
        outForm={form}
        addBtnProps={{
          type: 'default',
          visible: !readonly,
          styles: isApprovalPage ? { display: 'none' } : {},
          handleAdd: () => {
            form.resetFields();
          },
        }}
        rowKey={row => row.id || row.key}
        dataSource={departmentList}
        columns={columns}
        handleConfirm={saveEdit}
        scroll={{ y: 300, x: 'max-content' }}
        rightSection={
          enableApproval ? null : (
            <>
              <Upload {...(uploadProps as UploadProps)}>
                <Button icon={<Icon type="upload" />} disabled={loading} style={{ marginRight: 6 }} loading={uploading}>
                  {t('Upload')}
                </Button>
              </Upload>
              <Button icon={<Icon type="download" />} disabled={loading} loading={downloading} onClick={download}>
                {t('Download')}
              </Button>
            </>
          )
        }
        pagination={pagination}
        setDataSource={setDepartmentList}
      />
    );
  }
);
