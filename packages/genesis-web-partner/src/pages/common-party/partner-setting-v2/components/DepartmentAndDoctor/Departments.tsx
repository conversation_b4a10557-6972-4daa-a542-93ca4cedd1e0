import { Editable } from '@/pages/common-party/partner-setting/components/Editable';
import { usePagination } from '@/pages/common-party/partner-setting/hooks/pagination';
import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';
import {
  UploadOutlined,
  DownloadOutlined,
  PlusOutlined,
  CloseOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import { Button, message, Upload, Form, Input, Tooltip } from 'antd';
import type { UploadProps } from 'antd/es/upload';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';
import type { Department, CommonRespWithPagination } from 'genesis-web-service';
import { ChannelService, DownloadOrUploadType } from 'genesis-web-service';
import type { ColumnEx } from '@/types/common';
import { useCallback, useEffect, useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { EditSquareOutline, DeleteHollowOutline } from '@/components/Icons';
import cssVars from '@/variables.scss';
import styles from '../../style.scss';
import { TableActionsContainer } from '@zhongan/nagrand-ui';

interface DepartmentsProps {
  instituteId: number;
  readonly: boolean;
}

export const Departments = ({ instituteId, readonly }: DepartmentsProps) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const [downloading, setDownloading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [pagination, setPaginationTotal] = usePagination({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [departments, setDepartments] = useState<Department[]>([]);
  const [currentEditItem, setCurrentEditItem] = useState<Department>();

  const getDepartments = useCallback(async () => {
    setLoading(true);
    try {
      const departmentsResp = await ChannelService.getDepartments(instituteId, {
        pageSize: pagination.pageSize,
        pageIndex: pagination.current - 1,
      });

      setLoading(false);
      setDepartments(departmentsResp?.data ?? []);
      setPaginationTotal(departmentsResp?.totalElements || 0);
    } catch {
      setLoading(false);
    }
  }, [instituteId, pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (!instituteId) {
      return;
    }
    getDepartments();
  }, [instituteId, getDepartments]);

  useEffect(() => {
    setCurrentEditItem(null);
  }, [readonly]);

  const addDepartment = useCallback(() => {
    const newDepartment: Department = {
      id: null,
      departmentName: null,
      departmentDesc: null,
      instituteId,
    };

    setCurrentEditItem(newDepartment);
    setDepartments([newDepartment].concat(departments));
  }, [instituteId, departments]);

  const cancelEdit = useCallback(() => {
    // id 不存在时表示新增
    if (!currentEditItem.id) {
      const [, ...items] = departments;
      setDepartments(items);
    }
    setCurrentEditItem(null);
  }, [currentEditItem, departments]);

  const saveEdit = useCallback(() => {
    form
      .validateFields()
      .then(department => {
        setLoading(true);
        const newDepartment = { ...currentEditItem, ...department };
        const request: Promise<CommonRespWithPagination<Department>> =
          !currentEditItem.id
            ? ChannelService.addDepartment(instituteId, newDepartment)
            : ChannelService.updateDepartment(
              instituteId,
              newDepartment.id,
              newDepartment,
            );

        request
          .then(() => {
            message.success(t('Operated successfully'));
            getDepartments();
            setCurrentEditItem(null);
          })
          .catch((error: Error) => message.error(error.message))
          .finally(() => setLoading(false));
      })
      .catch(() => {});
  }, [form, currentEditItem, instituteId, t, getDepartments]);

  const deleteDepartment = useCallback(
    departmentId => {
      setLoading(true);
      ChannelService.deleteDepartment(instituteId, departmentId)
        .then(() => {
          message.success(t('Delete successfully'));
          getDepartments();
        })
        .catch((error: Error) => {
          message.error(error.message);
          // 删除后要重新请求数据，故不能放在finally里统一置为false
          setLoading(false);
        });
    },
    [instituteId, getDepartments, t],
  );

  const onEditClick = useCallback((event, department) => {
    event.preventDefault();
    setCurrentEditItem(department);
  }, []);

  useEffect(() => {
    if (currentEditItem) {
      form.setFieldsValue(currentEditItem);
    } else {
      form.resetFields();
    }
  }, [currentEditItem, form]);

  const commonColumns: ColumnEx<Department>[] = useMemo(
    () => [
      {
        title: t('Department Name'),
        dataIndex: 'departmentName',
        editable: true,
        ellipsis: true,
        editChildren: (
          <Form.Item
            style={{ margin: 0 }}
            name="departmentName"
            rules={[
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Department Name'),
                }),
              },
            ]}
          >
            <Input style={{ width: cssVars.formItemWidth }} />
          </Form.Item>
        ),
      },
      {
        title: t('Description'),
        dataIndex: 'departmentDesc',
        ellipsis: true,
        editable: true,
        editChildren: (
          <Form.Item style={{ margin: 0 }} name="departmentDesc">
            <Input style={{ width: cssVars.formItemWidth }} />
          </Form.Item>
        ),
      },
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        width: 114,
        render: (record: Department) =>
          !readonly && (
            <TableActionsContainer>
              {currentEditItem?.id === record.id ? (
                <>
                  <Button
                    type="link"
                    style={{ minWidth: 15 }}
                    className={styles.btnLink}
                    icon={<CheckOutlined />}
                    onClick={saveEdit}
                  />
                  <Button
                    type="link"
                    style={{ minWidth: 15 }}
                    className={styles.btnLink}
                    icon={<CloseOutlined />}
                    onClick={cancelEdit}
                  />
                </>
              ) : (
                <>
                  <Tooltip title={t('Edit')}>
                    <Button
                      type="link"
                      icon={<EditSquareOutline />}
                      disabled={!!currentEditItem}
                      onClick={event => onEditClick(event, record)}
                    />
                  </Tooltip>
                  <DeleteConfirm
                    disabled={!!currentEditItem}
                    onOk={() => deleteDepartment(record.id)}
                  >
                    <Tooltip title={t('Delete')}>
                      <Button
                        type="link"
                        icon={<DeleteHollowOutline />}
                        disabled={!!currentEditItem}
                      />
                    </Tooltip>
                  </DeleteConfirm>
                </>
              )}
            </TableActionsContainer>
          ),
      },
    ],
    [
      t,
      readonly,
      currentEditItem,
      saveEdit,
      cancelEdit,
      onEditClick,
      deleteDepartment,
    ],
  );

  const uploadProps = useMemo(
    () =>
      getUploadPropsNew(
        `/api/channel/v2/file/upload/?id=${instituteId}&type=${DownloadOrUploadType.HospitalDepartment}`,
        getDepartments,
        setUploading,
      ),
    [instituteId, getDepartments],
  );

  const download = useCallback(() => {
    setDownloading(true);
    ChannelService.download({
      id: instituteId,
      type: DownloadOrUploadType.HospitalDepartment,
    })
      .then(downloadFile)
      .finally(() => setDownloading(false));
  }, [instituteId]);

  return (
    <div>
      {!readonly && (
        <div className="flex-between" style={{ marginTop: 16 }}>
          <Button
            icon={<PlusOutlined />}
            onClick={addDepartment}
            disabled={!!currentEditItem || readonly || loading}
          >
            {t('Add New')}
          </Button>
          <div>
            <Upload {...(uploadProps as UploadProps)}>
              <Button
                icon={<UploadOutlined />}
                disabled={!!currentEditItem || readonly || loading}
                style={{ marginRight: 6 }}
                loading={uploading}
              >
                {t('Upload')}
              </Button>
            </Upload>
            <Button
              icon={<DownloadOutlined />}
              disabled={loading}
              loading={downloading}
              onClick={download}
            >
              {t('Download')}
            </Button>
          </div>
        </div>
      )}

      <Form form={form}>
        <Editable
          scroll={{ x: 'max-content' }}
          style={{ marginTop: 16, marginBottom: 16 }}
          loading={loading}
          commonColumns={commonColumns}
          dataSource={departments}
          currentEditItem={currentEditItem}
          handleValueChange={setCurrentEditItem}
          pagination={pagination}
        />
      </Form>
    </div>
  );
};
