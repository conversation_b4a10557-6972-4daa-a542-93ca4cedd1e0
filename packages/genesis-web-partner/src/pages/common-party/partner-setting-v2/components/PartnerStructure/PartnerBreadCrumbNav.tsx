/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-11-03 18:49:03
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-11-14 17:40:06
 * @Description:
 */
import { FC, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Breadcrumb, Divider, Skeleton } from 'antd';
import clsx from 'clsx';
import qs from 'qs';

import { Icon } from '@zhongan/nagrand-ui';

import type { StructureDetail } from 'genesis-web-service';

import type { BreadCrumbInfo } from '@/pages/common-party/partner-setting-v2/models/index.interface';
import { ModeEnum } from '@/types/common';
import type { LocationQueryParam } from '@/types/common-party';
import { useLocation, useNavigate, useSearchParams } from '@umijs/max';

import styles from './style.scss';
import type { StructureDataProps } from './useStructureData';
import { useStructureData } from './useStructureData';

export const PartnerBreadCrumbNav = ({ id, type, mode }: StructureDataProps) => {
  const navigate = useNavigate();
  const { t } = useTranslation('partner');
  const { state } = useLocation();
  const [searchParams] = useSearchParams();
  const { parentId } = qs.parse(searchParams.toString()) as unknown as LocationQueryParam;
  const currentLevelInstituteId = +(id || parentId);
  const { loading, structure } = useStructureData({
    id: currentLevelInstituteId,
    type,
  });
  const breadListData: BreadCrumbInfo[] = useMemo(() => {
    const tempBreadList: BreadCrumbInfo[] = [];
    const loop = (structureList: StructureDetail[]) => {
      structureList.forEach(data => {
        tempBreadList.push({
          id: data.id,
          name: data?.instituteName,
          query: {
            id: String(data.id),
            modeType: mode === ModeEnum.READ ? mode : ModeEnum.EDIT,
          },
        });
        if (data.childList?.length && data.id !== currentLevelInstituteId) {
          // structure接口会返回当前id所处层级的下一层，但是面包屑只需展示到当前层级，当匹配到id时，不继续往下
          loop(data.childList);
        }
      });
    };
    loop(structure ? [structure] : []);
    if (mode === ModeEnum.ADD) {
      // 新增往最后添加一条
      tempBreadList.push({
        name: t('Add New Partner'),
      });
    }
    return tempBreadList;
  }, [structure, type, mode]);

  const handleBreadCrumbClick = useCallback(
    ({ query }: BreadCrumbInfo) => {
      navigate(`?${qs.stringify(query)}`, {
        state,
      });
    },
    [state]
  );

  return (
    <Skeleton loading={loading} active title={false} paragraph={{ rows: 1 }}>
      {breadListData?.length >= 1 && (
        <Breadcrumb separator={<Divider type="vertical" />} className={styles.breadCrumb}>
          {breadListData?.map((breadCrumb, index) => {
            const active = index < breadListData.length - 1;
            return (
              <Breadcrumb.Item
                key={breadCrumb.id}
                className={clsx([styles.breadCrumbItems, active && styles.jumpBreadCrumb])}
                onClick={() => handleBreadCrumbClick(breadCrumb)}
              >
                <Icon type="detail" />
                {breadCrumb.name}
              </Breadcrumb.Item>
            );
          })}
        </Breadcrumb>
      )}
    </Skeleton>
  );
};
