import type { ReactNode } from 'react';
import { Fragment, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { But<PERSON>, Divider, Drawer, Skeleton, message } from 'antd';
import cls from 'clsx';

import type { StructureDetail } from 'genesis-web-service';
import { ChannelService, DownloadOrUploadType } from 'genesis-web-service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { ChannelTitleMap, MaxShownCount, OnceShownCount } from '@/pages/common-party/utils/constants';
import { DownOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons';

import styles from './style.scss';
import type { StructureDataProps } from './useStructureData';
import { useStructureData } from './useStructureData';

export const PartnerStructure = ({ id, type, mode }: StructureDataProps) => {
  const { loading, structure } = useStructureData({ id, type });
  const [structureVisible, setStructureVisible] = useState(false);
  const { t } = useTranslation('partner');
  const [totalChildren, setTotalChildren] = useState(0); // 当前节点总子节点数
  const [shownCount, setShownCount] = useState(OnceShownCount); // 当前展示的子节点数
  const [moreHoverVisible, setMoreHoverVisible] = useState(false);
  const [downloadHoverVisible, setDownloadHoverVisible] = useState(false);
  const [downloading, setDownloading] = useState(false);

  const moreCountBtnVisible = useMemo(
    () => shownCount < totalChildren && shownCount < MaxShownCount,
    [totalChildren, shownCount]
  );

  const downloadBtnVisible = useMemo(
    () => totalChildren > MaxShownCount && shownCount >= MaxShownCount,
    [totalChildren, shownCount]
  );

  const structureContent = useMemo(() => {
    const createStructure = (channels: StructureDetail[], isSubChannel?: boolean): ReactNode => {
      return channels?.map((item, index) => {
        if (item.id == +id) {
          // 找到当前节点，获取子节点总数
          setTotalChildren(item.childNum);
        }
        return (
          index < shownCount && (
            <Fragment key={item.id}>
              <div
                className={cls(styles.nodeBox, item.id == +id && styles.centerNode, isSubChannel && styles.childNode)}
                key={item.id}
              >
                {item.instituteCode}
              </div>
              {!isSubChannel && item?.childList?.length > 0 && (
                <div className={styles.line}>
                  <Divider type="vertical" />
                  <DownOutlined />
                </div>
              )}
              {item?.childList?.length > 0 && createStructure(item?.childList, item.id == +id)}
            </Fragment>
          )
        );
      });
    };
    return (
      structure && (
        <>
          <div className={styles.entry} key={type}>
            {ChannelTitleMap[type]}
          </div>
          <div className={styles.line}>
            <Divider type="vertical" />
            <DownOutlined />
          </div>
          {createStructure([structure])}
        </>
      )
    );
  }, [id, structure, shownCount, type]);

  const download = useCallback(() => {
    setDownloading(true);
    ChannelService.download({
      type: DownloadOrUploadType.CHANNEL_TREE,
      id: +id,
    })
      .then(downloadFile)
      .catch((error: Error) => message.error(error?.message))
      .finally(() => setDownloading(false));
  }, [id, type]);

  return (
    <>
      {id && (
        <>
          <Button style={{ marginRight: 16 }} size="large" disabled={!id} onClick={() => setStructureVisible(true)}>
            {t('View Structure')}
          </Button>
          <Drawer
            title={t('Structure Details')}
            open={structureVisible}
            maskClosable={false}
            width={641}
            rootClassName={styles.structureDrawer}
            onClose={() => setStructureVisible(false)}
          >
            <Skeleton loading={loading} active>
              <div className={styles.structure}>
                {structureContent}
                {moreCountBtnVisible && (
                  <div
                    className={cls(styles.nodeBox, styles.moreCountBox, moreHoverVisible && styles.hoverBox)}
                    onClick={() => setShownCount(shownCount + OnceShownCount)}
                    onMouseEnter={() => setMoreHoverVisible(true)}
                    onMouseLeave={() => setMoreHoverVisible(false)}
                  >
                    {moreHoverVisible ? <EyeOutlined /> : t('More Partners below', { count: t('50') })}
                  </div>
                )}
                {downloadBtnVisible && (
                  <Button
                    className={cls(styles.nodeBox, styles.moreCountBox, downloadHoverVisible && styles.hoverBox)}
                    onClick={download}
                    onMouseEnter={() => setDownloadHoverVisible(true)}
                    onMouseLeave={() => setDownloadHoverVisible(false)}
                    loading={downloading}
                    icon={downloadHoverVisible && <DownloadOutlined />}
                  >
                    {downloadHoverVisible
                      ? t('Download to View Details')
                      : t('More Partners below', { count: t('500+') })}
                  </Button>
                )}
              </div>
            </Skeleton>
          </Drawer>
        </>
      )}
    </>
  );
};
