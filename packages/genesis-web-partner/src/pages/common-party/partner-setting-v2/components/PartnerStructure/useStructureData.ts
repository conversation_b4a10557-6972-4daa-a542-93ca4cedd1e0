import type { InstituteTypeEnum } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';
import type { ModeEnum } from '@/types/common';
import { useRequest } from 'ahooks';

export interface StructureDataProps {
  id: number;
  type: InstituteTypeEnum;
  mode?: ModeEnum;
  parentId?: string;
}
/**
 *
 * @param id id
 * @param type 当前页面所属type
 * @description 获取面包屑
 */
export const useStructureData = ({ id, type }: StructureDataProps) => {
  const { loading, data: structure } = useRequest(
    async () => {
      if (id)
        return await ChannelService.getInstituteStructure(Number(id), {
          instituteType: type,
        });
    },
    {
      refreshDeps: [id, type],
      cacheKey: `${id}-${type}`,
    },
  );

  return {
    loading,
    structure,
  };
};
