import { ModeEnum } from '@/types/common';
import { useLocation, useNavigate } from '@umijs/max';
import { Icon } from '@zhongan/nagrand-ui';
import type { MenuProps } from 'antd';
import { Button, Dropdown, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import qs from 'qs';
import { useStructureData } from './useStructureData';
import type { StructureDetail } from 'genesis-web-service';
import { InstituteTypeEnum } from 'genesis-web-service';

interface AddNewLevelButtonProps {
  instituteId: number;
  instituteType: InstituteTypeEnum;
  parentId?: string;
  mode: ModeEnum;
  level: string;
}

export const AddNewLevelButton = ({
  instituteId,
  parentId,
  instituteType,
  mode,
  level,
}: AddNewLevelButtonProps) => {
  const { t } = useTranslation('partner');
  const { state } = useLocation();
  const navigate = useNavigate();
  const { loading, structure } = useStructureData({
    id: instituteId,
    type: instituteType,
  });

  const getArrayDepth = (value?: StructureDetail): number => {
    return value?.childList?.length && value?.id !== instituteId
      ? 1 + Math.max(1, getArrayDepth(value?.childList[0]))
      : 0;
  };

  const handleMenuClick: MenuProps['onClick'] = ({ key }: { key: string }) => {
    navigate(
      `/partner-setting-v2/partner-details?${qs.stringify({
        parentId: key === '1' ? parentId : instituteId,
        instituteType,
        modeType: ModeEnum.ADD,
      })}`,
      {
        state,
      },
    );
  };

  const items: MenuProps['items'] = [
    {
      label: t('at the Same Level'),
      key: '1',
      icon: <Icon type="business-category" />,
    },
    {
      label: t('at the Sub Level'),
      disabled: +(level || '1') >= 4,
      key: '2',
      icon: <Icon type="tree-list" />,
    },
  ];

  const menuProps = {
    items,
    onClick: handleMenuClick,
  };

  return (
    <>
      {instituteId &&
        instituteType === InstituteTypeEnum.SERVICE_COMPANY &&
        mode === ModeEnum.EDIT && (
        <Dropdown menu={menuProps}>
          <Button loading={loading} size="large" style={{ marginRight: 16 }}>
            <Space>
              {t('Add New Level')}
              <Icon type="down" />
            </Space>
          </Button>
        </Dropdown>
      )}
    </>
  );
};
