import { forwardRef, useCallback, useEffect, useImperative<PERSON><PERSON>le, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Divider, Form, message } from 'antd';

import { cloneDeep } from 'lodash-es';
import moment from 'moment';

import { Icon, SimpleSectionHeader, Table, TableActionsContainer, TextBody } from '@zhongan/nagrand-ui';

import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { AgreementType, InstituteTypeEnum } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import antdStyles from '@/antd-reset.scss';
import { CommonForm } from '@/components/CommonForm/Form';
import { useEnumsMapping } from '@/pages/common-party/partner-setting/hooks/useEnumsMapping';
import { useFormFields } from '@/pages/common-party/partner-setting/hooks/useFormFields';
import { FormTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';

import { useApproveModeContext } from '../../hooks/useContext';
import styles from '../../style.scss';
import { useServiceAgreementRelatedFields } from './useServiceAgreementFormFields';

interface Props {
  readonly: boolean;
  instituteId: number;
  renew?: boolean;
  instituteType: InstituteTypeEnum;
  serviceAgreements?: AgreementType[];
  isApprovalPage?: boolean;
}

/**
 * @param renew 是否需要重新获取list
 * @param readonly 是否只读
 * @param instituteId 机构id
 * @description Agreement信息展示
 */
export const ServiceAgreementTable = forwardRef(
  ({ readonly, instituteId, renew, instituteType, isApprovalPage = false, serviceAgreements }: Props, ref) => {
    const { t } = useTranslation(['partner']);
    const [form] = Form.useForm();
    const [visible, setVisible] = useState(false);
    const [editedIndex, setEditedIndex] = useState<number>();
    const [editedItem, setEditedItem] = useState<AgreementType>();
    const [shownList, setShownList] = useState<AgreementType[]>();
    const agreementStatusMapping = useEnumsMapping('salesAgreementStatus');
    const { enableApproval } = useApproveModeContext();

    const basicInfoFields = useFormFields({
      formType: FormTypeEnum.SERVICE_AGREEMENT,
      disabled: readonly,
      form,
      editedItem: editedItem,
    });

    const relatedInfoFields = useServiceAgreementRelatedFields({
      disabled: readonly,
      instituteType,
    });

    const queryAgreementList = () => {
      ChannelService.queryServiceAgreementList(instituteId)
        .then(setShownList)
        .catch((error: Error) => message.error(error.message));
    };

    const saveServiceAgreement = () => {
      ChannelService.modifyServiceAgreementList(instituteId, shownList)
        .then(() => {
          if (renew) {
            queryAgreementList();
          }
        })
        .catch((error: Error) => message.error(error.message));
    };

    useImperativeHandle(ref, () => {
      return {
        saveServiceAgreement,
        data: shownList,
      };
    });

    useEffect(() => {
      if (instituteId) {
        queryAgreementList();
      } else if (isApprovalPage) {
        setShownList(serviceAgreements);
      }
    }, [instituteId, serviceAgreements]);

    const editClick = useCallback(
      (item, index: number) => {
        item.effectivePeriod = [
          moment.tz(item.effectiveStartTime, item.zoneId),
          moment.tz(item.effectiveEndTime, item.zoneId),
        ];
        setEditedIndex(index);
        setEditedItem(item);
        form.setFieldsValue(item);
        setVisible(true);
      },
      [form]
    );

    const deleteClick = useCallback(
      (index: number) => {
        const cloneList = cloneDeep(shownList || []);
        cloneList.splice(index, 1);
        setShownList(cloneList);
      },
      [shownList]
    );

    const onClose = useCallback(() => {
      setVisible(false);
      setEditedIndex(null);
      setEditedItem(null);
      form.resetFields();
    }, [form]);

    const onSubmit = useCallback(() => {
      form.validateFields().then(values => {
        const cloneList = cloneDeep(shownList || []);
        values.id = editedItem?.id || null;
        values.effectiveStartTime = dateFormatInstance.formatTz(
          moment(values.effectivePeriod[0]).startOf('day'),
          values.zoneId
        );
        values.effectiveEndTime = dateFormatInstance.formatTz(
          moment(values.effectivePeriod[1]).endOf('day'),
          values.zoneId
        );
        if (values.serviceAgreementRelated) {
          values.serviceAgreementRelated = {
            ...editedItem?.serviceAgreementRelated,
            ...values.serviceAgreementRelated,
          };
        }
        delete values.effectivePeriod;
        if (editedItem) {
          cloneList.splice(editedIndex, 1, values);
        } else {
          cloneList.push(values);
        }
        setShownList(cloneList);
        onClose();
      });
    }, [shownList, form, editedItem, editedIndex, onClose, t]);

    const columns = [
      {
        title: t('Service Agreement Name'),
        dataIndex: 'agreementName',
      },
      {
        title: t('Service Agreement Code'),
        dataIndex: 'agreementCode',
      },
      {
        title: t('Effective Period'),
        render: (_, record?: AgreementType) => (
          <ComponentWithFallback>
            {dateFormatInstance.getDateRangeString(record.effectiveStartTime, record.effectiveEndTime, record.zoneId)}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('Service Agreement Status'),
        dataIndex: 'agreementStatus',
        render: (string: string) => agreementStatusMapping[string],
      },
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (_, item: AgreementType, index: number) => (
          <TableActionsContainer>
            <Icon type={readonly ? 'view' : 'edit'} style={{ fontSize: 16 }} onClick={() => editClick(item, index)} />
            {!item?.id && !enableApproval && (
              <Button disabled={readonly} style={{ margin: 0, padding: 0 }} type="text">
                <Icon type="delete" style={{ fontSize: 16 }} onClick={() => deleteClick(index)} />
              </Button>
            )}
          </TableActionsContainer>
        ),
      },
    ];

    return (
      <section className={styles.addressInfo}>
        {(enableApproval || instituteId) && (
          <>
            {isApprovalPage ? (
              <SimpleSectionHeader type="h5" weight="bold" style={{ marginTop: 8, marginBottom: 16 }}>
                {t('Service Agreement')}
              </SimpleSectionHeader>
            ) : (
              <>
                <Divider style={{ marginTop: 24, marginBottom: 24 }} />
                <p className={styles.sectionTitle}>
                  <TextBody type="h5">
                    <Divider type="vertical" className={styles.titleBefore} />
                    {t('Service Agreement')}
                  </TextBody>
                  <Button icon={<Icon type="add" />} disabled={readonly} onClick={() => setVisible(true)}>
                    {t('Add')}
                  </Button>
                </p>
              </>
            )}

            <Table scroll={{ x: 'max-content' }} key="id" dataSource={shownList} columns={columns} />
            <DrawerForm
              title={t('Service Agreement')}
              visible={visible}
              closable={false}
              onClose={onClose}
              onSubmit={onSubmit}
              cancelText={t('Cancel')}
              sendText={t('Submit')}
              submitBtnShow={!readonly}
              className={antdStyles.antdChannelCenter}
            >
              <div className={styles.agreementTitle}>{t('Basic Information')}</div>
              <CommonForm fields={basicInfoFields} form={form} />
              <Divider />
              <div className={styles.agreementTitle}>{t('Service Related Information')}</div>
              <CommonForm fields={relatedInfoFields} form={form} />
              <Divider />
            </DrawerForm>
          </>
        )}
      </section>
    );
  }
);
