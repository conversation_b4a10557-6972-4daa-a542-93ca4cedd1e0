/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-11-13 15:08:52
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-11-14 17:14:44
 * @Description:
 */
import type { LabeledValue } from '@/components/CommonForm';
import { FieldType } from '@/components/CommonForm';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { InstituteTypeEnum } from 'genesis-web-service';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

export const useServiceAgreementRelatedFields = ({
  disabled,
  instituteType,
}: {
  disabled: boolean;
  instituteType: InstituteTypeEnum;
}) => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, LabeledValue<string>[]>;

  return useMemo(() => {
    if (
      [InstituteTypeEnum.CLINIC, InstituteTypeEnum.HOSPITAL].includes(
        instituteType,
      )
    ) {
      return [
        {
          key: ['serviceAgreementRelated', 'discount'],
          label: t('Hospital Discount'),
          type: FieldType.TextArea,
          col: 24,
          ctrlProps: {
            disabled: disabled,
            allowClear: true,
            rows: 3,
          },
        },
      ];
    }
    return [
      {
        key: ['serviceAgreementRelated', 'settlementType'],
        label: t('Settlement Type'),
        type: FieldType.Select,
        ctrlProps: {
          allowClear: true,
          options: enums?.agreementSettlementType,
          disabled: disabled,
        },
      },
      {
        key: ['serviceAgreementRelated', 'isFixedRate'],
        label: t('Fixed Rate'),
        type: FieldType.Select,
        ctrlProps: {
          allowClear: true,
          options: enums?.yesNo as LabeledValue<string>[],
          disabled: disabled,
        },
      },
      {
        key: ['serviceAgreementRelated', 'initialRate'],
        label: t('Rate for Initial'),
        type: FieldType.InputNumber,
        col: 8,
        ctrlProps: {
          disabled: disabled,
          precision: 2,
          style: {
            width: 240,
          },
        },
      },
      {
        key: ['serviceAgreementRelated', 'additionalRate'],
        label: t('Rate for Additional'),
        type: FieldType.InputNumber,
        col: 8,
        ctrlProps: {
          disabled: disabled,
          precision: 2,
          style: {
            width: 240,
          },
        },
      },
      {
        key: ['serviceAgreementRelated', 'kmRate'],
        label: t('Rate per KM'),
        type: FieldType.InputNumber,
        col: 8,
        ctrlProps: {
          disabled: disabled,
          precision: 2,
          style: {
            width: 240,
          },
        },
      },
    ];
  }, [disabled, enums, instituteType]);
};
