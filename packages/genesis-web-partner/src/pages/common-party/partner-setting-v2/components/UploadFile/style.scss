@import '../../style.scss';
.channel-upload-document {
  padding: $gap-md;
  :global {
    .#{$antd-prefix}-upload.antd-partner-upload-select {
      width: 100%;
      margin-bottom: var(--gap-xs);
    }
    .nagrand-file-item {
      min-height: 62px;
      min-width: auto;
    }
    .nagrand-hover-right .hover-icon {
      margin-left: 8px;
    }
  }
  .doc-list-container {
    flex-wrap: wrap;
  }
  .upload-bottom {
    display: flex;
    .up-down-btn {
      margin-left: 16px;
      color: var(--primary-color);
      cursor: pointer;
      > span {
        margin-left: 4px;
        font-size: 12px;
      }
    }
  }
  .disabled{
    cursor: not-allowed;
    :global{
      .nagrand-upload-drag{
        cursor: not-allowed;
      }
    }
  }
}
