import type { ListShownType, ModeEnum } from '@/types/common';

export enum InstituteStatus {
  INACTIVE = 'INACTIVE',
  ACTIVE = 'ACTIVE',
}

export interface InstituteRouteState<T> {
  pagination: { current: number; pageSize: number };
  searchForm: T;
  currentMode: ListShownType;
}

export interface InstituteSearchForm {
  instituteCode: string;
  instituteName: string;
  instituteType: string;
  instituteStatus: string;
}

export interface BreadCrumbInfo {
  id?: number;
  name: string;
  pathname?: string;
  query?: {
    id: string;
    parentId?: string;
    type?: string;
    modeType: ModeEnum;
  };
}
