import { InstituteTypeEnum } from 'genesis-web-service';

export const ContactShownTypes = [
  InstituteTypeEnum.AssessmentCompany,
  InstituteTypeEnum.ExternalInsuranceCompany,
  InstituteTypeEnum.LegalService,
  InstituteTypeEnum.SERVICE_COMPANY,
];
export const AccountShownTypes = [
  InstituteTypeEnum.AssessmentCompany,
  InstituteTypeEnum.CLINIC,
  InstituteTypeEnum.HOSPITAL,
  InstituteTypeEnum.ExternalInsuranceCompany,
  InstituteTypeEnum.Investigator,
  InstituteTypeEnum.LegalService,
  InstituteTypeEnum.SERVICE_COMPANY,
];
export const ServiceAgreementShownTypes = [
  InstituteTypeEnum.AssessmentCompany,
  InstituteTypeEnum.CLINIC,
  InstituteTypeEnum.HOSPITAL,
  InstituteTypeEnum.Investigator,
];
export const StaffShownTypes = [
  InstituteTypeEnum.AssessmentCompany,
  InstituteTypeEnum.Investigator,
  InstituteTypeEnum.ExternalInsuranceCompany,
];
export const RelationshipShownTypes = [InstituteTypeEnum.SERVICE_COMPANY];
export const ExperiencedFeeShownTypes = [InstituteTypeEnum.CLINIC, InstituteTypeEnum.HOSPITAL];
