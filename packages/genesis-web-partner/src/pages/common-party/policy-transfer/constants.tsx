/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-10-17 14:34:43
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-10-18 17:11:36
 * @Description:
 */
import { Row, Typography } from 'antd';
import type { FormInstance } from 'antd';

import type { FieldDataType } from '@zhongan/nagrand-ui';
import { FieldType } from '@zhongan/nagrand-ui';

import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import type { BizDictItem } from 'genesis-web-service';
import { PolicyStatusEnum } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { RenderEnums, renderEnumsWithString } from '@/components/RenderEnums';
import { StatusTag } from '@/components/StatusTag';
import { TagsShape, TagsType } from '@/utils/constants';
import i18nInstance from '@/utils/i18n';
import { InvalidPolicyNoReg } from '@/utils/utils';

import { SalesChannelAndAgreement } from './components/SalesChannelAndAgreement';

export interface FormSchemaProps {
  agentStatusOptions?: BizDictItem[];
  disabled?: boolean;
}

export const QueryFields = (form: FormInstance): FieldDataType[] => [
  {
    key: 'policyNo',
    label: i18nInstance.t('Policy No.', { ns: 'partner' }),
    getValueFromEvent: event => {
      return event.target.value.replace(InvalidPolicyNoReg, '');
    },
    extraProps: {
      allowClear: true,
    },
  },
  {
    type: FieldType.Customized,
    col: 16,
    render: (
      <Row justify="space-between">
        <SalesChannelAndAgreement
          form={form}
          from="query"
          salesChannelCtrlProps={{
            required: false,
            label: i18nInstance.t('Sales Channel', { ns: 'partner' }),
            key: 'salesCode',
            colProps: {
              span: 12,
              flex: '280px',
            },
          }}
          salesAgreementCtrlProps={{
            required: false,
            label: i18nInstance.t('Sales Agreement', { ns: 'partner' }),
            key: 'agreementCode',
            colProps: {
              span: 12,
              flex: '280px',
            },
          }}
        />
      </Row>
    ),
  },
];

export const Columns = (enums: Record<string, BizDictItem[]>, isAgentTypeEnabled: boolean) => {
  const columns = [
    {
      title: i18nInstance.t('Policy No.', { ns: 'partner' }),
      dataIndex: 'policyNo',
      fixed: 'left',
    },
    {
      title: i18nInstance.t('Status', { ns: 'partner' }),
      dataIndex: 'status',
      render: (status: PolicyStatusEnum) => {
        return status !== PolicyStatusEnum.PolicyEffect ? (
          <StatusTag shape={TagsShape.Round} hasborder={false}>
            <RenderEnums enums={enums?.policyStatus} keyName={status} />
          </StatusTag>
        ) : (
          <StatusTag type={TagsType.Active} shape={TagsShape.Round} hasborder={false}>
            <RenderEnums enums={enums?.policyStatus} keyName={status} />
          </StatusTag>
        );
      },
    },
    {
      title: i18nInstance.t('Sales Channel Name', { ns: 'partner' }),
      dataIndex: 'salesName',
      render: (salesName: string) => (
        <ComponentWithFallback>
          {salesName && (
            <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: { salesName } }}>
              {salesName}
            </Typography.Text>
          )}
        </ComponentWithFallback>
      ),
    },
    {
      title: i18nInstance.t('Sales Channel Code', { ns: 'partner' }),
      dataIndex: 'salesCode',
      render: (salesCode: string) => (
        <ComponentWithFallback>
          {salesCode && (
            <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: { salesCode } }}>
              {salesCode}
            </Typography.Text>
          )}
        </ComponentWithFallback>
      ),
    },
    {
      title: i18nInstance.t('Sales Agreement Name', { ns: 'partner' }),
      dataIndex: 'salesAgreementName',
      render: (salesAgreementName: string) => (
        <ComponentWithFallback>
          {salesAgreementName && (
            <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: { salesAgreementName } }}>
              {salesAgreementName}
            </Typography.Text>
          )}
        </ComponentWithFallback>
      ),
    },
    {
      title: i18nInstance.t('Sales Agreement Code', { ns: 'partner' }),
      dataIndex: 'salesAgreementCode',
      render: (salesAgreementCode: string) => (
        <ComponentWithFallback>
          {salesAgreementCode && (
            <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: { salesAgreementCode } }}>
              {salesAgreementCode}
            </Typography.Text>
          )}
        </ComponentWithFallback>
      ),
    },
    {
      title: i18nInstance.t('Policyholder Name', { ns: 'partner' }),
      dataIndex: 'policyholderName',
    },
    {
      title: i18nInstance.t('Issue Date', { ns: 'partner' }),
      dataIndex: 'issueDate',
      render: (text: string) => dateFormatInstance?.getDateString(text),
    },
    {
      title: i18nInstance.t('Policy Effective Date', { ns: 'partner' }),
      dataIndex: 'effectiveDate',
      render: (text: string) => dateFormatInstance?.getDateString(text),
    },
    {
      title: i18nInstance.t('Expiry Date', { ns: 'partner' }),
      dataIndex: 'expiryDate',
      render: (text: string) => dateFormatInstance?.getDateString(text),
    },
  ];
  if (isAgentTypeEnabled) {
    columns.splice(2, 0, {
      title: i18nInstance.t('Agent Type', { ns: 'partner' }),
      dataIndex: 'agentRoleType',
      render: (agentTypes: string) => {
        const renderContent = agentTypes
          ?.split(',')
          ?.map(agentType => renderEnumsWithString(agentType, enums?.agentRoleType))
          ?.join(', ');
        return (
          <ComponentWithFallback>
            {renderContent && (
              <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: { renderContent } }}>
                {renderContent}
              </Typography.Text>
            )}
          </ComponentWithFallback>
        );
      },
    });
  }
  return columns;
};
