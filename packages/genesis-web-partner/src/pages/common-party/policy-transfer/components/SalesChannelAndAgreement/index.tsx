import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { ColProps, FormInstance } from 'antd';
import { Col, Form, Typography, message } from 'antd';

import { useDebounceFn, useRequest } from 'ahooks';

import { Select } from '@zhongan/nagrand-ui';

import { NoData } from 'genesis-web-component/lib/components/NoData';
import { SearchSelect } from 'genesis-web-component/lib/components/SearchSelect';
import type { SalesAgreementResp } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';

import styles from './index.scss';

interface CtrlProps {
  required: boolean;
  label: string;
  key: string;
  width?: number | string;
  colProps?: ColProps;
}

interface Props {
  form?: FormInstance;
  from: 'query' | 'register';
  salesChannelCtrlProps: CtrlProps;
  salesAgreementCtrlProps: CtrlProps;
}

export const SalesChannelAndAgreement = ({ form, from, salesAgreementCtrlProps, salesChannelCtrlProps }: Props) => {
  const { t } = useTranslation('partner');

  const [searchedAgreementList, setSearchedAgreementList] = useState<SalesAgreementResp[]>([]); // sales agreement搜索结果列表
  const [isAgreementSearchNoData, setAgreementIsSearchNoData] = useState(false); // sales agreement是否搜索无数据
  const [isSearchingAgreement, setIsSearchingAgreement] = useState(false);
  const [selectedAgentCode, setSelectedAgentCode] = useState<string>();

  // 搜索agreement
  const { run: onSearchAgreement } = useDebounceFn(
    (content?: string) => {
      if (!content && !selectedAgentCode) {
        setSearchedAgreementList([]);
        setAgreementIsSearchNoData(false);
        return;
      }
      setIsSearchingAgreement(true);
      ChannelService.querySalesAgreement({
        salesCode: selectedAgentCode,
        salesAgreement: content === '' ? undefined : content,
      })
        .then(res => {
          setSearchedAgreementList(res);
          setAgreementIsSearchNoData(!res?.length);
        })
        .catch((error: Error) => message.error(error?.message))
        .finally(() => setIsSearchingAgreement(false));
    },
    { wait: 200 }
  );

  const onChangeAgent = useCallback(
    (agentCode: string) => {
      setSelectedAgentCode(agentCode);
      form?.resetFields([salesAgreementCtrlProps.key]);
      onSearchAgreement();
    },
    [onSearchAgreement, form, salesAgreementCtrlProps.key]
  );

  return (
    <>
      <Col span={8} {...salesChannelCtrlProps?.colProps}>
        <Form.Item
          name={salesChannelCtrlProps.key}
          label={salesChannelCtrlProps.label}
          rules={[
            {
              required: salesChannelCtrlProps.required,
              message: t('channel.common.required', {
                label: salesChannelCtrlProps.label,
              }),
            },
          ]}
        >
          <SearchSelect
            apiService={ChannelService.queryAllAgents}
            paging={false}
            placeholder={t('Please input')}
            labelKey="firstName"
            valueKey="agentCode"
            searchKey="salesCodeLike"
            optionLabel={['label', 'data.agentCode']}
            debounceWait={300}
            onChange={value => onChangeAgent(value)}
            style={{ width: salesChannelCtrlProps.width ?? 280 }}
          />
        </Form.Item>
      </Col>
      <Col span={8} {...salesAgreementCtrlProps?.colProps}>
        <Form.Item
          name={salesAgreementCtrlProps.key}
          label={salesAgreementCtrlProps.label}
          rules={[
            {
              required: salesAgreementCtrlProps.required,
              message: t('channel.common.required', {
                label: salesAgreementCtrlProps.label,
              }),
            },
          ]}
        >
          <Select
            className={styles.agentSelect}
            showSearch
            placeholder={t('Please input')}
            loading={isSearchingAgreement}
            allowClear
            filterOption={false}
            disabled={from === 'register' && !selectedAgentCode}
            onSearch={onSearchAgreement}
            onClear={() => onSearchAgreement()}
            dropdownMatchSelectWidth={true}
            style={{ width: salesAgreementCtrlProps.width ?? 280 }}
          >
            {searchedAgreementList?.map(agreement => (
              <Select.Option value={agreement.agreementCode} key={agreement.agreementCode}>
                <div className={styles.searchItem}>
                  <Typography.Text
                    style={{ width: 160 }}
                    ellipsis={{
                      tooltip: agreement.agreementName,
                    }}
                  >
                    {agreement.agreementName}
                  </Typography.Text>
                  <Typography.Text ellipsis={{ tooltip: agreement.agreementCode }} className={styles.code}>
                    {agreement.agreementCode}
                  </Typography.Text>
                </div>
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Col>
    </>
  );
};
