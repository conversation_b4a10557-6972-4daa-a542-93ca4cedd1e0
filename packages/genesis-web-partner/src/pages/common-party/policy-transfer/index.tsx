import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useUpdateEffect } from 'ahooks';
import { Button, Form, message } from 'antd';

import { OperationContainer, QueryForm, QueryResultContainer, Table } from '@zhongan/nagrand-ui';

import type { BizDictItem, PolicyTransferReq, PolicyTransferResponseDataType } from 'genesis-web-service';
import { ChannelService, MetadataService, PolicyStatusEnum, YesNoType } from 'genesis-web-service';

import { RegisterOutline } from '@/components/Icons/RegisterOutline';
import { PaginationComponent } from '@/components/Pagination';
import { usePermission } from '@/hooks/usePermissions';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { useDispatch } from '@umijs/max';

import { PolicyTransferRegisterDrawer } from './PolicyTransferRegisterDrawer';
import { Columns, QueryFields } from './constants';
import { History } from './history';
import styles from './index.scss';

const PolicyTransfer = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [queryParams, setQueryParams] = useState<Partial<PolicyTransferReq>>({
    policyNo: undefined,
    agreementCode: undefined,
    salesCode: undefined,
    pageIndex: 0,
    pageSize: 10,
  });
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [policyTransferList, setPolicyTransferList] = useState<PolicyTransferResponseDataType[]>([]);
  const [registrationVisible, setRegistrationVisible] = useState(false);
  const [registrationPolicy, setRegistrationPolicy] = useState<PolicyTransferResponseDataType[]>();
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const hasEditAuth = usePermission('channel.policy-transfer.edit');
  const [isAgentTypeEnabled, setAgentTypeEnabled] = useState(false);

  const queryTransferList = useCallback(async () => {
    setLoading(true);
    // 搜索或者分页条件变更的时候将当前选中的行给清除掉
    setSelectedRows([]);
    const params = {
      policyNo: queryParams?.policyNo,
      salesCode: queryParams?.salesCode,
      agreementCode: queryParams?.agreementCode,
      pageIndex: queryParams?.pageIndex,
      pageSize: queryParams?.pageSize,
    };
    // 清除 搜索条件为空 或者 含有前后空格 的 请求参数
    if (!params?.policyNo?.trim()) {
      delete params?.policyNo;
    }
    if (!params?.salesCode?.trim()) {
      delete params?.salesCode;
    }
    if (!params?.agreementCode?.trim()) {
      delete params?.agreementCode;
    }

    ChannelService.queryPolicyTransferList(params)
      .then(res => {
        setPolicyTransferList(res?.data ?? []);
        // 如果搜索条件中含有policyNo时 默认选中第一项
        if (params?.policyNo) {
          //如果第一项状态为非 effectvite 时不设置为默认选中状态
          setSelectedRows(
            res?.data
              ?.slice(0, 1)
              ?.map(response => response?.status === PolicyStatusEnum?.PolicyEffect && response?.id)
              ?.filter(response => !!response)
          );
        }
        setTotal(res?.totalElements ?? 0);
      })
      .catch((error: Error) => {
        message.error(error?.message);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [queryParams, PolicyStatusEnum]);

  const transferQueryParams = useCallback(
    (values: Omit<PolicyTransferReq, 'pageIndex' | 'pageSize'>) => {
      // 判断查询条件是否为全部空
      const notEmptyQueryCondition = Object.values(values)?.some(queryCondition => queryCondition);
      // 如果没有任何搜索条件的话给出警告提示
      if (!notEmptyQueryCondition) {
        message.warning(t('Please enter at least one search criteria'));
      } else {
        setQueryParams(old => ({
          ...old,
          pageIndex: 0,
          ...values,
        }));
      }
    },
    [t]
  );

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: ['policyStatus', 'agentRoleType'],
    });
  }, [dispatch]);

  // 获取配置的 whetherToEnableAgentType
  useEffect(() => {
    MetadataService.queryBizDictConfigList('whetherToEnableAgentType').then(res => {
      const bizDictConfigList = res.tenantBizDictConfig?.whetherToEnableAgentType?.bizDictConfigList || [];
      setAgentTypeEnabled(bizDictConfigList?.[0]?.dictValue === YesNoType.Yes);
    });
  }, []);

  useUpdateEffect(() => {
    queryTransferList();
  }, [queryParams, queryTransferList]);

  const onChangePagination = useCallback((current: number, pageSize: number) => {
    setQueryParams(old => ({
      ...old,
      // 如果变更的是每页条数 就将当前页码恢复到第一页去
      pageIndex: current - 1,
      pageSize,
    }));
  }, []);

  const handleRegisterClick = useCallback(() => {
    if (!selectedRows?.length) {
      message.warning(t('You should choose a policy first!'));
      return;
    }
    // 传输之后的操作
    setRegistrationVisible(true);
    const selectedPolicy = policyTransferList?.filter(item => selectedRows?.includes(item.id));

    setRegistrationPolicy(selectedPolicy);
  }, [selectedRows, policyTransferList, t]);

  const onCloseRegistration = useCallback(
    (updated: boolean) => {
      if (updated) {
        queryTransferList();
      }
      setRegistrationVisible(false);
      setRegistrationPolicy(undefined);
    },
    [queryTransferList]
  );

  return (
    <div className={styles.policyTransferPage}>
      <QueryForm
        title={t('Policy Transfer')}
        queryFields={QueryFields(form)}
        onSearch={transferQueryParams}
        // 自定义组件需要通过这个方法清空字段
        onClear={() => form.resetFields()}
        formProps={{
          form,
        }}
        loading={loading}
      />
      <History isAgentTypeEnabled={isAgentTypeEnabled} />
      <QueryResultContainer>
        {hasEditAuth && (
          <OperationContainer>
            <OperationContainer.Left>
              <Button
                type="primary"
                ghost
                onClick={() => handleRegisterClick()}
                className={styles.registerButton}
                icon={<RegisterOutline />}
              >
                <span className={styles.registerText}>{t('Register')}</span>
              </Button>
            </OperationContainer.Left>
          </OperationContainer>
        )}
        <Table
          rowSelection={
            hasEditAuth && {
              onChange: (selectedRowKeys: React.Key[]) => {
                // selectedRowKeys: 选中的key列表 selectedRows: 选中的表格数据
                setSelectedRows(selectedRowKeys as number[]);
              },
              selectedRowKeys: selectedRows,
              getCheckboxProps: (record: PolicyTransferResponseDataType) => ({
                // 状态为非PolicyEffect时不可选中
                disabled: record?.status !== PolicyStatusEnum?.PolicyEffect, // Column configuration not to be checked
                name: record?.status,
              }),
              columnWidth: 64,
            }
          }
          loading={loading}
          scroll={{ x: 'max-content' }}
          columns={Columns(enums, isAgentTypeEnabled)}
          dataSource={policyTransferList}
          pagination={false}
          emptyType="icon"
          style={{ marginBottom: 16 }}
          rowKey="id"
        />
        <PaginationComponent
          className="margin-left-16 margin-right-16"
          total={total || 0}
          pagination={{
            current: queryParams?.pageIndex + 1,
            pageSize: queryParams?.pageSize,
          }}
          handlePaginationChange={onChangePagination}
        />
      </QueryResultContainer>
      <PolicyTransferRegisterDrawer
        visible={registrationVisible}
        registrationPolicy={registrationPolicy}
        isAgentTypeEnabled={isAgentTypeEnabled}
        onClose={onCloseRegistration}
      />
    </div>
  );
};

export default PolicyTransfer;
