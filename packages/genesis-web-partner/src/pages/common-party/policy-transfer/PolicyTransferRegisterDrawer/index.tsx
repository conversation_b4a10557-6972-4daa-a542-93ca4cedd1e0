import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DownloadOutlined } from '@ant-design/icons';
import { Form, Modal, Spin, Upload, message } from 'antd';
import type { RcFile } from 'antd/lib/upload';

import { useDispatch } from '@umijs/max';

import { intersection, isArray } from 'lodash-es';
import moment from 'moment';

import { Icon, UploadFileItem } from '@zhongan/nagrand-ui';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { BizDictItem, ChannelDocumentV2, PolicyTransferResponseDataType } from 'genesis-web-service';
import { ChannelService, YesOrNo } from 'genesis-web-service';
import { security } from 'genesis-web-shared';

import type { QueryFieldsType } from '@/components/CommonForm';
import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { AcceptFileTypeList } from '@/pages/common-party/utils/constants';
import { getUrlByFileUniqueCode } from '@/pages/common-party/utils/util';

import { SalesChannelAndAgreement } from '../components/SalesChannelAndAgreement';
import styles from './index.scss';

interface RegisterProps {
  registrationPolicy: PolicyTransferResponseDataType[];
  visible: boolean;
  onClose: (updated?: boolean) => void;
  isAgentTypeEnabled?: boolean;
}

// 需要特殊处理的agentRoleType
enum AgentRoleTypeEnums {
  ISSUE_AGENT = 'ISSUE_AGENT',
  SERVICE_AGENT = 'SERVICE_AGENT',
}

export enum PolicyTransferEffectiveDateType {
  IMMEDIATELY = 'IMMEDIATELY',
  USER_INPUT = 'USER_INPUT',
}

/**
 *
 * @param registrationPolicy 选中的policies
 * @param visible 是否可见
 * @param onClose 关闭抽屉
 * @description policy transfer registration 抽屉
 * @isAgentTypeEnabled tenant是否开启AgentType配置
 */
export const PolicyTransferRegisterDrawer = ({
  registrationPolicy,
  visible,
  onClose,
  isAgentTypeEnabled,
}: RegisterProps) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const agentRoleTypeValue = Form.useWatch('agentRoleType', form);
  const effectiveTypeValue = Form.useWatch('effectiveType', form);
  const [uploading, setUploading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [shownFileList, setShownFileList] = useState<ChannelDocumentV2[]>([]); // 展示的上传文件列表
  const uploadingListRef = useRef<RcFile[]>(); // 正在上传的文件列表

  useEffect(() => {
    if (visible) {
      dispatch({
        type: 'global/getTenantBizDict',
        payload: ['yesNo', 'policyTransferReason'],
      });
    }
  }, [visible]);

  const {
    whetherToEnableAgentType,
    policyNo,
    agentRoleTypeOptions,
    beforeSalesCodeOptions,
    beforeAgreementCodeOptions,
  } = useMemo(() => {
    // 是否开启AgentType的变更
    const INNER_whetherToEnableAgentType = isAgentTypeEnabled && registrationPolicy?.[0]?.isChannelSale === YesOrNo.NO;
    const INNER_policyNo = registrationPolicy?.map(item => item?.policyNo)?.join(', ');
    const INNER_agentRoleTypeOptions = intersection(
      ...(registrationPolicy?.map(item => item?.agentRoleType?.split(',')) || [])
    )?.filter(item => item !== AgentRoleTypeEnums.ISSUE_AGENT);
    const INNER_beforeSalesCodeOptions = intersection(
      ...(registrationPolicy?.map(item => item?.salesCode?.split(',')) || [])
    )?.map(salesCode => ({
      label: salesCode,
      value: salesCode,
    }));
    const INNER_beforeAgreementCodeOptions = intersection(
      ...(registrationPolicy?.map(item => item?.salesAgreementCode?.split(',')) || [])
    )?.map(salesAgreementCode => ({
      label: salesAgreementCode,
      value: salesAgreementCode,
    }));
    if (
      visible &&
      (!INNER_agentRoleTypeOptions?.length ||
        !INNER_beforeSalesCodeOptions?.length ||
        !INNER_beforeAgreementCodeOptions?.length)
    ) {
      message.warning(
        t(
          "Sorry, the transfer can't be conducted, because failed to find the same  Agent Type, Sales Channel Code or Sales Agreement Code on the chosen policies, please check again."
        )
      );
    }

    form.setFieldsValue({
      agentRoleType:
        enums?.defaultAgentTypesForPolicyTransfer?.map(item => item.enumItemName) ?? INNER_agentRoleTypeOptions,
      beforeSalesCode: INNER_beforeSalesCodeOptions?.[0]?.value,
      beforeAgreementCode: INNER_beforeAgreementCodeOptions?.[0]?.value,
    });

    return {
      whetherToEnableAgentType: INNER_whetherToEnableAgentType,
      policyNo: INNER_policyNo,
      agentRoleTypeOptions: INNER_agentRoleTypeOptions,
      beforeSalesCodeOptions: INNER_beforeSalesCodeOptions,
      beforeAgreementCodeOptions: INNER_beforeAgreementCodeOptions,
    };
  }, [registrationPolicy, isAgentTypeEnabled, visible, enums]);

  useEffect(() => {
    const isAgentRoleTypeValueEqualsServiceAgent =
      agentRoleTypeValue?.length === 1 && agentRoleTypeValue.includes(AgentRoleTypeEnums.SERVICE_AGENT);

    if (isAgentRoleTypeValueEqualsServiceAgent) {
      form.resetFields(['commissionReset']);
    }
  }, [agentRoleTypeValue]);

  // 自定义上传
  const handleUpload = useCallback(() => {
    // upload query 处理
    const formData = new FormData();
    uploadingListRef.current?.forEach(file => {
      formData.append('file', file);
    });
    setUploading(true);

    ChannelService.multipleUploadDocument(formData)
      .then(res => {
        if (res?.value?.length) {
          const clonedFileList = shownFileList?.concat(res.value ?? []) ?? res.value;
          setShownFileList(clonedFileList);
          if (res.failNum) {
            Modal.warning({
              title: t('warning'),
              content: t('A total of {totalNumber} documents were uploaded, {failNumber} of them failed to upload.', {
                totalNumber: res.totalNum,
                failNumber: res.failNum,
              }),
              maskClosable: false,
            });
          } else {
            message.success(t('Uploaded successfully'));
          }
        }
      })
      .catch(() => {
        message.error(t('Uploaded failed'));
      })
      .finally(() => {
        uploadingListRef.current = [];
        setUploading(false);
      });
  }, [shownFileList, t]);

  const formFields = useMemo(() => {
    const fields: Partial<QueryFieldsType>[] = [
      {
        key: 'beforeSalesCode',
        label: t('Original Sales Channel Code'),
        type: FieldType.Select,
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Original Sales Channel Code'),
            }),
          },
        ],
        ctrlProps: {
          style: { width: 280 },
          options: beforeSalesCodeOptions,
          allowClear: true,
          disabled: beforeSalesCodeOptions?.length === 1,
        },
      },
      {
        key: 'beforeAgreementCode',
        label: t('Original Sales Agreement Code'),
        type: FieldType.Select,
        col: 12,
        rules: [
          {
            required: registrationPolicy?.[0]?.isChannelSale === YesOrNo.NO,
            message: t('channel.common.required', {
              label: t('Original Sales Agreement Code'),
            }),
          },
        ],
        ctrlProps: {
          style: { width: 280 },
          options: beforeAgreementCodeOptions,
          allowClear: true,
          disabled: beforeAgreementCodeOptions?.length === 1,
        },
      },
      {
        type: FieldType.Customize,
        customerDom: (
          <SalesChannelAndAgreement
            form={form}
            from="register"
            salesChannelCtrlProps={{
              required: true,
              label: t('New Sales Channel'),
              key: 'afterSalesCode',
              colProps: {
                span: 12,
              },
            }}
            salesAgreementCtrlProps={{
              required: true,
              label: t('New Sales Agreement'),
              key: 'afterAgreementCode',
              colProps: {
                span: 12,
              },
            }}
          />
        ),
      },
      {
        key: 'commissionReset',
        label: t('Does the settled commission need to be transferred to the new sales channel'),
        type: FieldType.Radio,
        col: 24,
        initialValue: YesOrNo.NO,
        ctrlProps: {
          style: { width: 280 },
          options: enums?.yesNo,
          disabled: agentRoleTypeValue?.length === 1 && agentRoleTypeValue.includes(AgentRoleTypeEnums.SERVICE_AGENT),
        },
      },
      {
        key: 'effectiveType',
        label: t('Policy Transfer Effective Date Type'),
        type: FieldType.Radio,
        col: 24,
        initialValue: PolicyTransferEffectiveDateType.IMMEDIATELY,
        ctrlProps: {
          style: { width: 280 },
          options: [
            {
              value: PolicyTransferEffectiveDateType.IMMEDIATELY,
              label: t('Immediately'),
            },
            {
              value: PolicyTransferEffectiveDateType.USER_INPUT,
              label: t('User Input'),
            },
          ] as BizDictItem[],
          onChange: () => {
            form.setFieldValue('effectiveDate', undefined);
          },
        },
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Policy Transfer Effective Date Type'),
            }),
          },
        ],
      },
      {
        key: 'transferReason',
        label: t('Policy Transfer Reason'),
        type: FieldType.Select,
        col: 24,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Policy Transfer Reason'),
            }),
          },
        ],
        ctrlProps: {
          style: { width: 280 },
          options: enums?.policyTransferReason,
          allowClear: true,
        },
      },
      {
        key: 'remark',
        label: t('Remark'),
        type: FieldType.TextArea,
        col: 24,
        ctrlProps: {
          style: { width: 280 },
          allowClear: true,
          maxLength: 128,
        },
      },
    ];
    if (whetherToEnableAgentType) {
      fields.splice(2, 0, {
        key: 'agentRoleType',
        label: t('Agent Type'),
        type: FieldType.Select,
        col: 24,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Agent Type'),
            }),
          },
        ],
        ctrlProps: {
          style: { width: 280 },
          options: enums?.agentRoleType?.filter(data => agentRoleTypeOptions?.includes(data.enumItemName)),
          allowClear: true,
          mode: 'multiple',
        },
      });
    }
    if (effectiveTypeValue === PolicyTransferEffectiveDateType.USER_INPUT) {
      const effectiveTypeIndex = fields.findIndex(field => field.key === 'effectiveType');
      fields.splice(effectiveTypeIndex + 1, 0, {
        key: 'effectiveDate',
        label: t('Policy Transfer Effective Date'),
        type: FieldType.DatePicker,
        col: 24,
        ctrlProps: {
          minDate: moment().add(1, 'days'),
          style: { width: 280 },
        },
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Policy Transfer Effective Date'),
            }),
          },
        ],
      });
    }
    return fields;
  }, [
    enums,
    whetherToEnableAgentType,
    agentRoleTypeOptions,
    registrationPolicy,
    beforeAgreementCodeOptions,
    agentRoleTypeValue,
    effectiveTypeValue,
  ]);

  const uploadProps = useMemo(
    () => ({
      headers: { ...security.csrf() },
      showUploadList: false,
      multiple: true,
      beforeUpload: (curFile: RcFile, fileList: RcFile[]) => {
        if (curFile?.uid === fileList?.[fileList.length - 1]?.uid) {
          // 到最后一个文件，可以开始处理上传
          // 批量上传有多个不符合条件的文件，也只弹一次error message，且类型不符的优先于大小不符的
          let existFileTypeError = false;
          let existFileSizeError = false;
          fileList?.forEach(file => {
            const fileType = file.name.split('.').pop();
            const size = file.size / 1024 / 1024;
            if (AcceptFileTypeList.indexOf(`.${fileType}`) === -1) {
              existFileTypeError = true;
            } else if (size > 50) {
              existFileSizeError = true;
            } else {
              uploadingListRef.current = (uploadingListRef.current ?? []).concat(file);
            }
          });
          if (existFileTypeError) {
            message.error(t('You can only upload PDF/DOC/XLS/PNG/JPG'));
          } else if (existFileSizeError) {
            message.error(t('Upload Size Limit', { size: 50 }));
          }
          // 上传文件里有符合要求的
          if (uploadingListRef.current?.length) {
            return true;
          }
        }
        return false;
      },
      customRequest: handleUpload,
    }),
    [handleUpload]
  );

  const onCloseDrawer = useCallback(
    (updated?: boolean) => {
      form.resetFields();
      setShownFileList([]);
      onClose(updated);
    },
    [onClose]
  );

  const onDeleteFile = useCallback(
    (fileUniqueCode: string) => {
      const clonedFileList = [...(shownFileList ?? [])];
      const matchedIndex = clonedFileList.findIndex(file => file.fileUniqueCode === fileUniqueCode);
      clonedFileList.splice(matchedIndex, 1);
      setShownFileList(clonedFileList);
    },
    [shownFileList]
  );

  const onSubmit = useCallback(() => {
    form.validateFields().then(values => {
      setSubmitting(true);
      const transferParams = {
        ...values,
        policyNo,
        fileCodes: shownFileList?.map(file => file.fileUniqueCode)?.join(','),
      };
      // agentRoleType => agentRoleTypes
      if (whetherToEnableAgentType) {
        transferParams.agentRoleType = undefined;
        transferParams.agentRoleTypes = isArray(values?.agentRoleType)
          ? values?.agentRoleType
          : [values?.agentRoleType];
      }
      if (values.effectiveDate) {
        transferParams.effectiveDate = moment(transferParams.effectiveDate).format('YYYY-MM-DD');
      }
      ChannelService.transferPolicy(transferParams)
        .then(() => {
          message.success(t('The policy has been transferred successfully.'));
          setTimeout(() => {
            onCloseDrawer(true);
            setSubmitting(false);
          }, 3000);
        })
        .catch((error: Error) => {
          setSubmitting(false);
          message.error(error?.message);
        });
    });
  }, [policyNo, shownFileList, onCloseDrawer, whetherToEnableAgentType]);

  return (
    <DrawerForm
      visible={visible}
      title={t('Registration')}
      cancelText={t('Cancel')}
      sendText={t('Submit')}
      onSubmit={onSubmit}
      onClose={() => onCloseDrawer()}
      maskClosable={false}
      submitBtnProps={{ loading: submitting }}
      width={752}
      className={styles.transferDrawer}
    >
      <div className={styles.policy}>{`${t('Policy No.')}: ${policyNo}`}</div>
      <CommonForm form={form} fields={formFields} />
      <div className={styles.attachment}>
        <p>{t('Attachment')}</p>
        <Spin spinning={uploading}>
          <Upload.Dragger {...uploadProps} className={styles.dragUploadBox}>
            <Icon type="drag-upload" style={{ fontSize: 40 }} />
            <p className={styles.uploadText}>{t('Click or drag the file here to upload')}</p>
            <p className={styles.uploadHint}>{t('You can only upload PDF/DOC/XLS/PNG/JPG')}</p>
          </Upload.Dragger>
        </Spin>
        {shownFileList?.map(file => {
          const url = getUrlByFileUniqueCode('/api/channel/v2/file/download', file.fileUniqueCode);
          return (
            <UploadFileItem
              fileName={file.fileName}
              fileUrl={url}
              isShowHover={true}
              style={{ width: 640, marginTop: 16 }}
              modalGetContainer={false}
              hoverInfoList={[
                {
                  icon: (
                    <a href={url}>
                      <DownloadOutlined style={{ color: styles.textColor }} />
                    </a>
                  ),
                  key: 'download',
                },
                {
                  icon: <Icon type="delete" />,
                  onClick: () => onDeleteFile(file.fileUniqueCode),
                  disabled: uploading,
                  key: 'delete',
                },
              ]}
              needPreview={['.png', '.jpg', '.jpeg'].includes(file.fileName.slice(file.fileName.lastIndexOf('.')))}
            />
          );
        })}
      </div>
    </DrawerForm>
  );
};
