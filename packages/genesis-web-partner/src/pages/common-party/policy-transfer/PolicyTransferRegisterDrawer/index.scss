@import '@/variables.scss';

.transfer-drawer {
  .agent-select {
    :global {
      .#{$antd-prefix}-select-item-empty {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .search-item {
      display: flex;
      align-items: center;
      .code {
        flex: 1;
        text-align: right;
      }
    }
  }
  .policy {
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 19px;
  }
  .attachment {
    > p {
      color: var(--text-color);
    }
    .drag-upload-box {
      background-color: var(--form-bg);
      border-radius: 16px;
      > span {
        font-size: 40px;
      }
      .upload-text {
        margin-bottom: 4px;
        font-weight: 700;
        color: var(--primary-color);
        font-size: 14px;
      }
      .upload-hint {
        font-size: 12px;
        color: var(--text-disabled-color);
      }
    }
  }
}
