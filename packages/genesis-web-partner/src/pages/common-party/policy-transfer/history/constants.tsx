import { Tooltip, Typography } from 'antd';

import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import type { BizDictItem, PolicyTransferHistoryDataType } from 'genesis-web-service';
import type { PolicyStatusEnum } from 'genesis-web-service';

import { RenderEnums, renderEnumsWithString } from '@/components/RenderEnums';
import i18nInstance from '@/utils/i18n';

import { PolicyTransferEffectiveDateType } from '../PolicyTransferRegisterDrawer';

export const TableColumns = (enums: Record<string, BizDictItem[]>, isAgentTypeEnabled: boolean) => {
  const columns = [
    {
      title: i18nInstance.t('Policy No.', { ns: 'partner' }),
      dataIndex: 'policyNo',
      ellipsis: { showTitle: false },
      width: 240,
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: i18nInstance.t('Sales Channel(Before Change)', { ns: 'partner' }),
      dataIndex: 'beforeSalesName',
      render: (beforeSalesName: string) => (
        <ComponentWithFallback>
          {beforeSalesName && (
            <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: { beforeSalesName } }}>
              {beforeSalesName}
            </Typography.Text>
          )}
        </ComponentWithFallback>
      ),
    },
    {
      title: i18nInstance.t('Sales Channel(After Change)', { ns: 'partner' }),
      dataIndex: 'afterSalesName',
      render: (afterSalesName: string) => (
        <ComponentWithFallback>
          {afterSalesName && (
            <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: { afterSalesName } }}>
              {afterSalesName}
            </Typography.Text>
          )}
        </ComponentWithFallback>
      ),
    },
    {
      title: i18nInstance.t('Sales Agreement(Before Change)', {
        ns: 'partner',
      }),
      dataIndex: 'beforeSalesAgreementName',
      render: (beforeSalesAgreementName: string) => (
        <ComponentWithFallback>
          {beforeSalesAgreementName && (
            <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: { beforeSalesAgreementName } }}>
              {beforeSalesAgreementName}
            </Typography.Text>
          )}
        </ComponentWithFallback>
      ),
    },
    {
      title: i18nInstance.t('Sales Agreement(After Change)', { ns: 'partner' }),
      dataIndex: 'afterSalesAgreementName',
      render: (afterSalesAgreementName: string) => (
        <ComponentWithFallback>
          {afterSalesAgreementName && (
            <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: { afterSalesAgreementName } }}>
              {afterSalesAgreementName}
            </Typography.Text>
          )}
        </ComponentWithFallback>
      ),
    },
    {
      title: i18nInstance.t('Operator', { ns: 'partner' }),
      dataIndex: 'modifiedBy',
    },
    {
      title: i18nInstance.t('Error Message', { ns: 'partner' }),
      dataIndex: 'errorMsg',
    },
    {
      title: i18nInstance.t('Policy Transfer Reason', { ns: 'partner' }),
      dataIndex: 'transferReason',
      render: (status: PolicyStatusEnum) => <RenderEnums enums={enums?.policyTransferReason} keyName={status} />,
    },
    {
      title: i18nInstance.t('Policy Transfer Effective Date', { ns: 'partner' }),
      dataIndex: 'effectiveDate',
      render: (text: string, record: PolicyTransferHistoryDataType) => {
        if (record.effectiveType === PolicyTransferEffectiveDateType.IMMEDIATELY) {
          return i18nInstance.t('Immediately');
        }
        return text;
      },
    },
    {
      title: i18nInstance.t('Remark', { ns: 'partner' }),
      dataIndex: 'remark',
      render: (remark: string) => (
        <ComponentWithFallback>
          {remark && (
            <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: { remark } }}>
              {remark}
            </Typography.Text>
          )}
        </ComponentWithFallback>
      ),
    },
  ];
  if (isAgentTypeEnabled) {
    columns.splice(1, 0, {
      title: i18nInstance.t('Agent Type', { ns: 'partner' }),
      dataIndex: 'agentRoleType',
      render: (agentTypes: string) => {
        const renderContent = agentTypes
          ?.split(',')
          ?.map(agentType => renderEnumsWithString(agentType, enums?.agentRoleType))
          ?.join(', ');
        return (
          <ComponentWithFallback>
            {renderContent && (
              <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: { renderContent } }}>
                {renderContent}
              </Typography.Text>
            )}
          </ComponentWithFallback>
        );
      },
    });
  }
  return columns;
};
