import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ClockCircleOutlined, DownloadOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Drawer, Popconfirm, message } from 'antd';
import type { PaginationProps } from 'antd/es/pagination';
import type { ColumnsType } from 'antd/es/table';

import { useDispatch } from '@umijs/max';

import { ConditionFilter, StatusTag, Table, lessVars } from '@zhongan/nagrand-ui';
import type { StatusType } from '@zhongan/nagrand-ui/dist/components/StatusTag';

import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import { FieldType } from 'genesis-web-component/lib/interface/enum.interface';
import type { BizDictItem, ConditionFieldsConfig, PolicyTransferHistoryDataType } from 'genesis-web-service';
import { ChannelService, DownloadOrUploadType, Reflect<PERSON>ortOrder, SortOrder } from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { RenderEnums } from '@/components/RenderEnums';
import { usePermission } from '@/hooks/usePermissions';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';

import { handleDate } from '../../utils/util';
import { TableColumns } from './constants';
import styles from './index.scss';

interface filterParamsProps {
  modifiedTime?: [moment.Moment, moment.Moment];
  policyNo?: string;
}

// todo 前端先写死枚举，后续接后台国际化
export const useHistoryStatus: () => Record<string, Record<string, string>> = () => {
  const { t } = useTranslation('partner');

  return {
    PROGRESS: {
      name: t('In Progress', { ns: 'partner' }),
      type: 'info',
    },
    COMPLETED: {
      name: t('Completed', { ns: 'partner' }),
      type: 'success',
    },
    ERROR: {
      name: t('Error', { ns: 'partner' }),
      type: 'error',
    },
    CANCEL: {
      name: t('Cancel', { ns: 'partner' }),
      type: 'no-status',
    },
  };
};

export const History = ({ isAgentTypeEnabled }: { isAgentTypeEnabled: boolean }) => {
  const dispatch = useDispatch();
  const { t } = useTranslation('partner');
  const { dateFormat } = useL10n();
  const [loading, setLoading] = useState(false);
  const [policyTransferHistoryList, setPolicyTransferHistoryList] = useState<PolicyTransferHistoryDataType[]>([]);
  const [total, setTotal] = useState(0);
  const [sortOrder, setSortOrder] = useState<SortOrder>(SortOrder.DESC);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [pagination, setPagination] = useState<PaginationProps>({
    current: 1,
    pageSize: 10,
  });
  const [filterParams, setFilterParams] = useState<filterParamsProps>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const hasCancelAuth = usePermission('channel.policy-transfer.cancel');
  const historyStatus = useHistoryStatus();

  const queryTransferHistoryList = useCallback(async () => {
    setLoading(true);

    const uploadDateObj = handleDate('startDate', 'endDate', filterParams?.modifiedTime, 'YYYY-MM-DD HH:mm:ss');

    // 排序方式截取到后三位字符串然后转大写
    const params = {
      sort: ReflectSortOrder[sortOrder],
      pageIndex: pagination.current - 1,
      pageSize: pagination.pageSize,
      ...uploadDateObj,
      policyNo: filterParams.policyNo,
    };
    ChannelService.queryPolicyTransferHistoryList(params)
      .then(res => {
        const list: PolicyTransferHistoryDataType[] = [];
        (res?.data ?? []).forEach(item => {
          // 全部成功不需要分开
          if (
            item.transferDetails.length === 1 ||
            item.transferDetails.every(detail => detail.status === 'COMPLETED') ||
            item.transferDetails.every(detail => detail.status === 'PROGRESS') ||
            item.transferDetails.every(detail => detail.status === 'INITIALIZED')
          ) {
            list.push({
              ...item,
              errorMsg: item.status === 'ERROR' ? item.transferDetails?.[0]?.errorMsg || item.errorMsg : undefined,
            });
          } else {
            item.transferDetails.forEach((detail, index) => {
              list.push({
                ...item,
                ...detail,
                transferDetails: undefined,
                rowSpan: index === 0 ? item.transferDetails.length : 0,
                // 导出的时候要使用父元素的id,transferDetails里面的id没用到，直接覆盖
                id: index === 0 ? item.id : Math.random(),
                errorMsg: detail.status === 'ERROR' ? detail.errorMsg || item.errorMsg : undefined,
              });
            });
          }
        });
        setPolicyTransferHistoryList(list);
        setTotal(res?.totalElements ?? 0);
      })
      .catch((error: Error) => {
        message.error(error?.message);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [sortOrder, pagination, filterParams]);

  // 分页参数变更时获取页面信息
  const onChangePagination = useCallback((current: number, pageSize: number) => {
    setPagination(old => ({
      ...old,
      // 如果变更的是每页条数 就将当前页码恢复到第一页去
      current,
      pageSize,
    }));
  }, []);

  useEffect(() => {
    if (drawerVisible) {
      dispatch({
        type: 'global/getTenantBizDict',
        payload: ['yesNo', 'policyTransferReason'],
      });
    }
  }, [dispatch, drawerVisible]);

  useEffect(() => {
    if (drawerVisible) {
      queryTransferHistoryList();
    }
  }, [queryTransferHistoryList, drawerVisible]);

  const onTableChange = useCallback(
    (
      pages,
      filters,
      sorter,
      extra: {
        currentDataSource: PolicyTransferHistoryDataType[];
        action: string;
      }
    ) => {
      const sort = sortOrder === SortOrder.DESC ? SortOrder.ASC : sortOrder === undefined ? SortOrder.DESC : undefined;
      // 只有在当前操作为 sort 时才进行
      if (extra?.action === 'sort') {
        setSortOrder(sort);
        // 将当前页面重置为第一页
        setPagination(old => ({
          ...old,
          current: 1,
        }));
      }
    },
    [sortOrder]
  );

  const downloadFiles = useCallback((fileUniqueCodeList: string[]) => {
    ChannelService.downloadPolicyTransferHistory(fileUniqueCodeList)
      .then(downloadFile)
      .catch((error: Error) => {
        message.error(error?.message);
      });
  }, []);

  const handleChangeFilter = useCallback((value: filterParamsProps) => {
    setFilterParams(value);
    setPagination(old => ({
      ...old,
      current: 1,
    }));
  }, []);

  const columns: ColumnsType<PolicyTransferHistoryDataType> = useMemo(() => {
    const temp: ColumnsType<PolicyTransferHistoryDataType> = [
      {
        title: t('Operation Time'),
        dataIndex: 'modifiedTime',
        render: (text: string, record) => {
          return dateFormat?.getDateTimeString(text);
        },
        onCell: record => ({
          rowSpan: record.rowSpan ?? 1,
        }),
        sortOrder: sortOrder,
        sorter: true,
      },
      ...TableColumns(enums, isAgentTypeEnabled),
      {
        title: t('Attachment'),
        dataIndex: 'fileCodes',
        render: (fileCodes: string | null) => {
          if (!fileCodes) {
            return <ComponentWithFallback>{fileCodes}</ComponentWithFallback>;
          }
          return (
            <DownloadOutlined style={{ cursor: 'pointer' }} onClick={() => downloadFiles(fileCodes?.split(','))} />
          );
        },
      },
      {
        title: t('Does the settled commission need to be transferred to the new sales channel?'),
        dataIndex: 'commissionReset',
        render: (commissionReset: string) => <RenderEnums enums={enums?.yesNo} keyName={commissionReset} />,
      },
      {
        title: t('Status', { ns: 'partner' }),
        dataIndex: 'status',
        align: 'center',
        fixed: 'right',
        render: (status: string) => (
          <StatusTag
            needDot
            type={historyStatus[status]?.type as StatusType}
            statusI18n={historyStatus[status]?.name}
          />
        ),
      },
    ];

    if (hasCancelAuth) {
      temp.push({
        title: t('Actions'),
        dataIndex: '',
        align: 'center',
        fixed: 'right',
        render: (text, record) => {
          if (record.effectiveType === 'USER_INPUT' && record.status === 'PROGRESS') {
            return (
              <Popconfirm
                placement="left"
                title={t('Are you sure to cancel this record?')}
                okText={t('OK')}
                onConfirm={() => {
                  setLoading(true);
                  ChannelService.cancelTransfer(record.id)
                    .then(() => {
                      message.success(t('Cancel Successfully'));
                      handleChangeFilter({ ...filterParams });
                    })
                    .finally(() => {
                      setLoading(false);
                    });
                }}
              >
                <div style={{ color: lessVars['@primary-color'], cursor: 'pointer' }}>{t('Cancel')}</div>
              </Popconfirm>
            );
          }
          return '';
        },
      });
    }
    return temp;
  }, [dateFormat, t, sortOrder, downloadFiles, enums, hasCancelAuth, filterParams]);

  const panelConditions: ConditionFieldsConfig[] = useMemo(
    () => [
      {
        label: t('Operation Time'),
        fieldKey: 'modifiedTime',
        type: FieldType.RangePicker,
      },
      {
        label: t('Policy No.'),
        fieldKey: 'policyNo',
        type: FieldType.Input,
      },
    ],
    []
  );

  const rowSelection = {
    onChange: (newSelectedRowKeys: number[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    onCell: (record: { rowSpan: number }) => ({
      rowSpan: record.rowSpan ?? 1,
    }),
  };

  const handleDownload = () => {
    ChannelService.batchDownload({
      type: DownloadOrUploadType.POLICY_TRANSFER_HISTORY,
      ids: selectedRowKeys,
    })
      .then(downloadFile)
      .catch((error: Error) => message.error(error?.message || t('Download failed')));
  };

  return (
    <React.Fragment>
      <div className={styles.history} onClick={() => setDrawerVisible(true)}>
        <ClockCircleOutlined style={{ marginRight: 9 }} />
        <span className={styles.historyText}>{t('History')}</span>
      </div>
      <Drawer
        open={drawerVisible}
        onClose={() => {
          setDrawerVisible(false);
          setSelectedRowKeys([]);
          setFilterParams({});
        }}
        width={1096}
        title={t('History')}
        destroyOnClose
        maskClosable
      >
        <div className={styles.historyDrawerOperation}>
          <ConditionFilter
            mode="simple"
            panelConditions={panelConditions}
            appliedCondition={filterParams}
            onChange={handleChangeFilter}
            optionalProps={{
              dateTimeRender: (date: moment.Moment) => dateFormat.getDateString(date)!,
            }}
            className="margin-bottom-8"
          />

          <Button type="primary" ghost onClick={handleDownload} disabled={selectedRowKeys.length === 0}>
            {t('export')}
          </Button>
        </div>

        <Table
          loading={loading}
          scroll={{ x: 'max-content' }}
          columns={columns}
          dataSource={policyTransferHistoryList}
          pagination={{
            size: 'small',
            total: total || 0,
            current: pagination.current,
            pageSize: pagination.pageSize,
            onChange: onChangePagination,
          }}
          style={{ marginBottom: 16 }}
          onChange={onTableChange}
          rowKey="id"
          preserveSelectedRowKeys
          rowSelection={rowSelection}
        />
      </Drawer>
    </React.Fragment>
  );
};
