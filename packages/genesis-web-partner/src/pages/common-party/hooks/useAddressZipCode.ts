import { useMemo } from 'react';

import { useCustomerSchemaDef } from '@/hooks/useCustomerSchemaDef';
import {
  ChannelAddressFieldCodeMap,
  ChannelZipCodeFieldCode,
  DefaultAddressLabelMap,
  DefaultZipCodeLabel,
} from '@/pages/common-party/utils/constants';
import { CustomerAddressFieldCodeList, CustomerType, CustomerZipCodeFieldCode } from '@/utils/constants';

/**
 *
 * @param userType userType 用户类型；取自CustomerType；默认是PERSON
 * @description 获取用户address，zip code schema
 */
export const useAddressZipCodeSchemas = (userType?: CustomerType) => {
  const customerSchemas = useCustomerSchemaDef(userType ?? CustomerType.PERSON);
  return useMemo(() => {
    const addressSchemas = CustomerAddressFieldCodeList.map(fieldCode => {
      const matchedAddressField = customerSchemas?.ADDRESS?.find(field => field.code === fieldCode);
      return {
        label: matchedAddressField?.displayName || DefaultAddressLabelMap[fieldCode],
        key: ChannelAddressFieldCodeMap[fieldCode],
      };
    });

    const matchedZipCodeField = customerSchemas?.ADDRESS?.find(field => field.code === CustomerZipCodeFieldCode);
    const zipCodeSchema = {
      label: matchedZipCodeField?.displayName || DefaultZipCodeLabel,
      key: ChannelZipCodeFieldCode,
    };

    return {
      addressSchemas,
      zipCodeSchema,
    };
  }, [customerSchemas]);
};
