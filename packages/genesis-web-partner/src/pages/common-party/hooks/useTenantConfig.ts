import { useEffect } from 'react';
import { useTenant } from '@/hooks/useUserInfo';
import type { ConnectState } from '@/models/connect';
import type { TenantConfig } from 'genesis-web-service';
import { useDispatch, useSelector } from '@umijs/max';

export const useTenantConfig = () => {
  const dispatch = useDispatch();
  const tenant = useTenant();
  const tenantConfigInfo: TenantConfig = useSelector(
    ({ partnerManagement }: ConnectState) =>
      partnerManagement?.tenantConfigInfo,
  );

  useEffect(() => {
    if (tenant) {
      dispatch({
        type: 'partnerManagement/getTenantConfigInfo',
        payload: tenant,
      });
    }
  }, [tenant]);

  return tenantConfigInfo;
};

export const useOrgType = () => {
  const tenantConfigInfo = useTenantConfig();
  return tenantConfigInfo?.orgType;
};
