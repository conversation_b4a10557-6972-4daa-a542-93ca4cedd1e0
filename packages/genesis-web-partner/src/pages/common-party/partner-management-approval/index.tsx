import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useRequest } from 'ahooks';
import { Button, Form, Input, Tooltip, message } from 'antd';
import { omit, uniq } from 'lodash-es';
import type { Moment } from 'moment';
import moment from 'moment';

import type { ColumnsType } from '@zhongan/nagrand-ui';
import {
  CommonIconAction,
  EllipsisWithCount,
  FieldType,
  Icon,
  Modal,
  OperationContainer,
  QueryForm,
  QueryResultContainer,
  StatusTag,
  Table,
  TableActionsContainer,
  TextEllipsisDetect,
  ViewAction,
} from '@zhongan/nagrand-ui';
import type { StatusType } from '@zhongan/nagrand-ui/dist/components/StatusTag';

import type { ApprovalTaskResponse, ApprovalTaskStateEnum, BizDictItem } from 'genesis-web-service';
import { ChannelService, MetadataService, TaskOperationTypeEnum } from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { usePermission } from '@/hooks/usePermissions';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { useAuthUserInfo } from '@/hooks/useUserInfo';
import { DefaultDateFormat } from '@/pages/common-party/relationship/utils/constants';
import { useDispatch, useLocation, useNavigate } from '@umijs/max';

import { ApproveAndRejectRadioWithIcon } from './components/ApproveAndRejectRadioWithIcon';
import { UploadButton } from './components/UploadButton';
import { canApproveOwnTaskDisabled, stateDisabled } from './util';

enum AcceptAndReject {
  Accept = 'accept',
  Reject = 'reject',
}

interface PartnerManagementApprovalData {
  businessNo?: string;
  businessName?: string;
  businessType?: string;
  businessSubType?: string;
  state?: ApprovalTaskStateEnum;
  operationType?: TaskOperationTypeEnum;
  creationDateStart?: string;
  creationDateEnd?: string;
  creationDate?: Moment[];
}

const StatusTypeMap: Record<string, StatusType> = {
  APPROVAL_IN_PROGRESS: 'warning',
  EXECUTED: 'success',
  WAITING_FOR_APPROVAL: 'info',
  REJECTED: 'error',
};

type SearchQueryType = PartnerManagementApprovalData & {
  pageIndex: number;
  pageSize: number;
};

const PartnerManagementApproval = () => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const { search } = location;

  const searchQueryRef = useRef(
    (location.state as { partnerManagementApproval?: SearchQueryType }) || {
      partnerManagementApproval: {
        pageIndex: 0,
        pageSize: 10,
      },
    }
  );

  const searchQuery = searchQueryRef.current.partnerManagementApproval;
  const setSearchQuery = (values: SearchQueryType) => {
    searchQueryRef.current.partnerManagementApproval = {
      ...values,
    };
    navigate({ search }, { state: { partnerManagementApproval: values }, replace: true });
  };

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: ['instituteType', 'taskOperationType', 'approvalTaskState'],
    });
  }, []);

  const {
    l10n: { dateFormat },
  } = useL10n();

  const partnerTypeOptions = useTenantBizDict('instituteType') as BizDictItem[];
  const operationTypeOptions = useTenantBizDict('taskOperationType') as BizDictItem[];
  const stateOptions = useTenantBizDict('approvalTaskState') as BizDictItem[];

  const [dataSource, setDataSource] = useState<ApprovalTaskResponse[]>([]);
  const [total, setTotal] = useState<number>(0);
  const hasApprovalAuth = usePermission('channel.partner-approval.edit');
  const hasViewAuth = usePermission('channel.partner-approval.view');
  const userInfo = useAuthUserInfo();
  const [approvalModalVisible, setApprovalModalVisible] = useState<boolean>(false);
  const [batchApprovalSelectedRows, setBatchApprovalSelectedRows] = useState<number[]>();
  const [approvalSelectedRow, setApprovalSelectedRow] = useState<number>();

  useEffect(() => {
    searchForm.setFieldsValue({
      ...searchQuery,
      ...(searchQuery.creationDateStart &&
        searchQuery.creationDateEnd && {
          creationDate: [moment(searchQuery.creationDateStart), moment(searchQuery.creationDateEnd)],
        }),
    });
  }, []);

  const { data: { enableApproval, canApproveOwnTask } = {} } = useRequest(() =>
    MetadataService.getPartnerApprovalSwitch()
  );

  const { run: queryTaskList, loading: searchLoading } = useRequest(
    () => ChannelService.approvalQueryTaskList(searchQuery),
    {
      refreshDeps: [searchQuery],
      onSuccess: res => {
        setDataSource(res.data);
        setTotal(res.totalElements);
        setBatchApprovalSelectedRows(undefined);
      },
    }
  );

  const { loading: reviewLoading } = useRequest(
    () => {
      if (approvalModalVisible) {
        return ChannelService.approvalReview(
          (batchApprovalSelectedRows ?? [approvalSelectedRow])?.map(taskId => ({
            taskId,
          }))
        );
      }
    },
    {
      refreshDeps: [approvalModalVisible],
    }
  );

  const onSearch = (values: PartnerManagementApprovalData) => {
    setSearchQuery({
      ...omit(values, 'creationDate'),
      creationDateStart: values.creationDate?.[0]?.format(DefaultDateFormat),
      creationDateEnd: values.creationDate?.[1]?.format(DefaultDateFormat),
      pageIndex: 0,
      pageSize: 10,
    });
  };

  const onDownload = (fileUniqueCodeList: string[]) => {
    ChannelService.downloadPolicyTransferHistory(fileUniqueCodeList)
      .then(downloadFile)
      .catch((error: Error) => message.error(error?.message || t('Download failed')));
  };

  const columns: ColumnsType<ApprovalTaskResponse> = [
    {
      title: t('Partner Name'),
      dataIndex: 'businessName',
    },
    {
      title: t('Partner Code'),
      dataIndex: 'businessNo',
    },
    {
      title: t('Partner Type'),
      dataIndex: 'businessType',
      render: text => partnerTypeOptions?.find(item => item.value === text)?.label,
    },
    {
      title: t('Task Status'),
      dataIndex: 'state',
      render: text => {
        const label = stateOptions?.find(item => item.value === text)?.label;
        if (label) {
          return <StatusTag statusI18n={label} type={StatusTypeMap[text]} needDot />;
        }
      },
    },
    {
      title: t('Task Creation Date'),
      dataIndex: 'creationDate',
      render: text => text && moment(text)?.format(dateFormat.dateFormat),
    },
    {
      title: t('Operation Type'),
      dataIndex: 'operationType',
      render: text => operationTypeOptions?.find(item => item.value === text)?.label,
    },
    {
      title: t('Current Handler'),
      dataIndex: 'reviewerName',
    },
    {
      title: t('Batch Number'),
      dataIndex: 'batchNumber',
    },
    {
      title: t('File Name'),
      dataIndex: 'attachments',
      render: (text: FileRequest[]) =>
        text?.length ? <EllipsisWithCount items={text?.map(item => item.fileName)} connector="," /> : undefined,
    },
    {
      title: t('Comments'),
      dataIndex: 'reviewComment',
      render: text => text && <TextEllipsisDetect width={200} text={text} />,
    },
    {
      title: t('Attachment'),
      dataIndex: 'reviewAttachments',
      render: (text: FileRequest[]) =>
        text?.map(item => (
          <a className="underline block" onClick={() => onDownload([item.fileUniqueCode])}>
            {item?.fileName}
          </a>
        )),
    },
    {
      title: t('Action'),
      fixed: 'right',
      render: (_, record: ApprovalTaskResponse) => {
        return (
          <TableActionsContainer>
            {record.operationType === TaskOperationTypeEnum.BATCH_UPLOAD ? (
              <CommonIconAction
                icon={<Icon type="download" />}
                tooltipTitle={t('Download')}
                disabled={!!batchApprovalSelectedRows}
                onClick={() => onDownload(record.attachments?.map(item => item.fileUniqueCode))}
              />
            ) : (
              hasViewAuth && (
                <ViewAction
                  disabled={!!batchApprovalSelectedRows}
                  onClick={() => navigate(`/partner-setting-v2/partner-approval-details?id=${record.id}`)}
                />
              )
            )}
            {hasApprovalAuth && enableApproval && (
              <CommonIconAction
                icon={<Icon type="clipboard-check" />}
                disabled={
                  stateDisabled(record) ||
                  !!batchApprovalSelectedRows ||
                  canApproveOwnTaskDisabled(record, canApproveOwnTask, userInfo)
                }
                tooltipTitle={
                  canApproveOwnTaskDisabled(record, canApproveOwnTask, userInfo)
                    ? t('You do not have permission to process this task.')
                    : t('Approve')
                }
                onClick={() => {
                  setApprovalModalVisible(true);
                  setApprovalSelectedRow(record.id);
                  form.resetFields();
                }}
              />
            )}
          </TableActionsContainer>
        );
      },
    },
  ];

  const onOk = async () => {
    await form.validateFields();
    const { approvalResult, comment, attachments } = form.getFieldsValue();

    const requestList = (batchApprovalSelectedRows ?? [approvalSelectedRow])?.map(id => ({
      taskId: id,
      comment,
      attachments,
    }));

    if (approvalResult === AcceptAndReject.Accept) {
      await ChannelService.approvalApprove(requestList);
      message.success(t('Accept Successful'));
    } else {
      await ChannelService.approvalReject(requestList);
      message.success(t('Reject Successful'));
    }
    setApprovalModalVisible(false);
    setBatchApprovalSelectedRows(undefined);
    setApprovalSelectedRow(undefined);
    queryTaskList();
  };

  return (
    <div>
      <QueryForm
        title={t('Partner Management Approval')}
        queryFields={[
          {
            label: t('Partner Name'),
            key: 'businessName',
            type: FieldType.Input,
          },
          {
            label: t('Partner Code'),
            key: 'businessNo',
            type: FieldType.Input,
          },
          {
            label: t('Partner Type'),
            key: 'businessType',
            type: FieldType.Select,
            extraProps: {
              options: partnerTypeOptions,
            },
          },
          {
            label: t('Task Status'),
            key: 'state',
            type: FieldType.Select,
            extraProps: {
              options: stateOptions,
            },
          },
          {
            label: t('Task Creation Date'),
            key: 'creationDate',
            type: FieldType.DateRange,
            extraProps: {
              format: dateFormat.dateFormat,
            },
          },
          {
            label: t('Operation Type'),
            key: 'operationType',
            type: FieldType.Select,
            extraProps: {
              options: operationTypeOptions,
            },
          },
        ]}
        onSearch={onSearch}
        formProps={{
          form: searchForm,
        }}
        loading={searchLoading}
      />

      <QueryResultContainer>
        {hasApprovalAuth && enableApproval && (
          <OperationContainer>
            <OperationContainer.Left>
              {batchApprovalSelectedRows ? (
                <>
                  <Button
                    type="primary"
                    onClick={() => {
                      if (batchApprovalSelectedRows.length) {
                        setApprovalModalVisible(true);
                        form.resetFields();
                      } else {
                        message.error(t('Please select at least one item.'));
                      }
                    }}
                  >
                    {t('Approval')}
                    {`(${batchApprovalSelectedRows.length})`}
                  </Button>
                  <Button onClick={() => setBatchApprovalSelectedRows(undefined)} className="ml-4">
                    {t('Cancel')}
                  </Button>
                </>
              ) : (
                <Button onClick={() => setBatchApprovalSelectedRows([])} icon={<Icon type="clipboard-check" />}>
                  {t('Batch Approval')}
                </Button>
              )}
            </OperationContainer.Left>
          </OperationContainer>
        )}
        <Table<ApprovalTaskResponse>
          rowSelection={
            batchApprovalSelectedRows && {
              onChange: (selectedRowKeys: React.Key[]) => {
                if (selectedRowKeys.length === 0) {
                  setBatchApprovalSelectedRows([]);
                  return;
                }

                const tempKeys = uniq([
                  ...batchApprovalSelectedRows.filter(taskId => !dataSource?.some(record => record.id === taskId)),
                  ...selectedRowKeys,
                ]);

                if (tempKeys.length > 10) {
                  message.warning(t('The maximum number of exports is {{max}}', { max: 10 }));
                  return;
                }
                setBatchApprovalSelectedRows(tempKeys as number[]);
              },
              selectedRowKeys: batchApprovalSelectedRows,
              getCheckboxProps: record => {
                return {
                  disabled: stateDisabled(record) || canApproveOwnTaskDisabled(record, canApproveOwnTask, userInfo),
                  className: 'relative ml-4',
                  children: canApproveOwnTaskDisabled(record, canApproveOwnTask, userInfo) && (
                    <Tooltip title={t('You do not have permission to process this task.')}>
                      <div className="w-4 h-4 absolute left-0 top-0 z-10" />
                    </Tooltip>
                  ),
                };
              },
              columnWidth: 64,
              onCell: record => record,
            }
          }
          loading={searchLoading}
          scroll={{ x: 'max-content' }}
          columns={columns}
          dataSource={dataSource}
          pagination={{
            total,
            current: searchQuery?.pageIndex + 1,
            pageSize: searchQuery?.pageSize,
            onChange: (page: number, pageSize: number) => {
              setSearchQuery({
                ...searchQuery,
                pageIndex: page - 1,
                pageSize,
              });
            },
          }}
          emptyType="icon"
          rowKey="id"
        />
      </QueryResultContainer>
      <Modal
        title={t('Approval')}
        open={approvalModalVisible}
        onOk={onOk}
        onCancel={() => setApprovalModalVisible(false)}
        onClose={() => setApprovalModalVisible(false)}
        okButtonProps={{
          disabled: reviewLoading,
        }}
        destroyOnClose
      >
        <div>
          <Form layout="vertical" form={form}>
            <Form.Item
              label={
                <div className="font-medium">
                  {batchApprovalSelectedRows
                    ? t('All selected tasks perform the following operations')
                    : t('Please review the uploaded file and process this task.')}
                </div>
              }
              name="approvalResult"
              rules={[
                {
                  message: t('Please select'),
                  required: true,
                },
              ]}
              required={false}
            >
              <ApproveAndRejectRadioWithIcon
                options={[
                  {
                    label: t('Accept'),
                    value: AcceptAndReject.Accept,
                  },
                  {
                    label: t('Reject'),
                    value: AcceptAndReject.Reject,
                  },
                ]}
              />
            </Form.Item>
            <Form.Item
              label={t('Comments')}
              name="comment"
              rules={[
                {
                  message: t('Please input'),
                  required: true,
                },
              ]}
              required={false}
            >
              <Input.TextArea placeholder={t('Please Input')} />
            </Form.Item>
            <UploadButton form={form} />
          </Form>
        </div>
      </Modal>
    </div>
  );
};

export default PartnerManagementApproval;
