import type { OAuthUserInfo } from 'genesis-web-service';
import type { ApprovalTaskResponse } from 'genesis-web-service/service-types/channel-types/package';
import { ApprovalTaskStateEnum } from 'genesis-web-service/service-types/channel-types/package';

export const canApproveOwnTaskDisabled = (
  { author }: ApprovalTaskResponse,
  canApproveOwnTask: boolean,
  userInfo: OAuthUserInfo
) => {
  if (author === userInfo?.username && !canApproveOwnTask) {
    return true;
  }
  return false;
};

export const stateDisabled = ({ state }: ApprovalTaskResponse) =>
  ![ApprovalTaskStateEnum.APPROVAL_IN_PROGRESS, ApprovalTaskStateEnum.WAITING_FOR_APPROVAL]?.includes(state);
