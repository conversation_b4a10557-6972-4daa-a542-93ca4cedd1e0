import type { ApprovalTaskResponse, OAuthUserInfo } from 'genesis-web-service';
import { ApprovalTaskStateEnum } from 'genesis-web-service';

export const canApproveOwnTaskDisabled = (
  { author }: ApprovalTaskResponse,
  canApproveOwnTask: boolean,
  userInfo: OAuthUserInfo
) => {
  if (author === userInfo?.username && !canApproveOwnTask) {
    return true;
  }
  return false;
};

export const stateDisabled = ({ state }: ApprovalTaskResponse) =>
  ![ApprovalTaskStateEnum.APPROVAL_IN_PROGRESS, ApprovalTaskStateEnum.WAITING_FOR_APPROVAL]?.includes(state);
