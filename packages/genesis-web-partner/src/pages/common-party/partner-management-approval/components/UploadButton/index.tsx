import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { FormInstance } from 'antd';
import { Button, Form, Upload, message } from 'antd';

import { Icon, UploadFileItem } from '@zhongan/nagrand-ui';

import { ChannelService, DownloadOrUploadType } from 'genesis-web-service';
import { security } from 'genesis-web-shared/lib';

import { getUrlByFileUniqueCode } from '@/pages/common-party/utils/util';

type FileListItem = {
  fileName: string;
  fileUniqueCode: string;
};

export const UploadButton = ({ form }: { form: FormInstance }) => {
  const { t } = useTranslation('partner');
  const [fileList, setFileList] = useState<FileListItem[]>([]);

  const uploadProps = useCallback(
    ({ type, index }: { type: 'reload' | 'upload'; index?: number }) => {
      const customRequest = (options: Record<string, any>) => {
        const formData = new FormData();
        formData.append('file', options.file);
        formData.append('type', DownloadOrUploadType.APPROVAL_ATTACHMENT);
        formData.append('ext', JSON.stringify({ type: 3 }));

        ChannelService.channelUpload(formData)
          .then(response => {
            const currFile = {
              fileName: response.value.fileName,
              fileUniqueCode: response.value.fileUniqueCode,
            };

            if (type === 'reload') {
              fileList.splice(index, 1, currFile);
              setFileList([...fileList]);
            } else {
              fileList.push({
                fileName: response.value.fileName,
                fileUniqueCode: response.value.fileUniqueCode,
              });
            }
            setFileList([...fileList]);
            form.setFieldValue('attachments', [...fileList]);

            message.success(t('Uploaded successfully'));
          })
          .catch((error: Error) => {
            message.error(error?.message);
          });
      };
      return {
        name: 'file',
        accept: '.pdf,.doc,.xls,.xlsx,.png,.jpg,',
        headers: { ...security.csrf() },
        withCredentials: true,
        customRequest,
        showUploadList: false,
      };
    },
    [fileList]
  );

  return (
    <>
      <Form.Item label={t('Attachment')} name="attachments" className="mb-0">
        <Upload
          {...uploadProps({
            type: 'upload',
          })}
        >
          <Button icon={<Icon type="upload" />}>{t('Upload')}</Button>
          <p className="text-xs text-[var(--text-disabled-color)]">{t('You can only upload PDF/DOC/XLS/PNG/JPG')}</p>
        </Upload>
      </Form.Item>
      {fileList?.map((item, index) => {
        const url = getUrlByFileUniqueCode('/api/channel/v2/file/download', item.fileUniqueCode);

        return (
          <UploadFileItem
            className="mb-2"
            fileName={item.fileName}
            fileUrl={url}
            needPreview={['.png', '.jpg'].includes(item.fileName?.slice(item.fileName?.lastIndexOf('.')))}
            hoverInfoList={[
              {
                icon: (
                  <Upload
                    {...uploadProps({
                      type: 'reload',
                      index,
                    })}
                  >
                    <Icon type="reload" />
                  </Upload>
                ),
              },
              {
                icon: <Icon type="delete" />,
                onClick: () => {
                  setFileList(fileList?.filter(i => i !== item));
                },
              },
            ]}
          />
        );
      })}
    </>
  );
};
