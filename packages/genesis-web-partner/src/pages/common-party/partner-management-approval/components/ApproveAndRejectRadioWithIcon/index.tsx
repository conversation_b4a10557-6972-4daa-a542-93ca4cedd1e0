import { useState } from 'react';

import { Icon } from '@zhongan/nagrand-ui';

import styles from './index.scss';

export const ApproveAndRejectRadioWithIcon = ({
  options,
  value: defaultValue,
  onChange,
}: {
  options: {
    label: string;
    value: string;
  }[];
  value?: string;
  onChange?: (value: string) => void;
}) => {
  const [value, setValue] = useState(defaultValue);
  return (
    <div className={styles.acceptRejectButtonRadio}>
      {options?.map((option, index) => (
        <div
          key={option.value}
          className={`inline-block border-solid border-[1px] border-[var(--border-color-base)] rounded-[100px] px-6 py-2 mr-4 ${option.value === value ? (index === 0 ? styles.selectedPrimary : styles.selectedDanger) : ''}`}
          onClick={() => {
            setValue(option.value);
            onChange?.(option.value);
          }}
        >
          <span className="flex items-center gap-2 hover:bg-black">
            <span>{option.label}</span>
            {index === 0 ? <Icon type="icon_approve" /> : <Icon type="icon_reject" />}
          </span>
        </div>
      ))}
    </div>
  );
};
