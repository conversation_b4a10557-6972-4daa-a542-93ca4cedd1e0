import {
  useCallback,
  useMemo,
  useState,
  Suspense,
  useLayoutEffect,
  useRef,
  useEffect,
} from 'react';
import { useTranslation } from 'react-i18next';
import { Route, Routes, useNavigate, useSearchParams } from 'react-router-dom';
import {
  Input,
  Tree,
  Layout,
  Dropdown,
  Button,
  Spin,
  message,
  Skeleton,
  Typography,
} from 'antd';
import type { DataNode } from 'antd/es/tree';
import {
  FileAddOutlined,
  EllipsisOutlined,
  PlusOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { PageTitle } from '@/components/PageTitle';
import { DeleteOutline } from '@/components/Icons';
import { SearchOutline } from '@/components/Icons';
import { NoData } from 'genesis-web-component/lib/components/NoData';
import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import Content from '@/pages/common-party/employee-management/content';
import { usePermission } from '@/hooks/usePermissions';
import { useDebounceFn } from 'ahooks';
import {
  ChannelService,
  ChannelTeamType,
  TeamBusinessType,
} from 'genesis-web-service';
import type { ChannelTeamInfo } from 'genesis-web-service';
import { useDispatch } from '@umijs/max';
import { EmployeeManagementSubRoutes } from '../../../../config/routes.config';
import {
  DeletedContentMap,
  RouterMap,
  EmployeeManagementPrefix,
  TeamTypeTitleMap,
} from '@/pages/common-party/employee-management/util/constants';
import { EmployeeContext } from '@/pages/common-party/employee-management/hooks/useEmployeeManagement';
import styles from './index.scss';
import qs from 'qs';

interface TeamsMapProps {
  teamNames: string[];
  teamInfo: ChannelTeamInfo;
}

const EmployeeManagement = () => {
  const { t } = useTranslation('partner');
  const canEdit = usePermission('channel.employee-management.edit');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { id, parentId: routerParentId } = qs.parse(searchParams.toString());
  const dispatch = useDispatch();

  const [teams, setTeams] = useState<ChannelTeamInfo[]>();
  const [autoExpandParent, setAutoExpandParent] = useState(false); // 是否自动展开父节点
  const [expandedKeys, setExpandedKeys] = useState<number[]>(); // 展开的节点key
  const [selectedKey, setSelectedKey] = useState<number>(); // 选择的节点key
  const [hoveredId, setHoveredId] = useState<number>();
  const [loading, setLoading] = useState(false);
  const [teamsLoading, setTeamsLoading] = useState(false);
  const [isSearchNoData, setIsSearchNoData] = useState(false);
  const teamsMapRef = useRef<Record<number, TeamsMapProps>>();

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'relationship',
        'yesNo',
        'country',
        'certiType',
        'employeeStatus',
        'departmentType',
        'officeCity',
        'workingType',
        'agentStatus',
      ],
    });
  }, []);

  // 循环遍历处理teams
  const loopTeams = useCallback((teamsTree: ChannelTeamInfo[]) => {
    const teamsMap: Record<number, TeamsMapProps> = {};
    const loop = (searchedTeams: ChannelTeamInfo[]) => {
      searchedTeams?.forEach(team => {
        teamsMap[team.id] = {
          teamNames: (teamsMap[team.parentId]?.teamNames ?? []).concat(
            team.teamName,
          ), // 面包屑数组
          teamInfo: team,
        };
        if (team.children?.length) {
          loop(team.children);
        }
      });
    };
    loop(teamsTree);
    teamsMapRef.current = teamsMap;
  }, []);

  // 获取teams tree
  const queryTeams = useCallback(() => {
    setTeamsLoading(true);
    return new Promise((resolve, reject) => {
      ChannelService.queryTeamTree(TeamBusinessType.Employee.toLowerCase())
        .then(res => {
          setTeams(res);
          loopTeams(res);
          resolve(res);
        })
        .catch((error: Error) => {
          message.error(error.message);
          reject();
        })
        .finally(() => setTeamsLoading(false));
    });
  }, [loopTeams]);

  // 防抖搜索
  const { run: onSearch } = useDebounceFn(
    async (searchContent: string) => {
      setTeamsLoading(true);
      if (searchContent === '') {
        // 清除搜索内容时回到初始状态
        await queryTeams();
        setIsSearchNoData(false);
        setAutoExpandParent(false);
        setExpandedKeys([]);
        setSelectedKey(null);
        return;
      }
      ChannelService.searchTeamTree(
        TeamBusinessType.Employee.toLowerCase(),
        searchContent,
      )
        .then(res => {
          setTeams(res);
          loopTeams(res);
          if (!res?.length) {
            setIsSearchNoData(true);
            return;
          }
          // 拿到结果后全部展开
          setAutoExpandParent(true);
          const keys: number[] = Object.keys(teamsMapRef.current)?.map(
            key => +key,
          );
          setExpandedKeys(keys);
        })
        .catch((error: Error) => message.error(error.message))
        .finally(() => setTeamsLoading(false));
    },
    { wait: 200 },
  );

  // 展开收起
  const onExpand = useCallback((keys: React.Key[]) => {
    setExpandedKeys(keys as number[]);
    setAutoExpandParent(false);
  }, []);

  // 选择，根据type跳转不同页面
  const onSelect = useCallback((team: ChannelTeamInfo) => {
    setSelectedKey(team.id);
    navigate(
      `/${EmployeeManagementPrefix}/${RouterMap[team.teamType]}?${qs.stringify({
        id: String(team.id),
        level: String(team.level),
        teamType: team.teamType,
      })}`,
      {
        state: {
          teamNames: teamsMapRef.current?.[team.id]?.teamNames,
        },
      },
    );
  }, []);

  // 删除
  const onDelete = useCallback(
    (team: ChannelTeamInfo) => {
      setTeamsLoading(true);
      ChannelService.removeTeamInfo(team.id)
        .then(async () => {
          message.success(t('Delete successfully'));
          try {
            await queryTeams();
            // 删除成功后，region/branch跳转至上一层级，retail删除后展示初始无内容状态
            if (team.teamType === ChannelTeamType.Headquarter) {
              setSelectedKey(null);
              navigate(`/${EmployeeManagementPrefix}/index`);
            } else {
              const parentTeam = teamsMapRef.current?.[team.parentId]?.teamInfo;
              onSelect(parentTeam);
            }
          } catch {
            return;
          }
        })
        .catch((error: Error) => {
          message.error(error.message);
          setTeamsLoading(false);
        });
    },
    [queryTeams, onSelect],
  );

  // 新增，根据type跳转不同页面
  const onAdd = useCallback(
    (teamType: ChannelTeamType, parentId: number, level: number) => {
      navigate(
        `/${EmployeeManagementPrefix}/${RouterMap[teamType]}?${qs.stringify({
          parentId: parentId && String(parentId),
          level: String(level),
          teamType,
        })}`,
        {
          state: {
            teamNames: (
              teamsMapRef.current?.[parentId]?.teamNames ?? []
            ).concat(TeamTypeTitleMap[teamType]),
          },
        },
      );
    },
    [],
  );

  // 编辑/新增成功后更新teams tree
  const updateTeamTree = useCallback(
    async (team: ChannelTeamInfo) => {
      try {
        await queryTeams();
        // 新增成功后，左侧焦点自动选中新增
        if (!id) {
          let clonedExpandedKeys: number[] = [];
          if (routerParentId) {
            clonedExpandedKeys = expandedKeys?.concat([
              Number(routerParentId),
              team.id,
            ]);
          } else {
            // 新增时没有parentId表示新增Retail，只需展开retail
            clonedExpandedKeys = [team.id];
          }
          setExpandedKeys(clonedExpandedKeys);
          setAutoExpandParent(true);
          onSelect(team);
        }
      } catch {
        return;
      }
    },
    [id, routerParentId, expandedKeys, queryTeams, onSelect],
  );

  // 页面初始化，默认展开且选中第一个
  const initPage = useCallback(async () => {
    setLoading(true);
    try {
      const result = (await queryTeams()) as ChannelTeamInfo[];
      const defaultTeam = result?.[0];
      if (defaultTeam) {
        setAutoExpandParent(true);
        onExpand([defaultTeam?.id]);
        // 初始化时切换不拦截
        onSelect(defaultTeam);
      }
      setLoading(false);
    } catch {
      setLoading(false);
    }
  }, [queryTeams, onExpand, onSelect]);

  // 若使用useEffect，会闪一下No Data样式
  useLayoutEffect(() => {
    initPage();
  }, []);

  // 获取dropdown menu；branch只能删除，不能新增子节点
  const getMenuItems = useCallback(
    (team: ChannelTeamInfo) => {
      return [
        {
          key: 'add',
          label: (
            <Button
              icon={<FileAddOutlined style={{ fontSize: 18 }} />}
              type="link"
              disabled={!canEdit}
            >
              {t('Add EmployeeBranch')}
            </Button>
          ),
          onClick: () => onAdd(ChannelTeamType.Branch, team.id, team.level + 1),
          hidden: !(team.level < 10),
        },
        {
          key: 'delete',
          label: (
            <DeleteConfirm
              content={DeletedContentMap[team.teamType]}
              onOk={() => onDelete(team)}
            >
              <Button
                icon={<DeleteOutline style={{ fontSize: 18 }} />}
                type="link"
                disabled={!canEdit}
              >
                {t('Delete')}
              </Button>
            </DeleteConfirm>
          ),
        },
      ].filter(({ hidden }) => !hidden);
    },
    [onAdd, onDelete],
  );

  // 处理tree node title
  const getTreeNodeTitle = useCallback(
    (team: ChannelTeamInfo) => (
      <span
        className={styles.treeNodeTitle}
        onMouseEnter={() => setHoveredId(team.id)}
        onMouseLeave={() => setHoveredId(null)}
      >
        <FileTextOutlined style={{ marginRight: 10 }} />
        <span className={styles.teamName} onClick={() => onSelect(team)}>
          <Typography.Paragraph ellipsis={{ tooltip: true, rows: 1 }}>
            {team.teamName}
          </Typography.Paragraph>
        </span>
        {hoveredId === team.id && canEdit && (
          <div className={styles.hoverBox}>
            <Dropdown
              menu={{ items: getMenuItems(team) }}
              placement="bottomLeft"
            >
              <EllipsisOutlined />
            </Dropdown>
          </div>
        )}
        {/* 用来占位，拉长宽度，使得hover时能点击到 */}
        {hoveredId !== team.id && canEdit && (
          <div className={styles.hoverBox} />
        )}
      </span>
    ),
    [hoveredId, canEdit, getMenuItems, onSelect],
  );

  // 处理tree
  const treeData = useMemo(() => {
    const loop = (teamList: ChannelTeamInfo[]): DataNode[] =>
      teamList?.map(team => {
        if (team.children?.length) {
          return {
            key: team.id,
            title: getTreeNodeTitle(team),
            children: loop(team.children),
          };
        }
        return {
          key: team.id,
          title: getTreeNodeTitle(team),
        };
      });
    return loop(teams);
  }, [teams, getTreeNodeTitle]);

  return (
    <EmployeeContext.Provider
      value={{
        updateTeamTree,
      }}
    >
      <Skeleton loading={loading} active>
        <Layout
          style={{ height: '94vh' }}
          className={styles.employeeManagement}
        >
          <Layout.Sider theme="light" width="274" className={styles.left}>
            <PageTitle
              title={t('Employee Management')}
              className={styles.title}
            />
            <Input
              prefix={<SearchOutline style={{ fontSize: 20 }} />}
              placeholder={t('Search')}
              size="large"
              allowClear
              style={{ marginBottom: 21, marginRight: 10 }}
              onChange={event => onSearch(event.target.value)}
            />
            <Spin spinning={teamsLoading}>
              {teams?.length ? (
                <Tree
                  expandedKeys={expandedKeys}
                  selectedKeys={[selectedKey]}
                  autoExpandParent={autoExpandParent}
                  onExpand={onExpand}
                  treeData={treeData}
                />
              ) : !isSearchNoData ? (
                <div className={styles.treeNoData}>
                  <NoData />
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    disabled={!canEdit}
                    onClick={() => onAdd(ChannelTeamType.Headquarter, null, 0)}
                  >
                    {t('Add New Headquarter')}
                  </Button>
                </div>
              ) : (
                // 搜索导致无结果
                <div className={styles.treeNoData}>
                  <NoData emptyText={t('No search results found')} />
                </div>
              )}
            </Spin>
          </Layout.Sider>
          <Layout.Content>
            <Routes>
              {EmployeeManagementSubRoutes[0]?.routers?.map(route => (
                <Route
                  key={route.path}
                  path={route.path}
                  element={
                    <Suspense
                      fallback={
                        <div className={styles.loading}>
                          <Spin size="large" />
                        </div>
                      }
                    >
                      <Content />
                    </Suspense>
                  }
                />
              ))}
              <Route
                element={
                  <div className={styles.noData}>
                    <NoData />
                  </div>
                }
              />
            </Routes>
          </Layout.Content>
        </Layout>
      </Skeleton>
    </EmployeeContext.Provider>
  );
};

export default EmployeeManagement;
