import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import type { EmployeeContextProps } from '@/types/common-party';
import { usePermission } from '@/hooks/usePermissions';
import i18nInstance from '@/utils/i18n';
import { useRequest } from 'ahooks';
import type { PaginationReqV2 } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';

export const EmployeeContext = createContext<EmployeeContextProps>({
  updateTeamTree: () => {},
});

export const useEmployeeContext: () => EmployeeContextProps = () =>
  useContext(EmployeeContext);

export const useEditPermission = (): Record<string, boolean> => {
  const canEdit = usePermission('channel.employee-management.edit');

  const canView = usePermission('channel.employee-management.view');

  return {
    canEdit,
    canView,
  };
};

export const useToolTipText = <T extends object>(teamInfo: T, name: string) => {
  const tooltipText = useMemo(() => {
    if (!teamInfo) {
      return i18nInstance.t('Please complete Team Information first', {
        ns: 'partner',
        team: name.toLowerCase(),
      });
    }
  }, [teamInfo, name]);
  return tooltipText;
};

export const useEmployeeList = (params: PaginationReqV2) => {
  const [employeeListOption, setEmployeeListOption] = useState([]);
  const { data } = useRequest(
    () => ChannelService.queryAllEmployeeList(params),
    {
      cacheKey: JSON.stringify(params),
    },
  );

  useEffect(() => {
    const list =
      data?.data?.map(item => ({
        label: (
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span>{item.name}</span>
            <span>{item.code}</span>
          </div>
        ),
        value: item.code,
        name: `${item.name || ''}${item.code}`,
      })) || [];
    setEmployeeListOption(list);
  }, [data]);

  return { employeeListOption };
};
