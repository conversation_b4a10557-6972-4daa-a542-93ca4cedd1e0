import { useTranslation } from 'react-i18next';
import {
  useTenantBizDict,
  useTenantBizDictMap,
} from '@/hooks/useTenantBizDict';
import type {
  BizDictItem,
  ChannelTeamType as TeamType,
} from 'genesis-web-service';
import { EmployeeStatus, AgentStatusType } from 'genesis-web-service';
import type { EmployeeInfo } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import { EmployeeFormColumnType } from '@/types/common-party';
import { RenderEnums, renderEnumsWithString } from '@/components/RenderEnums';
import { StatusTag } from '@/components/StatusTag';
import { TagsShape, TagsType } from '@/utils/constants';
import { TeamTypeTitleMap } from '@/pages/common-party/employee-management/util/constants';
import { useQuerySelectOptions } from './request';

interface Props {
  formType: EmployeeFormColumnType;
  teamType?: TeamType;
}
export const useColumns = ({ formType, teamType }: Props) => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const { workingType } = useTenantBizDictMap();
  const { departmentOptions } = useQuerySelectOptions(formType, true);
  if (formType === EmployeeFormColumnType.TeamInfo) {
    const teamNameI18n = t('Team-Name', { team: TeamTypeTitleMap[teamType] });
    const teamCodeI18n = t('Team-Code', { team: TeamTypeTitleMap[teamType] });

    return [
      {
        title: teamNameI18n,
        dataIndex: 'teamName',
        fixed: 'left',
      },
      {
        title: teamCodeI18n,
        dataIndex: 'teamCode',
      },
      {
        title: t('Introduction'),
        dataIndex: 'introduction',
      },
      {
        title: t('Email'),
        dataIndex: 'email',
      },
      {
        title: t('Country Code'),
        dataIndex: 'countryCode',
      },
      {
        title: t('Phone Number'),
        dataIndex: 'phoneNo',
      },
    ];
  }

  if (formType === EmployeeFormColumnType.EmployeeInfo) {
    return [
      {
        title: t('Employee Code'),
        dataIndex: 'code',
      },
      {
        title: t('Working Status'),
        dataIndex: 'workingStatus',
        render: (workingStatus: EmployeeStatus) => {
          const tagType =
            workingStatus === EmployeeStatus.OnLeave
              ? TagsType.Default
              : TagsType.Active;
          return (
            <StatusTag shape={TagsShape.Round} type={tagType} hasborder={false}>
              <RenderEnums
                enums={enums?.employeeStatus}
                keyName={workingStatus}
              />
            </StatusTag>
          );
        },
      },
      {
        title: t('Department'),
        dataIndex: 'departmentColumn',
        render: (_: any, record: EmployeeInfo) =>
          record?.departmentCodes
            ?.map(
              item =>
                departmentOptions?.find(
                  departmentItem => departmentItem.value === item,
                )?.label ?? '',
            )
            .join(','),
      },
      {
        title: t('ID Type'),
        dataIndex: 'idType',
        render: (idType: string) => (
          <RenderEnums keyName={idType} enums={enums?.certiType} />
        ),
      },
      {
        title: t('ID Number'),
        dataIndex: 'idNo',
      },
      {
        title: t('Date of Birth'),
        dataIndex: 'birthday',
        render: (birthday: string) =>
          dateFormatInstance?.getDateString(birthday),
      },
      {
        title: t('Office City'),
        dataIndex: 'officeCity',
        render: (officeCity: string) => {
          const officeCities = officeCity?.split(',');
          const officeCityNames = officeCities?.map(item =>
            renderEnumsWithString(item, enums?.officeCity),
          );
          return !!officeCityNames?.length && officeCityNames.join('，');
        },
      },
      {
        title: t('Workload Rate'),
        dataIndex: 'workloadRate',
        render: (workloadRate: number) =>
          workloadRate >= 0 && `${workloadRate}%`,
      },
      {
        title: t('Third-party assessment company user name'),
        dataIndex: 'companyUserName',
      },
      {
        title: t('Email'),
        dataIndex: 'email',
      },
      {
        title: t('Country Code'),
        dataIndex: 'countryCode',
      },
      {
        title: t('Mobile Phone'),
        dataIndex: 'phoneNo',
      },
      {
        title: t('Status'),
        dataIndex: 'status',
        render: (status: AgentStatusType) => {
          const tagType =
            status === AgentStatusType.Resigned
              ? TagsType.Default
              : TagsType.Active;
          return (
            <StatusTag shape={TagsShape.Round} type={tagType}>
              <RenderEnums enums={enums?.agentStatus} keyName={status} />
            </StatusTag>
          );
        },
      },
      {
        title: t('Onboard Date'),
        dataIndex: 'onboardDate',
      },
      {
        title: t('Resign Date'),
        dataIndex: 'resignDate',
      },
      {
        title: t('Working Type'),
        dataIndex: 'workingTypes',
        render: (workingTypes: string[]) => {
          const workingTypesI18n: string[] = [];
          workingTypes?.forEach(workingTypesItem => {
            workingTypesI18n.push(
              workingType?.[workingTypesItem]?.dictValueName,
            );
          });
          return workingTypesI18n.join(',');
        },
      },
      {
        title: t('Remark'),
        dataIndex: 'remark',
      },
    ];
  }

  if (formType === EmployeeFormColumnType.DepartmentInfo) {
    return [
      {
        title: t('Employee Department Name'),
        dataIndex: 'name',
        fixed: 'left',
      },
      {
        title: t('Department Code'),
        dataIndex: 'code',
      },
      {
        title: t('Department Type'),
        dataIndex: 'departmentType',
        render: (departmentType: string) => (
          <RenderEnums keyName={departmentType} enums={enums?.departmentType} />
        ),
      },
    ];
  }
};
