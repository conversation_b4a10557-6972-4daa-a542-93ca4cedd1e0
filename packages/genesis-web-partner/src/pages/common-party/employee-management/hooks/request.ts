import { useCallback, useEffect, useState } from 'react';

import { useSearchParams, useSelector } from '@umijs/max';

import qs from 'qs';

import type { DepartmentInfo } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';

import type { LabeledValue } from '@/components/CommonForm';
import type { ConnectState } from '@/models/connect';
import type { LocationQueryParam } from '@/types/common-party';
import { EmployeeFormColumnType } from '@/types/common-party';

export const useQuerySelectOptions = (formType: EmployeeFormColumnType, modalVisible: boolean) => {
  const [searchParams] = useSearchParams();
  const { id } = qs.parse(searchParams.toString()) as unknown as LocationQueryParam;
  const { teamInfo } = useSelector(({ partnerManagement }: ConnectState) => ({
    teamInfo: partnerManagement.teamInfo,
  }));
  const [departmentOptions, setDepartmentOptions] = useState<LabeledValue<string>[]>([]);
  const [departmentMap, setDepartmentMap] = useState<Record<string, DepartmentInfo>>({});
  const [teamId, setTeamId] = useState<number>(id ?? teamInfo?.id);

  const queryOptions = useCallback(async () => {
    if (teamId) {
      const resp = await ChannelService.queryDepartmentList(teamId, {
        pageIndex: 0,
        pageSize: 1000,
      });
      const respMap: Record<string, DepartmentInfo> = {};
      const enums: LabeledValue<string>[] = resp?.data.map((item: DepartmentInfo) => {
        respMap[item.code] = item;
        return {
          label: `${item.name}_${item.code}`,
          value: item.code,
        };
      });
      setDepartmentOptions(enums);
      setDepartmentMap(respMap);
    }
  }, [teamId]);

  useEffect(() => {
    setTeamId(id ?? teamInfo?.id);
  }, [teamInfo, id]);

  useEffect(() => {
    if (formType === EmployeeFormColumnType.EmployeeInfo && modalVisible) {
      queryOptions();
    }
  }, [queryOptions, formType, modalVisible]);

  return {
    departmentOptions,
    departmentMap,
  };
};
