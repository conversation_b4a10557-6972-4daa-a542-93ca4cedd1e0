import { Col, Form } from 'antd';
import { useCallback, useState, useEffect } from 'react';
import type { FormInstance } from 'antd/lib/form';
import { useTranslation } from 'react-i18next';

import type {
  BizDictItem,
  ChannelTeamType as TeamType,
} from 'genesis-web-service';
import { AgentStatusType, CustomerTypeCode } from 'genesis-web-service';
import { useUniqueIdentification } from 'genesis-web-shared/lib/hook/useUniqueIdentification';

import { FieldType } from '@/components/CommonForm';
import { CountryCode } from '@/components/CountryCode';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { TeamTypeTitleMap } from '@/pages/common-party/employee-management/util/constants';
import { EmployeeFormColumnType } from '@/types/common-party';
import moment from 'moment';
import type { Moment } from 'moment';
import { useQuerySelectOptions } from './request';
import { useEmployeeList } from './useEmployeeManagement';

interface FieldsProps<T> {
  form: FormInstance<T>;
  formType: EmployeeFormColumnType;
  teamType?: TeamType.Headquarter | TeamType.Branch;
  disabled: boolean;
  information?: Record<string, string>;
  modalVisible?: boolean;
}

const MaxPageSize = 2000;

export const useFormFields = <T extends object>({
  formType,
  disabled,
  information,
  teamType,
  modalVisible,
  form,
}: FieldsProps<T>) => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const { uniqConfig, repeatedConfig } = useUniqueIdentification(
    CustomerTypeCode.person,
  );
  const { departmentOptions } = useQuerySelectOptions(formType, modalVisible);
  const { employeeListOption } = useEmployeeList({
    pageIndex: 0,
    pageSize: MaxPageSize,
  });
  const [status, setStatus] = useState<AgentStatusType>();

  useEffect(() => {
    setStatus(information?.status as AgentStatusType);
  }, [information]);
  const onChangeStatus = useCallback((selectedStatus: AgentStatusType) => {
    setStatus(selectedStatus);
    // 状态改为employed时，清空resignDate
    if (selectedStatus === AgentStatusType.Employed) {
      form?.resetFields(['resignDate']);
    }
  }, []);
  const onboardDateChange = useCallback((currentDate: Moment) => {
    if (currentDate > moment(form.getFieldValue('resignDate'))) {
      form?.resetFields(['resignDate']);
    }
  }, []);
  if (formType === EmployeeFormColumnType.TeamInfo) {
    const teamNameI18n = t('Team-Name', { team: TeamTypeTitleMap[teamType] });
    const teamCodeI18n = t('Team-Code', { team: TeamTypeTitleMap[teamType] });

    return [
      {
        key: 'teamName',
        label: teamNameI18n,
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: teamNameI18n,
            }),
          },
        ],
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'teamCode',
        label: teamCodeI18n,
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: teamCodeI18n,
            }),
          },
        ],
        ctrlProps: {
          disabled: disabled || !!information?.teamCode,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'introduction',
        label: t('Introduction'),
        col: 12,
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'email',
        label: t('Email'),
        col: 12,
        rules: [
          {
            type: 'email',
            message: t('Please input correct format'),
          },
        ],
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        type: FieldType.Customize,
        customerDom: (
          <Col span={12}>
            <Form.Item name="countryCode" label={t('Country Code')}>
              <CountryCode disabled={disabled} />
            </Form.Item>
          </Col>
        ),
      },
      {
        key: 'phoneNo',
        label: t('Phone Number'),
        col: 12,
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
    ];
  }

  const commonFieldsTop = [
    {
      key: 'idType',
      label: t('ID Type'),
      type: FieldType.Select,
      col: 12,
      rules: [
        {
          required: !!repeatedConfig?.certiType,
          message: t('channel.common.required', {
            label: t('ID Type'),
          }),
        },
      ],
      ctrlProps: {
        options: enums?.certiType,
        disabled:
          disabled ||
          (!!uniqConfig?.certiType && information?.id && information?.idType), // 为唯一要素标识时才不准二次编辑
        style: { width: '100%' },
        allowClear: true,
      },
    },
    {
      key: 'idNo',
      label: t('ID Number'),
      col: 12,
      rules: [
        {
          required: !!repeatedConfig?.certiNo,
          message: t('channel.common.required', {
            label: t('ID Number'),
          }),
        },
      ],
      ctrlProps: {
        disabled:
          disabled ||
          (!!uniqConfig?.certiNo && information?.id && !!information?.idNo),
        style: { width: '100%' },
        allowClear: true,
      },
    },
    {
      key: 'birthday',
      label: t('Date of Birth'),
      type: FieldType.DatePicker,
      col: 12,
      rules: [
        {
          required: !!repeatedConfig?.birthday,
          message: t('channel.common.required', {
            label: t('Date of Birth'),
          }),
        },
      ],
      ctrlProps: {
        disabled:
          disabled ||
          (!!uniqConfig?.birthday && information?.id && information?.birthday),
        style: { width: '100%' },
        allowClear: true,
      },
    },
  ];

  const commonFieldsBottom = [
    {
      key: 'email',
      label: t('Email'),
      col: 12,
      rules: [
        {
          type: 'email',
          message: t('Please input correct format'),
        },
        {
          required:
            formType === EmployeeFormColumnType.RelativeInfo &&
            !repeatedConfig?.email
              ? false
              : true,
          message: t('channel.common.required', {
            label: t('Email'),
          }),
        },
      ],
      ctrlProps: {
        disabled:
          disabled ||
          (!!uniqConfig?.email && information?.id && information?.email),
        style: { width: '100%' },
        allowClear: true,
      },
    },
    {
      type: FieldType.Customize,
      customerDom: (
        <Col span={12}>
          <Form.Item name="countryCode" label={t('Country Code')}>
            <CountryCode disabled={disabled} />
          </Form.Item>
        </Col>
      ),
    },
    {
      key: 'phoneNo',
      label: t('Mobile Phone'),
      col: 12,
      rules: [
        {
          required: !!repeatedConfig?.phoneNo,
          message: t('channel.common.required', {
            label: t('Mobile Phone'),
          }),
        },
      ],
      ctrlProps: {
        disabled:
          disabled ||
          (!!uniqConfig?.phoneNo && information?.id && information?.phoneNo),
        style: { width: '100%' },
        allowClear: true,
      },
    },
  ];

  if (formType === EmployeeFormColumnType.EmployeeInfo) {
    const fields = [
      {
        key: 'firstName',
        label: t('First Name'),
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('First Name'),
            }),
          },
        ],
        ctrlProps: {
          disabled:
            disabled ||
            ((!!uniqConfig?.firstName || !!uniqConfig?.fullName) &&
              information?.firstName),
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'lastName',
        label: t('Last Name'),
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Last Name'),
            }),
          },
        ],
        ctrlProps: {
          disabled:
            disabled ||
            ((!!uniqConfig?.lastName || !!uniqConfig?.fullName) &&
              information?.lastName),
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'firstName2',
        label: t('First Name2'),
        col: 12,
        ctrlProps: {
          disabled:
            disabled ||
            ((!!uniqConfig?.firstName2 || !!uniqConfig?.fullName) &&
              information?.firstName2),
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'lastName2',
        label: t('Last Name2'),
        col: 12,
        ctrlProps: {
          disabled:
            disabled ||
            ((!!uniqConfig?.lastName2 || !!uniqConfig?.fullName) &&
              information?.lastName2),
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'code',
        label: t('Employee Code'),
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Employee Code'),
            }),
          },
        ],
        ctrlProps: {
          disabled: disabled || !!information?.code,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'departmentCodes',
        label: t('Department'),
        type: FieldType.Select,
        col: 12,
        ctrlProps: {
          options: departmentOptions,
          disabled,
          style: { width: '100%' },
          allowClear: true,
          mode: 'multiple',
        },
      },
      ...commonFieldsTop,
      {
        key: 'officeCity',
        label: t('Office City'),
        col: 12,
        type: FieldType.Select,
        ctrlProps: {
          options: enums?.officeCity,
          mode: 'multiple',
          showArrow: true,
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'workloadRate',
        label: t('Workload Rate'),
        col: 12,
        type: FieldType.InputNumber,
        ctrlProps: {
          min: 0,
          disabled,
          addonAfter: '%',
          style: { width: '100%' },
        },
      },
      {
        key: 'companyUserName',
        label: t('Third-party assessment company user name'),
        col: 12,
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      ...commonFieldsBottom,
      {
        key: 'status',
        label: t('Status'),
        col: 12,
        type: FieldType.Select,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Status'),
            }),
          },
        ],
        ctrlProps: {
          options: enums?.agentStatus,
          disabled:
            disabled || information?.status === AgentStatusType.Resigned, // 已保存的status为resigned时，不能再修改
          onChange: onChangeStatus,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'onboardDate',
        label: t('Onboard Date'),
        type: FieldType.DatePicker,
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Onboard Date'),
            }),
          },
        ],
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
          onChange: onboardDateChange,
        },
      },
      {
        key: 'resignDate',
        label: t('Resign Date'),
        col: 12,
        type: FieldType.DatePicker,
        rules: [
          {
            required: status === AgentStatusType.Resigned,
            message: t('channel.common.required', {
              label: t('Resign Date'),
            }),
          },
        ],
        ctrlProps: {
          disabled: disabled || status === AgentStatusType.Employed,
          style: { width: '100%' },
          disabledDate: (currentDate: Moment) =>
            currentDate &&
            currentDate < moment(form.getFieldValue('onboardDate')),
        },
      },
      {
        key: 'workingTypes',
        label: t('Working Type'),
        col: 12,
        type: FieldType.Select,
        ctrlProps: {
          mode: 'multiple',
          options: enums?.workingType,
          disabled,
          style: { width: '100%' },
          allowClear: true,
          showArrow: true,
        },
      },
      {
        key: 'managerCode',
        label: t('Manager'),
        col: 12,
        type: FieldType.Select,
        ctrlProps: {
          options: employeeListOption,
          filterOption: (inputValue, option) =>
            (option?.name ?? '')
              .toLowerCase()
              .includes(inputValue.toLowerCase()),
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'remark',
        label: t('Remark'),
        type: FieldType.TextArea,
        col: 16,
        ctrlProps: {
          disabled,
        },
      },
    ];
    return fields;
  }

  if (formType === EmployeeFormColumnType.RelativeInfo) {
    return [
      {
        key: 'name',
        label: t('Relative Name'),
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Relative Name'),
            }),
          },
        ],
        ctrlProps: {
          disabled:
            disabled ||
            (!!uniqConfig?.fullName && information?.id && information?.name), // 唯一标识的情况下，已保存的不能再修改，有id表示已保存
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'relationType',
        label: t('Relationship With Employee'),
        type: FieldType.Select,
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Relationship With Employee'),
            }),
          },
        ],
        ctrlProps: {
          options: enums?.relationship,
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      ...commonFieldsTop,
      ...commonFieldsBottom,
    ];
  }

  if (formType === EmployeeFormColumnType.DepartmentInfo) {
    return [
      {
        key: 'code',
        label: t('Department Code'),
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Department Code'),
            }),
          },
        ],
        ctrlProps: {
          disabled: disabled || information?.code, // 编辑情况下不能修改, 有id表示是编辑
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'departmentType',
        label: t('Department Type'),
        type: FieldType.Select,
        col: 12,
        ctrlProps: {
          options: enums?.departmentType,
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'name',
        label: t('Employee Department Name'),
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Employee Department Name'),
            }),
          },
        ],
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
    ];
  }
};
