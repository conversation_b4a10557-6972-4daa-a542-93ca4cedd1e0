import { PlusOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Tooltip, Button, message } from 'antd';
import type { ColumnProps } from 'antd/es/table';
import { useMemo, useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from '@umijs/max';

import { useAddressConfig } from 'genesis-web-component/lib/components/Address';
import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import { ChannelService } from 'genesis-web-service';
import type { DepartmentInfo } from 'genesis-web-service';

import {
  EditOutline,
  DeleteOutline,
  ViewSquareOutline,
} from '@/components/Icons';
import { PaginationComponent } from '@/components/Pagination';
import type { ConnectState } from '@/models/connect';
import { useColumns } from '@/pages/common-party/employee-management/hooks/useColumns';
import {
  useEditPermission,
  useToolTipText,
} from '@/pages/common-party/employee-management/hooks/useEmployeeManagement';
import { TeamTypeTitleMap } from '@/pages/common-party/employee-management/util/constants';
import { ModeEnum } from '@/types/common';
import type { LocationQueryParam } from '@/types/common-party';
import { EmployeeFormColumnType } from '@/types/common-party';
import { DefaultTablePagination } from '@/utils/constants';
import { transferSchemaToTableProp } from '@/utils/utils';

import { DepartmentDrawer } from './DepartmentDrawer';

import styles from './index.scss';
import { useSearchParams } from 'react-router-dom';
import qs from 'qs';
import { Table, TableActionsContainer } from '@zhongan/nagrand-ui';
import { useAddressI18nList } from '@/hooks/useAddressI18nList';
/**
 *
 * @description department信息；
 */

export const DepartmenInformation: React.FC = () => {
  const { t } = useTranslation('partner');
  const dispatch = useDispatch();
  const { teamInfo } = useSelector(({ partnerManagement }: ConnectState) => ({
    teamInfo: partnerManagement.teamInfo,
  }));
  const [searchParams] = useSearchParams();
  const { teamType, id } = qs.parse(
    searchParams.toString(),
  ) as unknown as LocationQueryParam;
  const { canEdit, canView } = useEditPermission();

  const [drawerMode, setDrawerMode] = useState(ModeEnum.ADD);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [editedDepartmentId, setEditedDepartmentId] = useState<number>();
  const [teamId, setTeamId] = useState<number>();
  const [pagination, setPagination] = useState(DefaultTablePagination);

  const toolTipText = useToolTipText(teamInfo, TeamTypeTitleMap[teamType]);

  useEffect(() => {
    // id变化时pagination回到初始状态
    setTeamId((id && Number(id)) ?? teamInfo?.id);
    setPagination({ ...DefaultTablePagination });
  }, [id, teamInfo?.id]);

  // 查询
  const {
    data: departmentList,
    loading,
    run: queryDepartmentList,
  } = useRequest(
    () => {
      if (teamId)
        return ChannelService.queryDepartmentList(teamId, {
          pageIndex: pagination.current - 1,
          pageSize: pagination.pageSize,
        });
    },
    {
      refreshDeps: [teamId, pagination],
    },
  );

  const addressI18nList = useAddressI18nList(departmentList?.data);
  // 处理address & zipCode 表格表头展示
  const { formItems } = useAddressConfig();
  const addressAndZipCodeTableColumns = useMemo(() => {
    if (!formItems?.length) {
      return [];
    }
    return transferSchemaToTableProp(formItems, addressI18nList);
  }, [formItems, addressI18nList]);

  // 关闭抽屉
  const onCloseDrawer = useCallback(
    (updated?: boolean) => {
      if (updated) {
        if (!editedDepartmentId) {
          //新增成功后，回到初始页码条件
          setPagination({ ...DefaultTablePagination });
        } else {
          // 编辑成功，以当前条件请求
          queryDepartmentList();
          // 刷新emplyee列表
          dispatch({
            type: 'partnerManagement/updateRefreshEmployeeListStatus',
            payload: true,
          });
        }
      }
      setDrawerMode(ModeEnum.ADD);
      setEditedDepartmentId(undefined);
      setDrawerVisible(false);
    },
    [editedDepartmentId, teamId, queryDepartmentList],
  );

  // 查看/编辑
  const onViewOrEditDetail = useCallback(
    (departmentId: number, mode: ModeEnum) => {
      setDrawerMode(mode);
      setDrawerVisible(true);
      setEditedDepartmentId(departmentId);
    },
    [],
  );

  // 删除
  const removeDepartment = useCallback(
    (departmentId: number) =>
      ChannelService.removeEmployeeDepartment(teamId, departmentId)
        .then(() => {
          setPagination({ ...DefaultTablePagination });
          message.success(t('Delete successfully'));
          // 刷新emplyee列表
          dispatch({
            type: 'partnerManagement/updateRefreshEmployeeListStatus',
            payload: true,
          });
        })
        .catch((error: Error) => message.error(error.message)),
    [teamId],
  );

  const columns =
    useColumns({
      formType: EmployeeFormColumnType.DepartmentInfo,
      teamType,
    }) ?? [];

  const combinedColumns = useMemo(
    (): ColumnProps<DepartmentInfo>[] => [
      ...(columns as ColumnProps<DepartmentInfo>[]),
      ...addressAndZipCodeTableColumns,
      {
        title: t('action'),
        fixed: 'right',
        align: 'right',
        render: (_, record) => (
          <div className={styles.tableActions}>
            <TableActionsContainer>
              <Tooltip title={t('View')}>
                <Button
                  disabled={!canView}
                  type="link"
                  icon={<ViewSquareOutline />}
                  onClick={() => onViewOrEditDetail(record.id, ModeEnum.READ)}
                />
              </Tooltip>
              <Tooltip title={t('Edit')}>
                <Button
                  disabled={!canEdit}
                  type="link"
                  icon={<EditOutline />}
                  onClick={() => onViewOrEditDetail(record.id, ModeEnum.EDIT)}
                />
              </Tooltip>
              <DeleteConfirm
                onOk={() => removeDepartment(record.id)}
                disabled={!canEdit}
              >
                <Tooltip title={t('Delete')}>
                  <Button
                    disabled={!canEdit}
                    type="link"
                    icon={<DeleteOutline />}
                  />
                </Tooltip>
              </DeleteConfirm>
            </TableActionsContainer>
          </div>
        ),
      },
    ],
    [columns, canEdit, canView, teamInfo, removeDepartment, onViewOrEditDetail],
  );

  const onChangePagination = useCallback(
    (current: number, pageSize: number) => {
      setPagination(old => ({
        ...old,
        current,
        pageSize,
      }));
    },
    [],
  );

  return (
    <div className={styles.departmentContainer}>
      <p className={styles.pageTitle}>{t('Department')}</p>
      <div className={styles.actionsContainer}>
        <Tooltip title={toolTipText}>
          <Button
            icon={<PlusOutlined />}
            type="primary"
            disabled={!canEdit || !teamInfo}
            onClick={() => setDrawerVisible(true)}
          >
            {t('Add New Department')}
          </Button>
        </Tooltip>
      </div>
      <Table
        scroll={{ x: 'max-content' }}
        rowKey="id"
        columns={combinedColumns}
        loading={loading}
        dataSource={departmentList?.data}
        pagination={false}
      />
      <PaginationComponent
        className="margin-top-16 margin-bottom-16"
        total={departmentList?.totalElements || 0}
        pagination={pagination}
        handlePaginationChange={onChangePagination}
      />
      <DepartmentDrawer
        id={editedDepartmentId}
        teamId={teamId}
        visible={drawerVisible}
        disabled={drawerMode === ModeEnum.READ}
        mode={drawerMode}
        onCloseDrawer={onCloseDrawer}
      />
    </div>
  );
};
