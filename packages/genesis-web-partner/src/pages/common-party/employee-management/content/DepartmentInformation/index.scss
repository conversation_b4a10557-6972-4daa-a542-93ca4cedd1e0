@import '@/variables.scss';
.department-container {
  margin-top: 16px;
  padding: 24px 32px;
  background-color: var(--white);

  .page-title {
    font-size: 16px;
    line-height: 24px;
    font-weight: 700;
    margin-bottom: 11px;
  }

  .actions-container {
    display: flex;
    align-items: center;
    padding: 12px 0;
    margin-bottom: 16px;

    > div {
      flex: 1;
      text-align: right;
      > button {
        margin-left: 8px;
      }
    }
  }
  :global {
    .#{$antd-prefix}-btn {
      border-radius: var(--border-radius-base);
    }
  }
}
