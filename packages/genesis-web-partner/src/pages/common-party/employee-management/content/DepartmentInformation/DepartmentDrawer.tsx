import { useRequest } from 'ahooks';
import { Form, message, Skeleton } from 'antd';
import { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { AddressComponent } from 'genesis-web-component/lib/components/AddressComponent';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { ChannelService } from 'genesis-web-service';

import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import { useFormFields } from '@/pages/common-party/employee-management/hooks/useFormFields';
import { ModeEnum } from '@/types/common';
import type { LocationQueryParam } from '@/types/common-party';
import { EmployeeFormColumnType } from '@/types/common-party';

import styles from './index.scss';
import { useSearchParams } from 'react-router-dom';
import qs from 'qs';

interface DrawerProps {
  id: number;
  teamId: number;
  visible: boolean;
  disabled: boolean;
  mode: ModeEnum;
  onCloseDrawer: (updated?: boolean) => void;
}

const DrawerTitleI18nKey = {
  [ModeEnum.ADD]: 'Add New Department',
  [ModeEnum.EDIT]: 'Edit Department Information',
  [ModeEnum.READ]: 'View Department Information',
};

/**
 *
 * @param id 当前departmentId
 * @param teamId 当前department所属teamId
 * @param visible 是否可见
 * @param disabled 是否禁用
 * @param mode 当前操作类型：新增/查看/修改
 * @param onCloseDrawer 关闭抽屉
 * @description department 新增/查看/修改抽屉
 */
export const DepartmentDrawer: React.FC<DrawerProps> = (props: DrawerProps) => {
  const [form] = Form.useForm();
  const { t } = useTranslation('partner');
  const [searchParams] = useSearchParams();
  const { teamType } = qs.parse(
    searchParams.toString(),
  ) as unknown as LocationQueryParam;

  const [addressCascaderValue, setAddressCascaderValue] =
    useState<Record<string, string>>();

  const { id, teamId, visible, disabled, mode, onCloseDrawer } = props;

  const { data: departmentInfo, loading } = useRequest(
    () => {
      if (id && visible) {
        return ChannelService.queryDepartmentById(teamId, id);
      }
    },
    {
      refreshDeps: [visible, teamId, id],
      onSuccess: departmentResp => {
        if (departmentResp) {
          form.setFieldsValue({
            ...departmentResp,
          });
        }
      },
      onError: (error: Error) => message.error(error?.message),
    },
  );

  const onClose = useCallback(
    (updated?: boolean) => {
      form.resetFields();
      setAddressCascaderValue(undefined);
      onCloseDrawer(updated);
    },
    [onCloseDrawer],
  );

  const fields =
    useFormFields({
      form,
      disabled,
      information: departmentInfo,
      formType: EmployeeFormColumnType.DepartmentInfo,
      teamType,
    }) ?? [];

  const combinedFields = useMemo(() => {
    const clonedFields = [...fields];
    clonedFields.push({
      type: FieldType.Customize,
      customerDom: (
        <AddressComponent
          disabled={disabled}
          initialValue={departmentInfo ?? {}}
          onCascaderValueChange={setAddressCascaderValue}
          form={form}
          cascaderCol={24}
          colProps={{ span: 12 }}
          inputWidth="100%"
        />
      ),
    });

    return clonedFields;
  }, [fields, disabled, departmentInfo, form]);

  // 保存
  const { run: saveDepartmentInfo, loading: updating } = useRequest(
    department =>
      (mode === ModeEnum.ADD
        ? ChannelService.addEmployeeDepartment(teamId, {
          ...department,
        })
        : ChannelService.updateEmployeeDepartment({
          ...departmentInfo,
          ...department,
        })),
    {
      manual: true,
      onSuccess: () => onClose(true),
      onError: (error: Error) => message.error(error?.message),
    },
  );

  // 保存前数据处理
  const onSave = useCallback(async () => {
    const values = await form.validateFields();
    saveDepartmentInfo({
      ...departmentInfo,
      ...values,
      ...addressCascaderValue,
    });
  }, [saveDepartmentInfo, departmentInfo, addressCascaderValue]);

  return (
    <DrawerForm
      className={styles.employeeEditDrawer}
      visible={visible}
      disabled={disabled}
      onClose={() => onClose()}
      maskClosable={false}
      width={752}
      title={t(DrawerTitleI18nKey[mode])}
      cancelText={t('Cancel')}
      sendText={t('Submit')}
      onSubmit={onSave}
      submitBtnProps={{ loading: updating }}
    >
      <Skeleton loading={loading} active>
        <CommonForm form={form} fields={combinedFields} />
      </Skeleton>
    </DrawerForm>
  );
};
