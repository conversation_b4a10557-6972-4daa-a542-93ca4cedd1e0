import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

import { PlusOutlined } from '@ant-design/icons';

import { useRequest } from 'ahooks';
import { Button, Cascader, Divider, Form, Tooltip, message } from 'antd';
import type { ColumnProps } from 'antd/es/table';
import qs from 'qs';

import { Table, TableActionsContainer } from '@zhongan/nagrand-ui';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { ChannelService, TeamBusinessType } from 'genesis-web-service';
import type { ChannelTeamInfo, RoutesGoods } from 'genesis-web-service';

import { CommonForm } from '@/components/CommonForm/Form';
import { EditOutline, ViewSquareOutline } from '@/components/Icons';
import { AgentBreadcrumb } from '@/pages/common-party/agent-management/components/agent-breadcrumb';
import { useColumns } from '@/pages/common-party/employee-management/hooks/useColumns';
import {
  useEditPermission,
  useEmployeeContext,
} from '@/pages/common-party/employee-management/hooks/useEmployeeManagement';
import { useFormFields } from '@/pages/common-party/employee-management/hooks/useFormFields';
import { TeamTypeTitleMap } from '@/pages/common-party/employee-management/util/constants';
import { ModeEnum } from '@/types/common';
import type { LocationQueryParam } from '@/types/common-party';
import { EmployeeFormColumnType } from '@/types/common-party';
import { useDispatch } from '@umijs/max';
import { useLocation } from '@umijs/max';

import styles from './index.scss';

const { SHOW_CHILD } = Cascader;

/**
 *
 * @description 组织信息；只会有一条，新增成功后，Add New button隐藏，表格只支持查看和编辑，删除功能在左侧树状结构里
 */
export const TeamInformation: React.FC = () => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();

  const { state } = useLocation();
  const [searchParams] = useSearchParams();
  const { parentId, level, teamType, id: teamId } = qs.parse(searchParams.toString()) as unknown as LocationQueryParam;
  const dispatch = useDispatch();
  const { canEdit, canView } = useEditPermission();
  const { updateTeamTree } = useEmployeeContext();
  const name = TeamTypeTitleMap[teamType];

  const [teamNames, setTeamNames] = useState<string[]>();
  const [editing, setEditing] = useState(false);
  const [drawerMode, setDrawerMode] = useState(ModeEnum.EDIT);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [teamInfo, setTeamInfo] = useState<ChannelTeamInfo>();
  const [cascaderValue, setCascaderValue] = useState<string[][]>([]);
  const [cascaderOptions, setCascaderOptions] = useState<RoutesGoods[]>([]);
  const [cascaderMap, setCascaderMap] = useState({});

  const { loading: teamInfoLoading } = useRequest(
    () => {
      if (teamId) return ChannelService.queryTeamInfo(teamId);
    },
    {
      refreshDeps: [teamId],
      onSuccess: res => {
        setTeamInfo(res);
        setCascaderValue(res?.goodsSelected || []);
        const cascader = {};
        res?.goods?.forEach(item => {
          cascader[String(item.goodsId)] = item;
        });
        setCascaderMap(cascader);
      },
      onError: (error: Error) => {
        message.error(error?.message);
        setTeamInfo(undefined);
      },
    }
  );

  useRequest(() => ChannelService.queryRootesGoods(), {
    onSuccess: res => {
      setCascaderOptions(res);
    },
    onError: (error: Error) => {
      message.error(error?.message);
    },
  });
  const onCloseDrawer = useCallback(
    (updated?: boolean, options?: ChannelTeamInfo) => {
      setDrawerMode(ModeEnum.EDIT);
      setDrawerVisible(false);
      setEditing(false);
      form.resetFields();
      if (updated) {
        updateTeamTree?.(options);
        setTeamInfo(options);
      }
    },
    [updateTeamTree]
  );

  const { run: saveTeamInfo, loading: updating } = useRequest(
    (team: ChannelTeamInfo) =>
      !teamId
        ? ChannelService.addTeamInfo({
            ...team,
            parentId,
            teamType,
            level,
            teamBusinessType: TeamBusinessType.Employee,
          })
        : ChannelService.updateTeamInfo(teamId, {
            ...teamInfo,
            ...team,
          }),
    {
      manual: true,
      onSuccess: res => onCloseDrawer(true, res),
      onError: (error: Error) => message.error(error?.message),
    }
  );

  const onSave = useCallback(() => {
    form.validateFields().then(values => {
      const goods = cascaderValue?.map(item => {
        if (cascaderMap?.[item[1]]) {
          return {
            categoryId: item[0],
            goodsId: item[1],
            inheritFromTeamId: cascaderMap?.[item[1]].inheritFromTeamId,
          };
        }
        return { categoryId: item[0], goodsId: item[1] };
      });
      values.goods = goods;
      saveTeamInfo(values);
    });
  }, [cascaderValue, cascaderMap]);

  const onViewDetail = useCallback(() => {
    setEditing(true);
    setDrawerMode(ModeEnum.READ);
    setDrawerVisible(true);
  }, []);

  useEffect(() => {
    setDrawerVisible(editing);
    if (teamInfo && editing) {
      form.setFieldsValue(teamInfo);
    }
  }, [teamInfo, editing]);

  const fields = useFormFields({
    form,
    disabled: drawerMode === ModeEnum.READ,
    information: teamInfo,
    formType: EmployeeFormColumnType.TeamInfo,
    teamType,
  });

  const columns =
    useColumns({
      formType: EmployeeFormColumnType.TeamInfo,
      teamType,
    }) ?? [];

  const combinedColumns = useMemo(
    (): ColumnProps<ChannelTeamInfo>[] => [
      ...columns,
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: () => (
          <div className="table-actions">
            <TableActionsContainer>
              <Tooltip title={t('Edit')}>
                <Button disabled={!canEdit} type="link" icon={<EditOutline />} onClick={() => setEditing(true)} />
              </Tooltip>
              <Tooltip title={t('View')}>
                <Button disabled={!canView} type="link" icon={<ViewSquareOutline />} onClick={() => onViewDetail()} />
              </Tooltip>
            </TableActionsContainer>
          </div>
        ),
      },
    ],
    [columns, canEdit, canView, onViewDetail]
  );

  const teamInfoDrawerTitle = useMemo(() => {
    return !teamInfo
      ? t('Add New Team', {
          team: name,
        })
      : drawerMode === ModeEnum.READ
        ? t('View Team', {
            team: name,
          })
        : t('Edit Team', {
            team: name,
          });
  }, [teamInfo, drawerMode, name, t]);

  const onClose = () => {
    onCloseDrawer();
    setCascaderValue(teamInfo?.goodsSelected || []);
  };

  useEffect(() => {
    dispatch({
      type: 'partnerManagement/saveTeamInfo',
      payload: teamInfo,
    });
  }, [dispatch, teamInfo]);

  // 面包屑处理，最后一个需要根据组织信息来替换
  useEffect(() => {
    const clonedTeamNames = [...(state?.teamNames ?? [])];
    if (teamInfo?.teamName) {
      clonedTeamNames?.splice(clonedTeamNames.length - 1, 1, teamInfo?.teamName);
    }
    setTeamNames(clonedTeamNames);
  }, [state?.teamNames, teamInfo]);

  const onCascaderChange = (value: string[][]) => {
    setCascaderValue(value);
  };

  return (
    <div className={styles.teamInfoAndBread}>
      <AgentBreadcrumb teamNames={teamNames} />
      <div className={styles.teamInfo}>
        <p className={styles.pageTitle}>{t('Team Information', { team: name })}</p>
        {!!canEdit && !teamInfo && (
          <Button
            className={styles.addRuleBtn}
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => setDrawerVisible(true)}
          >
            {t('Add {{Team}} Information', {
              team: name,
            })}
          </Button>
        )}

        <Table
          scroll={{ x: 'max-content' }}
          rowKey="id"
          columns={combinedColumns}
          loading={teamInfoLoading}
          dataSource={teamInfo ? [teamInfo] : []}
          pagination={false}
        />
        <DrawerForm
          className={styles.agentEditDrawer}
          visible={drawerVisible}
          disabled={!canEdit || drawerMode === ModeEnum.READ}
          onClose={onClose}
          maskClosable={false}
          width={752}
          title={teamInfoDrawerTitle}
          cancelText={t('Cancel')}
          sendText={t('Submit')}
          onSubmit={onSave}
          submitBtnProps={{ loading: updating }}
        >
          <>
            <div className={styles.teamInfoTitle}>{t('Basic Information')}</div>
            <CommonForm form={form} fields={fields} />
            <Divider />
            <div className={styles.teamInfoTitle}>{t('Sales Authority Information')}</div>
            <Cascader
              disabled={!canEdit || drawerMode === ModeEnum.READ}
              style={{ width: '100%' }}
              options={cascaderOptions}
              onChange={onCascaderChange}
              multiple
              showArrow
              showCheckedStrategy={SHOW_CHILD}
              value={cascaderValue}
            />
          </>
        </DrawerForm>
      </div>
    </div>
  );
};
