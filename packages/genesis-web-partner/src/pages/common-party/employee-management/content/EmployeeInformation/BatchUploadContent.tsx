import { Button, Dropdown, Form, Radio, Upload, message } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import {
  Icon,
  Select,
  UploadDragCard,
  UploadFileItem,
} from '@zhongan/nagrand-ui';

import styles from './BatchUploadContent.scss';
import type {
  BizDictItem,
  ChannelDocumentV2,
  QueryUploadRecordsParams,
} from 'genesis-web-service';
import {
  ChannelService,
  DownloadOrUploadType,
  InstituteTypeEnum,
} from 'genesis-web-service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { security } from 'genesis-web-shared/lib';
import { useRequest } from 'ahooks';
import { UploadTable } from './UploadTable';

export const UploadPartnerTypeFieldName = 'partnerType';

enum TemplateTypeEnum {
  Empty = 'empty',
  CompleteData = 'completeData',
}

interface Props {
  teamId: string;
}

export const BatchUploadContent = ({
  teamId,
}: Props) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const uploadPartnerType = Form.useWatch(UploadPartnerTypeFieldName, form);
  const timer = useRef<any>();

  const typeEnums = useTenantBizDict('instituteType') as BizDictItem[];

  const [templateType, setTemplateType] = useState<
    TemplateTypeEnum | undefined
  >();
  const [currentFile, setCurrentFile] = useState<ChannelDocumentV2 | undefined>(
    undefined,
  );
  const [searchParams, setSearchParams] = useState<QueryUploadRecordsParams>({
    page: 0,
    size: 10,
  });

  const typeList = useMemo(
    () =>
      typeEnums?.filter(
        item => item?.enumItemName !== InstituteTypeEnum.SERVICE_COMPANY,
      ),
    [typeEnums],
  );

  const { loading: downloadFileLoading, run: downloadEmployee } = useRequest(
    () => {
      return ChannelService.download({
        type: DownloadOrUploadType['Employee'],
        isTemplate: templateType === TemplateTypeEnum.Empty,
        id: teamId,
      });
    },
    {
      manual: true,
      onSuccess: result => {
        downloadFile(result);
      },
      onError: error => {
        message.error(error?.message || t('Download failed'));
      },
    },
  );

  const dropdownMenuItems = useMemo(
    () =>
      typeList?.map(item => ({
        key: item.value,
        onClick: () => downloadEmployee(item.value),
        label: item.label,
      })),
    [typeList, downloadEmployee],
  );

  const uploadProps = useMemo(() => {
    const customRequest = (options: Record<string, any>) => {
      const formData = new FormData();
      formData.append('file', options.file);
      ChannelService.channelUpload(formData, {
        type: DownloadOrUploadType['Employee'],
        ext: JSON.stringify({ type: 3 }), // 写死3，后端接口需要，原因未知
        onlyUpload: true,
      })
        .then(response => {
          message.success(t('Uploaded successfully'));
          setCurrentFile(response.value);
        })
        .catch((error: Error) => {
          message.error(error?.message);
        });
    };
    return {
      name: 'file',
      accept: '.xlsx',
      headers: { ...security.csrf() },
      showUploadList: false,
      withCredentials: true,
      customRequest,
    };
  }, [uploadPartnerType]);

  const {
    data: uploadRecords,
    loading: queryUploadRecordsLoading,
    runAsync: queryUploadRecords,
  } = useRequest(
    () => {
      return ChannelService.queryUploadRecords({
        ...searchParams,
        type: DownloadOrUploadType['Employee'],
      });
    },
    {
      refreshDeps: [searchParams],
    },
  );

  const handleDelAttachment = useCallback(() => {
    setCurrentFile(undefined);
  }, []);

  const handleDownloadFile = useCallback(
    (fileUniqueCode?: string) => {
      ChannelService.download({
        fileUniqueCode,
        type: DownloadOrUploadType.COMMON_ENTITY_FILE,
      })
        .then(downloadFile)
        .catch((error: Error) =>
          message.error(error?.message || t('Download failed')),
        );
    },
    [currentFile],
  );

  const startPolling = useCallback(() => {
    timer.current = setTimeout(() => {
      queryUploadRecords().then(res => {
        if (res.data[0].status === 'PROCESSING') {
          startPolling();
        }
      })
    }, 5000);
  }, []);

  useEffect(() => {
    return () => {
      clearTimeout(timer.current);
    }
  }, []);


  const handleClearUpload = useCallback(() => {
    form.resetFields();
    setCurrentFile(undefined);
  }, [form]);

  const handleFinish = useCallback(() => {
    if (currentFile) {
      ChannelService.batchCreatePartners({
        type: DownloadOrUploadType['Employee'],
        ext: JSON.stringify({ teamId: teamId }),
        fileUniqueCode: currentFile.fileUniqueCode,
      })
        .then(() => {
          setSearchParams(old => ({ ...old, current: 1, pageSize: 10 }));
          startPolling();
        })
        .finally(() => handleClearUpload());
    } else {
      message.error(t('Please Upload File'));
    }
  }, [currentFile, teamId]);

  return (
    <div className={styles.batchUploadContent}>
      <div>
        <div className={styles.title}>
          1. <Trans i18nKey="Please Download Template First" ns="partner" />
        </div>
        <div className={styles.downloadContent}>
          <div className={styles.downloadTip}>
            <div>
              {t(
                'Blank Template: An upload template that does not contain any data. Users can add new data to create, edit, or delete employee information and its internal details.',
              )}
            </div>
            <div>
              {t(
                'Complete Data Set: An upload template that contains all data. Users can select portions of this data for processing.',
              )}
            </div>
          </div>

          <Radio.Group
            onChange={event => setTemplateType(event.target.value)}
            value={templateType}
          >
            <Radio
              value={TemplateTypeEnum.Empty}
              style={{ marginRight: 'var(--gap-huge)' }}
            >
              {t('Blank Template')}
            </Radio>
            <Radio value={TemplateTypeEnum.CompleteData}>
              {t('Complete Data Set')}
            </Radio>
          </Radio.Group>

          <div className={styles.downloadButton}>
            <Button
              loading={downloadFileLoading}
              icon={
                <Icon
                  type="download"
                  style={{ fontSize: 'var(--font-size-lg)' }}
                />
              }
              disabled={!templateType}
              onClick={downloadEmployee}
            >
              {t('Download Template')}
            </Button>
          </div>
        </div>
      </div>
      <div>
        <div className={styles.title}>2. {t('Upload File')}</div>
        <div className={styles.uploadContent}>
          <Form layout="vertical" form={form} onFinish={handleFinish}>
            <div className={styles.uploadDocumentWrapper}>
              {currentFile ? (
                <UploadFileItem
                  key={currentFile?.fileUniqueCode}
                  style={{ maxWidth: 440 }}
                  isShowHover={true}
                  needPreview={false}
                  fileName={currentFile.fileName}
                  hoverInfoList={[
                    {
                      icon: (
                        <Upload {...uploadProps}>
                          <Icon type="reload" />
                        </Upload>
                      ),
                    },
                    {
                      icon: <Icon type="download" />,
                      onClick: () =>
                        handleDownloadFile(currentFile.fileUniqueCode),
                    },
                    {
                      icon: <Icon type="delete" />,
                      onClick: () => handleDelAttachment(),
                    },
                  ]}
                />
              ) : (
                <Upload {...uploadProps}>
                  <UploadDragCard uploadTypeHint="" />
                </Upload>
              )}
            </div>

            <div className={styles.uploadBottom}>
              <Button
                style={{ marginRight: 'var(--gap-md)' }}
                onClick={handleClearUpload}
              >
                {t('Clear')}
              </Button>

              <Button
                htmlType="submit"
                type="primary"
                disabled={queryUploadRecordsLoading}
              >
                {t('Confirm')}
              </Button>
            </div>
          </Form>
        </div>
      </div>

      <UploadTable
        loading={queryUploadRecordsLoading}
        searchParams={searchParams}
        data={uploadRecords?.data}
        total={uploadRecords?.totalElements || 0}
        onDownload={handleDownloadFile}
        onSearch={setSearchParams}
      />
    </div>
  );
};
