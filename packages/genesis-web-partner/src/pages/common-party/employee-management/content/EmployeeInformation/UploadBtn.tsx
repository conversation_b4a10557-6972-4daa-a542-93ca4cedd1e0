import { useState, useCallback } from 'react';
import { Button, Upload, message, Form, Radio } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { RcFile } from 'antd/lib/upload/interface';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';
import { EmployeeAndRelativeEnum } from 'genesis-web-service';
import { EmployeeMenu } from '@/pages/common-party/employee-management/util/constants';
import { Icon, Modal } from '@zhongan/nagrand-ui';

interface UploadAgentProps {
  disabled?: boolean;
  teamId: number;
  handleUploadSuccess: () => void;
}

export const UploadBtn = ({
  disabled,
  teamId,
  handleUploadSuccess,
}: UploadAgentProps) => {
  const { t } = useTranslation();
  const [uploading, setUploading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [menuItem, setMenuItem] = useState(EmployeeAndRelativeEnum.Employee);

  const uploadProps = useCallback(
    (type: EmployeeAndRelativeEnum) =>
      getUploadPropsNew(
        `/api/channel/v2/file/upload/?id=${teamId}&type=${type}`,
        () => {
          setVisible(false);
          handleUploadSuccess();
        },
        setUploading,
      ),
    [teamId, handleUploadSuccess],
  );

  const handleBeforeUpload = useCallback(
    (file: RcFile) => {
      const fileNameLower = file.name.toLowerCase();
      if (!fileNameLower.endsWith('.xls') && !fileNameLower.endsWith('.xlsx')) {
        message.error(t('Only xls and xlsx files can be uploaded.'));
        setUploading(false);
        return false;
      }
      return true;
    },
    [t],
  );

  return (
    <>
      <Button
        icon={<UploadOutlined />}
        style={{ marginRight: 6 }}
        loading={uploading}
        disabled={disabled}
        onClick={() => setVisible(true)}
      >
        {t('Upload')}
      </Button>
      <Modal
        open={visible}
        title={t('Upload')}
        onCancel={() => setVisible(false)}
        cancelButtonProps={{
          style: { display: 'none' },
        }}
        okButtonProps={{
          style: { display: 'none' },
        }}
      >
        <Form layout="vertical">
          <Form.Item label="Type">
            <Radio.Group
              value={menuItem}
              onChange={e => setMenuItem(e.target.value)}
            >
              {EmployeeMenu.map(menuConfig => (
                <Radio value={menuConfig.key}>{menuConfig.title}</Radio>
              ))}
            </Radio.Group>
          </Form.Item>
          <Form.Item label="Upload File">
            <Upload
              {...uploadProps(menuItem)}
              accept=".xls, .xlsx"
              beforeUpload={handleBeforeUpload}
            >
              <Button icon={<Icon type="upload" />}>{t('Browse')}</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
