@import '@/variables.scss';

.batch-upload-content {
  margin-top: 24px;
  
  .title {
    font-weight: bold;
    color: var(--heading-color);
    padding-bottom: var(--gap-xs);
  }

  .download-content {
    padding-left: var(--gap-md);

    .download-tip {
      font-size: var(--font-size-sm);
      color: var(--text-color-quaternary);
      margin-bottom: var(--gap-md);
      width: 80%;
    }

    .download-button {
      margin: var(--gap-lg) 0;
    }
  }

  .upload-content {
    width: 422px;

    .upload-bottom {
      margin: var(--gap-lg) 0;
    }
  }

  :global {
    .#{$antd-prefix}-upload {
      width: 100%;
    }

    .nagrand-filter-pannel {
      max-width: 400px;
    }
  }

  .upload-history {
    display: flex;
    flex-direction: column;
    gap: var(--gap-xs);

    .title {
      font-weight: bold;
    }
  }

  .failed-text {
    color: var(--error-color);
  }
}