import { useMemo, useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, message, Tooltip, Dropdown } from 'antd';
import type { ColumnProps } from 'antd/es/table';
import { PlusOutlined } from '@ant-design/icons';
import {
  EditOutline,
  DeleteOutline,
  ViewSquareOutline,
  WorkSchedule,
} from '@/components/Icons';
import { PaginationComponent } from '@/components/Pagination';
import { ModeEnum } from '@/types/common';
import type { LocationQueryParam } from '@/types/common-party';
import { TeamTypeTitleMap } from '@/pages/common-party/employee-management/util/constants';
import type { EmployeeInfo, BizDictItem } from 'genesis-web-service';
import {
  ChannelService,
  YearlyWorkScheduleStatus,
  MetadataService,
} from 'genesis-web-service';
import { useAddressConfig } from 'genesis-web-component/lib/components/Address';
import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import { EmployeeDrawer } from './EmployeeDrawer';
import { WorkScheduleManagementDrawer } from '@/components/WorkScheduleMgmtDrawer';
import { PersonalWorkScheduleDrawer } from '@/components/PersonalWorkScheduleDrawer';
import { StatusTag } from '@/components/StatusTag';
import {
  useGetInputSearchDropdown,
  useGetSelectSearchDropdown,
} from '@/components/ColumnWithSearch';

import {
  useEditPermission,
  useToolTipText,
} from '@/pages/common-party/employee-management/hooks/useEmployeeManagement';
import { useColumns } from '@/pages/common-party/employee-management/hooks/useColumns';
import {
  EmployeeFormColumnType,
  EmployeeWorkScheduleTypeTabEnum,
} from '@/types/common-party';
import { DefaultTablePagination } from '@/utils/constants';
import type { ConnectState } from '@/models/connect';
import { transferSchemaToTableProp } from '@/utils/utils';
import { TagsShape, TagsType } from '@/utils/constants';
import { WorkScheduleType } from '@/types/common-party';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { renderEnumsWithString } from '@/components/RenderEnums';
import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import { WorkScheduleTypeContext } from '@/pages/common-party/partner-setting-v2/hooks/useContext';

import { useSelector, useDispatch } from '@umijs/max';
import { useRequest, useToggle } from 'ahooks';

import styles from './index.scss';
import { useSearchParams } from 'react-router-dom';
import qs from 'qs';
import {
  CommonIconAction,
  Icon,
  Table,
  TableActionsContainer,
} from '@zhongan/nagrand-ui';
import { useAddressI18nList } from '@/hooks/useAddressI18nList';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { BatchUploadContent } from './BatchUploadContent';

/**
 *
 * @description employee信息；
 */
export const EmployeeInformation: React.FC = () => {
  const { t } = useTranslation('partner');
  const [searchParams] = useSearchParams();
  const { teamType, id } = qs.parse(
    searchParams.toString(),
  ) as unknown as LocationQueryParam;
  const { canEdit, canView } = useEditPermission();

  const [drawerMode, setDrawerMode] = useState(ModeEnum.EDIT);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [editedEmployeeId, setEditedEmployeeId] = useState<number>();
  const [pagination, setPagination] = useState(DefaultTablePagination);
  const [teamId, setTeamId] = useState<number>();
  const [searchedName, setSearchedName] = useState<string>();
  const [searchedDepartment, setSearchedDepartment] = useState<string[]>();
  const [employeeCode, setEmployeeCode] = useState<string>();
  const [selectedScheduleYear, setSelectedScheduleYear] = useState<string>();
  const [personalScheduleVisible, setPersonalScheduleVisible] = useState(false);
  const [isTenantIncludesWorkType, setTenantIncludesWorkType] = useState(false);
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const [batchUploadDrawerVisible, { toggle: toggleBatchUploadDrawerVisible }] =
    useToggle();
  const { teamInfo, refreshEmployeeList } = useSelector(
    ({ partnerManagement }: ConnectState) => ({
      teamInfo: partnerManagement.teamInfo,
      refreshEmployeeList: partnerManagement.refreshEmployeeList,
    }),
  );
  const dispatch = useDispatch();
  const toolTipText = useToolTipText(teamInfo, TeamTypeTitleMap[teamType]);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: ['batchUploadPolicyViewStatus'],
    });
  }, [dispatch]);

  useEffect(() => {
    // id变化时pagination回到初始状态
    setTeamId((id && Number(id)) ?? teamInfo?.id);
    setPagination({ ...DefaultTablePagination });
    setSearchedName(undefined);
    setSearchedDepartment(undefined);
  }, [id, teamInfo?.id]);

  // 获取配置的 WorkScheduleType
  useEffect(() => {
    MetadataService.queryBizDictConfigList('workScheduleType').then(res => {
      const bizDictConfigList =
        res.tenantBizDictConfig?.workScheduleType?.bizDictConfigList || [];
      const isTenantConfigIncludesWorkingType = bizDictConfigList?.some(
        workScheduleType =>
          workScheduleType?.enumItemName ===
          EmployeeWorkScheduleTypeTabEnum.WORKING_TYPE,
      );
      setTenantIncludesWorkType(isTenantConfigIncludesWorkingType);
    });
  }, []);

  // 查询
  const {
    data: employeeList,
    loading,
    run: queryEmployeeList,
  } = useRequest(
    () => {
      if (teamId) {
        return ChannelService.queryEmployeeList(teamId, {
          pageIndex: pagination.current - 1,
          pageSize: pagination.pageSize,
          nameOrCode: searchedName,
          departmentNames: searchedDepartment,
        });
      }
    },
    {
      refreshDeps: [teamId, pagination, searchedName, searchedDepartment],
    },
  );

  const addressI18nList = useAddressI18nList(employeeList?.data);

  // 处理address & zipCode 表格表头展示
  const { formItems } = useAddressConfig();
  const addressAndZipCodeTableColumns = useMemo(() => {
    if (!formItems?.length) {
      return [];
    }
    return transferSchemaToTableProp(formItems, addressI18nList);
  }, [formItems, addressI18nList]);

  // 查询
  const {
    data: departmentOptions,
    loading: queryDepartmentListLoading,
    run: queryDepartmentList,
  } = useRequest(async () => {
    if (teamId) {
      const departmentListResp = await ChannelService.queryDepartmentList(
        teamId,
        {
          pageIndex: 0,
          pageSize: 100000,
        },
      );
      return departmentListResp?.data?.map(department => ({
        label: department.name,
        value: department.code,
      }));
    }
  });

  useEffect(() => {
    // 删除,编辑了department的时候，employee列表也要刷新
    const refreshEmployeeListFunc = async () => {
      await queryEmployeeList();
      dispatch({
        type: 'partnerManagement/updateRefreshEmployeeListStatus',
        payload: false,
      });
    };
    if (teamId && refreshEmployeeList) {
      refreshEmployeeListFunc();
    }
  }, [teamId, refreshEmployeeList]);

  // 关闭抽屉
  const onCloseDrawer = useCallback(
    (updated?: boolean) => {
      if (updated) {
        if (!editedEmployeeId) {
          //新增成功后，回到初始页码条件
          setPagination({ ...DefaultTablePagination });
        } else {
          // 编辑成功，以当前条件请求
          queryEmployeeList();
        }
      }
      setDrawerMode(ModeEnum.EDIT);
      setEditedEmployeeId(undefined);
      setDrawerVisible(false);
    },
    [editedEmployeeId, queryEmployeeList],
  );

  // 删除
  const removeAgent = useCallback(
    (agentId: number) =>
      ChannelService.removeEmployee(teamId, agentId)
        .then(() => {
          setPagination({ ...DefaultTablePagination });
          message.success(t('Delete successfully'));
        })
        .catch((error: Error) => message.error(error.message)),
    [teamId],
  );

  const onChangePagination = useCallback(
    (current: number, pageSize: number) => {
      setPagination(old => ({
        ...old,
        current,
        pageSize,
      }));
    },
    [],
  );

  // 查看/编辑
  const onOpenEmployeeDrawer = useCallback(
    (mode: ModeEnum, employeeId?: number) => {
      setDrawerMode(mode);
      setDrawerVisible(true);
      setEditedEmployeeId(employeeId);
    },
    [],
  );

  const onClickPersonalWorkSchedule = useCallback((code: string) => {
    setEmployeeCode(code);
    setPersonalScheduleVisible(true);
  }, []);

  // 搜索employee
  const onSearchEmployeeByName = useCallback((content: string) => {
    if (content) {
      // search，一次性查全部
      setPagination(old => ({
        ...old,
        current: 1,
      }));
    } else {
      // reset，回到初始页码
      setPagination({ ...DefaultTablePagination });
    }
    setSearchedName(content);
  }, []);

  // 根据department搜索employee
  const onSearchEmployeeByDepartment = useCallback((content: string[]) => {
    if (content) {
      // search，一次性查全部
      setPagination(old => ({
        ...old,
        current: 1,
      }));
    } else {
      // reset，回到初始页码
      setPagination({ ...DefaultTablePagination });
    }
    setSearchedDepartment(content);
  }, []);

  const columns =
    useColumns({
      formType: EmployeeFormColumnType.EmployeeInfo,
      teamType,
    }) ?? [];

  const getSearchDropdown = useGetInputSearchDropdown<EmployeeInfo>(
    onSearchEmployeeByName,
    t('Search by name'),
    !searchedName,
  );

  const getDepartmentSearchDropdown = useGetSelectSearchDropdown<EmployeeInfo>(
    onSearchEmployeeByDepartment,
    departmentOptions,
    queryDepartmentList,
    !searchedDepartment?.length,
    queryDepartmentListLoading,
  );

  const combinedColumns = useMemo((): ColumnProps<EmployeeInfo>[] => {
    const clonedColumns = isTenantIncludesWorkType
      ? [
          ...columns?.map(column => {
            const clonedColumn = { ...column };
            if (column?.dataIndex === 'workingTypes') {
              clonedColumn.dataIndex = 'scheduleWorkingTypes';
              clonedColumn.render = (scheduleWorkingTypes: string[]) => (
                <ComponentWithFallback>
                  {scheduleWorkingTypes?.length &&
                    scheduleWorkingTypes
                      ?.map(workingType =>
                        renderEnumsWithString(workingType, enums?.workingType),
                      )
                      ?.join(', ')}
                </ComponentWithFallback>
              );
            }
            return clonedColumn;
          }),
        ]
      : [...columns];
    return [
      {
        title: t('Employee Name'),
        render: item =>
          (item.firstName || item.lastName) &&
          `${item.firstName || ''} ${item.lastName || ''}`,
        fixed: 'left',
        ...(teamInfo ? getSearchDropdown() : undefined),
      },
      ...clonedColumns?.map(column => ({
        ...column,
        ...(column?.dataIndex === 'departmentColumn'
          ? getDepartmentSearchDropdown()
          : undefined),
      })),
      ...addressAndZipCodeTableColumns,
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (_, record) => (
          <div className="table-actions">
            <TableActionsContainer>
              <DeleteConfirm
                onOk={() => removeAgent(record.id)}
                disabled={!canEdit}
              >
                <Tooltip title={t('Delete')}>
                  <Button
                    disabled={!canEdit}
                    type="link"
                    icon={<DeleteOutline />}
                  />
                </Tooltip>
              </DeleteConfirm>
              <Tooltip title={t('Edit')}>
                <Button
                  disabled={!canEdit}
                  type="link"
                  icon={<EditOutline />}
                  onClick={() => onOpenEmployeeDrawer(ModeEnum.EDIT, record.id)}
                />
              </Tooltip>
              <Tooltip title={t('View')}>
                <Button
                  disabled={!canView}
                  type="link"
                  icon={<ViewSquareOutline />}
                  onClick={() => onOpenEmployeeDrawer(ModeEnum.READ, record.id)}
                />
              </Tooltip>
              <CommonIconAction
                icon={<Icon type="calendar" />}
                tooltipTitle={t('Personal Work Schedule')}
                onClick={() => onClickPersonalWorkSchedule(record.code)}
              />
            </TableActionsContainer>
          </div>
        ),
      },
    ];
  }, [
    enums,
    columns,
    canEdit,
    canView,
    addressAndZipCodeTableColumns,
    teamInfo,
    getSearchDropdown,
    removeAgent,
    onOpenEmployeeDrawer,
    onClickPersonalWorkSchedule,
    isTenantIncludesWorkType,
  ]);

  // 获取所有年份维护的排班表状态
  const { data: allYearHistory, run: queryAllYearsEmployeeHistory } =
    useRequest(
      () => {
        if (teamId) {
          return ChannelService.queryAllYearsEmployeeHistory(teamId);
        }
      },
      {
        refreshDeps: [teamId],
      },
    );

  const scheduleMenuItems = useMemo(
    () =>
      allYearHistory?.map(history => {
        const type =
          history.status === YearlyWorkScheduleStatus.Done
            ? TagsType.Default
            : TagsType.Regular;

        return {
          label: (
            <div className={styles.rosterMenuItem}>
              {history.year}
              <StatusTag
                hasDot={false}
                hasborder={false}
                shape={TagsShape.Round}
                color={
                  history.status === YearlyWorkScheduleStatus.Done
                    ? styles.primaryDisabledColor
                    : null
                }
                type={type}
              >
                {t(history.status)}
              </StatusTag>
            </div>
          ),
          key: history.year,
        };
      }),
    [allYearHistory],
  );

  // 关闭总表drawer
  const onCloseWorkScheduleDrawer = useCallback(
    (updated: boolean) => {
      setSelectedScheduleYear(undefined);
      if (updated) {
        setPagination({ ...DefaultTablePagination });
        queryAllYearsEmployeeHistory();
      }
    },
    [queryAllYearsEmployeeHistory],
  );

  // 关闭个人排班表drawer
  const onClosePersonalScheduleDrawer = useCallback(
    (updated: boolean) => {
      setPersonalScheduleVisible(false);
      if (updated) {
        queryEmployeeList();
      }
    },
    [queryEmployeeList],
  );

  const handleCloseDrawer = () => {
    toggleBatchUploadDrawerVisible();
    queryEmployeeList();
  };

  return (
    <WorkScheduleTypeContext.Provider value={{ isTenantIncludesWorkType }}>
      <div className={styles.employeeContainer}>
        <p className={styles.pageTitle}>{t('Employee Information')}</p>
        <div className={styles.actionsContainer}>
          <Tooltip title={toolTipText}>
            <Button
              icon={<PlusOutlined />}
              type="primary"
              disabled={!canEdit || !teamInfo}
              onClick={() => onOpenEmployeeDrawer(ModeEnum.ADD)}
            >
              {t('Add New Employee')}
            </Button>
          </Tooltip>
          <Dropdown
            menu={{
              items: scheduleMenuItems,
              onClick: ({ key: year }) => setSelectedScheduleYear(year),
            }}
            trigger={['click']}
            getPopupContainer={triggerNode => triggerNode?.parentElement}
            overlayStyle={{ width: 292 }}
            disabled={!allYearHistory?.length}
          >
            <Button
              icon={<WorkSchedule />}
              disabled={!teamInfo || (!canView && !canEdit)}
              className="margin-left-16"
            >
              {t('Work Schedule Management')}
            </Button>
          </Dropdown>
          <div>
            <Button
              disabled={!canEdit || !teamInfo}
              onClick={() => {
                toggleBatchUploadDrawerVisible();
              }}
            >
              {t('Batch Upload')}
            </Button>
          </div>
        </div>

        <Table
          scroll={{ x: 'max-content' }}
          rowKey="id"
          columns={combinedColumns}
          loading={loading}
          dataSource={employeeList?.data}
          pagination={false}
          getPopupContainer={triggerNode =>
            triggerNode?.parentElement?.parentElement?.parentElement ||
            document.body
          }
        />
        <PaginationComponent
          className="margin-top-16 margin-bottom-16"
          total={employeeList?.totalElements || 0}
          pagination={pagination}
          handlePaginationChange={onChangePagination}
        />
        <EmployeeDrawer
          id={editedEmployeeId}
          teamId={teamId}
          visible={drawerVisible}
          disabled={drawerMode === ModeEnum.READ}
          onCloseDrawer={onCloseDrawer}
          mode={drawerMode}
        />
        <WorkScheduleManagementDrawer
          year={selectedScheduleYear}
          id={teamId}
          visible={!!selectedScheduleYear}
          readonly={!canEdit}
          type={WorkScheduleType.EmployeeManagement}
          onClose={onCloseWorkScheduleDrawer}
        />
        <PersonalWorkScheduleDrawer
          personCode={employeeCode}
          visible={personalScheduleVisible}
          readonly={!canEdit}
          type={WorkScheduleType.EmployeeManagement}
          onClose={onClosePersonalScheduleDrawer}
        />
      </div>
      <DrawerForm
        title={t('Batch Upload')}
        visible={batchUploadDrawerVisible}
        onClose={handleCloseDrawer}
        cancelText={t('Close')}
        submitBtnShow={false}
        getContainer={() =>
          document.getElementsByClassName('partner-global')[0] as HTMLElement
        }
      >
        <BatchUploadContent teamId={id} />
      </DrawerForm>
    </WorkScheduleTypeContext.Provider>
  );
};
