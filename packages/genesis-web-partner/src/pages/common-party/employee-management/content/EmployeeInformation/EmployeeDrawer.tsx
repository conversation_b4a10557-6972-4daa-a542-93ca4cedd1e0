<<<<<<< HEAD
import { useMemo, useState, useCallback } from 'react';
=======
import type { ChangeEvent } from 'react';
import { useCallback, useMemo, useState } from 'react';
>>>>>>> f617d681332 ([GIS-127704] feat(partner): multi-language support for agent name)
import { useTranslation } from 'react-i18next';
import { Form, message, Divider, Col, Skeleton } from 'antd';
import type { LocationQueryParam } from '@/types/common-party';
import type { EmployeeInfo, BizDictItem } from 'genesis-web-service';
import { ChannelService, AgentType, IAMSourceType } from 'genesis-web-service';
import { CommonForm } from '@/components/CommonForm/Form';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { AddressComponent } from 'genesis-web-component/lib/components/AddressComponent';
import { Relationship } from './Relationship';
import { Account } from './Account';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { useFormFields } from '@/pages/common-party/employee-management/hooks/useFormFields';
import { useQuerySelectOptions } from '@/pages/common-party/employee-management/hooks/request';
import {
  EmployeeFormColumnType,
  EmployeeWorkScheduleTypeTabEnum,
} from '@/types/common-party';
import { FieldType } from '@/components/CommonForm';

import { useRequest } from 'ahooks';
import moment from 'moment';

import styles from './index.scss';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import { useSearchParams } from 'react-router-dom';
import qs from 'qs';

<<<<<<< HEAD
=======
import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import { I18nLanguage } from '@/components/I18nLanguage';
import { EntityType, useGenericSchemaFormItemFields } from '@/hooks/useGenericSchemaFormItemFields';
import { useNameGroupsFormatChange } from '@/hooks/useNameGroupsFormatConnector';
import { useQuerySelectOptions } from '@/pages/common-party/employee-management/hooks/request';
import { useEmployeeList } from '@/pages/common-party/employee-management/hooks/useEmployeeManagement';
import { ChannelOrganizationTypeEnum } from '@/pages/common-party/employee-management/util/constants';
import { AddressFormConfig } from '@/pages/common-party/utils/constants';
>>>>>>> f617d681332 ([GIS-127704] feat(partner): multi-language support for agent name)
import type { ModeEnum } from '@/types/common';
import { useWorkScheduleTypeContext } from '@/pages/common-party/partner-setting-v2/hooks/useContext';
import { AddressFormConfig } from '@/pages/common-party/utils/constants';
import { CustomerAddressFieldCodeList } from '@/utils/constants';
interface DrawerProps {
  id: number;
  teamId: number;
  visible: boolean;
  disabled: boolean;
  onCloseDrawer: (updated?: boolean) => void;
  mode: ModeEnum;
}

/**
 *
 * @param id 当前employeeId
 * @param teamId 当前employee所属teamId
 * @param visible 是否可见
 * @param disabled 是否禁用
 * @param onCloseDrawer 关闭抽屉
 * @description employee 新增/查看/修改抽屉
 */
export const EmployeeDrawer = ({
  id,
  teamId,
  visible,
  disabled,
  onCloseDrawer,
  mode,
}: DrawerProps) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const [searchParams] = useSearchParams();
  const { teamType } = qs.parse(
    searchParams.toString(),
  ) as unknown as LocationQueryParam;
  const { departmentMap } = useQuerySelectOptions(
    EmployeeFormColumnType.EmployeeInfo,
    visible,
  );
  const [addressCascaderValue, setAddressCascaderValue] =
    useState<Record<string, string>>();
  const { isTenantIncludesWorkType } = useWorkScheduleTypeContext();

  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  const { data: employeeInfo, loading } = useRequest(
    () => {
      if (id && visible) {
        return ChannelService.queryEmployeeById(teamId, id);
      }
    },
    {
      refreshDeps: [visible, teamId, id],
      onSuccess: employeeResp => {
        if (employeeResp) {
          form.setFieldsValue({
            ...employeeResp,
            birthday: employeeResp.birthday && moment(employeeResp.birthday),
            onboardDate:
              employeeResp.onboardDate && moment(employeeResp.onboardDate),
            resignDate:
              employeeResp.resignDate && moment(employeeResp.resignDate),
            officeCity: employeeResp.officeCity
              ? employeeResp.officeCity.split(',')
              : undefined,
            iamUserName:
              employeeResp.iamUserName &&
                employeeResp.iamSource === IAMSourceType.Native ?
                employeeResp.iamUserName?.split('@')?.[0] :
                employeeResp.iamUserName,
          });
        }
      },
      onError: (error: Error) => message.error(error?.message),
    },
  );

  const onClose = useCallback(
    (updated?: boolean) => {
      form.resetFields();
      // 手动重置这几个值为初始值,因为form。resetFields 无法重置这几个为初始值
      form.setFieldsValue({
        iamSource: IAMSourceType.Native,
        iamUserName: '',
      });
      setAddressCascaderValue(undefined);
      onCloseDrawer(updated);
    },
    [onCloseDrawer],
  );

  // 保存
  const { run: saveManagerInfo, loading: updating } = useRequest(
    (employee: EmployeeInfo) =>
    (!id
      ? ChannelService.addEmployee(teamId, {
        ...employee,
        agentType: AgentType.Employee,
      })
      : ChannelService.updateEmployee({
        ...employee,
      })),
    {
      manual: true,
      onSuccess: () => onClose(true),
      onError: (error: Error) => message.error(error?.message),
    },
  );

  // 保存前数据处理
  const onSave = useCallback(() => {
    form.validateFields().then(values => {
      const managerInfo = {
        ...employeeInfo,
        ...values,
        ...addressCascaderValue,
        birthday:
          values?.birthday && dateFormatInstance.formatDate(values.birthday),
        onboardDate:
          values?.onboardDate &&
          dateFormatInstance.formatDate(values.onboardDate),
        resignDate:
          values?.resignDate &&
          dateFormatInstance.formatDate(values.resignDate),
        officeCity: values.officeCity?.join(','),
      };

      // address11-18 & address1-8都传,一样的赋值
      const addressParams = {};
      for (const addressKey in managerInfo) {
        if (CustomerAddressFieldCodeList?.includes(addressKey)) {
          addressParams[AddressFormConfig[addressKey].key] =
            managerInfo[addressKey];
        }
      }

      saveManagerInfo({ ...managerInfo, ...addressParams });
    });
  }, [saveManagerInfo, employeeInfo, addressCascaderValue]);

  const drawerTitle = useMemo(() => {
    return !id
      ? t('Add New Employee')
      : disabled
        ? t('View Employee Information')
        : t('Edit Employee Information');
  }, [employeeInfo, disabled, t]);

  const fields =
    useFormFields({
      form,
      disabled,
      information: employeeInfo,
      formType: EmployeeFormColumnType.EmployeeInfo,
      teamType,
      modalVisible: visible,
    }) ?? [];

  const handleNameChange = useNameGroupsFormatChange(form);

  const combinedFields = useMemo(() => {
<<<<<<< HEAD
    // 如果config center配置了WORKING_TYPE就不在employee详情展示workingTypes
    const clonedFields = isTenantIncludesWorkType
      ? [...fields.filter(field => field?.key !== 'workingTypes')]
      : [...fields];
=======
    const [addressFields, nonAddressFields] = separateAddressFields(staticFields);

    nonAddressFields.forEach(field => {
      const props = field.ctrlProps ?? {};
      if (
        ['code', 'idType', 'idNo', 'birthday', 'resignDate'].includes(field.key) &&
        employeeInfo?.[field.key as keyof EmployeeInfo]
      ) {
        field.ctrlProps = {
          ...field.ctrlProps,
          disabled: true,
        };
      }

      const customerDom = (onChange?: (event: ChangeEvent<HTMLInputElement>) => void) => (
        <Col span={12}>
          <I18nLanguage
            label={field.label}
            name={field.key}
            disabled={disabled || employeeInfo?.[field.key as keyof EmployeeInfo]}
            onChange={event => onChange?.(event)}
          />
        </Col>
      );

      switch (field.key) {
        case 'agentFullName':
          field.type = FieldType.Customize;
          field.customerDom = customerDom;
          break;

        case 'firstName':
          field.type = FieldType.Customize;
          field.customerDom = customerDom((event: ChangeEvent<HTMLInputElement>) =>
            handleNameChange({
              nameList: [event.target.value, form.getFieldValue('lastName')],
              fullNameKey: 'agentFullName',
            })
          );
          break;

        case 'lastName':
          field.type = FieldType.Customize;
          field.customerDom = customerDom((event: ChangeEvent<HTMLInputElement>) =>
            handleNameChange({
              nameList: [form.getFieldValue('firstName'), event.target.value],
              fullNameKey: 'agentFullName',
            })
          );
          break;

        case 'departmentCodes':
          field.type = FieldType.Select;
          props.options = departmentOptions;
          break;

        case 'workloadRate':
          props.addonAfter = '%';
          props.min = 0;
          props.style = { width: '100%' };
          break;
>>>>>>> f617d681332 ([GIS-127704] feat(partner): multi-language support for agent name)

    clonedFields.push({
      type: FieldType.Customize,
      customerDom: (
        <AddressComponent
          disabled={disabled}
          initialValue={employeeInfo ?? {}}
          onCascaderValueChange={setAddressCascaderValue}
          form={form}
          cascaderCol={24}
          colProps={{ span: 12 }}
          inputWidth="100%"
        />
      ),
    });

    clonedFields.push({
      type: FieldType.Customize,
      customerDom: (
        <Col span={24}>
          <Form.Item name="relationshipList">
            <Divider />
            <Relationship
              initialRelations={employeeInfo?.relationshipList}
              propForm={form}
              disabled={disabled}
            />
          </Form.Item>
        </Col>
      ),
    });
    clonedFields.push({
      type: FieldType.Customize,
      customerDom: (
        <Account
          initialValue={employeeInfo}
          propForm={form}
          name="Graphene"
          mode={mode}
        />
      ),
    });

    return clonedFields;
  }, [
    fields,
    disabled,
    employeeInfo,
    form,
    enums,
    mode,
    isTenantIncludesWorkType,
  ]);

  return (
    <DrawerForm
      className={styles.employeeEditDrawer}
      visible={visible}
      disabled={disabled}
      onClose={() => onClose()}
      maskClosable={false}
      width={752}
      title={drawerTitle}
      cancelText={t('Cancel')}
      sendText={t('Submit')}
      onSubmit={onSave}
      submitBtnProps={{ loading: updating }}
    >
      <Skeleton loading={loading} active>
        <CommonForm form={form} fields={combinedFields} />
      </Skeleton>
    </DrawerForm>
  );
};
