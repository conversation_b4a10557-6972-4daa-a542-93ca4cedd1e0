import type { ChangeEvent } from 'react';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Divider, Form, Skeleton, message } from 'antd';

import { useRequest } from 'ahooks';
import moment from 'moment';

import { AddressComponent } from 'genesis-web-component/lib/components/AddressComponent';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { BizDictItem, EmployeeInfo, FactorsType } from 'genesis-web-service';
import { AgentType, ChannelService, IAMSourceType } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import { I18nLanguage } from '@/components/I18nLanguage';
import { EntityType, useGenericSchemaFormItemFields } from '@/hooks/useGenericSchemaFormItemFields';
import { useNameGroupsFormatChange } from '@/hooks/useNameGroupsFormatConnector';
import { useQuerySelectOptions } from '@/pages/common-party/employee-management/hooks/request';
import { useEmployeeList } from '@/pages/common-party/employee-management/hooks/useEmployeeManagement';
import { ChannelOrganizationTypeEnum } from '@/pages/common-party/employee-management/util/constants';
import { AddressFormConfig } from '@/pages/common-party/utils/constants';
import type { ModeEnum } from '@/types/common';
import { EmployeeFormColumnType } from '@/types/common-party';
import { CustomerAddressFieldCodeList } from '@/utils/constants';
import { separateAddressFields } from '@/utils/utils';

import { Account } from './Account';
import { Relationship } from './Relationship';
import styles from './index.scss';

interface DrawerProps {
  id: number;
  teamId: number;
  visible: boolean;
  disabled: boolean;
  onCloseDrawer: (updated?: boolean) => void;
  mode: ModeEnum;
}

const MaxPageSize = 2000;

/**
 *
 * @param id 当前employeeId
 * @param teamId 当前employee所属teamId
 * @param visible 是否可见
 * @param disabled 是否禁用
 * @param onCloseDrawer 关闭抽屉
 * @description employee 新增/查看/修改抽屉
 */
export const EmployeeDrawer = ({ id, teamId, visible, disabled, onCloseDrawer, mode }: DrawerProps) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const [addressCascaderValue, setAddressCascaderValue] = useState<Record<string, string>>();
  const { departmentOptions } = useQuerySelectOptions(EmployeeFormColumnType.EmployeeInfo, visible);
  const { employeeListOption } = useEmployeeList({
    pageIndex: 0,
    pageSize: MaxPageSize,
  });

  const { data: employeeInfo, loading } = useRequest(
    () => {
      if (id && visible) {
        return ChannelService.queryEmployeeById(teamId, id);
      }
    },
    {
      refreshDeps: [visible, teamId, id],
      onSuccess: employeeResp => {
        if (employeeResp) {
          form.setFieldsValue({
            ...employeeResp,
            birthday: employeeResp.birthday && moment(employeeResp.birthday),
            onboardDate: employeeResp.onboardDate && moment(employeeResp.onboardDate),
            resignDate: employeeResp.resignDate && moment(employeeResp.resignDate),
            officeCity: employeeResp.officeCity ? employeeResp.officeCity.split(',') : undefined,
            iamUserName:
              employeeResp.iamUserName && employeeResp.iamSource === IAMSourceType.Native
                ? employeeResp.iamUserName?.split('@')?.[0]
                : employeeResp.iamUserName,
          });
        }
      },
      onError: (error: Error) => message.error(error?.message),
    }
  );

  const onClose = useCallback(
    (updated?: boolean) => {
      form.resetFields();
      // 手动重置这几个值为初始值,因为form。resetFields 无法重置这几个为初始值
      form.setFieldsValue({
        iamSource: IAMSourceType.Native,
        iamUserName: '',
      });
      setAddressCascaderValue(undefined);
      onCloseDrawer(updated);
    },
    [onCloseDrawer]
  );

  // 保存
  const { run: saveManagerInfo, loading: updating } = useRequest(
    (employee: EmployeeInfo) =>
      !id
        ? ChannelService.addEmployee(teamId, {
            ...employee,
            agentType: AgentType.Employee,
          })
        : ChannelService.updateEmployee({
            ...employee,
          }),
    {
      manual: true,
      onSuccess: () => onClose(true),
      onError: (error: Error) => message.error(error?.message),
    }
  );

  // 保存前数据处理
  const onSave = useCallback(() => {
    form.validateFields().then(values => {
      const managerInfo = {
        ...employeeInfo,
        ...values,
        ...addressCascaderValue,
        birthday: values?.birthday && dateFormatInstance.formatDate(values.birthday),
        onboardDate: values?.onboardDate && dateFormatInstance.formatDate(values.onboardDate),
        resignDate: values?.resignDate && dateFormatInstance.formatDate(values.resignDate),
        officeCity: values.officeCity?.join(','),
      };

      // address11-18 & address1-8都传,一样的赋值
      const addressParams = {};
      for (const addressKey in managerInfo) {
        if (CustomerAddressFieldCodeList?.includes(addressKey)) {
          addressParams[AddressFormConfig[addressKey].key] = managerInfo[addressKey];
        }
      }

      saveManagerInfo({ ...managerInfo, ...addressParams });
    });
  }, [saveManagerInfo, employeeInfo, addressCascaderValue]);

  const drawerTitle = useMemo(() => {
    return !id ? t('Add New Employee') : disabled ? t('View Employee Information') : t('Edit Employee Information');
  }, [employeeInfo, disabled, t]);

  const { formItems: staticFields } = useGenericSchemaFormItemFields({
    staticOrDynamic: 'STATIC',
    category: 'BASE',
    disabled: disabled,
    type: ChannelOrganizationTypeEnum.EMPLOYEE,
    fieldKeyPrefix: 'employeeExtensions',
    entityType: EntityType.ORGANIZATION,
  });

  const handleNameChange = useNameGroupsFormatChange(form);

  const combinedFields = useMemo(() => {
    const [addressFields, nonAddressFields] = separateAddressFields(staticFields);

    nonAddressFields.forEach(field => {
      const props = field.ctrlProps ?? {};
      if (
        ['code', 'idType', 'idNo', 'birthday', 'resignDate'].includes(field.key) &&
        employeeInfo?.[field.key as keyof EmployeeInfo]
      ) {
        field.ctrlProps = {
          ...field.ctrlProps,
          disabled: true,
        };
      }

      const customerDom = (onChange?: (event: ChangeEvent<HTMLInputElement>) => void) => (
        <Col span={12}>
          <I18nLanguage
            label={field.label}
            name={field.key}
            disabled={disabled || employeeInfo?.[field.key as keyof EmployeeInfo]}
            onChange={event => onChange?.(event)}
          />
        </Col>
      );

      switch (field.key) {
        case 'agentFullName':
          field.type = FieldType.Customize;
          field.customerDom = customerDom;
          break;

        case 'firstName':
          field.type = FieldType.Customize;
          field.customerDom = customerDom((event: ChangeEvent<HTMLInputElement>) =>
            handleNameChange({
              nameList: [event.target.value, form.getFieldValue('lastName')],
              fullNameKey: 'agentFullName',
            })
          );
          break;

        case 'lastName':
          field.type = FieldType.Customize;
          field.customerDom = customerDom((event: ChangeEvent<HTMLInputElement>) =>
            handleNameChange({
              nameList: [form.getFieldValue('firstName'), event.target.value],
              fullNameKey: 'agentFullName',
            })
          );
          break;

        case 'departmentCodes':
          field.type = FieldType.Select;
          props.options = departmentOptions;
          break;

        case 'workloadRate':
          props.addonAfter = '%';
          props.min = 0;
          props.style = { width: '100%' };
          break;

        case 'countryCode':
          props.options = props.options?.map((option: BizDictItem) => ({
            ...option,
            label: (
              <span className="flex justify-between">
                {option.label}
                <span>{option.itemExtend2}</span>
              </span>
            ),
          }));
          props.filterOption = (input: string, option: BizDictItem) =>
            (option?.itemExtend2 ?? '').toLowerCase().includes(input.toLowerCase());
          break;

        case 'managerCode':
          field.type = FieldType.Select;
          props.options = employeeListOption;
          props.filterOption = (inputValue: string, option: BizDictItem) =>
            (option?.name ?? '').toLowerCase().includes(inputValue.toLowerCase());
          break;
      }
    });

    return [
      ...nonAddressFields,
      {
        type: FieldType.Customize,
        customerDom: (
          <AddressComponent
            disabled={disabled}
            initialValue={(employeeInfo as unknown as Record<string, string>) ?? {}}
            onCascaderValueChange={setAddressCascaderValue}
            form={form}
            cascaderCol={24}
            colProps={{ span: 12 }}
            inputWidth="100%"
            selfCustomFields={
              addressFields.map(field => {
                const { key, label, ctrlProps, rules } = field;

                return {
                  factorCode: key,
                  factorName: label,
                  isRequired: rules?.length ? 1 : 0,
                  options: ctrlProps?.options,
                };
              }) as FactorsType[]
            }
          />
        ),
      },
      {
        type: FieldType.Customize,
        customerDom: (
          <Col span={24}>
            <Form.Item name="relationshipList">
              <Divider />
              <Relationship initialRelations={employeeInfo?.relationshipList} propForm={form} disabled={disabled} />
            </Form.Item>
          </Col>
        ),
      },
      {
        type: FieldType.Customize,
        customerDom: <Account initialValue={employeeInfo} propForm={form} name="Graphene" mode={mode} />,
      },
    ];
  }, [staticFields, disabled, employeeInfo, form, mode]);

  const { formItems: dynamicFields } = useGenericSchemaFormItemFields({
    staticOrDynamic: 'DYNAMIC',
    category: 'BASE',
    disabled: disabled,
    type: ChannelOrganizationTypeEnum.EMPLOYEE,
    fieldKeyPrefix: 'employeeExtensions',
    entityType: EntityType.ORGANIZATION,
  });

  return (
    <DrawerForm
      className={styles.employeeEditDrawer}
      visible={visible}
      disabled={disabled}
      onClose={() => onClose()}
      maskClosable={false}
      width={752}
      title={drawerTitle}
      cancelText={t('Cancel')}
      sendText={t('Submit')}
      onSubmit={onSave}
      submitBtnProps={{ loading: updating }}
    >
      <Skeleton loading={loading} active>
        <CommonForm form={form} fields={[...combinedFields, ...dynamicFields]} />
      </Skeleton>
    </DrawerForm>
  );
};
