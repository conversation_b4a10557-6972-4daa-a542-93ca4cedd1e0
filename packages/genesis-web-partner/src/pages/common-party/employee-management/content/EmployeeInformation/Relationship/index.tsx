import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { PlusOutlined } from '@ant-design/icons';

import { Button, Divider, Form, Tooltip } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import type { ColumnProps } from 'antd/lib/table';
import moment from 'moment';

import { Table, TableActionsContainer } from '@zhongan/nagrand-ui';

import { AddressComponent } from 'genesis-web-component/lib/components/AddressComponent';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { BizDictItem, RelativeInfo } from 'genesis-web-service';
import { AddOrDeletedOrEnum } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import { DeleteOutline, EditOutline, ViewSquareOutline } from '@/components/Icons';
import { RenderEnums } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { useEditPermission } from '@/pages/common-party/employee-management/hooks/useEmployeeManagement';
import { useFormFields } from '@/pages/common-party/employee-management/hooks/useFormFields';
import { ModeEnum } from '@/types/common';
import { EmployeeFormColumnType } from '@/types/common-party';

import styles from './index.scss';

interface Props {
  propForm: FormInstance;
  initialRelations: RelativeInfo[];
  disabled: boolean;
}

/**
 *
 * @param propForm
 * @param initialRelations
 * @description relationship模块
 */
export const Relationship = ({ propForm, initialRelations, disabled }: Props) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const [drawerMode, setDrawerMode] = useState(ModeEnum.EDIT);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [shownRelations, setShownRelations] = useState<RelativeInfo[]>([]);
  const [deletedRelations, setDeletedRelations] = useState<RelativeInfo[]>([]);
  const [editedIndex, setEditedIndex] = useState<number>();
  const [addressCascaderValue, setAddressCascaderValue] = useState<Record<string, string>>();

  const { canEdit } = useEditPermission();
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  useEffect(() => {
    setShownRelations([...(initialRelations ?? [])]);
  }, [initialRelations]);

  useEffect(() => {
    propForm?.setFieldsValue({
      relationshipList: [...shownRelations, ...deletedRelations],
    });
  }, [propForm, shownRelations, deletedRelations]);

  const fields = useFormFields({
    disabled: drawerMode === ModeEnum.READ,
    formType: EmployeeFormColumnType.RelativeInfo,
    information: shownRelations?.[editedIndex],
    form,
  });
  const combinedFields = useMemo(() => {
    const clonedFields = [...fields];
    clonedFields.push({
      type: FieldType.Customize,
      customerDom: (
        <AddressComponent
          disabled={drawerMode === ModeEnum.READ}
          initialValue={shownRelations?.[editedIndex] ?? {}}
          onCascaderValueChange={setAddressCascaderValue}
          form={form}
          cascaderCol={24}
          colProps={{ span: 12 }}
          inputWidth="100%"
        />
      ),
    });
    return clonedFields;
  }, [fields, form, drawerMode, shownRelations, editedIndex]);

  useEffect(() => {
    if (editedIndex >= 0) {
      const matchedRelation = shownRelations[editedIndex];
      form.setFieldsValue({
        ...matchedRelation,
        birthday: matchedRelation.birthday && moment(matchedRelation.birthday),
      });
    }
  }, [editedIndex, shownRelations]);

  const onViewOrEditDetail = useCallback((index: number, mode: ModeEnum) => {
    setEditedIndex(index);
    setDrawerMode(mode);
    setDrawerVisible(true);
  }, []);

  const onDelete = useCallback(
    (index: number) => {
      const clonedRelations = [...shownRelations];
      const clonedDeletedRelations = [...deletedRelations];

      // 将已保存又删除的数据存下来，并修改isDelete为true，submit时传参需要
      const matchedRelation = clonedRelations[index];
      if (matchedRelation.id) {
        matchedRelation.isDeleted = AddOrDeletedOrEnum.Delete;
        clonedDeletedRelations.push(matchedRelation);
        setDeletedRelations(clonedDeletedRelations);
      }

      clonedRelations.splice(index, 1);
      setShownRelations(clonedRelations);
    },
    [shownRelations, deletedRelations]
  );

  const onCloseDrawer = useCallback(() => {
    setDrawerMode(ModeEnum.EDIT);
    setDrawerVisible(false);
    setEditedIndex(undefined);
    form.resetFields();
  }, []);

  const onSave = useCallback(() => {
    form.validateFields().then(values => {
      const clonedRelations = [...(shownRelations ?? [])];
      if (editedIndex >= 0) {
        // 编辑就替换
        clonedRelations.splice(editedIndex, 1, {
          ...clonedRelations[editedIndex],
          ...values,
          ...addressCascaderValue,
          birthday: values?.birthday && dateFormatInstance.formatDate(values.birthday),
        });
      } else {
        clonedRelations.push({
          ...values,
          ...addressCascaderValue,
          birthday: values?.birthday && dateFormatInstance.formatDate(values.birthday),
        });
      }
      setShownRelations(clonedRelations);
      onCloseDrawer();
    });
  }, [shownRelations, editedIndex, addressCascaderValue, onCloseDrawer]);

  const drawerTitle = useMemo(() => {
    if (editedIndex >= 0) {
      if (drawerMode === ModeEnum.READ) {
        return t('View Relative Information');
      }
      return t('Edit Relative Information');
    }
    return t('Add New Relative');
  }, [editedIndex, drawerMode]);

  const columns: ColumnProps<RelativeInfo>[] = useMemo(
    () => [
      {
        dataIndex: 'relationType',
        title: t('Relationship With Employee'),
        width: '40%',
        render: (relationship: string) => <RenderEnums enums={enums?.relationship} keyName={relationship} />,
      },
      {
        dataIndex: 'name',
        title: t('Name'),
        width: '45%',
      },
      {
        title: t('Actions'),
        key: 'actions',
        width: '15%',
        align: 'right',
        render: (_, record, index) => (
          <TableActionsContainer>
            {!disabled ? (
              <>
                <Tooltip title={t('Delete')}>
                  <Button icon={<DeleteOutline />} type="link" onClick={() => onDelete(index)} />
                </Tooltip>
                <Tooltip title={t('Edit')}>
                  <Button icon={<EditOutline />} type="link" onClick={() => onViewOrEditDetail(index, ModeEnum.EDIT)} />
                </Tooltip>
              </>
            ) : (
              <Tooltip title={t('View')}>
                <Button
                  icon={<ViewSquareOutline />}
                  type="link"
                  onClick={() => onViewOrEditDetail(index, ModeEnum.READ)}
                />
              </Tooltip>
            )}
          </TableActionsContainer>
        ),
      },
    ],
    [enums, disabled, onViewOrEditDetail, onDelete]
  );

  return (
    <section className={styles.relationSection}>
      <p className={styles.pageTitle}>{t('Relationship')}</p>
      <Button icon={<PlusOutlined />} type="primary" disabled={disabled} onClick={() => setDrawerVisible(true)}>
        {t('Add New Relative')}
      </Button>
      {!!shownRelations?.length && (
        <Table
          dataSource={shownRelations}
          columns={columns}
          size="small"
          className={styles.relationList}
          pagination={false}
        />
      )}
      <Divider />
      <DrawerForm
        visible={drawerVisible}
        disabled={!canEdit || drawerMode === ModeEnum.READ}
        onClose={onCloseDrawer}
        maskClosable={false}
        width={752}
        title={drawerTitle}
        cancelText={t('Cancel')}
        sendText={t('Submit')}
        onSubmit={onSave}
      >
        <CommonForm form={form} fields={combinedFields} />
      </DrawerForm>
    </section>
  );
};
