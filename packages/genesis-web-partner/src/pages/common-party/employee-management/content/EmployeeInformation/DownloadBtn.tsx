import { useState } from 'react';
import { Button, message, Form, Radio } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useRequest } from 'ahooks';
import { EmployeeAndRelativeEnum } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';
import { EmployeeMenu } from '@/pages/common-party/employee-management/util/constants';
import { Modal } from '@zhongan/nagrand-ui';

interface UploadAgentProps {
  disabled?: boolean;
  teamId: number;
}

export const DownloadBtn = ({ disabled, teamId }: UploadAgentProps) => {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [menuItem, setMenuItem] = useState(EmployeeAndRelativeEnum.Employee);
  // 下载
  const { loading: downloadLoading, runAsync: downloadList } = useRequest(
    (type: EmployeeAndRelativeEnum) =>
      ChannelService.download<{
        id: number;
        type: EmployeeAndRelativeEnum;
      }>({
        id: teamId,
        type,
      }),
    {
      manual: true,
      onSuccess: res => downloadFile(res),
      onError: (error: Error) =>
        message.error(error.message || t('Download failed')),
    },
  );

  return (
    <>
      <Button
        icon={<DownloadOutlined />}
        loading={downloadLoading}
        disabled={disabled}
        onClick={() => setVisible(true)}
      >
        {t('Download')}
      </Button>
      <Modal
        open={visible}
        title={t('Download')}
        onCancel={() => setVisible(false)}
        cancelButtonProps={{
          style: { display: 'none' },
        }}
        onOk={() => {
          downloadList(menuItem);
          setVisible(false);
        }}
      >
        <Form layout="vertical">
          <Form.Item label="Type">
            <Radio.Group
              value={menuItem}
              onChange={e => setMenuItem(e.target.value)}
            >
              {EmployeeMenu.map(menuConfig => (
                <Radio value={menuConfig.key}>{menuConfig.title}</Radio>
              ))}
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
