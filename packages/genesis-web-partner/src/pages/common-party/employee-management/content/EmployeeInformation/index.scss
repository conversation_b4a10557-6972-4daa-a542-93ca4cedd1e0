@import '@/variables.scss';

.employee-container {
  margin-top: 16px;
  padding: 24px 32px;
  background-color: var(--white);

  .page-title {
    font-size: 16px;
    line-height: 24px;
    font-weight: 700;
    margin-bottom: 11px;
  }

  .actions-container {
    display: flex;
    align-items: center;
    padding: 12px 0;
    margin-bottom: 16px;
    position: relative;
    > div {
      flex: 1;
      text-align: right;
      > button {
        margin-left: 8px;
      }
    }
  }

  .table-actions {
    margin: 0 -10px;

    :global {
      .#{$antd-prefix}-btn.#{$antd-prefix}-btn-link {
        padding: 0 5px;
        margin: 0 5px;
        font-size: 15px;

        &:hover {
          color: var(--primary-color);
        }

        &[disabled] {
          color: var(--text-disabled-color);
        }
      }
    }
  }

  :global {
    .#{$antd-prefix}-btn {
      border-radius: var(--border-radius-base);
    }
    .#{$antd-prefix}-table {
      overflow: unset;
    }
  }
}

.employee-edit-drawer {
  :global {
    .#{$antd-prefix}-btn-link {
      color: var(--text-color);
      width: 20px
    }
  }
}

.roster-menu-item {
  display: flex;
  align-items: center;
  > div {
    flex: 1;
    text-align: right;
  }
}
