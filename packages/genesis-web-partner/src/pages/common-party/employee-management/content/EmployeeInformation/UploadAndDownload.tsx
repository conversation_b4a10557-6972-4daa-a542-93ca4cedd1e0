import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';

import { useRequest } from 'ahooks';
import { Button, Dropdown, Upload, message } from 'antd';
import type { RcFile } from 'antd/lib/upload/interface';

import type { EmployeeAndRelativeEnum } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { EmployeeMenu } from '@/pages/common-party/employee-management/util/constants';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';

interface UploadAgentProps {
  disabled?: boolean;
  teamId: number;
  handleUploadSuccess: () => void;
}

/**
 * 这个组件是通过Dropdown来选择EmployeeMenu，先保留，以后可能交互会换回来
 * @returns
 */
export const UploadAndDownload = ({ disabled, teamId, handleUploadSuccess }: UploadAgentProps) => {
  const { t } = useTranslation();
  const [uploading, setUploading] = useState(false);

  const uploadProps = useCallback(
    (type: EmployeeAndRelativeEnum) =>
      getUploadPropsNew(`/api/channel/v2/file/upload/?id=${teamId}&type=${type}`, handleUploadSuccess, setUploading),
    [teamId, handleUploadSuccess]
  );

  const handleBeforeUpload = useCallback(
    (file: RcFile) => {
      const fileNameLower = file.name.toLowerCase();
      if (!fileNameLower.endsWith('.xls') && !fileNameLower.endsWith('.xlsx')) {
        message.error(t('Only xls and xlsx files can be uploaded.'));
        setUploading(false);
        return false;
      }
      return true;
    },
    [t]
  );

  // 下载
  const { loading: downloadLoading, runAsync: downloadList } = useRequest(
    (type: EmployeeAndRelativeEnum) =>
      ChannelService.download<{
        id: number;
        type: EmployeeAndRelativeEnum;
      }>({
        id: teamId,
        type,
      }),
    {
      manual: true,
      onSuccess: res => downloadFile(res),
      onError: (error: Error) => message.error(error.message || t('Download failed')),
    }
  );

  // 上传dropdown
  const uploadDropdownMenuItems = useMemo(
    () =>
      EmployeeMenu.map(menuItem => ({
        key: menuItem.key,
        label: (
          <Upload {...uploadProps(menuItem.key)} accept=".xls, .xlsx" beforeUpload={handleBeforeUpload}>
            {menuItem.title}
          </Upload>
        ),
      })),
    [uploadProps, handleBeforeUpload]
  );

  // 下载dropdown
  const downloadDropdownMenuItems = useMemo(
    () =>
      EmployeeMenu.map(menuItem => ({
        key: menuItem.key,
        label: menuItem.title,
        onClick: () => downloadList(menuItem.key),
      })),
    [downloadList]
  );

  return (
    <div>
      <Dropdown
        menu={{
          items: uploadDropdownMenuItems,
        }}
        trigger={['click']}
        disabled={disabled}
      >
        <Button icon={<UploadOutlined />} style={{ marginRight: 6 }} loading={uploading}>
          {t('Upload')}
        </Button>
      </Dropdown>
      <Dropdown menu={{ items: downloadDropdownMenuItems }} trigger={['click']}>
        <Button icon={<DownloadOutlined />} loading={downloadLoading}>
          {t('Download')}
        </Button>
      </Dropdown>
    </div>
  );
};
