import { Form, Radio, Input, Col } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import type { ValidateStatus } from 'antd/lib/form/FormItem';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type {
  BizDictItem,
  EmployeeInfo,
  CheckIamAccountParams,
} from 'genesis-web-service';
import {
  ChannelService,
  YesOrNo,
  ChannelCustomerStatus,
  AgentStatusType,
  IAMSourceType,
  MetadataService,
} from 'genesis-web-service';
import styles from './index.scss';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { ModeEnum } from '@/types/common';
import { useRequest } from 'ahooks';
import type { RadioChangeEvent } from 'antd';

interface Props {
  propForm: FormInstance;
  initialValue: EmployeeInfo;
  name: string;
  mode: ModeEnum;
}

type ValidatorCallback = (error?: string) => void;

/**
 *
 * @param propForm
 * @param initialValue
 * @description account模块
 */
export const Account = ({ propForm, initialValue, name, mode }: Props) => {
  const { t } = useTranslation(['partner']);
  const isCreateAccount = Form.useWatch('isCreateAccount', propForm);
  const iamSource = Form.useWatch('iamSource', propForm);
  const firstName = Form.useWatch('firstName', propForm);
  const lastName = Form.useWatch('lastName', propForm);
  const iamUserNameValue = Form.useWatch('iamUserName', propForm);
  const [iamUserNameValidateStatus, setIamUserNameValidateStatus] =
    useState<ValidateStatus>('');
  const [iamUserNameFeedback, setIamUserNameFeedback] = useState(undefined);
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  const { run: checkIamAccount } = useRequest(
    (data: CheckIamAccountParams, _: ValidatorCallback) =>
      ChannelService.checkIamAccount(data),
    {
      debounceWait: 1000,
      manual: true,
      onSuccess: (_, params) => {
        setIamUserNameValidateStatus('success');
        setIamUserNameFeedback(null);
        params?.[1]();
      },
      onError: err => {
        setIamUserNameValidateStatus('error');
        setIamUserNameFeedback(err?.toString()?.replace('Error: ', ''));
      },
    },
  );

  const { data: emailDomains } = useRequest(MetadataService.queryEmailDomains, {
    cacheKey: 'employee-query-domain',
  });
  // 暂时只存在一种,get0即可
  const accountDomain = useMemo(
    () => emailDomains?.domain?.[0] ?? '',
    [emailDomains],
  );

  /**
   * filedDisabled（isCreateAccount iamSource iamUserName 控件是否不可编辑）：read mode或者有初始值的情况
   */
  const filedDisabled = mode === ModeEnum.READ || !!initialValue?.iamUserName;

  const handleAccount = async (
    userFirstName: string,
    userLastName: string,
    currentIamValue: string,
    currentIamSource: IAMSourceType,
  ) => {
    // 先自动回填 iamUserName， 再删除，校验不通过，修改 name 自动回填，让控件重新挂载，不然会保留上一次校验不通过状态
    propForm.resetFields(['iamUserName']);
    setIamUserNameValidateStatus('');
    setIamUserNameFeedback(null);
    if (currentIamSource === IAMSourceType.Native) {
      propForm.setFieldValue(
        'iamUserName',
        (userFirstName || userLastName) &&
          `${userFirstName ? userFirstName + '.' : ''}${userLastName || ''}`,
      );
    } else {
      propForm.setFieldValue('iamUserName', currentIamValue);
    }
  };

  useEffect(() => {
    if (filedDisabled) {
      return;
    }
    handleAccount(firstName, lastName, iamUserNameValue, iamSource);
  }, [firstName, lastName, iamSource]);

  const validateAccount = (
    _: string,
    type: IAMSourceType,
    callback: ValidatorCallback,
  ) => {
    // 除了 add，和 edit 的下没有创建账号，其余情况下不需要校验
    if (!iamUserNameValue || filedDisabled) {
      return callback();
    } else {
      const data: CheckIamAccountParams = {
        iamSource: type,
        iamUserName: iamUserNameValue,
      };
      setIamUserNameValidateStatus('validating');
      checkIamAccount(data, callback);
    }
  };

  const onChangeLoginAccountType = (e: RadioChangeEvent) => {
    propForm.resetFields(['iamUserName']);
    if (
      e.target.value === IAMSourceType.SSO &&
      (mode === ModeEnum.ADD || mode === ModeEnum.EDIT)
    ) {
      propForm.setFieldValue('iamUserName', '');
    } else {
      handleAccount(firstName, lastName, '', iamSource);
    }
  };

  return (
    <Col span={16}>
      {mode !== ModeEnum.ADD && initialValue?.iamUserName && (
        <Form.Item
          initialValue={
            initialValue?.accountStatus || ChannelCustomerStatus.Effective
          }
          name="accountStatus"
          label={<b>{t('{{name}} Account Status', { name })}</b>}
        >
          <Radio.Group
            disabled={
              mode === ModeEnum.READ ||
              initialValue?.status === AgentStatusType.Resigned ||
              iamSource === IAMSourceType.SSO
            }
          >
            <Radio value={ChannelCustomerStatus.Effective}>
              {t('Effective')}
            </Radio>
            <Radio value={ChannelCustomerStatus.Frozen}>{t('Frozen')}</Radio>
          </Radio.Group>
        </Form.Item>
      )}
      <div className={styles.accountSection}>
        {!initialValue?.iamUserName && (
          <Form.Item
            initialValue={initialValue?.isCreateAccount || YesOrNo.NO}
            name="isCreateAccount"
            label={<b>{t('Whether to create {{name}} account?', { name })}</b>}
          >
            <Radio.Group disabled={filedDisabled}>
              {enums?.yesNo?.map(option => (
                <Radio value={option.value} key={option.value}>
                  {option.label}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
        )}
        {(isCreateAccount || initialValue?.isCreateAccount) === YesOrNo.YES && (
          <>
            <Form.Item
              initialValue={initialValue?.iamSource || IAMSourceType.Native}
              name="iamSource"
              label={t('Login Account Type')}
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Radio.Group
                disabled={filedDisabled}
                onChange={onChangeLoginAccountType}
              >
                <Radio value={IAMSourceType.Native}>
                  {t('Native Account')}
                </Radio>
                <Radio value={IAMSourceType.SSO}>{t('SSO Account')}</Radio>
              </Radio.Group>
            </Form.Item>
            {iamSource === IAMSourceType.Native ? (
              <Form.Item
                initialValue={initialValue?.iamUserName}
                name="iamUserName"
                label={t('{{name}} Account', { name })}
                hasFeedback
                help={iamUserNameFeedback}
                validateStatus={iamUserNameValidateStatus}
                validateTrigger={['onChange', 'onSubmit']}
                dependencies={['firstName', 'lastName']}
                rules={[
                  { required: true },
                  {
                    validator: (_, value, callback) =>
                      validateAccount(value, IAMSourceType.Native, callback),
                  },
                ]}
              >
                <Input
                  disabled={filedDisabled}
                  addonAfter={`@${accountDomain}`}
                />
              </Form.Item>
            ) : (
              <Form.Item
                initialValue={initialValue?.iamUserName}
                className={styles.accountInputWrapper}
                name="iamUserName"
                label={t('SSO Account')}
                hasFeedback
                help={iamUserNameFeedback}
                validateStatus={iamUserNameValidateStatus}
                validateTrigger={['onChange', 'onSubmit']}
                rules={[
                  { required: true },
                  {
                    max: 128,
                  },
                  {
                    validator: (_, value, callback) =>
                      validateAccount(value, IAMSourceType.SSO, callback),
                  },
                ]}
              >
                <Input disabled={filedDisabled} />
              </Form.Item>
            )}
          </>
        )}
      </div>
    </Col>
  );
};
