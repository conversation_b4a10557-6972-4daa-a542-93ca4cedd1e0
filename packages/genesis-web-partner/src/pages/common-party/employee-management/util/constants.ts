import i18nInstance from '@/utils/i18n';
import { ChannelTeamType, EmployeeAndRelativeEnum } from 'genesis-web-service';

export const TeamTypeTitleMap = {
  [ChannelTeamType.Headquarter]: i18nInstance.t('Headquarter', {
    ns: 'partner',
  }),
  [ChannelTeamType.Branch]: i18nInstance.t('EmployeeBranch', { ns: 'partner' }),
};

// 删除文案
export const DeletedContentMap = {
  [ChannelTeamType.Headquarter]: i18nInstance.t(
    'Are you sure to delete this headquarter and all relevant branch information?',
    { ns: 'partner' },
  ),
  [ChannelTeamType.Branch]: i18nInstance.t(
    'Are you sure to delete this branch and all relevant information?',
    {
      ns: 'partner',
    },
  ),
};

export const EmployeeManagementPrefix = 'employee-management';

export const RouterMap = {
  [ChannelTeamType.Headquarter]: 'headquarter',
  [ChannelTeamType.Branch]: 'branch',
};

export const EmployeeMenu = [
  {
    key: EmployeeAndRelativeEnum.Employee,
    title: i18nInstance.t('Employee', { ns: 'partner' }),
  },
  {
    key: EmployeeAndRelativeEnum.Relative,
    title: i18nInstance.t('Relative', { ns: 'partner' }),
  },
];
