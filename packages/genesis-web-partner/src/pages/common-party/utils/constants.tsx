import type { TreeDataItem } from '@zhongan/nagrand-ui';

import { InstituteTypeEnum, PartnerType, SubType } from 'genesis-web-service';

import { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { CustomerAddressFieldCode } from '@/utils/constants';
import { t } from '@/utils/i18n';

export const ChannelTitleMap: Partial<Record<ChannelTypeEnum | InstituteTypeEnum, string>> = {
  [ChannelTypeEnum.AGENCY]: t('Agency Company'),
  [ChannelTypeEnum.SALE_CHANNEL]: t('Sales Platform'),
  [ChannelTypeEnum.LeaseChannel]: t('Leasing Channel'),
  [ChannelTypeEnum.SERVICE]: t('Service Company'),
  [ChannelTypeEnum.Bank]: t('Bank'),
  [ChannelTypeEnum.BrokerCompany]: t('Broker Company'),
  [ChannelTypeEnum.TiedAgent]: t('Tied Agent'),
  [InstituteTypeEnum.SERVICE_COMPANY]: t('Service Company'),
  [InstituteTypeEnum.AssessmentCompany]: t('Assessment Company'),
  [InstituteTypeEnum.CLINIC]: t('Clinic'),
  [InstituteTypeEnum.HOSPITAL]: t('Hospital'),
  [InstituteTypeEnum.LegalService]: t('Legal Service Company'),
  [InstituteTypeEnum.Investigator]: t('Investigator'),
  [InstituteTypeEnum.ExternalInsuranceCompany]: t('External Insurance Company'),
};

export const keyMapping: Record<ChannelTypeEnum, PartnerType> = {
  [ChannelTypeEnum.AGENCY]: PartnerType.agencyCompany,
  [ChannelTypeEnum.SALE_CHANNEL]: PartnerType.salesChannel,
  [ChannelTypeEnum.LeaseChannel]: PartnerType.leaseChannel,
  [ChannelTypeEnum.Bank]: PartnerType.bank,
  [ChannelTypeEnum.BrokerCompany]: PartnerType.brokerCompany,
  [ChannelTypeEnum.TiedAgent]: PartnerType.tiedAgent,
  [ChannelTypeEnum.FRONT_LINE_CHANNEL]: PartnerType.frontLineChannel,
  [ChannelTypeEnum.KEY_ACCOUNT_CHANNEL]: PartnerType.keyAccountChannel,
};

export const SalesChannelManagementMenuList: TreeDataItem[] = [
  {
    title: t('Sales Channel'),
    key: ChannelTypeEnum.Default,
    children: [
      {
        title: ChannelTitleMap[ChannelTypeEnum.AGENCY],
        key: ChannelTypeEnum.AGENCY,
      },
      {
        title: ChannelTitleMap[ChannelTypeEnum.SALE_CHANNEL],
        key: ChannelTypeEnum.SALE_CHANNEL,
      },
      {
        title: ChannelTitleMap[ChannelTypeEnum.LeaseChannel],
        key: ChannelTypeEnum.LeaseChannel,
      },
      {
        title: t('Bank'),
        key: ChannelTypeEnum.Bank,
      },
      {
        title: t('Broker Company'),
        key: ChannelTypeEnum.BrokerCompany,
      },
      {
        title: ChannelTitleMap[ChannelTypeEnum.TiedAgent],
        key: ChannelTypeEnum.TiedAgent,
      },
      {
        title: t('Front Line Channel'),
        key: ChannelTypeEnum.FRONT_LINE_CHANNEL,
      },
      {
        title: t('Key Account Channel'),
        key: ChannelTypeEnum.KEY_ACCOUNT_CHANNEL,
      },
    ],
  },
  {
    title: t('Insurance Company'),
    key: ChannelTypeEnum.INSURANCE,
  },
];

export const SubTypeList = [
  {
    label: t('Yes'),
    value: SubType.Self,
    key: SubType.Self,
  },
  {
    label: t('No'),
    value: SubType.Other,
    key: SubType.Other,
  },
];

export const PageSizeOptions = ['10', '20', '30', '40'];

export const CardPageSizeOptions = ['12', '24', '60', '120'];

export const MaxLevel = 4;
export const OnceShownCount = 50; // 每次点击展示多少条数据
export const MaxShownCount = 500; // 最多展示多少条数据

export const CodeColumnTitle: Record<ChannelTypeEnum, string> = {
  [ChannelTypeEnum.AGENCY]: t('Agency Code'),
  [ChannelTypeEnum.SALE_CHANNEL]: t('Sales Channel Code'),
};

export const NameColumnTitle: Record<ChannelTypeEnum, string> = {
  [ChannelTypeEnum.AGENCY]: t('Agency Name'),
  [ChannelTypeEnum.SALE_CHANNEL]: t('Sales Channel Name'),
};

export const ChannelCardList = [ChannelTypeEnum.AGENCY, ChannelTypeEnum.SALE_CHANNEL];

// 由customer config field code转换成channel后端接收的字段
export const ChannelAddressFieldCodeMap = {
  [CustomerAddressFieldCode.Address11]: 'address1',
  [CustomerAddressFieldCode.Address12]: 'address2',
  [CustomerAddressFieldCode.Address13]: 'address3',
  [CustomerAddressFieldCode.Address14]: 'address4',
  [CustomerAddressFieldCode.Address15]: 'address5',
  [CustomerAddressFieldCode.Address16]: 'address6',
  [CustomerAddressFieldCode.Address17]: 'address7',
  [CustomerAddressFieldCode.Address18]: 'address8',
};

export const ChannelZipCodeFieldCode = 'zipCode';

// address label默认配置，customer config没有配置时使用
export const DefaultAddressLabelMap = {
  [CustomerAddressFieldCode.Address11]: t('Address1'),
  [CustomerAddressFieldCode.Address12]: t('Address2'),
  [CustomerAddressFieldCode.Address13]: t('Address3'),
  [CustomerAddressFieldCode.Address14]: t('Address4'),
  [CustomerAddressFieldCode.Address15]: t('Address5'),
};

export const DefaultZipCodeLabel = t('Zip Code', {
  ns: 'partner',
});
export const AcceptFileTypeList = ['.png', '.jpg', '.jpeg', '.pdf', '.doc', '.docx', '.xls', '.xlsx'];

/**
 * @description channel几个地址录入页面使用的字段是address1, address2等，但是AddressComponent组件从schema获取到的是address11,address12
 * 传递给AddressComponent组件转换使用
 */
export const AddressFormConfig = {
  address11: {
    key: 'address1',
    required: false,
  },
  address12: {
    key: 'address2',
    required: false,
  },
  address13: {
    key: 'address3',
    required: false,
  },
  address14: {
    key: 'address4',
    required: false,
  },
  address15: {
    key: 'address5',
    required: false,
  },
  address16: {
    key: 'address6',
    required: false,
  },
  address17: {
    key: 'address7',
    required: false,
  },
  address18: {
    key: 'address8',
    required: false,
  },
  zipCode: {
    required: false,
  },
};

export const OriginType = {
  sales: 'sales',
  agent: 'agent',
};
