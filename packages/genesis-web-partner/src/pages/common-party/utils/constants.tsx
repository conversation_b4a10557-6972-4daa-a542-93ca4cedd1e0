import { InstituteTypeEnum, PartnerType, SubType, YesOrNo } from 'genesis-web-service';

import { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { CustomerAddressFieldCode } from '@/utils/constants';
import i18nInstance from '@/utils/i18n';

export const ChannelTitleMap: Partial<Record<ChannelTypeEnum | InstituteTypeEnum, string>> = {
  [ChannelTypeEnum.AGENCY]: i18nInstance.t('Agency Company', { ns: 'partner' }),
  [ChannelTypeEnum.SALE_CHANNEL]: i18nInstance.t('Sales Platform', {
    ns: 'partner',
  }),
  [ChannelTypeEnum.LeaseChannel]: i18nInstance.t('Leasing Channel', {
    ns: 'partner',
  }),
  [ChannelTypeEnum.SERVICE]: i18nInstance.t('Service Company', {
    ns: 'partner',
  }),
  [ChannelTypeEnum.Bank]: i18nInstance.t('Bank', {
    ns: 'partner',
  }),
  [ChannelTypeEnum.BrokerCompany]: i18nInstance.t('Broker Company', {
    ns: 'partner',
  }),
  [ChannelTypeEnum.TiedAgent]: i18nInstance.t('Tied Agent', {
    ns: 'partner',
  }),
  [InstituteTypeEnum.SERVICE_COMPANY]: i18nInstance.t('Service Company', {
    ns: 'partner',
  }),
  [InstituteTypeEnum.AssessmentCompany]: i18nInstance.t('Assessment Company', {
    ns: 'partner',
  }),
  [InstituteTypeEnum.CLINIC]: i18nInstance.t('Clinic', {
    ns: 'partner',
  }),
  [InstituteTypeEnum.HOSPITAL]: i18nInstance.t('Hospital', {
    ns: 'partner',
  }),
  [InstituteTypeEnum.LegalService]: i18nInstance.t('Legal Service Company', {
    ns: 'partner',
  }),
  [InstituteTypeEnum.Investigator]: i18nInstance.t('Investigator', {
    ns: 'partner',
  }),
  [InstituteTypeEnum.ExternalInsuranceCompany]: i18nInstance.t('External Insurance Company', {
    ns: 'partner',
  }),
};

export const keyMapping: Record<ChannelTypeEnum, PartnerType> = {
  [ChannelTypeEnum.AGENCY]: PartnerType.agencyCompany,
  [ChannelTypeEnum.SALE_CHANNEL]: PartnerType.salesChannel,
  [ChannelTypeEnum.LeaseChannel]: PartnerType.leaseChannel,
  [ChannelTypeEnum.Bank]: PartnerType.bank,
  [ChannelTypeEnum.BrokerCompany]: PartnerType.brokerCompany,
  [ChannelTypeEnum.TiedAgent]: PartnerType.tiedAgent,
  [ChannelTypeEnum.FRONT_LINE_CHANNEL]: PartnerType.frontLineChannel,
  [ChannelTypeEnum.KEY_ACCOUNT_CHANNEL]: PartnerType.keyAccountChannel,
};

export const MenuList = [
  {
    label: i18nInstance.t('Sales Channel', { ns: 'partner' }),
    type: 'group',
    children: [
      {
        label: ChannelTitleMap[ChannelTypeEnum.AGENCY],
        key: ChannelTypeEnum.AGENCY,
      },
      {
        label: ChannelTitleMap[ChannelTypeEnum.SALE_CHANNEL],
        key: ChannelTypeEnum.SALE_CHANNEL,
      },
      {
        label: ChannelTitleMap[ChannelTypeEnum.LeaseChannel],
        key: ChannelTypeEnum.LeaseChannel,
      },
      {
        label: i18nInstance.t('Bank', { ns: 'partner' }),
        key: ChannelTypeEnum.Bank,
      },
      {
        label: i18nInstance.t('Broker Company', { ns: 'partner' }),
        key: ChannelTypeEnum.BrokerCompany,
      },
      {
        label: ChannelTitleMap[ChannelTypeEnum.TiedAgent],
        key: ChannelTypeEnum.TiedAgent,
      },
      {
        label: i18nInstance.t('Front Line Channel', { ns: 'partner' }),
        key: ChannelTypeEnum.FRONT_LINE_CHANNEL,
      },
      {
        label: i18nInstance.t('Key Account Channel', { ns: 'partner' }),
        key: ChannelTypeEnum.KEY_ACCOUNT_CHANNEL,
      },
    ],
  },
];

export const SubTypeList = [
  {
    label: i18nInstance.t('Yes', { ns: 'partner' }),
    value: SubType.Self,
    key: SubType.Self,
  },
  {
    label: i18nInstance.t('No', { ns: 'partner' }),
    value: SubType.Other,
    key: SubType.Other,
  },
];

export const PageSizeOptions = ['10', '20', '30', '40'];

export const CardPageSizeOptions = ['12', '24', '60', '120'];

export const MaxLevel = 4;
export const OnceShownCount = 50; // 每次点击展示多少条数据
export const MaxShownCount = 500; // 最多展示多少条数据

export const CodeColumnTitle: Record<ChannelTypeEnum, string> = {
  [ChannelTypeEnum.AGENCY]: i18nInstance.t('Agency Code', { ns: 'partner' }),
  [ChannelTypeEnum.SALE_CHANNEL]: i18nInstance.t('Sales Channel Code', {
    ns: 'partner',
  }),
};

export const NameColumnTitle: Record<ChannelTypeEnum, string> = {
  [ChannelTypeEnum.AGENCY]: i18nInstance.t('Agency Name', { ns: 'partner' }),
  [ChannelTypeEnum.SALE_CHANNEL]: i18nInstance.t('Sales Channel Name', {
    ns: 'partner',
  }),
};

export const ChannelCardList = [ChannelTypeEnum.AGENCY, ChannelTypeEnum.SALE_CHANNEL];

// 由customer config field code转换成channel后端接收的字段
export const ChannelAddressFieldCodeMap = {
  [CustomerAddressFieldCode.Address11]: 'address1',
  [CustomerAddressFieldCode.Address12]: 'address2',
  [CustomerAddressFieldCode.Address13]: 'address3',
  [CustomerAddressFieldCode.Address14]: 'address4',
  [CustomerAddressFieldCode.Address15]: 'address5',
  [CustomerAddressFieldCode.Address16]: 'address6',
  [CustomerAddressFieldCode.Address17]: 'address7',
  [CustomerAddressFieldCode.Address18]: 'address8',
};

export const ChannelZipCodeFieldCode = 'zipCode';

// address label默认配置，customer config没有配置时使用
export const DefaultAddressLabelMap = {
  [CustomerAddressFieldCode.Address11]: i18nInstance.t('Address1', {
    ns: 'partner',
  }),
  [CustomerAddressFieldCode.Address12]: i18nInstance.t('Address2', {
    ns: 'partner',
  }),
  [CustomerAddressFieldCode.Address13]: i18nInstance.t('Address3', {
    ns: 'partner',
  }),
  [CustomerAddressFieldCode.Address14]: i18nInstance.t('Address4', {
    ns: 'partner',
  }),
  [CustomerAddressFieldCode.Address15]: i18nInstance.t('Address5', {
    ns: 'partner',
  }),
};

export const DefaultZipCodeLabel = i18nInstance.t('Zip Code', {
  ns: 'partner',
});
export const AcceptFileTypeList = ['.png', '.jpg', '.jpeg', '.pdf', '.doc', '.docx', '.xls', '.xlsx'];

/**
 * @description channel几个地址录入页面使用的字段是address1, address2等，但是AddressComponent组件从schema获取到的是address11,address12
 * 传递给AddressComponent组件转换使用
 */
export const AddressFormConfig = {
  address11: {
    key: 'address1',
    required: false,
  },
  address12: {
    key: 'address2',
    required: false,
  },
  address13: {
    key: 'address3',
    required: false,
  },
  address14: {
    key: 'address4',
    required: false,
  },
  address15: {
    key: 'address5',
    required: false,
  },
  address16: {
    key: 'address6',
    required: false,
  },
  address17: {
    key: 'address7',
    required: false,
  },
  address18: {
    key: 'address8',
    required: false,
  },
  zipCode: {
    required: false,
  },
};

export const OriginType = {
  sales: 'sales',
  agent: 'agent',
};
