import type { UploadProps } from 'antd';
import { message } from 'antd';
import type { UploadChangeParam } from 'antd/lib/upload/interface';
import { clone, isEmpty, isString, set } from 'lodash-es';
import moment from 'moment';

import { DownloadOrUploadType } from 'genesis-web-service';
import { security } from 'genesis-web-shared';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { showModal, showModalNew } from '@/pages/common-party/utils/showModal';
import i18nInstance from '@/utils/i18n';
import { formatReg } from '@/utils/utils';

/**
 *
 * @param url upload url
 * @param handleOnSuccess upload success callback
 * @description 目前用于institute detail（experiencedFee）； 获取uploadProps
 */
export const getUploadProps = (url: string, handleOnSuccess: () => void) => ({
  name: 'file',
  accept: '.xlsx',
  headers: { ...security.csrf() },
  showUploadList: false,
  withCredentials: true,
  action: url,
  onChange({ file: { response, status } }: UploadChangeParam) {
    if (status === 'done') {
      const { data, flag } = response;
      if (flag) {
        message.success(i18nInstance.t('Uploaded successfully', { ns: 'partner' }));
        const { total: totalUploaded, success, existData = [], invalidData = [] } = data || {};
        showModal(totalUploaded, success, invalidData, existData);
        handleOnSuccess();
      } else {
        const { total: totalUploaded, success, existData = [], invalidData = [] } = data || {};
        showModal(totalUploaded, success, invalidData, existData);
      }
    } else if (status === 'error') {
      const { message: data, code } = response || {};
      if (code === '1001') {
        message.error(
          i18nInstance.t('Please select correct upload template(xlsx)', {
            ns: 'partner',
          })
        );
      } else if (code === '1003') {
        message.error(i18nInstance.t('Unknown error, Please check your file or retry later', { ns: 'partner' }));
      } else if (code === '1002') {
        const { total: totalUploaded, success, existData = [], invalidData = [] } = data || {};
        showModal(totalUploaded, success, invalidData, existData);
      } else {
        message.error(i18nInstance.t('Uploaded failed', { ns: 'partner' }));
      }
    }
  },
});

/**
 *
 * @param url upload url
 * @param handleOnSuccess upload success callback
 * @param changeUploading change upload status
 * @description 目前用于partner setting institute menu component；获取upload Props（由于新旧upload接口response不一致）
 */
export const getUploadPropsNew = (
  url: string,
  handleOnSuccess: () => void,
  changeUploading?: (status: boolean) => void,
  extraData?: Record<string, string | number>,
  customRequest?: (options: any) => void
): UploadProps => ({
  name: 'file',
  accept: '.xlsx',
  data: extraData,
  headers: { ...security.csrf() },
  showUploadList: false,
  withCredentials: true,
  action: url,
  customRequest,
  onChange({ file: { response, status } }: UploadChangeParam) {
    if (status === 'uploading' && changeUploading) {
      changeUploading(true);
    }
    if (status === 'done') {
      if (changeUploading) {
        changeUploading(false);
      }
      if (response?.success === false) {
        // 目前：后端接口上传失败，但是httpCode是200，故会走到done里，再借助response.success判断是否是真的成功，等后续后端接口规范后，可移除
        message.error(response.msg || i18nInstance.t('Uploaded failed', { ns: 'partner' }));
        return;
      }
      message.success(i18nInstance.t('Uploaded successfully', { ns: 'partner' }));
      const { totalElements: totalUploaded, numberOfSuccess: success, failData = {} } = response || {};
      showModalNew(totalUploaded, success, failData);
      handleOnSuccess();
    } else if (status === 'error') {
      if (changeUploading) {
        changeUploading(false);
      }

      const { message: data, code } = response || {};

      if (code === '1001') {
        message.error(
          i18nInstance.t('Please select correct upload template(xlsx)', {
            ns: 'partner',
          })
        );
      } else if (code === '1003') {
        message.error(i18nInstance.t('Unknown error, Please check your file or retry later', { ns: 'partner' }));
      } else if (code === '1002') {
        const { totalElements: totalUploaded, numberOfSuccess: success, failData = {} } = data || {};
        showModalNew(totalUploaded, success, failData);
      } else {
        message.error(i18nInstance.t('Uploaded failed', { ns: 'partner' }));
      }
    }
  },
});

export const isUrl = (url: string) => {
  return url && /^http(s?):\/\/.*/.test(url);
};

export const getUrlByFileUniqueCode = (api: string, fileUniqueCode: string) => {
  let filePath = '';
  if (fileUniqueCode) {
    filePath = isUrl(fileUniqueCode)
      ? fileUniqueCode
      : `${api}/?type=${DownloadOrUploadType.COMMON_ENTITY_FILE}&fileUniqueCode=${fileUniqueCode}`;
  }

  return filePath;
};

export const replaceSpecialChar = <T extends {}>(object: T, formatKeys: (keyof T)[], replaceValue = ''): T => {
  return formatKeys.reduce<T>((acc, key) => {
    const previous = object[key];

    if (isString(previous)) {
      set(acc, key, object[key].toString().replace(formatReg, replaceValue));
    }

    return acc;
  }, clone(object));
};

export const deepCloneMap = <T>(data: Map<string, T>) => {
  const cloneMap = new Map();
  for (const [key, value] of data.entries()) {
    cloneMap.set(key, { ...value });
  }
  return cloneMap;
};

/**
 *
 * @description day i18n处理
 */
export const formatDay = (day: number) => {
  switch (day) {
    case 1:
      return i18nInstance.t('1st', { ns: 'partner' });
    case 2:
      return i18nInstance.t('2nd', { ns: 'partner' });
    case 3:
      return i18nInstance.t('3rd', { ns: 'partner' });
    default:
      return i18nInstance.t('(n)th', { n: day, ns: 'partner' });
  }
};

/**
 *
 * @param value form fields value
 * @description 判断表单内容是否为空
 */
export const isEmptyForm = <T>(value: T) => {
  if (isEmpty(value)) {
    return true;
  }
  for (const key in value) {
    if (value[key]) {
      return false;
    }
  }
  return true;
};

export const handleDate = (
  startTimeFieldName: string,
  endTimeFieldName: string,
  dateTimeArr?: [moment.MomentInput, moment.MomentInput] | string[],
  format?: string
) => {
  const dateObj: Record<string, string> = {};
  if (dateTimeArr && dateTimeArr?.length > 0) {
    dateObj[startTimeFieldName] = format
      ? moment(dateTimeArr[0]).startOf('day').format(format)
      : (dateFormatInstance.formatTz(
          moment(dateTimeArr[0]).startOf('day'),
          dateFormatInstance.defaultTimeZone
        ) as string);
    dateObj[endTimeFieldName] = format
      ? moment(dateTimeArr[1]).endOf('day').format(format)
      : (dateFormatInstance.formatTz(
          moment(dateTimeArr[1]).endOf('day'),
          dateFormatInstance.defaultTimeZone
        ) as string);
  }
  return dateObj;
};
