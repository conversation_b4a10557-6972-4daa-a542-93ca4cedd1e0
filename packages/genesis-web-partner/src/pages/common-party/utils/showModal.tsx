import { Fragment } from 'react';

import { Modal } from 'antd';

import i18nInstance from '@/utils/i18n';

import styles from '../partner-setting/style.scss';

/**
 *
 * @param totalUploaded 总上传数量
 * @param success 成功数量
 * @param invalidData 有效数据
 * @param existData 已存在的数据
 * @description 目前用于institute detail（experiencedFee）；upload弹窗
 */
export const showModal = (totalUploaded: number, success: number, invalidData: number[], existData: number[]) => {
  Modal.warning({
    className: styles.warningModal,
    title: i18nInstance.t('Result', { ns: 'partner' }),
    content: (
      <Fragment>
        <div>
          {i18nInstance.t('Total upload number', {
            num: totalUploaded,
            ns: 'partner',
          })}
        </div>
        <div>
          {i18nInstance.t('Upload successfully number', {
            num: success,
            ns: 'partner',
          })}
        </div>
        {!!invalidData?.length && (
          <div>
            {i18nInstance.t('Invalid rows', {
              num: invalidData.join(','),
              ns: 'partner',
            })}
          </div>
        )}
        {!!existData?.length && (
          <div>
            {i18nInstance.t('Existed rows', {
              num: existData.join(','),
              ns: 'partner',
            })}
          </div>
        )}
      </Fragment>
    ),
  });
};

/**
 *
 * @param totalUploaded 总共上传数量
 * @param success 成功数量
 * @param failData 失败信息
 * @description 目前用于partner setting institute menu component； 新版partner mgmt upload 弹窗（由于新旧upload接口response不一致）
 */
export const showModalNew = (totalUploaded: number, success: number, failData: Record<string, string>) => {
  const failedInfo: string[] = [];
  Object.entries(failData).map(([key, value]) => {
    failedInfo.push(`${key}: ${value}`);
  });

  Modal.warning({
    className: styles.warningModal,
    title: i18nInstance.t('Result', { ns: 'partner' }),
    okText: i18nInstance.t('OK'),
    content: (
      <Fragment>
        <div>
          {i18nInstance.t('Total upload number', {
            num: totalUploaded,
            ns: 'partner',
          })}
        </div>
        <div>
          {i18nInstance.t('Upload successfully number', {
            num: success,
            ns: 'partner',
          })}
        </div>
        {!!failedInfo.length && (
          <div>
            {i18nInstance.t('Upload failed information', {
              ns: 'partner',
            })}
            {failedInfo.map(item => (
              <div>{item}</div>
            ))}
          </div>
        )}
      </Fragment>
    ),
  });
};
