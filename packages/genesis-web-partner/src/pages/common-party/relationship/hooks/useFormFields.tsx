import { useTranslation } from 'react-i18next';

import type { FieldDataType } from '@zhongan/nagrand-ui';
import { FieldType as NagrandQueryFormFieldType } from '@zhongan/nagrand-ui';

import type { BizDictItem } from 'genesis-web-service';

import { FieldType } from '@/components/CommonForm';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';

import { useQuerySelectOptions } from './request';

export const useQueryFormFields = (): FieldDataType[] => {
  const { t } = useTranslation('partner');
  const { creatorOptions, insuranceOptions, salesChannelOptions, agencyCompanyOptions } = useQuerySelectOptions();

  return [
    {
      key: 'code',
      label: t('Relationship Code'),
      col: 8,
      rules: [
        {
          pattern: /^[0-9A-Za-z]+$/,
          message: t('Please input correct format'),
        },
      ],
      extraProps: {
        allowClear: true,
      },
    },
    {
      type: NagrandQueryFormFieldType.Empty,
    },
    {
      type: NagrandQueryFormFieldType.Empty,
    },
    {
      key: 'insuranceId',
      label: t('Insurance Company'),
      type: NagrandQueryFormFieldType.Select,
      extraProps: {
        options: insuranceOptions,
        allowClear: true,
      },
    },
    {
      key: 'saleChannelId',
      label: t('Sales Channel'),
      type: NagrandQueryFormFieldType.Select,
      extraProps: {
        options: salesChannelOptions,
        allowClear: true,
      },
    },
    {
      key: 'agencyId',
      label: t('Agency Company'),
      type: NagrandQueryFormFieldType.Select,
      extraProps: {
        options: agencyCompanyOptions,
        allowClear: true,
      },
    },
    {
      key: 'creator',
      label: t('Create'),
      type: NagrandQueryFormFieldType.Select,
      extraProps: {
        options: creatorOptions,
        allowClear: true,
      },
    },
    {
      key: 'queryDate',
      label: t('Period of Validity'),
      type: NagrandQueryFormFieldType.DatePicker,
      extraProps: {
        allowClear: true,
      },
    },
  ];
};

interface FieldProps<T> {
  disabled: boolean;
  channelRelationDetail?: T;
  onChange: (value: string | number) => void;
}

export const useDrawerBasicFormFields = <T extends Record<string, string>>({
  disabled,
  channelRelationDetail,
}: FieldProps<T>) => {
  const { t } = useTranslation('partner');

  return [
    {
      key: 'code',
      label: t('Relationship Code'),
      rules: [
        {
          required: true,
          pattern: /^[0-9A-Za-z]+$/,
          message: t('Please input correct format'),
        },
      ],
      ctrlProps: {
        disabled: disabled || channelRelationDetail?.code,
        allowClear: true,
      },
    },
    {
      key: 'name',
      label: t('Relationship Name'),
      rules: [
        {
          required: true,
          message: t('channel.common.required', {
            label: t('Relationship Name'),
          }),
        },
      ],
      ctrlProps: {
        allowClear: true,
        disabled,
      },
    },
  ];
};

export const useReconciliationFormFields = ({ disabled }: { disabled: boolean }) => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  return [
    {
      key: 'code',
      label: t('Setting Code'),
      rules: [
        {
          required: true,
          message: t('channel.common.required', {
            label: t('Setting Code'),
          }),
        },
        {
          pattern: /^[0-9A-Za-z]+$/,
          message: t('Please input correct format'),
        },
      ],
      ctrlProps: {
        disabled,
        allowClear: true,
        maxLength: 20,
      },
    },
    {
      key: 'payMethodList',
      label: t('Pay Method'),
      type: FieldType.Select,
      rules: [
        {
          required: true,
          message: t('channel.common.required', {
            label: t('Pay Method'),
          }),
        },
      ],
      ctrlProps: {
        options: enums?.paymentMethod,
        disabled,
        allowClear: true,
        mode: 'multiple',
        showArrow: true,
      },
    },
    {
      key: 'transactionTypeList',
      label: t('Transaction Type'),
      type: FieldType.Select,
      rules: [
        {
          required: true,
          message: t('channel.common.required', {
            label: t('Transaction Type'),
          }),
        },
      ],
      ctrlProps: {
        options: enums?.transType,
        disabled,
        allowClear: true,
        mode: 'multiple',
        showArrow: true,
      },
    },
    {
      key: 'effectiveDate',
      label: t('Effective Date'),
      type: FieldType.DatePicker,
      rules: [
        {
          required: true,
          message: t('channel.common.required', {
            label: t('Effective Date'),
          }),
        },
      ],
      ctrlProps: {
        disabled,
      },
    },
    {
      key: 'expiryDate',
      label: t('Expiry Date'),
      type: FieldType.DatePicker,
      rules: [
        {
          required: true,
          message: t('channel.common.required', {
            label: t('Expiry Date'),
          }),
        },
      ],
      ctrlProps: {
        disabled,
      },
    },
  ];
};
