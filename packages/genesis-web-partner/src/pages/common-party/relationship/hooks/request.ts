import { useCallback, useEffect, useState } from 'react';

import { message } from 'antd';

import { ChannelService, TenantOrgType } from 'genesis-web-service';
import type { ChannelDetail, ChannelUser, CommonRespWithPagination } from 'genesis-web-service';

import type { LabeledValue } from '@/components/CommonForm';

export const useQuerySelectOptions = () => {
  const [insuranceOptions, setInsuranceOptions] = useState<LabeledValue<number>[]>([]);
  const [salesChannelOptions, setSalesChannelOptions] = useState<LabeledValue<number>[]>([]);
  const [agencyCompanyOptions, setAgencyCompanyOptions] = useState<LabeledValue<number>[]>([]);
  const [creatorOptions, setCreatorOptions] = useState<LabeledValue<number>[]>([]);

  const transferChannelToOptions = useCallback(
    (channelList: ChannelDetail[]) =>
      channelList?.map(channel => ({
        label: channel.name,
        value: channel.id,
      })),
    []
  );

  const queryOptions = useCallback(async () => {
    const promiseList = [
      ChannelService.queryRegisterBy(),
      ChannelService.getChannelList({
        pageIndex: 0,
        pageSize: 9999,
        type: TenantOrgType.INSURANCE,
      }),
      ChannelService.getChannelList({
        pageIndex: 0,
        pageSize: 9999,
        type: TenantOrgType.SALE_CHANNEL,
      }),
      ChannelService.getChannelList({
        pageIndex: 0,
        pageSize: 9999,
        type: TenantOrgType.AGENCY,
      }),
    ];
    const results = await Promise.allSettled(promiseList);
    if (results?.[0].status === 'fulfilled') {
      const creatorResp = results[0].value as ChannelUser[];
      const tempCreatorOptions = creatorResp?.map((creator: ChannelUser) => ({
        label: creator.realUserName,
        value: creator.channelUseNo,
      }));
      setCreatorOptions(tempCreatorOptions);
    } else {
      setCreatorOptions([]);
      message.error(results[0].reason);
    }

    if (results?.[1].status === 'fulfilled') {
      const insuranceResp = results[1].value as CommonRespWithPagination<ChannelDetail>;
      const tempInsuranceOptions = transferChannelToOptions(insuranceResp?.data);
      setInsuranceOptions(tempInsuranceOptions);
    } else {
      setInsuranceOptions([]);
      message.error(results[1].reason);
    }

    if (results?.[2].status === 'fulfilled') {
      const salesResp = results[2].value as CommonRespWithPagination<ChannelDetail>;
      const tempSalesChannelOptions = transferChannelToOptions(salesResp?.data);
      setSalesChannelOptions(tempSalesChannelOptions);
    } else {
      setSalesChannelOptions([]);
      message.error(results[2].reason);
    }

    if (results?.[3].status === 'fulfilled') {
      const agencyResp = results[3].value as CommonRespWithPagination<ChannelDetail>;
      const tempAgencyCompanyOptions = transferChannelToOptions(agencyResp?.data);
      setAgencyCompanyOptions(tempAgencyCompanyOptions);
    } else {
      setAgencyCompanyOptions([]);
      message.error(results[3].reason);
    }
  }, []);

  useEffect(() => {
    queryOptions();
  }, [queryOptions]);

  return {
    insuranceOptions,
    salesChannelOptions,
    agencyCompanyOptions,
    creatorOptions,
  };
};
