import { useTranslation } from 'react-i18next';

import type { ColumnProps } from 'antd/lib/table';
import moment from 'moment';

import type { BizDictItem, ChannelRelationData, ReconSettleConfig } from 'genesis-web-service';
import { EffectiveDateType } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { renderEnumsWithString } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { getFrequencyInfo } from '@/pages/common-party/relationship/utils/util';

export const useRelationshipColumns = (): ColumnProps<ChannelRelationData>[] => {
  const { t } = useTranslation('partner');
  return [
    {
      title: t('Relationship Code'),
      dataIndex: 'code',
      fixed: 'left',
    },
    {
      title: t('Associated'),
      dataIndex: 'associated',
      render: (_, { sourceChannel, targetChannel }) => {
        return `${sourceChannel?.name}-${targetChannel?.name}`;
      },
    },
    {
      title: t('Period of Validity'),
      dataIndex: 'period',
      render: (_, record) => {
        if (record.effectiveDateType === EffectiveDateType.DetermineSection) {
          return `${dateFormatInstance.getDateString(
            record.startDate
          )} - ${dateFormatInstance.getDateString(record.endDate)}`;
        }
        return t('Long Term');
      },
    },
    {
      title: t('Create'),
      dataIndex: 'creator',
    },
    {
      title: t('Create Time'),
      dataIndex: 'gmtCreated',
      render: (text: string) => dateFormatInstance.getDateTimeString(text),
    },
  ];
};

export const useReconciliationColumns = (): ColumnProps<ReconSettleConfig>[] => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  return [
    {
      title: t('Reconciliation Code'),
      dataIndex: 'code',
    },
    {
      title: t('Payment Method'),
      dataIndex: 'payMethodList',
      width: 200,
      render: (_, { payMethodList }) => {
        const payLabelList: string[] = [];
        payMethodList?.forEach(payMethod => {
          const payLabel = renderEnumsWithString(payMethod, enums?.paymentMethod);
          payLabelList.push(payLabel);
        });
        return payLabelList.join(',');
      },
    },
    {
      title: t('Transaction Type'),
      dataIndex: 'transactionTypeList',
      width: 200,
      render: (_, { transactionTypeList }) => {
        const transactionTypeLabelList: string[] = [];
        transactionTypeList?.forEach(transactionType => {
          const transactionTypeLabel = renderEnumsWithString(transactionType, enums?.transType);
          transactionTypeLabelList.push(transactionTypeLabel);
        });
        return transactionTypeLabelList.join(',');
      },
    },
    {
      title: t('Reconciliation Frequency'),
      dataIndex: 'frequency',
      render: (_, { frequency, frequencyValue, frequencyValue2 }) =>
        getFrequencyInfo(frequencyValue, frequency, frequencyValue2, enums?.weekday),
    },
    {
      title: t('Effective Date'),
      dataIndex: 'effectiveDate',
      render: date => date && dateFormatInstance.getDateString(date),
    },
    {
      title: t('Expiry Date'),
      dataIndex: 'expiryDate',
      render: date => date && dateFormatInstance.getDateTimeString(date),
    },
  ];
};
