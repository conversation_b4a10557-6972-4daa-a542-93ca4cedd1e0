@import '@/variables.scss';

.insurance-agency {
  .insurance-agency-title {
    width: 100%;
    height: 100px;
    background-color: var(--border-default);
    padding: 25px;

    p {
      margin-bottom: 8px;
      color: var(--text-color);
    }
    span {
      color: var(--text-color-secondary);
    }
  }

  .insurance-agency-content {
    margin-top: 10px;

    .step-name {
      padding-top: 13px;

      :global {
        .#{$antd-prefix}-steps-item {
          border-bottom: 1px solid var(--border-default);
          margin-right: 0 !important;
          padding-bottom: 10px;
          text-align: left !important;
          &::after {
            display: none !important;
          }
          &::before {
            height: 1px !important;
          }
          .#{$antd-prefix}-steps-item-content {
            pointer-events: none;
          }
          .#{$antd-prefix}-steps-item-container {
            margin-left: 0 !important;
          }
        }

        .#{$antd-prefix}-steps-item-active {
          .#{$antd-prefix}-steps-item-icon {
            background: var(--primary-color);
            border: 1px solid var(--primary-color);

            .#{$antd-prefix}-steps-icon {
              color: var(--white);
            }
          }

          .#{$antd-prefix}-steps-item-title {
            color: var(--text-color) !important;
            font-weight: 500;
          }
        }
      }
    }
    .insurance-agency-step-info {
      .insurance-agency-step-info-title {
        margin: 16px 0;
        font-size: 16px;
        font-weight: bold;
        color: var(--text-color);
      }

      :global {
        .#{$antd-prefix}-radio-wrapper {
          .radio-content {
            display: block;
            margin: 16px 0 0 24px;
          }
        }
      }

      .long-add-btn {
        width: 100%;
        margin-bottom: 8px;
        border-style: dashed;
        color: var(--text-color);
      }

      .settlement-config {
        border: 1px solid var(--primary-color);
        border-radius: var(--border-radius-base);
        padding: 24px 21px;
        margin-bottom: 10px;

        .settlement-config-title {
          margin-bottom: 10px;
          font-size: 14px;
        }

        .settlement-config-btn-group {
          margin-top: 23px;
        }

        .settlement-list-empty {
          height: 160px;
          border: 1px solid var(--border-default);
          text-align: center;
          margin-top: 20px;
        }

        .table-actions {
          color: var(--text-color);
          :global {
            .#{$antd-prefix}-btn.#{$antd-prefix}-btn-link {
              color: var(--text-color);
              width: fit-content;
              font-size: 16px;
              margin-left: 16px;

              &:hover {
                color: var(--primary-color);
              }

              &[disabled] {
                color: var(--text-disabled-color);
              }
              &:first-child {
                margin-left: 0;
              }
            }
          }
        }
      }

      .list-li {
        display: flex;
        align-items: center;
        position: relative;
        padding: 16px;
        border: 1px solid var(--border-default);
        border-radius: var(--border-radius-base);
        margin-bottom: 10px;
        list-style: none;
        height: 82px;
        .li-img {
          display: inline-block;
          width: 48px;
          height: 48px;
          background: $li-active-bg;
          text-align: center;
          line-height: 48px;

          .anticon {
            color: $li-active-color;
          }
        }
        .li-col {
          margin-left: 24px;
          width: 276px;
          height: 48px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          color: var(--text-color-secondary);

          .dark {
            font-weight: bold;
            color: var(--text-color);
          }
        }
        .li-icon {
          position: absolute;
          right: 16px;
          height: 48px;
          line-height: 48px;
          i {
            margin-right: 16px;

            &:last-child {
              margin-right: 0;
            }
          }
          :global {
            .#{$antd-prefix}-btn.#{$antd-prefix}-btn-link {
              color: var(--text-color);
              &:hover {
                color: var(--primary-color);
              }
            }
          }
        }
      }
    }
  }
}

.insurance-agency-reconciliation {
  .insurance-agency-reconciliation-title {
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: bold;
    color: var(--text-color);
  }
}
.picker-day {
  :global {
    .#{$antd-prefix}-calendar-header {
      display: none;
    }
    .#{$antd-prefix}-calendar-footer {
      display: none;
    }
    .#{$antd-prefix}-calendar-body .#{$antd-prefix}-calendar-table thead {
      display: none;
    }
  }
}
