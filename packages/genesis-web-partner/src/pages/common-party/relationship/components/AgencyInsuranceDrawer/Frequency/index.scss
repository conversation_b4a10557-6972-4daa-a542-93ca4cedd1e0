@import '@/variables.scss';

.frequency {
  .frequency-title {
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: bold;
    color: var(--text-color);
  }

  .month-fre {
    display: inline;
  }

  .frequency-result {
    p {
      margin: 10px 0;
    }
    span {
      color: var(--text-color-tertiary);
    }
  }

  .frequency-content {
    display: flex;

    .repeat-every {
      font-size: 11px;
      height: 32px;
    }
    .firstFrequency {
      width: 140px;
      :global {
        .#{$antd-prefix}-select-selector {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }
      }
    }
    .divider {
      width: 18px;
      border-top: 1px solid var(--text-color);
      margin: 16px 8px 0 8px;
    }
    :global {
      .#{$antd-prefix}-form-item {
        margin-bottom: 0;
      }
      .hide-require {
        .#{$antd-prefix}-form-item-required::before {
          content: ' ';
        }
      }
    }
  }
}
.picker-day {
  :global {
    .#{$antd-prefix}-picker-header {
      display: none;
    }
    .#{$antd-prefix}-picker-footer {
      display: none;
    }
    .#{$antd-prefix}-picker-body .#{$antd-prefix}-picker-content thead {
      display: none;
    }
  }
}
