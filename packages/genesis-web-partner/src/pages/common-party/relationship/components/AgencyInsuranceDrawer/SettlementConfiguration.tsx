import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ApartmentOutlined, PlusOutlined } from '@ant-design/icons';

import { Button, Form, Tooltip } from 'antd';
import { cloneDeep } from 'lodash-es';

import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import type { ChannelDataType, ChannelRelationData, ReconSettleConfig } from 'genesis-web-service';
import { SettlementConfig, TenantOrgType } from 'genesis-web-service';

import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import { DeleteOutline, EditOutline, ViewSquareOutline } from '@/components/Icons';
import { useInsuranceContext } from '@/pages/common-party/relationship/hooks/useContext';
import { getRelationSourceType } from '@/pages/common-party/relationship/utils/util';

import { Reconciliation } from './Reconciliation';
import styles from './index.scss';

interface ConfigProps {
  relationWithReconSettleConfigList: ChannelRelationData[];
  channelRelationList: ChannelRelationData[];
  readonly: boolean;
  onChangeRelationWithReconList: (relationList: ChannelRelationData[]) => void;
}

/**
 * @param relationWithReconSettleConfigList 有reconciliation settlement的relation list
 * @param channelRelationList 所有的relation list，用于Relationship of Agency & Channel form item下拉选项
 * @param readonly 是否只读
 * @param onChangeRelationWithReconList change method
 * @description reconciliation & settlement配置；展示relation列表，每个relation里可以配置reconciliation，可编辑 & 新增。在点击drawer上的submit按钮时调用接口保存，该页面上的save都只是暂存，不调用接口
 */
export const SettlementConfiguration = ({
  relationWithReconSettleConfigList,
  channelRelationList,
  readonly,
  onChangeRelationWithReconList,
}: ConfigProps) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();

  const { orgType } = useInsuranceContext();

  const [settlementConfigVisible, setSettlementConfigVisible] = useState(false); // 是否展示settlement配置
  const [editedRelationIndex, setEditedRelationIndex] = useState<number>(); // 当前编辑的relation index；settlement config save时需要依赖该字段更新shownRelationList

  const [reconciliationList, setReconciliationList] = useState<ReconSettleConfig[]>([]); // reconciliation 表格数据
  const [targetChannel, setTargetChannel] = useState<Partial<ChannelDataType>>(); // 当前编辑relation的targetChannel或者Relationship of Agency & Channel当前选中的targetChannel
  const [channelRelationId, setChannelRelationId] = useState<number>(); // 当前编辑relation的channelRelationId或者Relationship of Agency & Channel当前选中的channelRelationId
  const [relationChannelName, setRelationChannelName] = useState<string>(); // settlement config里的Channel form item value；Channel从Relationship of Agency & Channel下拉框选中值获取，不能自己输入

  useEffect(() => {
    const editedRelation = relationWithReconSettleConfigList?.[editedRelationIndex];
    setReconciliationList([...(editedRelation?.reconSettleConfigList ?? [])]);
    setTargetChannel(editedRelation?.targetChannel);
    setChannelRelationId(editedRelation?.channelRelationId);
    setRelationChannelName(editedRelation?.name);
    form.setFieldsValue({
      relationshipCode: editedRelation?.code,
      name: editedRelation?.name,
    });
  }, [relationWithReconSettleConfigList, editedRelationIndex]);

  // 已配置的relation ids
  const shownRelationIds = useMemo(
    () => relationWithReconSettleConfigList?.map(relation => relation.channelRelationId) ?? [],
    [relationWithReconSettleConfigList]
  );

  // 获取还没配置的relation list
  const restRelations = useMemo(
    () => channelRelationList?.filter(relation => !shownRelationIds.includes(relation.channelRelationId)) ?? [],
    [channelRelationList, shownRelationIds]
  );

  // Relationship of Agency & Channel 下拉框选项
  const relationCodeOptions = useMemo(() => {
    if (editedRelationIndex === undefined) {
      // 新增
      return restRelations?.map(curRelation => ({
        label: curRelation.name,
        value: curRelation.code,
      }));
    } else {
      // 编辑
      const editedRelation = relationWithReconSettleConfigList[editedRelationIndex];
      return channelRelationList
        ?.filter(
          relation => !shownRelationIds?.includes(relation.channelRelationId) || editedRelation.channelRelationId
        )
        ?.map(curRelation => ({
          label: curRelation.name,
          value: curRelation.code,
        }));
    }
  }, [relationWithReconSettleConfigList, channelRelationList, restRelations, shownRelationIds, editedRelationIndex]);

  // ------ relation list method start -------

  // 新增/编辑relation
  const onEditRelation = useCallback((index?: number) => {
    setEditedRelationIndex(index);
    setSettlementConfigVisible(true);
  }, []);

  // 删除
  const onDeleteRelation = useCallback(
    (index: number) => {
      const clonedRelationList = [...relationWithReconSettleConfigList];
      clonedRelationList.splice(index, 1);
      onChangeRelationWithReconList(clonedRelationList);
    },
    [relationWithReconSettleConfigList, onChangeRelationWithReconList]
  );

  // 修改relation code
  const onChangeRelationCode = useCallback(
    (code: string) => {
      const matchedRelation = channelRelationList?.find(relation => relation.code === code);
      setRelationChannelName(matchedRelation?.targetChannel?.name);
      setTargetChannel(matchedRelation?.targetChannel);
      setChannelRelationId(matchedRelation?.channelRelationId);
      form.setFieldValue('name', matchedRelation?.targetChannel?.name);
    },
    [channelRelationList, form]
  );

  // ------ relation list method end --------

  // ------ reconciliation & settlement config method start -------

  // 关闭settlement 配置
  const onCloseSettlementConfig = useCallback(() => {
    setSettlementConfigVisible(false);
    setEditedRelationIndex(undefined);
  }, []);

  // 保存settlement 配置；如果是修改，则替换，新增则添加（对relationWithReconSettleConfigList）
  const onSaveSettlementConfig = useCallback(() => {
    const clonedShownRelationList = cloneDeep(relationWithReconSettleConfigList) ?? [];

    const relationshipConfig: Partial<ChannelRelationData> = {
      reconSettleConfigList: reconciliationList,
      name: relationChannelName,
      code: form.getFieldValue('relationshipCode'),
      targetChannel,
    };
    if (editedRelationIndex >= 0) {
      clonedShownRelationList.splice(editedRelationIndex, 1, relationshipConfig as ChannelRelationData);
    } else {
      clonedShownRelationList.push(relationshipConfig as ChannelRelationData);
    }
    onChangeRelationWithReconList(clonedShownRelationList);
    onCloseSettlementConfig();
  }, [
    relationWithReconSettleConfigList,
    reconciliationList,
    relationChannelName,
    targetChannel,
    editedRelationIndex,
    onChangeRelationWithReconList,
  ]);

  // 修改reconciliationList
  const onChangeReconciliationList = useCallback(
    (reconList: ReconSettleConfig[]) => {
      const extendedList = reconList?.map(reconciliation => ({
        ...reconciliation,
        saleChannelCode: targetChannel?.code,
        relationshipCode: form.getFieldValue('relationshipCode'),
        channelRelationId,
        targetChannel,
        name: relationChannelName,
        sourceType: getRelationSourceType(orgType, TenantOrgType.INSURANCE, TenantOrgType.SALE_CHANNEL),
        configType: SettlementConfig.Reconciliation,
      }));
      setReconciliationList(extendedList);
    },
    [targetChannel, channelRelationId, relationChannelName, orgType]
  );

  // ------ reconciliation & settlement config method end -------

  // form fields
  const settlementConfigFields = useMemo(
    () => [
      {
        key: 'relationshipCode',
        label: t('Relationship of Agency & Channel'),
        type: FieldType.Select,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Relationship of Agency & Channel'),
            }),
          },
        ],
        ctrlProps: {
          options: relationCodeOptions,
          disabled: readonly,
          allowClear: true,
          onChange: (value: string) => onChangeRelationCode(value),
        },
      },
      {
        key: 'name',
        label: t('Channel'),
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('channel'),
            }),
          },
        ],
        ctrlProps: {
          disabled: true,
        },
      },
    ],
    [readonly, relationCodeOptions, onChangeRelationCode]
  );

  // 新增/修改settlement config
  const settlementConfig = useMemo(() => {
    return (
      <div className={styles.settlementConfig}>
        <CommonForm fields={settlementConfigFields} form={form} />
        <Reconciliation
          readonly={readonly}
          addBtnDisabled={readonly || !relationChannelName}
          reconciliationList={reconciliationList}
          onChangeReconciliationList={onChangeReconciliationList}
        />
        <div className={styles.settlementConfigBtnGroup} style={{ display: readonly ? 'none' : 'block' }}>
          <Button
            onClick={onSaveSettlementConfig}
            disabled={!reconciliationList.length}
            type="primary"
            style={{ marginRight: 8, display: 'inline-block' }}
          >
            {t('Save')}
          </Button>
          <Button onClick={onCloseSettlementConfig} style={{ display: 'inline-block' }}>
            {t('Cancel')}
          </Button>
        </div>
      </div>
    );
  }, [
    settlementConfigFields,
    readonly,
    reconciliationList,
    relationChannelName,
    onChangeReconciliationList,
    onSaveSettlementConfig,
    onCloseSettlementConfig,
  ]);

  return (
    <div className={styles.insuranceAgencyStepInfo}>
      <div className={styles.insuranceAgencyStepInfoTitle}>{t('Settlement Configuration')}</div>
      <section>
        <Button
          onClick={() => onEditRelation()}
          disabled={readonly || !restRelations?.length || settlementConfigVisible}
          className={styles.longAddBtn}
          icon={<PlusOutlined />}
        >
          {t('Add')}
        </Button>
        {relationWithReconSettleConfigList?.map((relation, index) => (
          <section key={relation.channelRelationId}>
            <li
              className={styles.listLi}
              style={{
                display: index === editedRelationIndex ? 'none' : 'flex',
              }}
            >
              <div className={styles.liImg}>
                <ApartmentOutlined />
              </div>
              <div className={styles.liCol}>
                <span className={styles.dark}>{t('Relationship Name')}</span>
                <span>{relation.name}</span>
              </div>
              <div className={styles.liCol}>
                <span className={styles.dark}>{t('channel')}</span>
                <span>{relation.targetChannel?.name}</span>
              </div>
              <div className={styles.liIcon}>
                {readonly ? (
                  <Tooltip title={t('View')}>
                    <Button type="link" icon={<ViewSquareOutline />} onClick={() => onEditRelation(index)} />
                  </Tooltip>
                ) : (
                  <>
                    <Tooltip title={t('Edit')}>
                      <Button type="link" icon={<EditOutline />} onClick={() => onEditRelation(index)} />
                    </Tooltip>
                    {!relation.reconSettleConfigList?.length && (
                      <DeleteConfirm onOk={() => onDeleteRelation(index)}>
                        <Tooltip title={t('Delete')}>
                          <Button type="link" icon={<DeleteOutline />} style={{ marginLeft: 10 }} />
                        </Tooltip>
                      </DeleteConfirm>
                    )}
                  </>
                )}
              </div>
            </li>
            {index === editedRelationIndex && settlementConfigVisible && settlementConfig}
          </section>
        ))}
        {/* 新增 */}
        {settlementConfigVisible && editedRelationIndex === undefined && settlementConfig}
      </section>
    </div>
  );
};
