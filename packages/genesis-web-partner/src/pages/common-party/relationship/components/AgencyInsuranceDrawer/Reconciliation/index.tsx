import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { PlusOutlined } from '@ant-design/icons';

import { Button, Tooltip } from 'antd';
import type { ColumnProps } from 'antd/lib/table';
import { cloneDeep } from 'lodash-es';

import { Table, TableActionsContainer } from '@zhongan/nagrand-ui';

import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import type { ReconSettleConfig } from 'genesis-web-service';

import { DeleteOutline, EditOutline, ViewSquareOutline } from '@/components/Icons';
import { useReconciliationColumns } from '@/pages/common-party/relationship/hooks/useColumns';
import { useInsuranceContext } from '@/pages/common-party/relationship/hooks/useContext';

import styles from '../index.scss';
import { ReconciliationDrawer } from './ReconciliationDrawer';

interface ConfigProps {
  reconciliationList: ReconSettleConfig[];
  readonly: boolean;
  addBtnDisabled: boolean;
  onChangeReconciliationList: (reconciliationList: ReconSettleConfig[]) => void;
}

/**
 * @param reconciliationList reconciliationList
 * @param addBtnDisabled add button是否禁用
 * @param readonly 是否只读
 * @param onChangeReconciliationList 修改reconciliationList method
 * @description reconciliationList配置
 */
export const Reconciliation = ({
  reconciliationList,
  readonly,
  addBtnDisabled,
  onChangeReconciliationList,
}: ConfigProps) => {
  const { t } = useTranslation('partner');

  const reconciliationColumns = useReconciliationColumns();
  const { orgType } = useInsuranceContext();

  const [reconciliationDrawerVisible, setReconciliationDrawerVisible] = useState(false); // 是否打开reconciliation配置抽屉
  const [editedReconIndex, setEditedReconIndex] = useState<number>(); // 当前编辑的reconciliation index；save时需要依赖该字段更新reconciliationList

  // 新增/编辑reconciliation
  const onEditReconciliation = useCallback((index?: number) => {
    setEditedReconIndex(index);
    setReconciliationDrawerVisible(true);
  }, []);

  // 删除reconciliation
  const onDeleteReconciliation = useCallback(
    (deletedIndex: number) => {
      const clonedReconciliationList = [...reconciliationList];
      clonedReconciliationList.splice(deletedIndex, 1);
      onChangeReconciliationList(clonedReconciliationList);
    },
    [onChangeReconciliationList]
  );

  // 关闭reconciliation drawer
  const onCloseReconciliation = useCallback(() => {
    setReconciliationDrawerVisible(false);
    setEditedReconIndex(undefined);
  }, []);

  // 保存reconciliation
  const onSaveReconciliation = useCallback(
    (reconciliation: ReconSettleConfig) => {
      const clonedReconList = cloneDeep(reconciliationList) ?? [];
      if (editedReconIndex >= 0) {
        clonedReconList.splice(editedReconIndex, 1, reconciliation);
      } else {
        clonedReconList.push(reconciliation);
      }
      onChangeReconciliationList(clonedReconList);
      onCloseReconciliation();
    },
    [reconciliationList, orgType, editedReconIndex, onChangeReconciliationList]
  );

  const combinedReconColumns: ColumnProps<ReconSettleConfig>[] = useMemo(
    () => [
      ...reconciliationColumns,
      {
        title: t('Actions'),
        className: 'actions',
        fixed: 'right',
        align: 'right',
        render: (_, record, index) => (
          <div className={styles.tableActions}>
            <TableActionsContainer>
              {readonly ? (
                <Tooltip title={t('View')}>
                  <Button type="link" icon={<ViewSquareOutline />} onClick={() => onEditReconciliation(index)} />
                </Tooltip>
              ) : (
                <>
                  <Tooltip title={t('Edit')}>
                    <Button type="link" icon={<EditOutline />} onClick={() => onEditReconciliation(index)} />
                  </Tooltip>
                  <DeleteConfirm onOk={() => onDeleteReconciliation(index)}>
                    <Tooltip title={t('Delete')}>
                      <Button type="link" icon={<DeleteOutline />} />
                    </Tooltip>
                  </DeleteConfirm>
                </>
              )}
            </TableActionsContainer>
          </div>
        ),
      },
    ],
    [reconciliationColumns, readonly, onDeleteReconciliation, onEditReconciliation]
  );

  return (
    <section>
      <div className={styles.settlementConfigTitle}>{t('Reconciliation')}</div>
      <Button
        onClick={() => onEditReconciliation()}
        disabled={addBtnDisabled}
        className={styles.longAddBtn}
        icon={<PlusOutlined />}
      >
        {t('Add')}
      </Button>
      <Table
        columns={combinedReconColumns}
        rowKey={record => record.code}
        scroll={{ x: 'max-content' }}
        dataSource={reconciliationList}
      />
      <ReconciliationDrawer
        readonly={readonly}
        visible={reconciliationDrawerVisible}
        editedReconciliation={reconciliationList[editedReconIndex]}
        onClose={onCloseReconciliation}
        onSubmit={onSaveReconciliation}
      />
    </section>
  );
};
