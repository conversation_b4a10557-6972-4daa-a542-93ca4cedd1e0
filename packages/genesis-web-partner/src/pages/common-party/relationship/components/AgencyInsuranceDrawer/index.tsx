import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Divider, Form, Radio, Row, Skeleton, Steps, message } from 'antd';
import moment from 'moment';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import {
  ChannelService,
  EffectiveDateType,
  RelationOperateType,
  RelationType,
  SettlementConfig,
  TenantOrgType,
} from 'genesis-web-service';
import type {
  AgencyChannelQuery,
  ChannelRelationData,
  ReconSettleConfig,
  RelationSourceType,
} from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import { useOrgType } from '@/pages/common-party/hooks/useTenantConfig';
import { SettlementConfiguration } from '@/pages/common-party/relationship/components/AgencyInsuranceDrawer/SettlementConfiguration';
import { PeriodOfValidity } from '@/pages/common-party/relationship/components/PeriodOfValidity';
import { InsuranceContext } from '@/pages/common-party/relationship/hooks/useContext';
import { useDrawerBasicFormFields } from '@/pages/common-party/relationship/hooks/useFormFields';
import { StepsInfo } from '@/pages/common-party/relationship/utils/constants';
import { getRelationSourceType } from '@/pages/common-party/relationship/utils/util';

import styles from './index.scss';

interface Props {
  visible: boolean;
  readonly: boolean;
  channelRelationId: number;
  agencyId: number;
  onClose: (updated: boolean) => void;
}

/**
 *
 * @param visible 是否可见
 * @param readonly 是否只读
 * @param channelRelationId 当前的channelRelationId
 * @param agencyId 当前的agencyId
 * @param onClose 关闭抽屉
 * @description insurance drawer；共两块信息：basic info 和settlement configuration
 */
export const AgencyInsuranceDrawer = ({ visible, readonly, channelRelationId, agencyId, onClose }: Props) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const orgType = useOrgType();

  const [channelRelationDetail, setChannelRelationDetail] = useState<ChannelRelationData>();
  const [effectiveDateType, setEffectiveDateType] = useState(EffectiveDateType.DetermineSection);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [basicInfo, setBasicInfo] = useState<AgencyChannelQuery>();
  const [insuranceId, setInsuranceId] = useState<number>();
  const [agencyName, setAgencyName] = useState<string>();
  const [insuranceName, setInsuranceName] = useState<string>();
  const [channelRelationList, setChannelRelationList] = useState<ChannelRelationData[]>([]);
  const [relationWithReconSettleConfigList, setRelationWithReconSettleConfigList] = useState<ChannelRelationData[]>([]);

  const fields = useDrawerBasicFormFields<ChannelRelationData>({
    disabled: readonly,
    channelRelationDetail,
  });
  const combinedBasicFields = useMemo(() => {
    const clonedFields = [...fields];
    clonedFields.push({
      type: FieldType.Customize,
      customerDom: (
        <PeriodOfValidity
          readonly={readonly}
          effectiveDateType={effectiveDateType}
          onChangeEffectiveDateType={setEffectiveDateType}
        />
      ),
    });

    clonedFields.push({
      type: FieldType.Customize,
      customerDom: (
        <Col span={24}>
          <Divider dashed />
          <div className={styles.insuranceAgencyStepInfoTitle}>{t('Settlement Configuration')}</div>
          <Form.Item
            name="settlementConfig"
            label={t('Configuration')}
            rules={[
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Configuration'),
                }),
              },
            ]}
            initialValue={SettlementConfig.Reconciliation}
            style={{ marginBottom: 0 }}
          >
            <Radio.Group disabled={readonly}>
              <Radio
                style={{
                  display: 'block',
                  margin: '10px 0',
                }}
                value={SettlementConfig.Reconciliation}
              >
                {t('Reconciliation&Settlement')}
              </Radio>
              <Radio value={SettlementConfig.SettlementOnly} disabled>
                {t('Settlement Only')}
              </Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
      ),
    });

    return clonedFields;
  }, [fields, readonly, effectiveDateType]);

  // 获取agencyId对应的relationshipList
  const queryChannelRelationList = useCallback(() => {
    ChannelService.queryChannelRelationList<{ agencyId: number }>({
      condition: {
        operateType: RelationOperateType.QueryRelationPage,
        type: RelationType.RelationAgencyAndSaleChannel,
        agencyId,
      },
      limit: 1000,
    })
      .then(res => {
        const relationList = res.value?.results ?? [];
        const tempRelationWithReconSettleConfigList = relationList.filter(relation => !!relation.reconSettleConfigList);
        setRelationWithReconSettleConfigList(tempRelationWithReconSettleConfigList);
        setChannelRelationList(relationList);
      })
      .catch(() => {
        setRelationWithReconSettleConfigList([]);
        setChannelRelationList([]);
      });
  }, [agencyId]);

  // 获取当前relation详情
  const queryRelationDetail = useCallback(() => {
    setLoading(true);
    ChannelService.queryChannelRelationList<{
      channelRelationId: number;
      reconSettleConfigList: { sourceType: RelationSourceType }[];
    }>({
      condition: {
        operateType: RelationOperateType.QueryRelationDetail,
        reconSettleConfigList: [
          {
            sourceType: getRelationSourceType(orgType, TenantOrgType.AGENCY, TenantOrgType.INSURANCE),
          },
        ],
        channelRelationId,
      },
    })
      .then(res => {
        const relationDetailResp = res?.value?.results?.[0];
        const sourceChannel = relationDetailResp?.sourceChannel;
        const targetChannel = relationDetailResp?.targetChannel;
        if (sourceChannel?.type === TenantOrgType.SERVICE) {
          setAgencyName(sourceChannel?.name);
          setInsuranceId(targetChannel?.channelId);
          setInsuranceName(targetChannel?.name);
        } else {
          setAgencyName(targetChannel?.name);
          setInsuranceId(sourceChannel?.channelId);
          setInsuranceName(sourceChannel?.name);
        }
        setChannelRelationDetail(relationDetailResp);
        setEffectiveDateType(relationDetailResp?.effectiveDateType as EffectiveDateType);
        form.setFieldsValue({
          ...relationDetailResp,
          pickerDate: relationDetailResp.startDate && [
            moment(relationDetailResp.startDate),
            moment(relationDetailResp.endDate),
          ],
        });
      })
      .catch((error: Error) => message.error(error?.message))
      .finally(() => setLoading(false));
  }, [channelRelationId, orgType]);

  const onCloseDrawer = useCallback((updated?: boolean) => {
    setCurrentStep(0);
    setBasicInfo(undefined);
    onClose(updated);
  }, []);

  const onChangeStep = useCallback(
    (step: number) => {
      if (step === 1) {
        form.validateFields().then(values => {
          let startDate;
          let endDate;
          if (values.effectiveDateType === EffectiveDateType.DetermineSection) {
            const pickerDate = values.pickerDate;
            startDate = pickerDate[0]?.format('YYYY-MM-DD 00:00:00');
            endDate = pickerDate[1]?.format('YYYY-MM-DD 00:00:00');
          }
          const tempBasicInfo: AgencyChannelQuery = {
            channelRelationId,
            name: values.name,
            code: values.code,
            effectiveDateType: values.effectiveDateType,
            startDate,
            endDate,
            agencyId,
            insuranceId,
            type: channelRelationDetail?.type as RelationType,
          };
          if (channelRelationDetail?.type === RelationType.RelationInsuranceAndService) {
            tempBasicInfo.saleChannelId = insuranceId;
          }
          setCurrentStep(step);
          setBasicInfo(tempBasicInfo);
        });
      } else {
        setCurrentStep(step);
        setEffectiveDateType(basicInfo?.effectiveDateType);
        form.setFieldsValue({
          code: basicInfo?.code,
          name: basicInfo?.name,
          effectiveDateType: basicInfo?.effectiveDateType,
          pickerDate: basicInfo?.startDate && [moment(basicInfo?.startDate), moment(basicInfo?.endDate)],
        });
      }
    },
    [channelRelationDetail, basicInfo, channelRelationId, agencyId, insuranceId]
  );

  const onSubmit = useCallback(() => {
    setSubmitting(true);
    let reconSettleConfigList: ReconSettleConfig[] = [];
    relationWithReconSettleConfigList?.forEach(relation => {
      const tempReconSettleConfigList = relation.reconSettleConfigList?.map(reconSettle => ({
        ...reconSettle,
        effectiveDate:
          reconSettle.effectiveDate && dateFormatInstance.formatTz(moment(reconSettle.effectiveDate).startOf('day')),
        expiryDate:
          reconSettle.expiryDate && dateFormatInstance.formatTz(moment(reconSettle.expiryDate).startOf('day')),
        targetChannel: {},
        agencyCode: channelRelationList?.[0]?.sourceChannel?.code,
        insuranceCompanyCode: insuranceId,
        gmtCreated: undefined,
        gmtModified: undefined,
      }));
      if (tempReconSettleConfigList?.length) {
        reconSettleConfigList = reconSettleConfigList.concat(tempReconSettleConfigList);
      }
    });
    const relationParams = {
      ...basicInfo,
      startDate: basicInfo.startDate && dateFormatInstance.formatTz(moment(basicInfo.startDate).startOf('day')),
      endDate: basicInfo.endDate && dateFormatInstance.formatTz(moment(basicInfo.endDate).startOf('day')),
      agencyChannelRelationList: [{ reconSettleConfigList }],
    };

    ChannelService.saveChannelRelation([relationParams])
      .then(() => onCloseDrawer(true))
      .catch((error: Error) => message.error(error?.message))
      .finally(() => {
        setSubmitting(false);
      });
  }, [relationWithReconSettleConfigList, channelRelationList, basicInfo, insuranceId, onCloseDrawer]);

  // 获取当前step对应的按钮文案 & 方法
  const { cancelText, sendText, onClickCancelBtn, onClickConfirmBtn } = useMemo(() => {
    if (currentStep === 0) {
      return {
        cancelText: t('Cancel'),
        sendText: t('Next'),
        onClickCancelBtn: () => onCloseDrawer(),
        onClickConfirmBtn: () => onChangeStep(1),
      };
    } else {
      return {
        cancelText: t('Back'),
        sendText: t('Submit'),
        onClickCancelBtn: () => onChangeStep(0),
        onClickConfirmBtn: () => onSubmit(),
      };
    }
  }, [currentStep, onChangeStep, onCloseDrawer, onSubmit]);

  useEffect(() => {
    if (visible && channelRelationId) {
      queryRelationDetail();
      queryChannelRelationList();
    }
  }, [visible, channelRelationId, queryRelationDetail, queryChannelRelationList]);

  return (
    <DrawerForm
      title={t('Relationship of Agency & Channel')}
      visible={visible}
      width={1040}
      onClose={onClickCancelBtn}
      maskClosable={false}
      cancelText={cancelText}
      sendText={sendText}
      onSubmit={onClickConfirmBtn}
      submitBtnShow={!readonly}
      submitBtnProps={{ loading: submitting }}
      className={styles.agencyInsuranceDrawer}
    >
      <Skeleton active loading={loading}>
        <div className={styles.insuranceAgency}>
          <section className={styles.insuranceAgencyTitle}>
            <Row>
              <Col span={8}>
                <p>{t('Insurance Company')}</p>
                <span>{insuranceName}</span>
              </Col>
              <Col span={8}>
                <p>
                  {channelRelationDetail?.sourceChannel?.type === TenantOrgType.SERVICE
                    ? t('Service Company')
                    : t('Agency')}
                </p>
                <span>{agencyName}</span>
              </Col>
            </Row>
          </section>
          <section className={styles.insuranceAgencyContent}>
            <Steps
              current={currentStep}
              size="small"
              className={styles.stepName}
              type="navigation"
              onChange={step => onChangeStep(step)}
            >
              {StepsInfo.map(item => (
                <Steps.Step key={item.key} title={item.title} status={item.status} disabled={item.disabled} />
              ))}
            </Steps>
            {currentStep === 0 && (
              <div className={styles.insuranceAgencyStepInfo}>
                <div className={styles.insuranceAgencyStepInfoTitle}>{t('Relationship Basic Info')}</div>
                <CommonForm form={form} fields={combinedBasicFields} />
              </div>
            )}
            {currentStep === 1 && (
              <InsuranceContext.Provider value={{ orgType }}>
                <SettlementConfiguration
                  channelRelationList={channelRelationList}
                  relationWithReconSettleConfigList={relationWithReconSettleConfigList}
                  readonly={readonly}
                  onChangeRelationWithReconList={setRelationWithReconSettleConfigList}
                />
              </InsuranceContext.Provider>
            )}
          </section>
        </div>
      </Skeleton>
    </DrawerForm>
  );
};
