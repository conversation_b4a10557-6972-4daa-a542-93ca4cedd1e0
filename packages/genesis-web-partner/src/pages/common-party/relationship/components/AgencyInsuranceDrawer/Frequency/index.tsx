import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Input, TimePicker } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import moment from 'moment';

import { DatePicker, Select } from '@zhongan/nagrand-ui';

import type { BizDictItem } from 'genesis-web-service';
import { SettlementFrequency } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { getFrequencyInfo } from '@/pages/common-party/relationship/utils/util';

import styles from './index.scss';

// 月份枚举项，0-31
const monthOptions = Array.from(new Array(31).keys()).map(key => ({
  value: String(key + 1),
  label: String(key + 1),
}));

interface Props {
  form: FormInstance;
  readonly?: boolean;
  frequencyType: SettlementFrequency;
  frequencyIntervalValue: string;
  frequencyRepeatValue: string;
}

/**
 *
 * @param form form实例
 * @param readonly 是否只读
 * @param frequencyIntervalValue 间隔时间；每x/日/周/月
 * @param frequencyType 频率类型；Day/Week/Month
 * @param frequencyRepeatValue 具体重复时间。Day对应的是HH:mm格式的时间；Week对应的是周几；Month对应的是几日
 * @description 处理reconciliation frequency；例：每3周的周一
 */
export const Frequency = ({ form, readonly, frequencyType, frequencyRepeatValue, frequencyIntervalValue }: Props) => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  const [frequencyInfo, setFrequencyInfo] = useState<string>();
  const [curFrequencyType, setCurFrequencyType] = useState<SettlementFrequency>();

  useEffect(() => {
    setFrequencyInfo(getFrequencyInfo(frequencyIntervalValue, frequencyType, frequencyRepeatValue, enums?.weekday));
    setCurFrequencyType(frequencyType);
  }, [frequencyRepeatValue, frequencyIntervalValue, frequencyType, enums]);

  const frequencyTypeOptions = useMemo(() => {
    return enums?.channelSettlementFrequency?.filter(
      item => item.value !== SettlementFrequency.Season && item.value !== SettlementFrequency.Year
    );
  }, [enums]);

  const onChangeRepeatEvery = useCallback(
    (repeatEvery: string) => {
      const { frequency } = form.getFieldsValue() ?? {};
      let { frequencyValue2 } = form.getFieldsValue() ?? {};
      if (frequency === SettlementFrequency.Day) {
        frequencyValue2 = moment(frequencyValue2).format(dateFormatInstance.timeFormatNoSecond || 'HH:mm');
      } else if (frequency === SettlementFrequency.Month) {
        frequencyValue2 = moment(frequencyValue2).format('DD');
      }

      setFrequencyInfo(getFrequencyInfo(repeatEvery, frequency, frequencyValue2, enums?.weekday));
    },
    [enums]
  );

  const onChangeFrequencyType = useCallback(
    (selectedFrequencyType: SettlementFrequency) => {
      setCurFrequencyType(selectedFrequencyType);
      form.resetFields(['frequencyValue2']);
      setFrequencyInfo('');
    },
    [form]
  );

  const onChangeFrequencyValue2 = useCallback((frequencyValue2: string) => {
    const { frequency, frequencyValue } = form.getFieldsValue() ?? {};
    setFrequencyInfo(getFrequencyInfo(frequencyValue, frequency, frequencyValue2, enums?.weekday));
  }, []);

  return (
    <div className={styles.frequency}>
      <div className={styles.frequencyTitle}>{t('Frequency')}</div>
      <Form.Item label={t('Reconciliation Frequency')} required>
        <section className={styles.frequencyContent}>
          <Input.Group compact style={{ width: 240 }}>
            <Input className={styles.repeatEvery} disabled value={t('Repeat Every')} style={{ width: 100 }} />
            <Form.Item
              name="frequencyValue"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <Select
                options={monthOptions}
                placeholder={t('Please select')}
                allowClear
                showSearch
                disabled={readonly}
                onChange={onChangeRepeatEvery}
                className={styles.firstFrequency}
              />
            </Form.Item>
          </Input.Group>
          <span className={styles.divider} />
          <Form.Item
            name="frequency"
            rules={[
              {
                required: true,
                message: t('Please select'),
              },
            ]}
            className={styles.hideRequire}
          >
            <Select
              options={frequencyTypeOptions}
              placeholder={t('Please select')}
              allowClear
              showSearch
              disabled={readonly}
              onChange={onChangeFrequencyType}
              style={{ width: 240 }}
            />
          </Form.Item>
          {curFrequencyType && <span className={styles.divider} />}
          {curFrequencyType === SettlementFrequency.Day && (
            <Form.Item
              name="frequencyValue2"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
              className={styles.hideRequire}
            >
              <TimePicker
                style={{ width: 240 }}
                format={dateFormatInstance.timeFormatNoSecond || 'HH:mm'}
                disabled={readonly}
                placeholder={t('Please select')}
                onChange={time =>
                  onChangeFrequencyValue2(time.format(dateFormatInstance.timeFormatNoSecond || 'HH:mm'))
                }
              />
            </Form.Item>
          )}
          {curFrequencyType === SettlementFrequency.Week && (
            <Form.Item
              name="frequencyValue2"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
              className={styles.hideRequire}
            >
              <Select
                options={enums?.weekday}
                placeholder={t('Please select')}
                allowClear
                showSearch
                disabled={readonly}
                style={{ width: 240 }}
                onChange={onChangeFrequencyValue2}
              />
            </Form.Item>
          )}
          {curFrequencyType === SettlementFrequency.Month && (
            <Form.Item
              name="frequencyValue2"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
              className={styles.hideRequire}
            >
              <DatePicker
                popupClassName={styles.pickerDay}
                style={{ width: 240 }}
                format="DD"
                mode="date"
                disabled={readonly}
                onChange={day => onChangeFrequencyValue2(day.format('DD'))}
              />
            </Form.Item>
          )}
        </section>
      </Form.Item>
      <section className={styles.frequencyResult}>
        <p>{t('This flow will run')}</p>
        <span>{frequencyInfo}</span>
      </section>
    </div>
  );
};
