import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Divider, Form } from 'antd';
import moment from 'moment';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { ReconSettleConfig } from 'genesis-web-service';
import { SettlementFrequency } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import { useReconciliationFormFields } from '@/pages/common-party/relationship/hooks/useFormFields';
import { DefaultDateFormat } from '@/pages/common-party/relationship/utils/constants';

import { Frequency } from '../Frequency';

interface ReconciliationProps {
  readonly: boolean;
  visible: boolean;
  editedReconciliation: ReconSettleConfig;
  onClose: () => void;
  onSubmit: (reconciliation: ReconSettleConfig) => void;
}

/**
 *
 * @param visible 是否可见
 * @param readonly 是否只读
 * @param editedReconciliation 当前编辑的reconciliation
 * @param onClose 关闭抽屉
 * @param onSubmit 提交
 * @description reconciliation drawer
 */
export const ReconciliationDrawer = ({
  visible,
  readonly,
  editedReconciliation,
  onClose,
  onSubmit,
}: ReconciliationProps) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const fields = useReconciliationFormFields({ disabled: readonly }) ?? [];

  const combinedFields = useMemo(
    () => [
      ...fields,
      {
        type: FieldType.Customize,
        col: 24,
        customerDom: (
          <Col span={24}>
            <Divider dashed />
            <Frequency
              form={form}
              readonly={readonly}
              frequencyType={editedReconciliation?.frequency}
              frequencyIntervalValue={editedReconciliation?.frequencyValue}
              frequencyRepeatValue={editedReconciliation?.frequencyValue2}
            />
          </Col>
        ),
      },
    ],
    [fields, form, readonly, editedReconciliation]
  );

  useEffect(() => {
    if (editedReconciliation) {
      let frequencyValue2 = editedReconciliation.frequencyValue2;
      if (editedReconciliation.frequency === SettlementFrequency.Day && frequencyValue2) {
        frequencyValue2 = moment(frequencyValue2, dateFormatInstance.timeFormatNoSecond || 'HH:mm');
      } else if (editedReconciliation.frequency === SettlementFrequency.Month && frequencyValue2) {
        frequencyValue2 = moment(frequencyValue2, 'DD');
      }

      form.setFieldsValue({
        ...editedReconciliation,
        effectiveDate: editedReconciliation.effectiveDate && moment(editedReconciliation.effectiveDate),
        expiryDate: editedReconciliation.expiryDate && moment(editedReconciliation.expiryDate),
        frequencyValue2: frequencyValue2,
      });
    }
  }, [editedReconciliation]);

  const onCloseDrawer = useCallback(() => {
    form.resetFields();
    onClose();
  }, [onClose]);

  const handleSubmit = useCallback(() => {
    form.validateFields().then(values => {
      let frequencyValue2 = values.frequencyValue2;
      if (values.frequency === SettlementFrequency.Day) {
        frequencyValue2 = moment(values.frequencyValue2).format(dateFormatInstance.timeFormatNoSecond || 'HH:mm');
      } else if (values.frequency === SettlementFrequency.Month) {
        frequencyValue2 = moment(values.frequencyValue2).format('DD');
      }
      const submittedData = {
        ...editedReconciliation,
        ...values,
        effectiveDate: moment(values.effectiveDate).format(`${DefaultDateFormat} 00:00:00`),
        expiryDate: moment(values.expiryDate).format(`${DefaultDateFormat} 00:00:00`),
        frequencyValue2,
      };
      onSubmit(submittedData);
      onCloseDrawer();
    });
  }, [editedReconciliation, onSubmit, onCloseDrawer]);

  return (
    <DrawerForm
      title={t('Add Reconciliation')}
      visible={visible}
      width={1040}
      onClose={() => onCloseDrawer()}
      maskClosable={false}
      cancelText={t('Cancel')}
      sendText={t('Submit')}
      onSubmit={handleSubmit}
      submitBtnShow={!readonly}
    >
      <CommonForm form={form} fields={combinedFields} />
    </DrawerForm>
  );
};
