import { useTranslation } from 'react-i18next';

import { Col, Form, Radio } from 'antd';

import { DatePicker } from '@zhongan/nagrand-ui';

import { EffectiveDateType } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

interface Props {
  effectiveDateType: EffectiveDateType;
  readonly: boolean;
  onChangeEffectiveDateType: (effectiveDateType: EffectiveDateType) => void;
}

export const PeriodOfValidity = ({ effectiveDateType, readonly, onChangeEffectiveDateType }: Props) => {
  const { t } = useTranslation('partner');

  return (
    <Col span={24}>
      <Form.Item
        name="effectiveDateType"
        label={t('Period of Validity')}
        rules={[
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Period of Validity'),
            }),
          },
        ]}
        style={{ marginBottom: 0 }}
        initialValue={EffectiveDateType.DetermineSection}
      >
        <Radio.Group disabled={readonly} onChange={event => onChangeEffectiveDateType(event.target.value)}>
          <Radio
            value={EffectiveDateType.DetermineSection}
            style={{
              display: 'block',
              margin: '10px 0',
            }}
          >
            {t('Custom Cycle')}
            {effectiveDateType === EffectiveDateType.DetermineSection && (
              <div
                style={{
                  display: 'block',
                  margin: '16px 0 0 24px',
                }}
              >
                <Form.Item
                  name="pickerDate"
                  rules={[
                    {
                      required: true,
                      message: t('Please select'),
                    },
                  ]}
                >
                  <DatePicker.RangePicker
                    disabled={readonly}
                    placeholder={[t('Start Date'), t('End Date')]}
                    format={`${dateFormatInstance.dateFormat || 'YYYY-MM-DD'} 00:00`}
                  />
                </Form.Item>
              </div>
            )}
          </Radio>
          <Radio value={EffectiveDateType.LongTerm}>{t('Long Term')}</Radio>
        </Radio.Group>
      </Form.Item>
    </Col>
  );
};
