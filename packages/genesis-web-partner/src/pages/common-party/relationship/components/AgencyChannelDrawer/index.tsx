import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Skeleton, message } from 'antd';
import moment from 'moment';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { ChannelService, EffectiveDateType, RelationOperateType, RelationType } from 'genesis-web-service';
import type { AgencyChannelQuery, ChannelRelationData } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import { PeriodOfValidity } from '@/pages/common-party/relationship/components/PeriodOfValidity';
import { useDrawerBasicFormFields } from '@/pages/common-party/relationship/hooks/useFormFields';

import styles from './index.scss';

interface Props {
  visible: boolean;
  readonly: boolean;
  channelRelationId: number;
  onClose: (updated: boolean) => void;
}
/**
 *
 * @param visible 是否可见
 * @param readonly 是否只读
 * @param channelRelationId 当前查看/编辑channelRelationId
 * @param onClose 关闭抽屉
 * @description agency channel 抽屉
 */
export const AgencyChannelDrawer = ({ visible, readonly, channelRelationId, onClose }: Props) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();

  const [channelRelationDetail, setChannelRelationDetail] = useState<ChannelRelationData>();
  const [effectiveDateType, setEffectiveDateType] = useState(EffectiveDateType.DetermineSection);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const fields =
    useDrawerBasicFormFields<ChannelRelationData>({
      disabled: readonly,
      channelRelationDetail: channelRelationDetail,
    }) ?? [];

  const queryRelationDetail = useCallback(() => {
    setLoading(true);
    ChannelService.queryChannelRelationList<{ channelRelationId: number }>({
      condition: {
        operateType: RelationOperateType.QueryRelationDetail,
        type: RelationType.RelationAgencyAndSaleChannel,
        channelRelationId,
      },
    })
      .then(res => {
        const relationDetailResp = res?.value?.results?.[0];
        setChannelRelationDetail(relationDetailResp);
        setEffectiveDateType(relationDetailResp?.effectiveDateType as EffectiveDateType);
        form.setFieldsValue({
          ...relationDetailResp,
          pickerDate: relationDetailResp.startDate && [
            moment(relationDetailResp.startDate),
            moment(relationDetailResp.endDate),
          ],
        });
      })
      .catch((error: Error) => message.error(error?.message))
      .finally(() => setLoading(false));
  }, [channelRelationId]);

  useEffect(() => {
    if (visible) {
      queryRelationDetail();
    }
  }, [visible, queryRelationDetail]);

  const formFields = useMemo(() => {
    const clonedFields = [...fields];
    clonedFields.push({
      type: FieldType.Customize,
      customerDom: (
        <PeriodOfValidity
          readonly={readonly}
          effectiveDateType={effectiveDateType}
          onChangeEffectiveDateType={setEffectiveDateType}
        />
      ),
    });

    return clonedFields;
  }, [fields, readonly, effectiveDateType]);

  const onCloseDrawer = useCallback(
    (updated?: boolean) => {
      form.resetFields();
      onClose(updated);
    },
    [onClose]
  );

  const onSubmit = useCallback(() => {
    form.validateFields().then(values => {
      setSubmitting(true);
      let startDate;
      let endDate;
      if (values.effectiveDateType === EffectiveDateType.DetermineSection) {
        const pickerDate = values.pickerDate;
        startDate = pickerDate?.[0] && dateFormatInstance.formatTz(moment(pickerDate[0]).startOf('day'));
        endDate = pickerDate?.[1] && dateFormatInstance.formatTz(moment(pickerDate[1]).startOf('day'));
      }
      ChannelService.saveChannelRelation<AgencyChannelQuery[]>([
        {
          channelRelationId,
          startDate,
          endDate,
          name: values.name,
          code: values.code,
          effectiveDateType: values.effectiveDateType,
          agencyId: channelRelationDetail?.sourceChannel?.channelId,
          saleChannelId: channelRelationDetail?.targetChannel?.channelId,
          type: RelationType.RelationAgencyAndSaleChannel,
        },
      ])
        .then(() => onCloseDrawer(true))
        .catch((error: Error) => message.error(error?.message))
        .finally(() => setSubmitting(false));
    });
  }, [channelRelationId, channelRelationDetail, onCloseDrawer]);

  return (
    <DrawerForm
      title={t('Relationship of Agency & Channel')}
      visible={visible}
      width={1040}
      onClose={() => onCloseDrawer()}
      maskClosable={false}
      cancelText={t('Cancel')}
      sendText={t('Submit')}
      onSubmit={onSubmit}
      submitBtnShow={!readonly}
      submitBtnProps={{ loading: submitting }}
      className={styles.agencyChannelDrawer}
    >
      <Skeleton active loading={loading}>
        <CommonForm form={form} fields={formFields} />
      </Skeleton>
    </DrawerForm>
  );
};
