import { RelationType, TenantOrgType } from 'genesis-web-service';

import { Step } from '@/types/common';
import i18nInstance from '@/utils/i18n';

export const AgencyInsuranceTypeList = [
  RelationType.RelationInsuranceAndAgency,
  RelationType.RelationInsuranceAndService,
];

export const StepsInfo = [
  {
    key: 0,
    title: i18nInstance.t('Basic Information', { ns: 'partner' }),
    status: Step.Process,
    disabled: false,
  },
  {
    key: 1,
    title: i18nInstance.t('Settlement Configuration', { ns: 'partner' }),
    status: Step.Wait,
    disabled: false,
  },
];

export const TypeStatusMap = {
  [TenantOrgType.SERVICE]: RelationType.RelationInsuranceAndService,
  [TenantOrgType.AGENCY]: RelationType.RelationInsuranceAndAgency,
};

export const DefaultDateFormat = 'YYYY-MM-DD';
