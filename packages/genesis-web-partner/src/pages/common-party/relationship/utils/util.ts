import type { BizDictItem } from 'genesis-web-service';
import { RelationSourceType, SettlementFrequency, TenantOrgType } from 'genesis-web-service';

import i18nInstance from '@/utils/i18n';

enum FrequencyInterval {
  Every = '1',
  EveryOther = '2',
}

/**
 *
 * @param frequencyIntervalValue 频率间隔
 * @param frequencyType 频率类型
 * @param frequencyValue 什么时候重复
 * @param weekdayEnums weekday的枚举项
 * @description 处理配置频率的展示文案
 */
export const getFrequencyInfo = (
  frequencyIntervalValue: string,
  frequencyType: SettlementFrequency,
  frequencyValue: string,
  weekdayEnums: BizDictItem[]
) => {
  let frequencyInfo = '';
  if (frequencyType === SettlementFrequency.Day) {
    if (frequencyIntervalValue === FrequencyInterval.Every) {
      frequencyInfo = i18nInstance.t('Every day {{time}}', {
        ns: 'partner',
        time: frequencyValue,
      });
    } else if (frequencyIntervalValue === FrequencyInterval.EveryOther) {
      frequencyInfo = i18nInstance.t('Every other day {{time}}', {
        ns: 'partner',
        time: frequencyValue,
      });
    } else {
      frequencyInfo = i18nInstance.t('Every {{dayInterval}} day {{time}}', {
        ns: 'partner',
        dayInterval: frequencyIntervalValue,
        time: frequencyValue,
      });
    }
  } else if (frequencyType === SettlementFrequency.Month) {
    if (frequencyIntervalValue === FrequencyInterval.Every) {
      frequencyInfo = i18nInstance.t('On day {{date}} of every month', {
        ns: 'partner',
        date: frequencyValue,
      });
    } else if (frequencyIntervalValue === FrequencyInterval.EveryOther) {
      frequencyInfo = i18nInstance.t('On day {{date}} of every other month', {
        ns: 'partner',
        date: frequencyValue,
      });
    } else {
      frequencyInfo = i18nInstance.t('On day {{date}} of every {{monthInterval}} months', {
        ns: 'partner',
        date: frequencyValue,
        monthInterval: frequencyIntervalValue,
      });
    }
  } else if (frequencyType === SettlementFrequency.Year) {
    if (frequencyIntervalValue === FrequencyInterval.Every) {
      frequencyInfo = i18nInstance.t('On {{date}} of every year', {
        ns: 'partner',
        date: frequencyValue,
      });
    } else if (frequencyIntervalValue === FrequencyInterval.EveryOther) {
      frequencyInfo = i18nInstance.t('On {{date}} of every other year', {
        ns: 'partner',
        date: frequencyValue,
      });
    } else {
      frequencyInfo = i18nInstance.t('On {{date}} of every {{yearInterval}} year', {
        ns: 'partner',
        date: frequencyValue,
        yearInterval: frequencyIntervalValue,
      });
    }
  } else if (frequencyType === SettlementFrequency.Week) {
    const matchedWeekday = weekdayEnums?.find(item => item.value === frequencyValue);
    const weekdayName = matchedWeekday?.label;
    if (frequencyIntervalValue === FrequencyInterval.Every) {
      frequencyInfo = i18nInstance.t('Every week on {{weekday}}', {
        ns: 'partner',
        weekday: weekdayName,
      });
    } else if (frequencyIntervalValue === FrequencyInterval.EveryOther) {
      frequencyInfo = i18nInstance.t('Every other week on {{weekday}}', {
        ns: 'partner',
        weekday: weekdayName,
      });
    } else {
      frequencyInfo = i18nInstance.t('Every {{weekInterval}} weeks on {{weekday}}', {
        ns: 'partner',
        weekday: weekdayName,
        weekInterval: frequencyIntervalValue,
      });
    }
  }
  return frequencyInfo;
};

/**
 *
 * @param orgType
 * @param name1
 * @param name2
 * @description 获取relation所需sourceType
 */
export const getRelationSourceType = (orgType: TenantOrgType, name1: TenantOrgType, name2: TenantOrgType) => {
  if (orgType === TenantOrgType.INSURANCE) {
    if (name1 === TenantOrgType.AGENCY && name2 === TenantOrgType.INSURANCE) {
      return RelationSourceType.InsuranceToAgency;
    }
    if (name1 === TenantOrgType.AGENCY && name2 === TenantOrgType.SALE_CHANNEL) {
      return RelationSourceType.AgencyToSaleChannel;
    }
    return RelationSourceType.InsuranceToAgencyAndSaleChannel;
  }
  if (orgType === TenantOrgType.SALE_CHANNEL) {
    if (name1 === TenantOrgType.AGENCY && name2 === TenantOrgType.INSURANCE) {
      return RelationSourceType.InsuranceToAgency;
    }
    if (name1 === TenantOrgType.AGENCY && name2 === TenantOrgType.SALE_CHANNEL) {
      return RelationSourceType.AgencyToSaleChannel;
    }
    return RelationSourceType.SaleChanelToAgency;
  }
  if (orgType === TenantOrgType.AGENCY) {
    if (name1 === TenantOrgType.AGENCY && name2 === TenantOrgType.INSURANCE) {
      return RelationSourceType.AgencyToInsurance;
    }
    if (name1 === TenantOrgType.AGENCY && name2 === TenantOrgType.SALE_CHANNEL) {
      return RelationSourceType.AgencyToSaleChannel;
    }
    return RelationSourceType.AgencyAndSaleChannelToInsurance;
  }
  return null;
};
