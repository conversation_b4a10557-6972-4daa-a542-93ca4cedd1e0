import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useUpdateEffect } from 'ahooks';
import { Button, Form, Tooltip, message } from 'antd';
import type { ColumnProps } from 'antd/lib/table';
import moment from 'moment';

import { QueryForm, QueryResultContainer, Table, TableActionsContainer } from '@zhongan/nagrand-ui';

import { NoData } from 'genesis-web-component/lib/components/NoData';
import { ChannelService, RelationOperateType, RelationType, TenantOrgType } from 'genesis-web-service';
import type { ChannelRelationData, ChannelRelationFormDTO } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { EditSquareOutline, ViewSquareOutline } from '@/components/Icons';
import { PaginationComponent } from '@/components/Pagination';
import { usePermission } from '@/hooks/usePermissions';
import { AgencyChannelDrawer } from '@/pages/common-party/relationship/components/AgencyChannelDrawer';
import { AgencyInsuranceDrawer } from '@/pages/common-party/relationship/components/AgencyInsuranceDrawer';
import { useRelationshipColumns } from '@/pages/common-party/relationship/hooks/useColumns';
import { useQueryFormFields } from '@/pages/common-party/relationship/hooks/useFormFields';
import { AgencyInsuranceTypeList } from '@/pages/common-party/relationship/utils/constants';
import { Mode } from '@/types/common';
import { DefaultTablePagination } from '@/utils/constants';
import { useDispatch } from '@umijs/max';

import styles from './index.scss';

const Relationship = () => {
  const { t } = useTranslation('partner');
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const canEdit = usePermission('channel.relationship.edit');

  const [relationshipList, setRelationshipList] = useState<ChannelRelationData[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState(DefaultTablePagination);
  const [selectedRelation, setSelectedRelation] = useState<ChannelRelationData>();
  const [mode, setMode] = useState<Mode>();

  const fields = useQueryFormFields();
  const columns = useRelationshipColumns() ?? [];

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: ['paymentMethod', 'transType', 'weekday', 'channelSettlementFrequency'],
    });
  }, []);

  const queryChannelRelation = useCallback(() => {
    const values: ChannelRelationFormDTO = form.getFieldsValue();
    setLoading(true);
    ChannelService.queryChannelRelationList<ChannelRelationFormDTO>({
      condition: {
        operateType: RelationOperateType.QueryRelationPage,
        ...values,
        creator: values?.creator === -10 ? 1 : values?.creator, // TODO: values?.creator = -10时传递1的逻辑原因待确认(时间太久，无法回溯)
        queryDate: values?.queryDate && dateFormatInstance.formatTz(moment(values.queryDate).startOf('day')),
      },
      limit: pagination.pageSize,
      start: (pagination.current - 1) * pagination.pageSize,
    })
      .then(res => {
        setRelationshipList(res.value.results ?? []);
        setTotal(res.value.total);
      })
      .catch((error: Error) => message.error(error?.message))
      .finally(() => setLoading(false));
  }, [pagination]);

  const transferQueryParams = useCallback(params => {
    const isEmpty = Object.values(params).every(param => !param);
    if (isEmpty) {
      message.error(t('Please enter at least one search criteria'));
      return;
    }
    form.validateFields().then(() =>
      setPagination({
        ...DefaultTablePagination,
      })
    );
  }, []);

  const onClose = useCallback(
    (updated: boolean) => {
      if (updated) {
        setPagination({ ...pagination });
      }
      setSelectedRelation(null);
      setMode(null);
    },
    [pagination]
  );

  const onTableActionClick = useCallback((relationship: ChannelRelationData, modeType: Mode) => {
    setSelectedRelation(relationship);
    setMode(modeType);
  }, []);

  const combinedColumns: ColumnProps<ChannelRelationData>[] = useMemo(
    () => [
      ...columns,
      {
        title: t('Actions'),
        align: 'right',
        fixed: 'right',
        render: (_, record) => {
          return (
            <div className="table-actions">
              <TableActionsContainer>
                <Tooltip title={t('View')}>
                  <Button
                    icon={<ViewSquareOutline />}
                    type="link"
                    onClick={() => onTableActionClick(record, Mode.READ)}
                  />
                </Tooltip>
                <Tooltip title={t('Edit')}>
                  <Button
                    icon={<EditSquareOutline />}
                    type="link"
                    onClick={() => onTableActionClick(record, Mode.EDIT)}
                    disabled={!canEdit}
                  />
                </Tooltip>
              </TableActionsContainer>
            </div>
          );
        },
      },
    ],
    [columns, canEdit, onTableActionClick]
  );

  const handlePaginationChange = useCallback((current: number, pageSize: number) => {
    setPagination(old => ({
      ...old,
      current,
      pageSize,
    }));
  }, []);

  useUpdateEffect(() => {
    queryChannelRelation();
  }, [queryChannelRelation]);

  return (
    <div className={styles.relationshipManagement}>
      <QueryForm
        formProps={{ form }}
        queryFields={fields}
        title={t('Relationship Management')}
        onSearch={transferQueryParams}
        loading={loading}
      />
      <QueryResultContainer>
        <Table
          loading={loading}
          columns={combinedColumns}
          dataSource={relationshipList}
          rowKey="channelRelationId"
          scroll={{ x: 'max-content' }}
          pagination={false}
          emptyType="icon"
        />
        <PaginationComponent pagination={pagination} total={total} handlePaginationChange={handlePaginationChange} />
      </QueryResultContainer>
      <AgencyChannelDrawer
        visible={selectedRelation?.type === RelationType.RelationAgencyAndSaleChannel}
        readonly={mode === Mode.READ}
        channelRelationId={selectedRelation?.channelRelationId}
        onClose={onClose}
      />
      <AgencyInsuranceDrawer
        visible={AgencyInsuranceTypeList.includes(selectedRelation?.type as RelationType)}
        readonly={mode === Mode.READ}
        channelRelationId={selectedRelation?.channelRelationId}
        onClose={onClose}
        agencyId={
          selectedRelation?.sourceChannel?.type === TenantOrgType.SERVICE
            ? selectedRelation?.sourceChannel?.channelId
            : selectedRelation?.targetChannel?.channelId
        }
      />
    </div>
  );
};

export default Relationship;
