/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import type { ChannelDataType, ChannelTeamInfo, FormulaProps, TenantConfig } from 'genesis-web-service';
import { CalculatorService, ChannelService } from 'genesis-web-service';

import type { ConnectModel } from '@/types/common';
import type { Effect, Reducer } from '@umijs/max';

export interface PartnerManagementState {
  tenantInfo: ChannelDataType;
  tenantConfigInfo: TenantConfig;
  teamInfo: ChannelTeamInfo;
  teamOrgStructureId: number;
  hasEmployedManager: boolean;
  refreshEmployeeList: boolean;
  formulaList: FormulaProps[];
}

export interface PartnerManagementType extends ConnectModel<PartnerManagementState> {
  effects: {
    getTenantInfo: Effect;
    getTenantConfigInfo: Effect;
    getFormulaList: Effect;
  };
  reducers: {
    saveTenantInfo: Reducer;
    saveTenantConfigInfo: Reducer;
    saveTeamInfo: Reducer;
    saveTeamOrgStructureId: Reducer;
    saveHasEmployedManager: Reducer;
    updateRefreshEmployeeListStatus: Reducer;
    saveFormulaList: Reducer;
  };
}

const PartnerManagementModel: PartnerManagementType = {
  namespace: 'partnerManagement',
  state: {
    tenantInfo: null,
    tenantConfigInfo: null,
    teamInfo: null,
    teamOrgStructureId: null,
    hasEmployedManager: false,
    refreshEmployeeList: false,
    formulaList: [],
  },
  effects: {
    *getTenantInfo(_, { call, put }) {
      const channelResult = yield call(ChannelService.getInsurance);

      yield put({
        type: 'saveTenantInfo',
        payload: channelResult ?? null,
      });
    },
    *getTenantConfigInfo({ payload: tenantCode }, { call, put }) {
      const resp = yield call(ChannelService.queryTenantConfig, tenantCode);

      yield put({
        type: 'saveTenantConfigInfo',
        payload: resp,
      });
    },
    *getFormulaList({ payload: formulaType }, { call, put }) {
      const resp = yield call(CalculatorService.queryFormulaListWhitoutSchema, formulaType);

      yield put({
        type: 'saveFormulaList',
        payload: resp?.value || [],
      });
    },
  },
  reducers: {
    saveTenantInfo(state: PartnerManagementState, { payload }) {
      return {
        ...state,
        tenantInfo: payload,
      };
    },
    saveTenantConfigInfo(state: PartnerManagementState, { payload }) {
      return {
        ...state,
        tenantConfigInfo: payload,
      };
    },
    saveTeamInfo(state: ChannelTeamInfo, { payload }) {
      return {
        ...state,
        teamInfo: payload,
      };
    },
    saveTeamOrgStructureId(state: ChannelTeamInfo, { payload }) {
      return {
        ...state,
        teamOrgStructureId: payload,
      };
    },
    saveHasEmployedManager(state, { payload }) {
      return {
        ...state,
        hasEmployedManager: payload,
      };
    },
    updateRefreshEmployeeListStatus(state, { payload }) {
      return {
        ...state,
        refreshEmployeeList: payload,
      };
    },
    saveFormulaList(state, { payload }) {
      return {
        ...state,
        formulaList: payload,
      };
    },
  },
};

export default PartnerManagementModel;
