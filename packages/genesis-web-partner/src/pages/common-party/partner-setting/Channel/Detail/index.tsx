import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Drawer, Form, Modal, Skeleton, message } from 'antd';

import { useDispatch, useSelector } from '@umijs/max';

import moment from 'moment';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import type {
  ChannelDetail as ChannelDetailResp,
  ChannelFormDTO,
  InstituteDetailV2,
  InstituteTypeEnum,
} from 'genesis-web-service';
import { ChannelService, SalesTypeEnum, TenantOrgType, YesOrNo } from 'genesis-web-service';
import { DMSService } from 'genesis-web-service/lib/data-mgmt-service/data-mgmt-service.service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { ChannelSchemaFieldsContext } from '@/hooks/ChannelSchemaFieldsContext';
import { useChannelInsuranceSchemaFields, useChannelSchemaFields } from '@/hooks/useGenericChannelSchemaFields';
import type { ConnectState } from '@/models/connect';
import { RelationshipList } from '@/pages/common-party/partner-setting-v2/components/RelationshipList';
import { ChannelDetailFooter } from '@/pages/common-party/partner-setting/Channel/Footer/index';
import { AccountInfo } from '@/pages/common-party/partner-setting/components/AccountInfo';
import { Address } from '@/pages/common-party/partner-setting/components/Address';
import { BasicInfo } from '@/pages/common-party/partner-setting/components/BasicInfo';
import { BasicInfoTip } from '@/pages/common-party/partner-setting/components/BasicInfoTip';
import { Staff } from '@/pages/common-party/partner-setting/components/ChannelStaff';
import { ContactPerson } from '@/pages/common-party/partner-setting/components/ContactPerson';
import { SalesAgreement } from '@/pages/common-party/partner-setting/components/SalesAgreement';
import { Structure } from '@/pages/common-party/partner-setting/components/Structure';
import { UploadFile } from '@/pages/common-party/partner-setting/components/UploadFile';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import type { PartnerSettingSearchQuery } from '@/pages/common-party/partner-setting/index';
import { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { ModeEnum } from '@/types/common';

import {
  covertChannelType2SchemaChannelType,
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
} from '../../../../../hooks/useGenericSchemaFormItemFields';
import { useBreadCrumbNav } from '../../hooks/useBreadCrumb';
import commonStyles from '../../style.scss';

const ShowRelationshipTypeList = new Set([TenantOrgType.AGENCY, TenantOrgType.SERVICE, TenantOrgType.SALE_CHANNEL]);
const ShowStaffTypeList = new Set([TenantOrgType.AGENCY]);
const HideSalesAgreementTypeList = new Set([TenantOrgType.SERVICE, TenantOrgType.INSURANCE]);

const ChannelDetail = () => {
  const [searchParams] = useRouterState<PartnerSettingSearchQuery>();
  const { id: channelId, channelType: type, modeType, parentId, level } = searchParams;

  const dispatch = useDispatch();
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const basicInfoRef = useRef(null);
  const addressRef = useRef(null);
  const salesAgreementRef = useRef(null);
  const concatPersonRef = useRef(null);
  const accountInfoRef = useRef(null);
  const documentsRef = useRef(null);

  const { schemaDefFields: channelSchemaDefFields, schemaUsedBizDictMap: channelSchemaUsedBizDictMap } =
    useChannelSchemaFields();
  const {
    schemaDefFields: channelInsuranceSchemaDefFields,
    schemaUsedBizDictMap: channelInsuranceSchemaUsedBizDictMap,
  } = useChannelInsuranceSchemaFields();

  const schemaDefFields = [...channelSchemaDefFields, ...channelInsuranceSchemaDefFields];
  const schemaUsedBizDictMap = {
    ...channelSchemaUsedBizDictMap,
    ...channelInsuranceSchemaUsedBizDictMap,
  };

  const [channelData, setChannelData] = useState<ChannelDetailResp>();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [structureVisible, setStructureVisible] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const { canEdit } = usePartnerSettingPermission();
  const disabled = modeType === ModeEnum.READ || !canEdit;

  const { breadCrumbList = [], setBreadCrumbList } = useBreadCrumbNav({
    id: channelId || parentId,
    type,
  });

  // tenantInfo为列表数据，传入RelationshipList组件
  const { tenantInfo } = useSelector(({ partnerManagement }: ConnectState) => ({
    tenantInfo: partnerManagement.tenantInfo,
  }));

  const getChannelData = useCallback(() => {
    setLoading(true);
    ChannelService.queryChannelDetail(channelId)
      .then(setChannelData)
      .catch((error: Error) => message.error(error.message))
      .finally(() => setLoading(false));
  }, [channelId]);

  useEffect(() => {
    if (channelId) {
      getChannelData();
    }
  }, [channelId]);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'accountSubType',
        'accountType',
        'agencyExclusiveCategory',
        'agencyIOType',
        'agentStatus',
        'certiType',
        'channelCustomerType',
        'channelOrgAccountType',
        'companyAddressType',
        'country',
        'gender',
        'organizationIdType',
        'salesAgreementStatus',
        'settlementRule',
        'yesNo',
      ],
    });
  }, [dispatch]);

  useEffect(() => {
    if (channelData) {
      const basicInfodynamicFields =
        schemaDefFields
          .find(item => item.category === 'BASE' && item.channelType === covertChannelType2SchemaChannelType(type))
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForDisplay(channelData?.extensions, basicInfodynamicFields);
      form.setFieldsValue({
        ...channelData,
        registrationDate: channelData.registrationDate && moment(channelData.registrationDate),
      });
    }
  }, [channelData, form, schemaDefFields, type]);

  const verify = useCallback(
    (formFieldsValue: ChannelFormDTO) => {
      return new Promise((resolve, reject) => {
        ChannelService.verifyV2({
          ...formFieldsValue,
          type,
          channelId,
        })
          .then((existSameChannel: boolean) => {
            if (existSameChannel) {
              Modal.confirm({
                title: t('confirm'),
                className: commonStyles.deleteModal,
                content: (
                  <p>
                    {t(
                      'The same partner already exists. If you click "Save", the new information will overwrite the old information, please confirm.'
                    )}
                  </p>
                ),
                onOk: () => {
                  // 点击弹窗confirm才会继续调用saveChannel接口
                  Modal.destroyAll();
                  resolve(null);
                },
                onCancel: () => {
                  Modal.destroyAll();
                  reject();
                },
              });
            } else {
              resolve(null);
            }
          })
          .catch((error: Error) => {
            message.error(error.message);
            reject();
          });
      });
    },
    [type, channelId, t]
  );

  const saveChannel = useCallback(() => {
    form?.validateFields().then(async values => {
      setSaving(true);
      // [GIS-49795] Service Company第一层和Sales Channel第二层需进行二次校验
      if (
        (type === ChannelTypeEnum.SERVICE && +(level || channelData?.level) === 1) ||
        (type === ChannelTypeEnum.SALE_CHANNEL && +(level || channelData?.level) === 2)
      ) {
        try {
          await verify(values);
        } catch (err) {
          setSaving(false);
          return;
        }
      }
      const logoUrl = basicInfoRef.current || '';
      const addressList = addressRef.current || [];
      const customerList = concatPersonRef.current || [];
      const accountList = accountInfoRef.current || [];
      const documentList = documentsRef.current || [];
      const basicInfodynamicFields =
        schemaDefFields
          .find(item => item.category === 'BASE' && item.channelType === covertChannelType2SchemaChannelType(type))
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForSubmit(values.extensions, basicInfodynamicFields, { dateFormatInstance });
      let submitReq: Promise<ChannelDetailResp>;
      if (channelData) {
        submitReq = ChannelService.updateChannel(channelData.id, {
          ...values,
          type,
          parentId: channelData.parentId,
          level: channelData.level,
          addressList,
          accountList,
          customerList,
          logoUrl,
          documentList,
          teamId: channelData.teamId,
          dataPathCode: channelData.dataPathCode,
          registrationDate:
            values.registrationDate && dateFormatInstance.formatTz(moment(values.registrationDate).startOf('day')),
        });
      } else {
        submitReq = ChannelService.addChannel({
          ...values,
          type,
          parentId,
          level,
          addressList,
          accountList,
          customerList,
          logoUrl,
          documentList,
          registrationDate:
            values.registrationDate && dateFormatInstance.formatTz(moment(values.registrationDate).startOf('day')),
        });
      }
      submitReq
        .then(resp => {
          message.success(t('Save successfully'));
          setChannelData(resp);
          if (resp) {
            const clonedBreadList = [...breadCrumbList];
            if (clonedBreadList.length) {
              // 保存成功，更新最后一条name
              clonedBreadList[clonedBreadList.length - 1].name = resp.name;
            }

            setBreadCrumbList(clonedBreadList);
            if (salesAgreementRef?.current?.saveAgreement) {
              salesAgreementRef.current.saveAgreement(resp.code, resp.id, type);
            }
          }
        })
        .catch((error: Error) => message.error(error.message))
        .finally(() => {
          setSaving(false);
        });
    });
  }, [form, channelData, type, parentId, level, breadCrumbList, verify, t]);

  const [institute, setInstitute] = useState<InstituteDetailV2>();

  const handelExport = () => {
    setExportLoading(true);

    DMSService.bizExport([
      {
        modelCode: 'Channel',
        version: '1',
      },
    ])
      .then(downloadFile)
      .catch((error: Error) => {
        message.error(error?.message);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };

  return (
    <ChannelSchemaFieldsContext.Provider
      value={{
        channelSchemaDefFields: schemaDefFields,
        schemaUsedBizDictMap: schemaUsedBizDictMap,
      }}
    >
      <Skeleton loading={loading} active>
        <div>
          <BasicInfo
            disabled={disabled}
            ref={basicInfoRef}
            form={form}
            channelDetail={channelData}
            fileUniqueCode={channelData?.logoUrl}
            basicInfoType={type}
          />
          {!channelData && (
            <Button type="primary" loading={saving} onClick={() => saveChannel()}>
              {t('Create')}
            </Button>
          )}
        </div>
        {!HideSalesAgreementTypeList.has(type) && (
          <div>
            {!channelData && <BasicInfoTip />}
            <SalesAgreement
              ref={salesAgreementRef}
              channelCode={channelData?.code}
              renew={true}
              channelType={type}
              salesType={SalesTypeEnum.CHANNEL}
              disabled={disabled}
            />
          </div>
        )}

        <Address ref={addressRef} initialList={channelData?.addressList} channelType={type} disabled={disabled} />
        <ContactPerson
          ref={concatPersonRef}
          channelType={type}
          initialList={channelData?.customerList}
          disabled={disabled}
        />

        <AccountInfo
          ref={accountInfoRef}
          initialList={channelData?.accountList}
          channelType={type}
          disabled={disabled}
        />
        <UploadFile savedFileList={channelData?.documentList} ref={documentsRef} disabled={disabled} />
        {ShowStaffTypeList.has(type) && <Staff channelId={channelData?.id} channelType={type} disabled={disabled} />}
        {ShowRelationshipTypeList.has(type) && (
          <RelationshipList
            channelCode={channelData?.code}
            channelName={channelData?.name}
            sourceChannelId={channelData?.id}
            tenantInfo={tenantInfo}
            type={type as unknown as TenantOrgType | InstituteTypeEnum}
            partnerCode={channelData?.code}
            disabled={disabled}
          />
        )}
        <ChannelDetailFooter
          handelExport={handelExport}
          loading={exportLoading}
          handleSave={saveChannel}
          showViewStructure={ShowRelationshipTypeList.has(type) && !!channelData}
          viewStructure={() => setStructureVisible(true)}
          channelType={type}
          disabled={disabled}
        />
        <Drawer
          title={t('Structure Details')}
          open={structureVisible}
          maskClosable={false}
          width={641}
          className={commonStyles.structureDrawer}
          onClose={() => setStructureVisible(false)}
        >
          <Structure channelId={channelData?.id} channelType={type} visible={structureVisible} />
        </Drawer>
      </Skeleton>
    </ChannelSchemaFieldsContext.Provider>
  );
};

export default ChannelDetail;
