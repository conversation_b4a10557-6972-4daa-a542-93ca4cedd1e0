import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

import { Button, Card, Divider, Drawer, Form, Layout, Modal, Skeleton, message } from 'antd';
import moment from 'moment';
import qs from 'qs';

import type { ChannelDetail as ChannelDetailResp, ChannelFormDTO } from 'genesis-web-service';
import { ChannelService, SalesTypeEnum, TenantOrgType, YesOrNo } from 'genesis-web-service';
import { DMSService } from 'genesis-web-service/lib/data-mgmt-service/data-mgmt-service.service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { ChannelSchemaFieldsContext } from '@/hooks/ChannelSchemaFieldsContext';
import { useChannelSchemaFields } from '@/hooks/useChannelSchemaFields';
import type { ConnectState } from '@/models/connect';
import { RelationshipList } from '@/pages/common-party/partner-setting-v2/components/RelationshipList';
import { AccountInfo } from '@/pages/common-party/partner-setting/components/AccountInfo';
import { Address } from '@/pages/common-party/partner-setting/components/Address';
import { BasicInfo } from '@/pages/common-party/partner-setting/components/BasicInfo';
import { BasicInfoTip } from '@/pages/common-party/partner-setting/components/BasicInfoTip';
import { Staff } from '@/pages/common-party/partner-setting/components/ChannelStaff';
import { CommonHeader } from '@/pages/common-party/partner-setting/components/CommonHeader';
import { ContactPerson } from '@/pages/common-party/partner-setting/components/ContactPerson';
import { SalesAgreement } from '@/pages/common-party/partner-setting/components/SalesAgreement';
import { Structure } from '@/pages/common-party/partner-setting/components/Structure';
import { UploadFile } from '@/pages/common-party/partner-setting/components/UploadFile';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { ModeEnum } from '@/types/common';
import type { LocationQueryParam } from '@/types/common-party';
import { useDispatch, useSelector } from '@umijs/max';

import { ImportButton } from '../../components/ImportButton';
import { useBreadCrumbNav } from '../../hooks/useBreadCrumb';
import {
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
} from '../../hooks/useChannelSchemaFieldsByCategory';
import commonStyles from '../../style.scss';
import styles from './style.scss';

const ShowRelationshipTypeList = new Set([TenantOrgType.AGENCY, TenantOrgType.SERVICE, TenantOrgType.SALE_CHANNEL]);
const ShowStaffTypeList = new Set([TenantOrgType.AGENCY]);
const HideSalesAgreementTypeList = new Set([TenantOrgType.SERVICE, TenantOrgType.INSURANCE]);

const ChannelDetail = () => {
  const [searchParams] = useSearchParams();
  const {
    id: channelId,
    type,
    modeType,
    parentId,
    level,
  } = qs.parse(searchParams.toString()) as unknown as LocationQueryParam;
  const dispatch = useDispatch();
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const basicInfoRef = useRef(null);
  const addressRef = useRef(null);
  const salesAgreementRef = useRef(null);
  const concatPersonRef = useRef(null);
  const accountInfoRef = useRef(null);
  const documentsRef = useRef(null);

  const { channelSchemaDefFields, schemaUsedBizDictMap } = useChannelSchemaFields();
  const [channelData, setChannelData] = useState<ChannelDetailResp>();
  const [mode, setMode] = useState<ModeEnum>();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [structureVisible, setStructureVisible] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);

  const { canEdit } = usePartnerSettingPermission();

  const { breadCrumbList = [], setBreadCrumbList } = useBreadCrumbNav({
    id: channelId || parentId,
    type,
  });

  // tenantInfo为列表数据，传入RelationshipList组件
  const { tenantInfo } = useSelector(({ partnerManagement }: ConnectState) => ({
    tenantInfo: partnerManagement.tenantInfo,
  }));

  useEffect(() => {
    dispatch({
      type: 'partnerManagement/getTenantInfo',
    });
  }, []);

  const getChannelData = useCallback(() => {
    setLoading(true);
    ChannelService.queryChannelDetail(channelId)
      .then(setChannelData)
      .catch((error: Error) => message.error(error.message))
      .finally(() => setLoading(false));
  }, [channelId]);

  useEffect(() => {
    if (channelId) {
      getChannelData();
    }
  }, [channelId]);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'accountSubType',
        'accountType',
        'agencyExclusiveCategory',
        'agencyIOType',
        'agentStatus',
        'certiType',
        'channelCustomerType',
        'channelOrgAccountType',
        'companyAddressType',
        'country',
        'gender',
        'organizationIdType',
        'salesAgreementStatus',
        'settlementRule',
        'yesNo',
      ],
    });
  }, [dispatch]);

  useEffect(() => {
    if (modeType) {
      setMode(modeType);
    } else if (channelId) {
      setMode(ModeEnum.READ);
    }
  }, [modeType, channelId]);

  useEffect(() => {
    if (channelData) {
      const basicInfodynamicFields =
        channelSchemaDefFields
          .find(item => item.category === 'BASE')
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForDisplay(channelData?.extensions, basicInfodynamicFields);
      form.setFieldsValue({
        ...channelData,
        registrationDate: channelData.registrationDate && moment(channelData.registrationDate),
      });
    }
  }, [channelData, form, channelSchemaDefFields]);

  const handleCancel = useCallback(() => {
    form.resetFields();
    setMode(ModeEnum.READ);
    setChannelData({
      ...channelData,
      addressList: [...(channelData.addressList ?? [])],
      customerList: [...(channelData.customerList ?? [])],
      accountList: [...(channelData.accountList ?? [])],
      documentList: [...(channelData.documentList ?? [])],
    });
  }, [form, channelData]);

  const verify = useCallback(
    (formFieldsValue: ChannelFormDTO) => {
      return new Promise((resolve, reject) => {
        ChannelService.verifyV2({
          ...formFieldsValue,
          type,
          channelId,
        })
          .then((existSameChannel: boolean) => {
            if (existSameChannel) {
              Modal.confirm({
                title: t('confirm'),
                className: commonStyles.deleteModal,
                content: (
                  <p>
                    {t(
                      'The same partner already exists. If you click "Save", the new information will overwrite the old information, please confirm.'
                    )}
                  </p>
                ),
                onOk: () => {
                  // 点击弹窗confirm才会继续调用saveChannel接口
                  Modal.destroyAll();
                  resolve(null);
                },
                onCancel: () => {
                  Modal.destroyAll();
                  reject();
                },
              });
            } else {
              resolve(null);
            }
          })
          .catch((error: Error) => {
            message.error(error.message);
            reject();
          });
      });
    },
    [type, channelId, t]
  );

  const saveChannel = useCallback(() => {
    form?.validateFields().then(async values => {
      setSaving(true);
      // [GIS-49795] Service Company第一层和Sales Channel第二层需进行二次校验
      if (
        (type === ChannelTypeEnum.SERVICE && +(level || channelData?.level) === 1) ||
        (type === ChannelTypeEnum.SALE_CHANNEL && +(level || channelData?.level) === 2)
      ) {
        try {
          await verify(values);
        } catch (err) {
          setSaving(false);
          return;
        }
      }
      const logoUrl = basicInfoRef.current || '';
      const addressList = addressRef.current || [];
      const customerList = concatPersonRef.current || [];
      const accountList = accountInfoRef.current || [];
      const documentList = documentsRef.current || [];
      const basicInfodynamicFields =
        channelSchemaDefFields
          .find(item => item.category === 'BASE')
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForSubmit(values.extensions, basicInfodynamicFields, { dateFormatInstance });
      let submitReq: Promise<ChannelDetailResp>;
      if (channelData) {
        submitReq = ChannelService.updateChannel(channelData.id, {
          ...values,
          type,
          parentId: channelData.parentId,
          level: channelData.level,
          addressList,
          accountList,
          customerList,
          logoUrl,
          documentList,
          teamId: channelData.teamId,
          dataPathCode: channelData.dataPathCode,
          registrationDate:
            values.registrationDate && dateFormatInstance.formatTz(moment(values.registrationDate).startOf('day')),
        });
      } else {
        submitReq = ChannelService.addChannel({
          ...values,
          type,
          parentId,
          level,
          addressList,
          accountList,
          customerList,
          logoUrl,
          documentList,
          registrationDate:
            values.registrationDate && dateFormatInstance.formatTz(moment(values.registrationDate).startOf('day')),
        });
      }
      submitReq
        .then(resp => {
          message.success(t('Save successfully'));
          setChannelData(resp);
          setMode(ModeEnum.READ);
          if (resp) {
            const clonedBreadList = [...breadCrumbList];
            if (clonedBreadList.length) {
              // 保存成功，更新最后一条name
              clonedBreadList[clonedBreadList.length - 1].name = resp.name;
            }

            setBreadCrumbList(clonedBreadList);
            if (salesAgreementRef?.current?.saveAgreement) {
              salesAgreementRef.current.saveAgreement(resp.code, resp.id, type);
            }
          }
        })
        .catch((error: Error) => message.error(error.message))
        .finally(() => {
          setSaving(false);
        });
    });
  }, [form, channelData, type, parentId, level, breadCrumbList, verify, t]);

  const handelExport = () => {
    setExportLoading(true);

    DMSService.bizExport([
      {
        modelCode: 'Channel',
        version: '1',
      },
    ])
      .then(downloadFile)
      .catch((error: Error) => {
        message.error(error?.message);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };

  return (
    <ChannelSchemaFieldsContext.Provider
      value={{
        channelSchemaDefFields: channelSchemaDefFields,
        schemaUsedBizDictMap: schemaUsedBizDictMap,
      }}
    >
      <Skeleton loading={loading} active>
        <Layout.Content className={styles.channelDetail}>
          <CommonHeader
            loading={saving}
            readonly={mode === ModeEnum.READ}
            showCancel={!!channelData}
            showViewStructure={ShowRelationshipTypeList.has(type) && !!channelData}
            handleEdit={setMode}
            handleCancel={handleCancel}
            handleSave={saveChannel}
            viewStructure={() => setStructureVisible(true)}
            name={channelData?.name}
            breadCrumbList={breadCrumbList}
            extraRightSection={
              canEdit && mode !== ModeEnum.READ && type === ChannelTypeEnum.INSURANCE ? (
                <span style={{ marginRight: 16 }}>
                  <Button onClick={handelExport} loading={exportLoading}>
                    {t('Export')}
                  </Button>
                  <ImportButton
                    buttonProps={{
                      icon: null,
                      style: { marginLeft: 16 },
                    }}
                  />
                </span>
              ) : null
            }
          />
          <div className={styles.section}>
            <Card bordered={false}>
              <div className={commonStyles.basicInfoSection}>
                <BasicInfo
                  ref={basicInfoRef}
                  form={form}
                  readonly={mode === ModeEnum.READ}
                  channelDetail={channelData}
                  fileUniqueCode={channelData?.logoUrl}
                  basicInfoType={type}
                />
                {!channelData && (
                  <Button type="primary" loading={saving} onClick={() => saveChannel()}>
                    {t('Create')}
                  </Button>
                )}
              </div>
              <div className={commonStyles.extraInfoSection}>
                {!channelData && <BasicInfoTip />}
                {!HideSalesAgreementTypeList.has(type) && (
                  <>
                    <SalesAgreement
                      ref={salesAgreementRef}
                      readonly={mode === ModeEnum.READ || !channelData}
                      channelCode={channelData?.code}
                      mode={mode}
                      renew={true}
                      salesType={SalesTypeEnum.CHANNEL}
                    />
                    <Divider dashed />
                  </>
                )}
                <Address
                  ref={addressRef}
                  readonly={mode === ModeEnum.READ || !channelData}
                  initialList={channelData?.addressList}
                  mode={mode}
                />
                <Divider dashed />
                <ContactPerson
                  ref={concatPersonRef}
                  channelType={type}
                  readonly={mode === ModeEnum.READ || !channelData}
                  initialList={channelData?.customerList}
                  mode={mode}
                />
                <Divider dashed />
                <AccountInfo
                  ref={accountInfoRef}
                  readonly={mode === ModeEnum.READ || !channelData}
                  initialList={channelData?.accountList}
                  mode={mode}
                />
                <Divider dashed />
                <UploadFile
                  savedFileList={channelData?.documentList}
                  readonly={mode === ModeEnum.READ || !channelData}
                  ref={documentsRef}
                />
                {ShowStaffTypeList.has(type) && (
                  <>
                    <Divider dashed />
                    <Staff
                      readonly={mode === ModeEnum.READ || !channelData}
                      channelId={channelData?.id}
                      channelType={type}
                      mode={mode}
                    />
                  </>
                )}
                {ShowRelationshipTypeList.has(type) && (
                  <RelationshipList
                    channelCode={channelData?.code}
                    channelName={channelData?.name}
                    sourceChannelId={channelData?.id}
                    tenantInfo={tenantInfo}
                    type={type}
                    readonly={mode === ModeEnum.READ || !channelData}
                    level={+(level || channelData?.level)}
                    mode={mode}
                    partnerCode={channelData?.code}
                  />
                )}
              </div>
              <Drawer
                title={t('Structure Details')}
                open={structureVisible}
                maskClosable={false}
                width={641}
                className={commonStyles.structureDrawer}
                onClose={() => setStructureVisible(false)}
              >
                <Structure channelId={channelData?.id} channelType={type} visible={structureVisible} />
              </Drawer>
            </Card>
          </div>
        </Layout.Content>
      </Skeleton>
    </ChannelSchemaFieldsContext.Provider>
  );
};

export default ChannelDetail;
