import { useTranslation } from 'react-i18next';

import { Button } from 'antd';

import { ImportButton } from '@/pages/common-party/partner-setting/components/ImportButton';

import { ChannelTypeEnum } from '../../models/index.interface';

type ChannelDetailFooterProps = {
  showViewStructure?: boolean;
  handleSave: () => void;
  handelExport?: () => void;
  viewStructure?: () => void;
  loading?: boolean;
  channelType: ChannelTypeEnum;
  disabled: boolean;
};
export const ChannelDetailFooter = ({
  showViewStructure,
  handleSave,
  viewStructure,
  loading,
  handelExport,
  channelType,
  disabled,
}: ChannelDetailFooterProps) => {
  const { t } = useTranslation('partner');

  return (
    <span className="border-solid border-x-0 border-b-0 border-[var(--border-line-color3)] h-[72px] border-t-[1px] flex items-center bg-white absolute bottom-0 w-full z-10 gap-4 justify-end pr-4">
      {showViewStructure && (
        <Button onClick={viewStructure} size="large">
          {t('View Structure')}
        </Button>
      )}
      {!disabled ? (
        <>
          {channelType === ChannelTypeEnum.INSURANCE ? (
            <>
              <Button onClick={handelExport} loading={loading} size="large">
                {t('Export')}
              </Button>
              <ImportButton
                buttonProps={{
                  icon: null,
                  style: {
                    marginLeft: 0,
                  },
                  size: 'large',
                }}
              />
            </>
          ) : null}
          <Button onClick={handleSave} type="primary" size="large">
            {t('Save')}
          </Button>
        </>
      ) : null}
    </span>
  );
};
