import styles from './style.scss';
import commonStyles from '../style.scss';
import { BasicInfo } from '@/pages/common-party/partner-setting/components/BasicInfo';
import { Address } from '@/pages/common-party/partner-setting/components/Address';
import { <PERSON><PERSON>erson } from '@/pages/common-party/partner-setting/components/ContactPerson';
import { AccountInfo } from '@/pages/common-party/partner-setting/components/AccountInfo';
import { CommonHeader } from '@/pages/common-party/partner-setting/components/CommonHeader';
import { Staff } from '@/pages/common-party/partner-setting/components/ChannelStaff';
import { Structure } from '@/pages/common-party/partner-setting/components/Structure';
import { SalesAgreement } from '@/pages/common-party/partner-setting/components/SalesAgreement';
import { ModeEnum } from '@/types/common';
import {
  Button,
  Card,
  Divider,
  Drawer,
  Form,
  Layout,
  message,
  Skeleton,
} from 'antd';
import type { ChannelDetail } from 'genesis-web-service';
import { ChannelService, SalesTypeEnum, YesOrNo } from 'genesis-web-service';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from '@umijs/max';
import { BasicInfoTip } from '@/pages/common-party/partner-setting/components/BasicInfoTip';
import { useBreadCrumbNav } from '../hooks/useBreadCrumb';
import type { LocationQueryParam } from '@/types/common-party';
import qs from 'qs';
import { useSearchParams } from 'react-router-dom';
import { ChannelSchemaFieldsContext } from '@/hooks/ChannelSchemaFieldsContext';
import { useChannelSchemaFields } from '@/hooks/useChannelSchemaFields';
import {
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
} from '../hooks/useChannelSchemaFieldsByCategory';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

const BankDetail = () => {
  const [searchParams] = useSearchParams();
  const { id, type, modeType, parentId, level } = qs.parse(
    searchParams.toString(),
  ) as unknown as LocationQueryParam;

  const dispatch = useDispatch();
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const basicInfoRef = useRef<string>(null);
  const addressRef = useRef(null);
  const agreementRef = useRef(null);
  const concatPersonRef = useRef(null);
  const accountInfoRef = useRef(null);

  const { channelSchemaDefFields, schemaUsedBizDictMap } =
    useChannelSchemaFields();

  const [bankData, setBankData] = useState<ChannelDetail>();
  const [mode, setMode] = useState<ModeEnum>();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [structureVisible, setStructureVisible] = useState(false);

  const { breadCrumbList = [], setBreadCrumbList } = useBreadCrumbNav({
    id: (id || parentId)?.toString(),
    type,
  });

  const getBankData = useCallback(() => {
    setLoading(true);
    ChannelService.queryChannelDetail(id)
      .then(setBankData)
      .catch((error: Error) => message.error(error.message))
      .finally(() => setLoading(false));
  }, [id]);

  useEffect(() => {
    if (id) {
      getBankData();
    }
  }, [id]);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'country',
        'organizationIdType',
        'companyAddressType',
        'gender',
        'channelCustomerType',
        'certiType',
        'channelOrgAccountType',
        'accountSubType',
        'accountType',
        'yesNo',
        'settlementRule',
        'salesAgreementStatus',
        'agentStatus',
      ],
    });
  }, [dispatch]);

  useEffect(() => {
    if (modeType) {
      setMode(modeType);
    } else if (id) {
      setMode(ModeEnum.READ);
    }
  }, [modeType, id]);

  useEffect(() => {
    if (bankData) {
      const basicInfodynamicFields =
        channelSchemaDefFields
          .find(item => item.category === 'BASE')
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForDisplay(
        bankData?.extensions,
        basicInfodynamicFields,
      );
      form.setFieldsValue({
        ...bankData,
      });
    }
  }, [bankData, form]);

  const handleCancel = useCallback(() => {
    form.resetFields();
    setMode(ModeEnum.READ);
    setBankData({
      ...bankData,
      addressList: [...(bankData.addressList ?? [])],
      customerList: [...(bankData.customerList ?? [])],
      accountList: [...(bankData.accountList ?? [])],
    });
  }, [form, bankData]);

  const saveBank = useCallback(() => {
    form?.validateFields().then(values => {
      setSaving(true);
      const logoUrl = basicInfoRef.current || '';
      const addressList = addressRef.current || [];
      const customerList = concatPersonRef.current || [];
      const accountList = accountInfoRef.current || [];

      const basicInfodynamicFields =
        channelSchemaDefFields
          .find(item => item.category === 'BASE')
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForSubmit(values.extensions, basicInfodynamicFields, {
        dateFormatInstance,
      });

      let submitReq: Promise<ChannelDetail>;
      if (bankData) {
        submitReq = ChannelService.updateChannel(bankData.id, {
          ...values,
          type,
          parentId: bankData.parentId,
          level: bankData.level,
          addressList,
          accountList,
          customerList,
          logoUrl,
          teamId: bankData.teamId,
          dataPathCode: bankData.dataPathCode,
        });
      } else {
        submitReq = ChannelService.addChannel({
          ...values,
          type,
          parentId,
          level,
          addressList,
          accountList,
          customerList,
          logoUrl,
        });
      }
      submitReq
        .then(resp => {
          message.success(t('Save successfully'));
          setBankData(resp);
          setMode(ModeEnum.READ);
          if (resp) {
            const clonedBreadList = [...breadCrumbList];
            if (clonedBreadList.length > 1) {
              // 保存成功，更新最后一条name
              clonedBreadList[clonedBreadList.length - 1].name = resp.name;
            }
            agreementRef.current.saveAgreement(resp.code, resp.id, type);
            setBreadCrumbList(clonedBreadList);
          }
        })
        .catch((error: Error) => message.error(error.message))
        .finally(() => setSaving(false));
    });
  }, [form, bankData, type, parentId, level, breadCrumbList, t]);

  return (
    <ChannelSchemaFieldsContext.Provider
      value={{
        channelSchemaDefFields: channelSchemaDefFields,
        schemaUsedBizDictMap: schemaUsedBizDictMap,
      }}
    >
      <Skeleton loading={loading} active>
        <Layout.Content className={styles.channelDetail}>
          <CommonHeader
            loading={saving}
            readonly={mode === ModeEnum.READ}
            showCancel={!!bankData}
            showViewStructure={!!bankData}
            handleEdit={setMode}
            handleCancel={handleCancel}
            handleSave={saveBank}
            viewStructure={() => setStructureVisible(true)}
            name={bankData?.name}
            breadCrumbList={breadCrumbList}
          />
          <div className={styles.section}>
            <Card bordered={false}>
              <div className={commonStyles.basicInfoSection}>
                <BasicInfo
                  ref={basicInfoRef}
                  form={form}
                  readonly={mode === ModeEnum.READ}
                  channelDetail={bankData}
                  fileUniqueCode={bankData?.logoUrl}
                  basicInfoType={type}
                />
                {!bankData && (
                  <Button
                    type="primary"
                    loading={saving}
                    onClick={() => saveBank()}
                  >
                    {t('Create')}
                  </Button>
                )}
              </div>
              <div className={commonStyles.extraInfoSection}>
                {!bankData && <BasicInfoTip />}
                <SalesAgreement
                  ref={agreementRef}
                  readonly={mode === ModeEnum.READ || !bankData}
                  channelCode={bankData?.code}
                  mode={mode}
                  renew={true}
                  salesType={SalesTypeEnum.CHANNEL}
                />
                <Divider dashed />
                <Address
                  ref={addressRef}
                  readonly={mode === ModeEnum.READ || !bankData}
                  initialList={bankData?.addressList}
                  mode={mode}
                />
                <Divider dashed />
                <ContactPerson
                  ref={concatPersonRef}
                  channelType={type}
                  readonly={mode === ModeEnum.READ || !bankData}
                  initialList={bankData?.customerList}
                  mode={mode}
                />
                <Divider dashed />
                <AccountInfo
                  ref={accountInfoRef}
                  readonly={mode === ModeEnum.READ || !bankData}
                  initialList={bankData?.accountList}
                  mode={mode}
                />
                <Divider dashed />
                <Staff
                  readonly={mode === ModeEnum.READ || !bankData}
                  channelId={bankData?.id}
                  channelType={type}
                  mode={mode}
                />
              </div>
              <Drawer
                title={t('Structure Details')}
                open={structureVisible}
                maskClosable={false}
                width={641}
                rootClassName={commonStyles.structureDrawer}
                onClose={() => setStructureVisible(false)}
              >
                <Structure
                  channelId={bankData?.id}
                  channelType={type}
                  visible={structureVisible}
                />
              </Drawer>
            </Card>
          </div>
        </Layout.Content>
      </Skeleton>
    </ChannelSchemaFieldsContext.Provider>
  );
};

export default BankDetail;
