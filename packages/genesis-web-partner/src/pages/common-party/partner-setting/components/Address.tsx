import { forwardRef, useCallback, useEffect, useImperative<PERSON>andle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';

import { cloneDeep } from 'lodash-es';

import { DeleteAction, EditAction, Table, ViewAction } from '@zhongan/nagrand-ui';

import { useAddressConfig } from 'genesis-web-component/lib/components/Address';
import { AddressComponent } from 'genesis-web-component/lib/components/AddressComponent';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { AddressItem, FactorsType } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import { useAddressI18nList } from '@/hooks/useAddressI18nList';
import { EntityType, useGenericSchemaFormItemFields } from '@/hooks/useGenericSchemaFormItemFields';
import { AddNewTitle } from '@/pages/common-party/partner-setting/components/AddNewTitle/index';
import { useFormFields } from '@/pages/common-party/partner-setting/hooks/useFormFields';
import { FormTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import type { SchemaProps } from '@/types/common';
import { CustomerType } from '@/utils/constants';
import { transferSchemaToTableProp } from '@/utils/utils';

import {
  covertChannelType2SchemaChannelType,
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
} from '../../../../hooks/useGenericSchemaFormItemFields';
import { ChannelTypeEnum } from '../models/index.interface';

interface Props {
  initialList: AddressItem[];
  channelType: ChannelTypeEnum;
  disabled: boolean;
}

/**
 * @param initialList 初始化列表
 * @description Address信息展示
 */
export const Address = forwardRef(({ initialList, channelType, disabled }: Props, ref) => {
  const { t } = useTranslation(['partner']);
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [editedIndex, setEditedIndex] = useState<number>();
  const [editedItem, setEditedItem] = useState<AddressItem>();
  const [shownList, setShownList] = useState<AddressItem[]>();
  const [addressCascaderValue, setAddressCascaderValue] = useState<Record<string, string>>();
  const [readonly, setReadonly] = useState(false);

  const addressI18nList = useAddressI18nList(shownList, CustomerType.COMPANY);
  const { formItems: staticFields } = useGenericSchemaFormItemFields({
    staticOrDynamic: 'STATIC',
    category: 'ADDRESS',
    disabled: readonly,
    type: covertChannelType2SchemaChannelType(channelType),
    entityType: EntityType.CHANNEL,
    fieldKeyPrefix: '',
  });

  const insuranceFields = useFormFields({
    formType: FormTypeEnum.ADDRESS,
    basicInfoType: channelType,
    channelDetail: {},
    disabled: readonly,
    form,
    setAddressResult: setAddressCascaderValue,
  });

  const { formItems } = useAddressConfig(channelType, false);

  const fields = [
    staticFields.find(field => field.key === 'addressType'),
    {
      type: FieldType.Customize,
      customerDom: (
        <AddressComponent
          disabled={readonly}
          initialValue={(editedItem as unknown as Record<string, string>) ?? {}}
          onCascaderValueChange={setAddressCascaderValue}
          form={form}
          cascaderCol={24}
          colProps={{ span: 12 }}
          inputWidth="100%"
          selfCustomFields={
            staticFields
              ?.filter(field => field.key !== 'addressType')
              .map(field => {
                const { key, label, ctrlProps, rules } = field;

                return {
                  factorCode: key,
                  factorName: label,
                  isRequired: rules?.length ? 1 : 0,
                  options: ctrlProps?.options,
                };
              }) as FactorsType[]
          }
        />
      ),
    },
  ];

  const { formItems: dynamicFields, schemaFields } = useGenericSchemaFormItemFields({
    staticOrDynamic: 'DYNAMIC',
    category: 'ADDRESS',
    disabled: readonly,
    type: covertChannelType2SchemaChannelType(channelType),
    entityType: channelType === ChannelTypeEnum.INSURANCE ? EntityType.CHANNEL_INSURANCE : EntityType.CHANNEL,
  });

  useEffect(() => {
    setShownList([...(initialList || [])]);
  }, [initialList]);

  useImperativeHandle(ref, () => {
    return shownList;
  });

  const editClick = useCallback(
    (item: AddressItem, index: number) => {
      setVisible(true);
      setEditedIndex(index);
      setEditedItem(item);
      dealSchemaFieldValueForDisplay(item?.extensions, schemaFields);
      form.setFieldsValue(item);
    },
    [form, schemaFields]
  );

  const deleteClick = useCallback(
    (index: number) => {
      const cloneList = cloneDeep(shownList || []);
      cloneList.splice(index, 1);
      setShownList(cloneList);
    },
    [shownList]
  );

  const onClose = useCallback(() => {
    setVisible(false);
    setEditedIndex(null);
    setEditedItem(null);
    form.resetFields();
  }, [form]);

  const onSubmit = useCallback(() => {
    form.validateFields().then(values => {
      const cloneList = cloneDeep(shownList || []);
      values.id = editedItem?.id;
      dealSchemaFieldValueForSubmit(values.extensions, schemaFields, { dateFormatInstance });
      if (editedItem) {
        cloneList.splice(editedIndex, 1, {
          ...values,
          ...addressCascaderValue,
        });
      } else {
        cloneList.push({
          ...values,
          ...addressCascaderValue,
        });
      }
      setShownList(cloneList);
      onClose();
    });
  }, [shownList, form, editedItem, editedIndex, addressCascaderValue, onClose, schemaFields, t]);

  const title = useMemo(() => {
    if (readonly) {
      return t('View Address');
    } else if (editedItem) {
      return t('Edit Address');
    } else {
      return t('Add Address');
    }
  }, [readonly, editedItem, t]);

  return (
    <section>
      <AddNewTitle
        title={t('Address')}
        onAddClick={() => {
          setVisible(true);
          setReadonly(false);
        }}
        readonly={disabled}
      />

      <Table
        columns={[
          ...transferSchemaToTableProp(
            [
              ...(channelType === ChannelTypeEnum.INSURANCE ? [insuranceFields[0], ...formItems] : staticFields),
              ...dynamicFields,
            ] as SchemaProps[],
            addressI18nList
          ),
          {
            title: t('Action'),
            render: (_, record, index) => (
              <>
                <ViewAction
                  onClick={() => {
                    editClick(record, index);
                    setReadonly(true);
                  }}
                />
                <EditAction
                  disabled={disabled}
                  onClick={() => {
                    editClick(record, index);
                    setReadonly(false);
                  }}
                />
                <DeleteAction
                  deleteConfirmContent={t('Are you sure to delete?')}
                  onClick={() => deleteClick(index)}
                  disabled={disabled}
                />
              </>
            ),
          },
        ]}
        dataSource={shownList}
        pagination={false}
        scroll={{ x: 'max-content' }}
      />

      <DrawerForm
        title={title}
        visible={visible}
        closable={false}
        onClose={onClose}
        onSubmit={onSubmit}
        cancelText={t('Cancel')}
        sendText={t('Submit')}
        submitBtnShow={!readonly}
      >
        <CommonForm
          fields={[...(channelType === ChannelTypeEnum.INSURANCE ? insuranceFields : fields), ...dynamicFields]}
          form={form}
        />
      </DrawerForm>
    </section>
  );
});
