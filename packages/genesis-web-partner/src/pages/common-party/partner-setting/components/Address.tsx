/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { Fragment, forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { EnvironmentOutlined } from '@ant-design/icons';

import { Form, message } from 'antd';
import classNames from 'classnames';
import { cloneDeep } from 'lodash-es';

import { getAddressDisplayString } from 'genesis-web-component/lib/components';
import { AddressComponent } from 'genesis-web-component/lib/components/AddressComponent';
import { ComponentWithFallback } from 'genesis-web-component/lib/components/ComponentWithFallback';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { AddressItem, BizDictItem } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { FieldType } from '@/components/CommonForm';
import { CommonForm } from '@/components/CommonForm/Form';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import type { ConnectState } from '@/models/connect';
import { ChannelCard } from '@/pages/common-party/partner-setting/components/ChannelCard';
import { useEnumsMapping } from '@/pages/common-party/partner-setting/hooks/useEnumsMapping';
import type { ModeEnum } from '@/types/common';
import { CustomerType } from '@/utils/constants';
import { useSelector } from '@umijs/max';

import {
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
  useSchemaFormItemFieldsByCategory,
} from '../hooks/useChannelSchemaFieldsByCategory';
import CommonStyles from '../style.scss';
import styles from './ChannelCard/style.scss';

interface Props {
  readonly: boolean;
  initialList: AddressItem[];
  mode: ModeEnum;
}

/**
 * @param initialList 初始化列表
 * @param readonly 是否只读
 * @description Address信息展示
 */
export const Address = forwardRef(({ initialList, readonly, mode }: Props, ref) => {
  const { t } = useTranslation(['partner']);
  const companyAddressTypesMapping = useEnumsMapping('companyAddressType');
  const [form] = Form.useForm();
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const { loadingEnums } = useSelector(({ loading }: ConnectState) => ({
    loadingEnums: !!loading.effects['global/getTenantBizDict'],
  }));

  const [visible, setVisible] = useState(false);
  const [editedIndex, setEditedIndex] = useState<number>();
  const [editedItem, setEditedItem] = useState<AddressItem>();
  const [shownList, setShownList] = useState<AddressItem[]>();
  const [addressCascaderValue, setAddressCascaderValue] = useState<Record<string, string>>();
  const [addressI18nList, setAddressI18nList] = useState<string[]>([]);

  // 处理国际化
  const formatAddress = useCallback(async () => {
    if (shownList?.length) {
      try {
        const tempAddressI18nList = await Promise.all(
          shownList.map(async addressItem => {
            const i18n = await getAddressDisplayString(addressItem, ' ', CustomerType.COMPANY);
            return i18n ?? '';
          })
        );
        setAddressI18nList(tempAddressI18nList);
      } catch (error) {
        message.error((error as Error)?.message);
        setAddressI18nList([]);
      }
    } else {
      setAddressI18nList([]);
    }
  }, [shownList]);

  useEffect(() => {
    formatAddress();
  }, [formatAddress]);

  const fields = [
    {
      key: 'addressType',
      label: t('Address Type'),
      type: FieldType.Select,
      rules: [
        {
          required: true,
          message: t('channel.common.required', {
            label: t('Address Type'),
          }),
        },
      ],
      ctrlProps: {
        options: enums?.companyAddressType,
        allowClear: true,
        loading: loadingEnums,
        disabled: readonly,
      },
    },
    {
      type: FieldType.Customize,
      customerDom: (
        <AddressComponent
          disabled={readonly}
          initialValue={editedItem ?? {}}
          onCascaderValueChange={setAddressCascaderValue}
          form={form}
          cascaderCol={24}
          colProps={{ span: 12 }}
          inputWidth="100%"
        />
      ),
    },
  ];

  const { formItems: dynamicFields, schemaFields } = useSchemaFormItemFieldsByCategory({
    staticOrDynamic: 'DYNAMIC',
    category: 'ADDRESS',
    disabled: readonly,
  });

  useEffect(() => {
    setShownList([...(initialList || [])]);
  }, [initialList]);

  useImperativeHandle(ref, () => {
    return shownList;
  });

  const editClick = useCallback(
    (item: AddressItem, index: number) => {
      setVisible(true);
      setEditedIndex(index);
      setEditedItem(item);
      dealSchemaFieldValueForDisplay(item?.extensions, schemaFields);
      form.setFieldsValue(item);
    },
    [form, schemaFields]
  );

  const deleteClick = useCallback(
    (index: number) => {
      const cloneList = cloneDeep(shownList || []);
      cloneList.splice(index, 1);
      setShownList(cloneList);
    },
    [shownList]
  );

  const onClose = useCallback(() => {
    setVisible(false);
    setEditedIndex(null);
    setEditedItem(null);
    form.resetFields();
  }, [form]);

  const onSubmit = useCallback(() => {
    form.validateFields().then(values => {
      const cloneList = cloneDeep(shownList || []);
      values.id = editedItem?.id;
      dealSchemaFieldValueForSubmit(values.extensions, schemaFields, { dateFormatInstance });
      if (editedItem) {
        cloneList.splice(editedIndex, 1, {
          ...values,
          ...addressCascaderValue,
        });
      } else {
        cloneList.push({
          ...values,
          ...addressCascaderValue,
        });
      }
      setShownList(cloneList);
      onClose();
    });
  }, [shownList, form, editedItem, editedIndex, addressCascaderValue, onClose, schemaFields, t]);

  const cardContent = useCallback(
    (item: AddressItem, index: number) => {
      const address = addressI18nList[index];
      return (
        <Fragment>
          <span className={styles.name}>{companyAddressTypesMapping[item.addressType]}</span>
          <p className={styles.desc}>{item.zipCode}</p>

          <p className={classNames(styles.location, !address && styles.gray)}>
            <EnvironmentOutlined />
            <ComponentWithFallback>{address}</ComponentWithFallback>
          </p>
        </Fragment>
      );
    },
    [companyAddressTypesMapping, addressI18nList]
  );

  const title = useMemo(() => {
    if (readonly) {
      return t('View Address');
    } else if (editedItem) {
      return t('Edit Address');
    } else {
      return t('Add Address');
    }
  }, [readonly, editedItem, t]);

  return (
    <section className={styles.addressInfo}>
      <p className={CommonStyles.sectionTitle}>{t('Address')}</p>
      <ChannelCard
        shownList={shownList}
        className={styles.address}
        readonly={readonly}
        mode={mode}
        addText={t('Add New Address')}
        cardContent={cardContent}
        editClick={editClick}
        deleteClick={deleteClick}
        addClick={() => setVisible(true)}
      />
      <DrawerForm
        title={title}
        visible={visible}
        closable={false}
        onClose={onClose}
        onSubmit={onSubmit}
        cancelText={t('Cancel')}
        sendText={t('Submit')}
        submitBtnShow={!readonly}
      >
        <CommonForm fields={[...fields, ...dynamicFields]} form={form} />
      </DrawerForm>
    </section>
  );
});
