import type { ForwardedRef } from 'react';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { FormInstance } from 'antd/lib/form';

import { SimpleSectionHeader, UploadImage } from '@zhongan/nagrand-ui';

import { DownloadOrUploadType, UploadTypeEnum } from 'genesis-web-service';
import { security } from 'genesis-web-shared';

import { CommonForm } from '@/components/CommonForm/Form';
import { EntityType, useGenericSchemaFormItemFields } from '@/hooks/useGenericSchemaFormItemFields';
import { ChannelTypeEnum, FormTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { getUrlByFileUniqueCode } from '@/pages/common-party/utils/util';

import { covertChannelType2SchemaChannelType } from '../../../../hooks/useGenericSchemaFormItemFields';
import { useFormFields } from '../hooks/useFormFields';
import styles from '../style.scss';

interface Props<T> {
  form: FormInstance;
  channelDetail: T;
  fileUniqueCode: string;
  basicInfoType: ChannelTypeEnum;
  disabled: boolean;
}

type BasicInfoComp = <T extends object>(props: Props<T> & { ref?: ForwardedRef<string> }) => JSX.Element;

/**
 * @param form form实例
 * @param channelDetail response detail
 * @param basicInfoType 需要哪类基础信息，ChannelTypeEnum枚举
 * @description basic Information form组件
 */
export const BasicInfo: BasicInfoComp = forwardRef(
  ({ form, channelDetail, fileUniqueCode, basicInfoType, disabled }, ref) => {
    const { t } = useTranslation('partner');

    const [fileCode, setFileCode] = useState<string>();

    const { formItems: staticFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'STATIC',
      category: 'BASE',
      disabled,
      type: covertChannelType2SchemaChannelType(basicInfoType),
      entityType: EntityType.CHANNEL,
      fieldKeyPrefix: '',
    });
    const fields = useFormFields({
      formType: FormTypeEnum.BASIC_INFO,
      basicInfoType,
      channelDetail,
      disabled,
      form,
    });
    const { formItems: dynamicFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'DYNAMIC',
      category: 'BASE',
      disabled,
      type: covertChannelType2SchemaChannelType(basicInfoType),
      entityType: basicInfoType === ChannelTypeEnum.INSURANCE ? EntityType.CHANNEL_INSURANCE : EntityType.CHANNEL,
    });

    useEffect(() => {
      setFileCode(fileUniqueCode);
    }, [channelDetail]);

    useImperativeHandle(ref, () => {
      return fileCode;
    });

    return (
      <section className={styles.basicInfo}>
        <SimpleSectionHeader type="h5" weight="bold">
          {t('Basic Information')}
        </SimpleSectionHeader>
        <div className="text-[var(--label)] my-2">{t('You can only upload PNG/JPG')}</div>
        <UploadImage
          disabled={disabled}
          requestUrl="/api/channel/v2/file/upload/"
          name="file"
          data={{
            type: DownloadOrUploadType.COMMON_ENTITY_FILE,
            ext: JSON.stringify({ type: UploadTypeEnum.ChannelLogo }),
          }}
          filePath={getUrlByFileUniqueCode('/api/channel/v2/file/download', fileCode)}
          onChange={value => setFileCode(value ? (value.value.fileUniqueCode as string) : '')}
          headers={{
            ...security.csrf(),
          }}
          showHint={false}
          accept={['.png', '.jpg']}
          customizeDescription={
            <>
              <div>{t('Size')}:300*300</div>
              <div>{t('File Size')}:1MB</div>
            </>
          }
        />
        <div style={{ marginTop: 20 }}>
          <CommonForm
            fields={[...(basicInfoType === ChannelTypeEnum.INSURANCE ? fields : staticFields), ...dynamicFields]}
            form={form}
            disabled={disabled}
          />
        </div>
      </section>
    );
  }
);
