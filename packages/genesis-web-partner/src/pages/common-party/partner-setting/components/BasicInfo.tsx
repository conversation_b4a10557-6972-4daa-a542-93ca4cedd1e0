import type { ForwardedRef } from 'react';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Divider } from 'antd';
import type { FormInstance } from 'antd/lib/form';

import { UploadImage } from 'genesis-web-component/lib/components/UploadImage';
import { DownloadOrUploadType, UploadTypeEnum } from 'genesis-web-service';
import { security } from 'genesis-web-shared';

import { CommonForm } from '@/components/CommonForm/Form';
import { useFormFields } from '@/pages/common-party/partner-setting/hooks/useFormFields';
import type { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { FormTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { getUrlByFileUniqueCode } from '@/pages/common-party/utils/util';

import { useSchemaFormItemFieldsByCategory } from '../hooks/useChannelSchemaFieldsByCategory';
import styles from '../style.scss';

interface Props<T> {
  form: FormInstance;
  readonly: boolean;
  channelDetail: T;
  fileUniqueCode: string;
  basicInfoType: ChannelTypeEnum;
}

type BasicInfoComp = <T extends object>(props: Props<T> & { ref?: ForwardedRef<string> }) => JSX.Element;

/**
 * @param form form实例
 * @param readonly 是否只读
 * @param channelDetail response detail
 * @param basicInfoType 需要哪类基础信息，ChannelTypeEnum枚举
 * @description basic Information form组件
 */
export const BasicInfo: BasicInfoComp = forwardRef(
  ({ form, readonly, channelDetail, fileUniqueCode, basicInfoType }, ref) => {
    const { t } = useTranslation('partner');
    const fields = useFormFields({
      formType: FormTypeEnum.BASIC_INFO,
      basicInfoType,
      channelDetail,
      disabled: readonly,
      form,
    });
    const [fileCode, setFileCode] = useState<string>();
    const { formItems: dynamicFields, schemaFields } = useSchemaFormItemFieldsByCategory({
      staticOrDynamic: 'DYNAMIC',
      category: 'BASE',
      disabled: readonly,
    });

    useEffect(() => {
      setFileCode(fileUniqueCode);
    }, [channelDetail]);

    useImperativeHandle(ref, () => {
      return fileCode;
    });

    return (
      <section className={styles.basicInfo}>
        <p className={styles.sectionTitle}>{t('Basic Information')}</p>
        <Divider dashed />
        <p>{t('Logo')}</p>
        <UploadImage
          requestUrl="/api/channel/v2/file/upload/"
          name="file"
          data={{
            type: DownloadOrUploadType.COMMON_ENTITY_FILE,
            ext: JSON.stringify({ type: UploadTypeEnum.ChannelLogo }),
          }}
          disabled={readonly}
          filePath={getUrlByFileUniqueCode('/api/channel/v2/file/download', fileCode)}
          onChange={value => setFileCode(value ? (value.fileUniqueCode as string) : '')}
          headers={{
            ...security.csrf(),
          }}
        />
        <div style={{ marginTop: 20 }}>
          <CommonForm fields={[...fields, ...dynamicFields]} form={form} />
        </div>
      </section>
    );
  }
);
