import type { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

import { Button } from 'antd';

import { Icon, SimpleSectionHeader } from '@zhongan/nagrand-ui';

type AddNewTitleProps = {
  title: string;
  onAddClick: () => void;
  extra?: ReactNode;
  readonly?: boolean;
};

export const AddNewTitle = ({ title, onAddClick, readonly, extra }: AddNewTitleProps) => {
  const { t } = useTranslation(['partner']);

  return (
    <div className="relative mb-4">
      <SimpleSectionHeader type="h5" weight="bold">
        {title}
      </SimpleSectionHeader>
      <span className="absolute right-0 top-0">
        {!readonly ? (
          <Button icon={<Icon type="add" />} onClick={onAddClick}>
            {t('Add New')}
          </Button>
        ) : null}

        {extra}
      </span>
    </div>
  );
};
