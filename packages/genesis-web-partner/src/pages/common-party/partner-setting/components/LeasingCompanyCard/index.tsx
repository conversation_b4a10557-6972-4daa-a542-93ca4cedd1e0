import type { Dispatch, SetStateAction } from 'react';
import { Fragment, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Drawer, Form, Modal, message } from 'antd';
import type { AnyObject } from 'antd/es/_util/type';
import type { PaginationProps } from 'antd/es/pagination';
import type { TableRowSelection } from 'antd/es/table/interface';

import type { FieldDataType } from '@zhongan/nagrand-ui';
import { Icon, QueryForm } from '@zhongan/nagrand-ui';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import type { ChannelDetail } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';
import type { BizExportParams } from 'genesis-web-service/lib/data-mgmt-service/data-mgmt-interface';

import type { PartnerSettingSearchQuery } from '@/pages/common-party/partner-setting';
import { ImportAndExport } from '@/pages/common-party/partner-setting/components/ImportAndExport';
import { useRowSelection } from '@/pages/common-party/partner-setting/components/ImportAndExport/index';
import { Structure } from '@/pages/common-party/partner-setting/components/Structure';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import type { ChannelRouteState, ChannelSearchForm } from '@/pages/common-party/partner-setting/models/index.interface';
import { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { ModeEnum } from '@/types/common';

import style from '../../style.scss';
import { CommonTable } from './Table';

/**
 *
 * @description partner setting bank menu component
 */

export const LeasingCompanyCard = ({
  setSearchQuery,
}: {
  setSearchQuery?: Dispatch<SetStateAction<PartnerSettingSearchQuery>>;
}) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();

  const [searchParams] = useRouterState<Partial<ChannelRouteState<ChannelSearchForm>>>();

  const { canEdit } = usePartnerSettingPermission();

  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState<number>();
  const [channelList, setChannelList] = useState<ChannelDetail[]>([]);
  const [pagination, setPagination] = useState<PaginationProps>();
  const [structureVisible, setStructureVisible] = useState(false);
  const [selectedLeasingId, setSelectedLeasingId] = useState<number>();
  const paginationRef = useRef<PaginationProps>(); // 用于同步获取pagination，setState是异步更新，setPagination后立即获取的pagination是旧值

  const [exportCodes, setExportCodes] = useState<BizExportParams[]>();
  const [exportLoading, setExportLoading] = useState(false);
  const rowSelection = useRowSelection({
    channelType: ChannelTypeEnum.LeaseChannel,
    exportCodes,
    setExportCodes,
    channelList,
  });

  // 若将handleSearch作为useEffect依赖，handleSearch依赖pagination变化时自动调用，则初始化时会调用两遍（一次是channelType, 一次是pagination）
  // 故在需要的地方手动调用
  const handleSearch = useCallback(() => {
    form.validateFields().then(values => {
      setLoading(true);
      const searchValues = { ...values };
      Object.keys(searchValues).forEach(key => {
        if (searchValues[key] === '') {
          searchValues[key] = undefined;
        }
      });
      ChannelService.getChannelList({
        pageIndex: paginationRef.current?.current - 1,
        pageSize: paginationRef.current?.pageSize,
        type: ChannelTypeEnum.LeaseChannel,
        ...searchValues,
      })
        .then(res => {
          setChannelList(res.data);
          setTotal(res.totalElements);
        })
        .catch(error => message.error(error?.message))
        .finally(() => setLoading(false));
    });
  }, []);

  // 初始化
  useEffect(() => {
    // 切换menu，只有channelType匹配才会使用state初始化表单和页码信息
    if (searchParams?.channelType === ChannelTypeEnum.LeaseChannel) {
      form.setFieldsValue(searchParams?.searchForm);
    } else {
      form.resetFields();
    }
    paginationRef.current = searchParams?.pagination;
    setPagination(paginationRef.current);
    handleSearch();
  }, [form]);

  const handleSearchFieldChange = useCallback(() => {
    paginationRef.current = { current: 1, pageSize: 10 };
    setPagination(paginationRef.current);
    handleSearch();
  }, [handleSearch]);

  const handlePaginationChange = useCallback(
    (current, pageSize) => {
      paginationRef.current = { current, pageSize };
      setPagination(paginationRef.current);
      handleSearch();
    },
    [handleSearch]
  );

  const addLeasing = (parentId: number, level: number) => {
    setSearchQuery?.(() => ({
      parentId: parentId && String(parentId),
      level: String(level),
      channelType: ChannelTypeEnum.LeaseChannel,
      modeType: ModeEnum.ADD,
      visible: true,
      searchForm: form.getFieldsValue(),
      pagination,
    }));
  };

  const viewDetail = (leasingId: number, modeType: ModeEnum.READ) => {
    setSearchQuery?.(() => ({
      id: String(leasingId),
      channelType: ChannelTypeEnum.LeaseChannel,
      modeType,
      visible: true,
      searchForm: form.getFieldsValue(),
      pagination,
    }));
  };

  const deleteRecord = useCallback(
    (id: number) => {
      Modal.confirm({
        title: t('Delete'),
        className: style.deleteModal,
        content: (
          <p>
            {t(
              'All affiliated partner information will be deleted at the same time. Are you sure to delete this record?'
            )}
          </p>
        ),
        onOk: () => {
          Modal.destroyAll();
          setLoading(true);
          ChannelService.deleteChannel(id)
            .then(() => {
              message.success(t('Delete successfully'));
              handleSearchFieldChange();
            })
            .catch((error: Error) => {
              message.error(error?.message);
              // 删除后要重新请求数据，故不能放在finally里统一置为false
              setLoading(false);
            });
        },
      });
    },
    [handleSearchFieldChange]
  );

  const viewStructure = useCallback((leasingId: number) => {
    setStructureVisible(true);
    setSelectedLeasingId(leasingId);
  }, []);

  const fields: FieldDataType[] = useMemo(
    () => [
      {
        key: 'name',
        label: t('Leasing Channel Name'),
      },
      {
        key: 'code',
        label: t('Leasing Channel Code'),
      },
    ],
    [t]
  );

  return (
    <Fragment>
      <QueryForm queryFields={fields} onSearch={handleSearchFieldChange} formProps={{ form }} />
      <div>
        <ImportAndExport
          addBtn={
            <Button
              icon={<Icon type="add" />}
              color="primary"
              variant="outlined"
              disabled={!canEdit}
              onClick={() => addLeasing(null, 1)}
            >
              {t('Add New')}
            </Button>
          }
          exportCodes={exportCodes}
          setExportCodes={setExportCodes}
          exportLoading={exportLoading}
          setExportLoading={setExportLoading}
          reload={handleSearchFieldChange}
        />
        <CommonTable
          loading={loading}
          data={channelList}
          viewDetail={viewDetail}
          handleDelete={deleteRecord}
          addLeasing={addLeasing}
          viewStructure={viewStructure}
          rowSelection={rowSelection as unknown as TableRowSelection<AnyObject>}
          rowKey="code"
          pagination={{
            current: pagination?.current,
            pageSize: pagination?.pageSize,
            total: total,
            onChange: handlePaginationChange,
          }}
        />
        <Drawer
          title={t('Structure Details')}
          open={structureVisible}
          maskClosable={false}
          width={641}
          rootClassName={style.structureDrawer}
          onClose={() => setStructureVisible(false)}
        >
          <Structure
            channelId={selectedLeasingId}
            channelType={ChannelTypeEnum.LeaseChannel}
            visible={structureVisible}
          />
        </Drawer>
      </div>
    </Fragment>
  );
};
