import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import type { TableProps } from 'antd/es/table';

import type { ColumnsType } from '@zhongan/nagrand-ui';
import {
  CommonIconAction,
  DeleteAction,
  EditAction,
  Icon,
  Table,
  TableActionsContainer,
  ViewAction,
} from '@zhongan/nagrand-ui';

import type { ChannelDetail } from 'genesis-web-service';

import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import { MaxLevel } from '@/pages/common-party/utils/constants';
import { ModeEnum } from '@/types/common';

interface TableProp {
  loading: boolean;
  data: ChannelDetail[];
  viewDetail: (channelId: number, modeType: ModeEnum) => void;
  handleDelete: (channelId: number) => void;
  addLeasing: (parentId: number, level: number) => void;
  viewStructure: (channelId: number) => void;
}

/**
 *
 * @param loading 是否在加载
 * @param data 表格数据
 * @param viewDetail 跳转method
 * @param handleDelete delete method
 * @param addLeasing add leasing method
 * @param viewStructure view structure method
 * @description 用于partner setting agencyCompany/salesChannel/serviceCompany menu component； 数据展示
 */
export const CommonTable = ({
  loading,
  data,
  viewDetail,
  handleDelete,
  addLeasing,
  viewStructure,
  ...rest
}: TableProp & TableProps) => {
  const { t } = useTranslation('partner');
  const { canEdit } = usePartnerSettingPermission();

  const columns: ColumnsType<ChannelDetail> = useMemo(
    () => [
      {
        title: t('Leasing Channel Code'),
        fixed: 'left',
        dataIndex: 'code',
      },
      {
        title: t('Leasing Channel Name'),
        dataIndex: 'name',
      },
      {
        title: t('Email'),
        dataIndex: 'email',
      },
      {
        title: t('Phone No.'),
        dataIndex: 'hotLine',
      },
      {
        title: t('Actions'),
        fixed: 'right',
        width: 64,
        align: 'center',
        render: (item: ChannelDetail) => (
          <TableActionsContainer>
            <ViewAction onClick={() => viewDetail(item.id, ModeEnum.READ)} />
            <EditAction disabled={!canEdit} onClick={() => viewDetail(item.id, ModeEnum.EDIT)} />
            <DeleteAction disabled={!canEdit} onClick={() => handleDelete(item.id)} />
            <CommonIconAction
              icon={<Icon type="identity-access-management" />}
              tooltipTitle={t('Add New at the Same Level')}
              disabled={!canEdit}
              onClick={() => addLeasing(item.parentId, +item.level)}
            />
            {+item.level < MaxLevel && (
              <CommonIconAction
                icon={<Icon type="tree-list" />}
                tooltipTitle={t('Add New at the Sub Level')}
                disabled={!canEdit}
                onClick={() => addLeasing(item.id, +item.level + 1)}
              />
            )}
            <CommonIconAction
              icon={<Icon type="permission-management" />}
              tooltipTitle={t('View Structure')}
              onClick={() => viewStructure(item.id)}
            />
          </TableActionsContainer>
        ),
      },
    ],
    [viewDetail, t, handleDelete, addLeasing, viewStructure, canEdit]
  );

  return (
    <Table
      scroll={{ x: 'max-content' }}
      emptyType="icon"
      loading={loading}
      dataSource={data}
      columns={columns}
      rowKey="id"
      pagination={false}
      style={{ marginBottom: 16 }}
      {...rest}
    />
  );
};
