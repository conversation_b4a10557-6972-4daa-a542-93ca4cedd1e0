import { Input } from 'antd';
import type { InputRef } from 'antd';
import type { ColumnEx } from '@/types/common';
import type { ChangeEvent, ReactNode } from 'react';
import { useEffect, useCallback, useState, useRef } from 'react';

interface EditableRowProp<T> extends ColumnEx<T> {
  index: number;
}

type EditableCellProp<T> = ColumnEx<T> & {
  record: T;
  handleValueChange: (val: Record<string, string>) => void;
  children: ReactNode[];
  index: string;
};

export const EditableRow = <T extends Record<string, string>>({
  index,
  ...props
}: EditableRowProp<T>) => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  return <tr {...props} />;
};

/**
 *
 * @description 目前用于 Editable；可编辑表格cell
 */
export const EditableCell = <T extends Record<string, string>>(
  props: EditableCellProp<T>,
) => {
  const {
    editable,
    dataIndex,
    title,
    record,
    index,
    handleValueChange,
    children,
    placeholder,
    editChildren,
    ...restProps
  } = props;
  const inputRef = useRef<InputRef>(null);
  const [inputValue, setInputValue] = useState<string>();

  useEffect(() => {
    if (record && dataIndex) {
      setInputValue(record[dataIndex as string]);
    }
  }, [record, dataIndex]);

  const valueChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      setInputValue(event.target.value);
      handleValueChange({
        ...record,
        [dataIndex as string]: event.target.value,
      });
    },
    [handleValueChange, record, dataIndex],
  );

  const renderCell = editChildren || (
    <Input
      ref={inputRef}
      style={{ width: 240 }}
      placeholder={placeholder}
      value={inputValue}
      onChange={valueChange}
    />
  );
  return <td {...restProps}>{editable ? renderCell : children}</td>;
};
