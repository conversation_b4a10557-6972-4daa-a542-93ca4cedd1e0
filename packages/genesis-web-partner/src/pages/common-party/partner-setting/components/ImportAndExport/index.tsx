import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, message } from 'antd';
import type { AnyObject } from 'antd/es/_util/type';

import { uniq } from 'lodash-es';

import type { ChannelDetail } from 'genesis-web-service';
import type { BizExportParams } from 'genesis-web-service/lib/data-mgmt-service/data-mgmt-interface';
import { DMSService } from 'genesis-web-service/lib/data-mgmt-service/data-mgmt-service.service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import DataExport from '@/assets/data-export.svg';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting-v2/hooks/usePartnerSettingPermission';
import { ImportButton } from '@/pages/common-party/partner-setting/components/ImportButton';
import type { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { ChannelTypeCodeMap } from '@/pages/common-party/partner-setting/models/index.interface';

export const MAX_EXPORT_COUNT = 100;

export const useRowSelection = ({
  channelType,
  exportCodes,
  setExportCodes,
  channelList,
}: {
  channelType: ChannelTypeEnum;
  exportCodes: BizExportParams[];
  setExportCodes: React.Dispatch<React.SetStateAction<BizExportParams[]>>;
  channelList: ChannelDetail[];
}) => {
  const { t } = useTranslation('partner');

  useEffect(() => {
    setExportCodes(undefined);
  }, [channelList]);

  return exportCodes
    ? {
        selectedRowKeys: exportCodes?.map(item => item.identity),
        onChange: (selectedKeys: string[], selectedRows: AnyObject[]) => {
          if (selectedRows.length === 0) {
            // 取消全选
            setExportCodes([]);
            return;
          }

          const tempKeys = uniq([
            ...exportCodes
              .filter(item => !channelList?.some(record => record.code === item.identity))
              ?.map(item => item.identity),
            ...selectedKeys,
          ]);

          if (tempKeys.length > MAX_EXPORT_COUNT) {
            message.warning(t('The maximum number of exports is {{max}}', { max: MAX_EXPORT_COUNT }));
            return;
          }

          setExportCodes(
            tempKeys.map(code => ({
              identity: code,
              modelCode: 'Channel',
              version: ChannelTypeCodeMap[channelType as unknown as keyof typeof ChannelTypeCodeMap],
            }))
          );
        },
      }
    : undefined;
};

export const ImportAndExport = ({
  addBtn,
  exportCodes,
  setExportCodes,
  exportLoading,
  setExportLoading,
  reload,
}: {
  addBtn: React.ReactNode;
  exportCodes: BizExportParams[];
  setExportCodes: React.Dispatch<React.SetStateAction<BizExportParams[]>>;
  exportLoading: boolean;
  setExportLoading: React.Dispatch<React.SetStateAction<boolean>>;
  reload: () => void;
}) => {
  const { t } = useTranslation('partner');
  const { canEdit } = usePartnerSettingPermission();

  const handelExport = () => {
    setExportLoading(true);

    DMSService.bizExport(exportCodes)
      .then(downloadFile)
      .catch((error: Error) => {
        message.error(error?.message);
      })
      .finally(() => {
        setExportLoading(false);
        setExportCodes(undefined);
      });
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
      {exportCodes ? (
        <div>
          <Button
            loading={exportLoading}
            onClick={handelExport}
            disabled={!exportCodes.length}
          >{`${t('Export')} (${exportCodes.length}) `}</Button>
          <Button onClick={() => setExportCodes(undefined)} style={{ marginLeft: 8 }}>
            {t('Cancel')}
          </Button>
        </div>
      ) : (
        <>
          {addBtn}
          <div>
            <Button icon={<DataExport />} disabled={!canEdit} onClick={() => setExportCodes([])}>
              {t('Export')}
            </Button>

            <ImportButton
              buttonProps={{
                disabled: !canEdit,
              }}
              reload={reload}
            />
          </div>
        </>
      )}
    </div>
  );
};
