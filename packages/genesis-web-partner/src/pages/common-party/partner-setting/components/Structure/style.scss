.structure {
  .entry {
    width: 280px;
    text-align: center;
    font-weight: 700;
    font-size: 16px;
    color: var(--text-color);
  }
  .node-box {
    width: 280px;
    height: 60px;
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-base);
    text-align: center;
    line-height: 60px;
  }
  .center-node {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
  }
  .child-node {
    margin-bottom: 0;
    border-radius: 0;
    border-bottom: none;
    color: var(--text-color-tertiary);
  }
  .more-count-box {
    border: 1px dashed var(--border-light);
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    cursor: pointer;
  }
  .hover-box {
    background-color: var(--item-bg-selected);
    border: 1px solid var(--item-bg-selected);
    color: var(--text-color);
  }
  .child-node:last-child {
    border-bottom: 1px solid var(--border-light);
  }
  .line {
    width: 280px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 4px;
    > div {
      height: 21px;
      border-left: 1.5px solid var(--border-gray2);
    }
    > span {
      margin-top: -7px;
      font-size: 16px;
      color: var(--border-gray2);
    }
  }
}
