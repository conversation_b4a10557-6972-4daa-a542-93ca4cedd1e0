import type { FC, ReactNode } from 'react';
import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DownOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons';
import { Button, Divider, Skeleton, message } from 'antd';

import cls from 'clsx';

import { ChannelService, DownloadOrUploadType } from 'genesis-web-service';
import type { StructureDetail } from 'genesis-web-service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import type { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { ChannelTitleMap, MaxShownCount, OnceShownCount } from '@/pages/common-party/utils/constants';

import styles from './style.scss';

interface StructureProps {
  channelId: number;
  channelType: ChannelTypeEnum;
  visible?: boolean;
}

/**
 *
 * @param channelId 当前查看的channelId
 * @param channelType 当前所属channelType
 * @param  visible 是否可见
 * @description 树状结构展示
 */
export const Structure: FC<StructureProps> = ({ channelId, channelType, visible = true }) => {
  const { t } = useTranslation('partner');
  const [totalChildren, setTotalChildren] = useState(0); // 当前节点总子节点数
  const [shownCount, setShownCount] = useState(OnceShownCount); // 当前展示的子节点数
  const [moreHoverVisible, setMoreHoverVisible] = useState(false);
  const [downloadHoverVisible, setDownloadHoverVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [structureDetail, setStructureDetail] = useState<StructureDetail[]>();

  useEffect(() => {
    if (visible) {
      // 为了解决删除相关节点后再view structure还是旧数据，故表格处的view structure每次都重新查询数据（通过visible控制）
      setShownCount(OnceShownCount);
      setLoading(true);
      ChannelService.getStructure(channelId, {
        type: channelType,
      })
        .then(setStructureDetail)
        .catch(error => message.error(error?.message))
        .finally(() => setLoading(false));
    }
  }, [channelId, channelType, visible]);

  const moreCountBtnVisible = useMemo(
    () => shownCount < totalChildren && shownCount < MaxShownCount,
    [totalChildren, shownCount]
  );

  const downloadBtnVisible = useMemo(
    () => totalChildren > MaxShownCount && shownCount >= MaxShownCount,
    [totalChildren, shownCount]
  );

  const structureContent = useMemo(() => {
    const createStructure = (channels: StructureDetail[], isSubChannel?: boolean): ReactNode => {
      return channels?.map((item, index) => {
        if (item.id == channelId) {
          // 找到当前节点，获取子节点总数
          setTotalChildren(item.childNum);
        }
        return (
          index < shownCount && (
            <Fragment key={item.id}>
              <div
                className={cls(
                  styles.nodeBox,
                  item.id == channelId && styles.centerNode,
                  isSubChannel && styles.childNode
                )}
                key={item.id}
              >
                {item.name}
              </div>
              {!isSubChannel && item?.childList?.length > 0 && (
                <div className={styles.line}>
                  <Divider type="vertical" />
                  <DownOutlined />
                </div>
              )}
              {item?.childList?.length > 0 && createStructure(item?.childList, item.id == channelId)}
            </Fragment>
          )
        );
      });
    };
    return (
      structureDetail && (
        <>
          <div className={styles.entry} key={channelType}>
            {ChannelTitleMap[channelType]}
          </div>
          <div className={styles.line}>
            <Divider type="vertical" />
            <DownOutlined />
          </div>
          {createStructure(structureDetail)}
        </>
      )
    );
  }, [channelId, structureDetail, shownCount, channelType]);

  const download = useCallback(() => {
    setDownloading(true);
    ChannelService.download({
      type: DownloadOrUploadType.CHANNEL_TREE,
      id: channelId,
    })
      .then(downloadFile)
      .catch((error: Error) => message.error(error?.message))
      .finally(() => setDownloading(false));
  }, [channelId, channelType]);

  return (
    <Skeleton loading={loading} active>
      <div className={styles.structure}>
        {structureContent}
        {moreCountBtnVisible && (
          <div
            className={cls(styles.nodeBox, styles.moreCountBox, moreHoverVisible && styles.hoverBox)}
            onClick={() => setShownCount(shownCount + OnceShownCount)}
            onMouseEnter={() => setMoreHoverVisible(true)}
            onMouseLeave={() => setMoreHoverVisible(false)}
          >
            {moreHoverVisible ? <EyeOutlined /> : t('More Partners below', { count: t('50') })}
          </div>
        )}
        {downloadBtnVisible && (
          <Button
            className={cls(styles.nodeBox, styles.moreCountBox, downloadHoverVisible && styles.hoverBox)}
            onClick={download}
            onMouseEnter={() => setDownloadHoverVisible(true)}
            onMouseLeave={() => setDownloadHoverVisible(false)}
            loading={downloading}
            icon={downloadHoverVisible && <DownloadOutlined />}
          >
            {downloadHoverVisible ? t('Download to View Details') : t('More Partners below', { count: t('500+') })}
          </Button>
        )}
      </div>
    </Skeleton>
  );
};
