import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import type { ColumnProps, TableProps } from 'antd/es/table';

import { Table } from '@zhongan/nagrand-ui';

import type { ChannelDetail } from 'genesis-web-service';

import { TableActionEllipsis } from '@/components/TableActionEllipsis';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import { MaxLevel } from '@/pages/common-party/utils/constants';

interface TableProp {
  loading: boolean;
  data: ChannelDetail[];
  viewDetail: (channelId: number) => void;
  handleDelete: (channelId: number) => void;
  addBank: (parentId: number, level: number) => void;
  viewStructure: (channelId: number) => void;
}

/**
 *
 * @param loading 是否在加载
 * @param data 表格数据
 * @param viewDetail 跳转method
 * @param handleDelete delete method
 * @param addBank add bank method
 * @param viewStructure view structure method
 * @description 用于partner setting agencyCompany/salesChannel/serviceCompany menu component； 数据展示
 */
export const CommonTable = ({
  loading,
  data,
  viewDetail,
  handleDelete,
  addBank,
  viewStructure,
  ...rest
}: TableProp & TableProps) => {
  const { t } = useTranslation('partner');
  const { canEdit } = usePartnerSettingPermission();

  const tableActionMenu = useCallback(
    (channelItem: ChannelDetail) => {
      const menu = [
        {
          title: t('Add New at the Same Level'),
          key: 'addSameLevel',
          disabled: !canEdit,
          onClick: () => addBank(channelItem.parentId, +channelItem.level),
        },
        {
          title: t('View Structure'),
          key: 'viewStructure',
          onClick: () => viewStructure(channelItem.id),
        },
        {
          title: t('Delete'),
          key: 'delete',
          disabled: !canEdit,
          onClick: () => handleDelete(channelItem.id),
        },
      ];
      if (+channelItem.level < MaxLevel) {
        menu.splice(1, 0, {
          title: t('Add New at the Sub Level'),
          key: 'addSubLevel',
          disabled: !canEdit,
          onClick: () => addBank(channelItem.id, +channelItem.level + 1),
        });
      }
      return menu;
    },
    [canEdit, addBank, viewStructure, handleDelete]
  );

  const columns: ColumnProps<ChannelDetail>[] = useMemo(
    () => [
      {
        title: t('Bank Code'),
        fixed: 'left',
        render: (item: ChannelDetail) => <a onClick={() => viewDetail(item.id)}>{item.code}</a>,
      },
      {
        title: t('Bank Name'),
        dataIndex: 'name',
      },
      {
        title: t('Email'),
        dataIndex: 'email',
      },
      {
        title: t('Phone No.'),
        dataIndex: 'hotLine',
      },
      {
        title: t('Actions'),
        fixed: 'right',
        width: 64,
        align: 'center',
        render: (item: ChannelDetail) => <TableActionEllipsis menuItems={tableActionMenu(item)} />,
      },
    ],
    [viewDetail, t, tableActionMenu]
  );

  return (
    <Table
      scroll={{ x: 'max-content' }}
      emptyType="icon"
      loading={loading}
      dataSource={data}
      columns={columns}
      rowKey="id"
      pagination={false}
      style={{ marginBottom: 16 }}
      {...rest}
    />
  );
};
