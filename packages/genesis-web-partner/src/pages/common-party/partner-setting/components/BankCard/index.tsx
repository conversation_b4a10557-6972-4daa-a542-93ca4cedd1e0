import { Fragment, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { But<PERSON>, Divider, Drawer, Form, Modal, message } from 'antd';
import type { AnyObject } from 'antd/es/_util/type';
import type { PaginationProps } from 'antd/es/pagination';
import type { TableRowSelection } from 'antd/es/table/interface';
import qs from 'qs';

import type { FieldDataType } from '@zhongan/nagrand-ui';
import { Icon, QueryForm } from '@zhongan/nagrand-ui';

import type { ChannelDetail } from 'genesis-web-service';
import { BankType, ChannelService } from 'genesis-web-service';
import type { BizExportParams } from 'genesis-web-service/lib/data-mgmt-service/data-mgmt-interface';

import { PaginationComponent } from '@/components/Pagination';
import { ImportAndExport } from '@/pages/common-party/partner-setting/components/ImportAndExport';
import { useRowSelection } from '@/pages/common-party/partner-setting/components/ImportAndExport/index';
import { Structure } from '@/pages/common-party/partner-setting/components/Structure';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import type { ChannelRouteState, ChannelSearchForm } from '@/pages/common-party/partner-setting/models/index.interface';
import { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { PageSizeOptions } from '@/pages/common-party/utils/constants';
import { ModeEnum } from '@/types/common';
import { useLocation } from '@umijs/max';

import style from '../../style.scss';
import { CommonTable } from './Table';

/**
 *
 * @description partner setting bank menu component
 */
export const BankCard = () => {
  const { t } = useTranslation('partner');
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { state } = useLocation() as {
    state: ChannelRouteState<ChannelSearchForm>;
  };

  const { canEdit } = usePartnerSettingPermission();

  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState<number>();
  const [channelList, setChannelList] = useState<ChannelDetail[]>([]);
  const [pagination, setPagination] = useState<PaginationProps>();
  const [structureVisible, setStructureVisible] = useState(false);
  const [selectedBankId, setSelectedBankId] = useState<number>();
  const paginationRef = useRef<PaginationProps>(); // 用于同步获取pagination，setState是异步更新，setPagination后立即获取的pagination是旧值

  const [exportCodes, setExportCodes] = useState<BizExportParams[]>();
  const [exportLoading, setExportLoading] = useState(false);
  const rowSelection = useRowSelection({ channelType: ChannelTypeEnum.Bank, exportCodes, setExportCodes });

  // 若将handleSearch作为useEffect依赖，handleSearch依赖pagination变化时自动调用，则初始化时会调用两遍（一次是channelType, 一次是pagination）
  // 故在需要的地方手动调用
  const handleSearch = useCallback(() => {
    form.validateFields().then(values => {
      setLoading(true);
      const searchValues = { ...values };
      Object.keys(searchValues).forEach(key => {
        if (searchValues[key] === '') {
          searchValues[key] = undefined;
        }
      });
      ChannelService.getChannelList({
        pageIndex: paginationRef.current?.current - 1,
        pageSize: paginationRef.current?.pageSize,
        type: BankType.Bank,
        ...searchValues,
      })
        .then(res => {
          setChannelList(res.data);
          setTotal(res.totalElements);
        })
        .catch(error => message.error(error?.message))
        .finally(() => setLoading(false));
    });
  }, []);

  // 初始化
  useEffect(() => {
    // Todo: state不是bank？？需要调查一下这个哪里来的
    // 切换menu，只有channelType匹配才会使用state初始化表单和页码信息
    if (state?.channelType === ChannelTypeEnum.Bank) {
      form.setFieldsValue(state?.searchForm);
      paginationRef.current = state?.pagination;
    } else {
      form.resetFields();
      paginationRef.current = { current: 1, pageSize: 10 };
    }
    setPagination(paginationRef.current);
    handleSearch();
  }, [form, state]);

  const handleSearchFieldChange = useCallback(() => {
    paginationRef.current = { current: 1, pageSize: 10 };
    setPagination(paginationRef.current);
    handleSearch();
  }, [handleSearch]);

  const handlePaginationChange = useCallback(
    (current, pageSize) => {
      paginationRef.current = { current, pageSize };
      setPagination(paginationRef.current);
      handleSearch();
    },
    [handleSearch]
  );

  const addBank = useCallback(
    (parentId: number, level: number) => {
      navigate(
        `/bank?${qs.stringify({
          parentId: parentId && String(parentId),
          level: String(level),
          type: ChannelTypeEnum.Bank,
          modeType: ModeEnum.ADD,
        })}`,
        {
          state: {
            pagination: pagination,
            searchForm: form.getFieldsValue(),
            channelType: ChannelTypeEnum.Bank,
          },
        }
      );
    },
    [pagination, form]
  );

  const viewDetail = useCallback(
    (bankId: number) => {
      navigate(
        `/bank?${qs.stringify({
          id: String(bankId),
          type: ChannelTypeEnum.Bank,
          modeType: ModeEnum.READ,
        })}`,
        {
          state: {
            pagination: pagination,
            searchForm: form.getFieldsValue(),
            channelType: ChannelTypeEnum.Bank,
          },
        }
      );
    },
    [pagination, form]
  );

  const deleteRecord = useCallback(
    (id: number) => {
      Modal.confirm({
        title: t('Delete'),
        className: style.deleteModal,
        content: <p>{t('Are you sure to delete this bank and all relevant information?')}</p>,
        onOk: () => {
          Modal.destroyAll();
          setLoading(true);
          ChannelService.deleteChannel(id)
            .then(() => {
              message.success(t('Delete successfully'));
              handleSearchFieldChange();
            })
            .catch((error: Error) => {
              message.error(error?.message);
              // 删除后要重新请求数据，故不能放在finally里统一置为false
              setLoading(false);
            });
        },
      });
    },
    [handleSearchFieldChange]
  );

  const viewStructure = useCallback((bankId: number) => {
    setStructureVisible(true);
    setSelectedBankId(bankId);
  }, []);

  const fields: FieldDataType[] = useMemo(
    () => [
      {
        key: 'name',
        label: t('Bank Name'),
      },
      {
        key: 'code',
        label: t('Bank Code'),
      },
    ],
    [t]
  );

  return (
    <Fragment>
      <QueryForm queryFields={fields} onSearch={handleSearchFieldChange} formProps={{ form }} />
      <Divider dashed />

      <ImportAndExport
        addBtn={
          <Button icon={<Icon type="add" />} type="primary" disabled={!canEdit} onClick={() => addBank(null, 1)}>
            {t('Add New')}
          </Button>
        }
        exportCodes={exportCodes}
        setExportCodes={setExportCodes}
        exportLoading={exportLoading}
        setExportLoading={setExportLoading}
      />

      <CommonTable
        loading={loading}
        data={channelList}
        viewDetail={viewDetail}
        handleDelete={deleteRecord}
        addBank={addBank}
        viewStructure={viewStructure}
        rowSelection={rowSelection as unknown as TableRowSelection<AnyObject>}
        rowKey="code"
      />
      <PaginationComponent
        pagination={pagination}
        total={total}
        pageSizeOptions={PageSizeOptions}
        handlePaginationChange={handlePaginationChange}
      />
      <Drawer
        title={t('Structure Details')}
        open={structureVisible}
        maskClosable={false}
        width={641}
        rootClassName={style.structureDrawer}
        onClose={() => setStructureVisible(false)}
      >
        <Structure channelId={selectedBankId} channelType={ChannelTypeEnum.Bank} visible={structureVisible} />
      </Drawer>
    </Fragment>
  );
};
