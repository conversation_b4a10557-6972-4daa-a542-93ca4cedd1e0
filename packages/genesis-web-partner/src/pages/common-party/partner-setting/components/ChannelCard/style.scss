@import '../../../../../variables.scss';
.card-list {
  display: flex;
  flex-direction: row;
  overflow-x: auto;
  .card {
    position: relative;
    overflow: hidden;
    width: 320px;
    min-width: 320px;
    min-height: 162px;
    box-sizing: border-box;
    border: 1px solid var(--border-light);
    border-radius: 16px;
    margin-right: 16px;
    padding: 16px;
    color: var(--text-color);
    .card-footer {
      display: none;
      justify-content: space-between;
      align-items: center;
      background: var(--card-bg-hover);
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 40px;

      > button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 150px;
        height: 40px;
        cursor: pointer;
      }
    }

    &:hover {
      border-color: var(--primary-color);
      .card-footer {
        display: flex;
      }
    }
  }

  .add-card {
    text-align: center;
    cursor: pointer;
    background-color: var(--table-header-bg);
    border: 1px dashed var(--disabled-color);
    border-radius: 16px;
    > span {
      font-size: 80px;
    }
    p {
      font-size: 12px;
      line-height: 20px;
      margin-bottom: 0;
    }
  }

  .name {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .card-top {
    height: 22px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    position: relative;
    .type {
      position: absolute;
      right: 0;
      min-width: 54px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      border-radius: 50vh;
      background-color: var(--primary-color-5-percent);
      color: var(--primary-color);
      font-size: 12px;
      padding: 0 5px;
    }

    .name {
      font-size: 16px;
      max-width: 196px;
    }

    img {
      width: 15px;
      height: 16px;
      line-height: 22px;
      margin-left: 10px;
    }
  }

  .location {
    font-size: 14px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    > span {
      margin-right: 8px;
      color: var(--text-color-tertiary);
    }
    &.gray {
      color: var(--text-color-tertiary);
    }
  }

  .agreement {
    background-image: url(~@/assets/agreement.svg);
    background-repeat: no-repeat;
    background-position: 90% bottom;
    .name {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      width: 300px;
      margin-bottom: 8px;
    }
    .desc {
      color: $label;
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      margin-bottom: 8px;
      text-overflow: ellipsis;
      overflow: auto;
    }
  }

  .address {
    background-image: url(~@/assets/icon_card_building.svg);
    background-repeat: no-repeat;
    background-position: 95% bottom;
    .name {
      font-weight: bold;
      font-size: 16px;
      width: 300px;
    }
    .desc {
      margin-bottom: 16px;
      text-overflow: ellipsis;
      overflow: auto;
    }
  }

  .account {
    background-image: url(~@/assets/card_bank.png);
    background-repeat: no-repeat;

    .card-top {
      margin-bottom: 3px;

      .name {
        font-size: 14px;
        font-weight: bold;
      }
    }
    .sub-name {
      text-overflow: ellipsis;
      overflow: auto;
      word-wrap: break-all;
    }
    .branch {
      margin-top: 30px;
      p {
        margin-bottom: 0;
        line-height: 1.5;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 16px;
        font-weight: bold;
      }
      span {
        font-size: 14px;
      }
    }
  }
}

.card-disabled {
  .card {
    .card-footer {
      > span {
        cursor: not-allowed;
      }
    }
  }
  .add-card {
    cursor: not-allowed;
    background-color: var(--disabled-bg);
    &:hover {
      background-color: var(--disabled-bg);
      border-color: var(--disabled-color);
    }
  }
}

.agreement-title {
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
}

.link-button {
  padding-left: 0;
  padding-bottom: 4px;
  color: var(--primary-color) !important;
  margin-bottom: 20px;
  span {
    text-decoration: underline;
  }
}
