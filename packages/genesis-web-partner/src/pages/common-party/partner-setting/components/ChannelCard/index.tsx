/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { Fragment } from 'react';
import { useTranslation } from 'react-i18next';

import { But<PERSON>, Divider } from 'antd';
import cls from 'clsx';

import { NoData } from 'genesis-web-component/lib/components/NoData';

import Icon_addInfo from '@/assets/addInfo.svg';
import { ModeEnum } from '@/types/common';
import { Icon } from '@ant-design/compatible';
import { DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';

import commonStyles from '../../style.scss';
import styles from './style.scss';

interface Props<T> {
  readonly?: boolean;
  mode: ModeEnum;
  shownList: T[];
  className?: string;
  addText: string;
  cardContent: (item: T, index: number) => JSX.Element;
  addClick: () => void;
  editClick: (item: T, index: number) => void;
  deleteClick: (index: number) => void;
  deleteBtnVisible?: (item: T) => boolean;
}

/**
 *
 * @param readonly 是否只读
 * @param shownList 展示列表
 * @param className 自定义className
 * @param addText add card文案
 * @param cardContent card渲染内容
 * @param addClick click add callback
 * @param editClick click edit callback
 * @param deleteClick click delete callback
 * @param deleteBtnVisible  delete button显示隐藏控制，优先使用这个，没有的话使用id
 * @description 目前AccountInfo、Address、ContactPerson用到；card展示公共组件
 */
export const ChannelCard = <T extends Record<string, any>>({
  readonly,
  mode,
  shownList,
  className,
  addText,
  cardContent,
  addClick,
  editClick,
  deleteBtnVisible,
  deleteClick,
}: Props<T>) => {
  const { t } = useTranslation(['partner']);

  return (
    <div className={cls(styles.cardList, readonly && styles.cardDisabled)}>
      {shownList?.map((item, index) => (
        <div className={cls(styles.card, className)} key={item?.id || item?.localTempKey}>
          {cardContent(item, index)}
          <div
            className={styles.cardFooter}
            style={{
              justifyContent: !item.id && !readonly ? 'space-between' : 'center',
            }}
          >
            {readonly ? (
              <Button type="link" icon={<EyeOutlined />} onClick={() => editClick && editClick(item, index)} />
            ) : (
              <Fragment>
                <Button type="link" icon={<EditOutlined />} onClick={() => editClick && editClick(item, index)}>
                  {t('Edit')}
                </Button>
                {(deleteBtnVisible ? deleteBtnVisible(item) : !item.id) && (
                  <Fragment>
                    <Divider type="vertical" />
                    <Button type="link" icon={<DeleteOutlined />} onClick={() => deleteClick && deleteClick(index)}>
                      {t('Delete')}
                    </Button>
                  </Fragment>
                )}
              </Fragment>
            )}
          </div>
        </div>
      ))}
      {readonly && mode === ModeEnum.ADD ? (
        <NoData disabled={true} inTable={false} className={cls(styles.card, commonStyles.noData)} />
      ) : (
        <Button
          type="link"
          disabled={readonly}
          className={cls(styles.card, styles.addCard)}
          onClick={() => addClick && addClick()}
          icon={<Icon component={Icon_addInfo} />}
        >
          <p className={styles.top}>{addText}</p>
        </Button>
      )}
    </div>
  );
};
