import type { FC } from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DownloadOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';

import { useAntdTable } from 'ahooks';
import { Button, Form, Tooltip, Upload, message } from 'antd';
import type { PaginationProps } from 'antd/lib/pagination';
import type { ColumnProps } from 'antd/lib/table';
import moment from 'moment';

import { Table, TableActionsContainer } from '@zhongan/nagrand-ui';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import type { BizDictItem, ChannelStaff } from 'genesis-web-service';
import { ChannelService, DownloadOrUploadType, MetadataService, SalesTypeEnum } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { CommonForm } from '@/components/CommonForm/Form';
import { DeleteOutline, EditOutline, ViewSquareOutline } from '@/components/Icons';
import { PaginationComponent } from '@/components/Pagination';
import { SalesAgreement } from '@/pages/common-party/partner-setting/components/SalesAgreement';
import { useStaffFormFields } from '@/pages/common-party/partner-setting/hooks/useFormFields';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';
import type { ModeEnum } from '@/types/common';
import { DefaultTablePagination } from '@/utils/constants';

import {
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
  useSchemaFormItemFieldsByCategory,
} from '../../hooks/useChannelSchemaFieldsByCategory';
import styles from '../../style.scss';

/**
 * Sales Channel的staff信息
 * 目前用在Agency Company，Leasing Channel，Broker Company，Bank四个外部销售渠道中
 */
export const Staff: FC<{
  readonly: boolean;
  channelId: number;
  channelType: string;
  mode: ModeEnum;
}> = ({ channelId, readonly, mode, channelType }) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();

  const [visible, setVisible] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [editedStaff, setEditedStaff] = useState<ChannelStaff>();
  const [channelAgentCategoryEnums, setChannelAgentCategoryEnums] = useState<BizDictItem[]>();
  const fields = useStaffFormFields(form, readonly, editedStaff, channelAgentCategoryEnums);
  const { formItems: dynamicFields, schemaFields } = useSchemaFormItemFieldsByCategory({
    staticOrDynamic: 'DYNAMIC',
    category: 'STAFF',
    disabled: readonly,
  });
  const agreementRef = useRef(null);

  // 获取租户配置的 channelAgentCategory
  useEffect(() => {
    MetadataService.queryBizDictConfigList('channelAgentCategory').then(res => {
      const bizDictConfigList = res.tenantBizDictConfig?.channelAgentCategory?.bizDictConfigList || [];
      setChannelAgentCategoryEnums(bizDictConfigList);
    });
  }, []);

  const getTableData = useCallback(
    async ({ current, pageSize }: PaginationProps) => {
      if (channelId) {
        const staffs = await ChannelService.queryChannelStaffs(channelId, {
          pageIndex: current - 1,
          pageSize: pageSize,
        });
        return {
          total: staffs?.totalElements,
          list: staffs?.data,
        };
      } else {
        return {
          total: 0,
          list: [],
        };
      }
    },
    [channelId]
  );

  const { tableProps, search } = useAntdTable(getTableData, {
    onError: error => message.error(error?.message),
    refreshDeps: [channelId],
  });

  const onEdit = useCallback(
    staff => {
      setVisible(true);
      dealSchemaFieldValueForDisplay(staff?.extensions, schemaFields);
      const formattedStaffInfo = {
        ...staff,
        onboardDate: staff?.onboardDate && moment(staff.onboardDate),
        resignDate: staff?.resignDate && moment(staff.resignDate),
      };
      setEditedStaff(formattedStaffInfo);
      form.setFieldsValue(formattedStaffInfo);
    },
    [form, schemaFields]
  );

  const onDelete = useCallback(
    (staffId: number) => {
      return ChannelService.deleteChannelStaff(channelId, staffId)
        .then(() => {
          message.success(t('Delete successfully'));
          search.submit();
        })
        .catch((error: Error) => {
          message.error(error?.message);
        });
    },
    [channelId, search, t]
  );

  const onClose = useCallback(() => {
    form.resetFields();
    setVisible(false);
    setEditedStaff(undefined);
  }, [form]);

  const onSubmit = useCallback(() => {
    form.validateFields().then(values => {
      setSubmitting(true);
      let submitReq: Promise<void>;
      dealSchemaFieldValueForSubmit(values.extensions, schemaFields, { dateFormatInstance });
      if (editedStaff) {
        submitReq = ChannelService.updateChannelStaff(channelId, editedStaff.id, {
          channelId: channelId,
          channelCustomerId: editedStaff.channelCustomerId,
          isCreateAccount: editedStaff.isCreateAccount,
          ...values,
          onboardDate: values?.onboardDate && dateFormatInstance.formatDate(values.onboardDate),
          resignDate: values?.resignDate && dateFormatInstance.formatDate(values.resignDate),
        }).then(res => {
          message.success(t('Edited successfully'));
          agreementRef.current.saveAgreement(res.staffCode, res.id, channelType);
          onClose();
          search.submit();
        });
      } else {
        submitReq = ChannelService.addChannelStaff(channelId, {
          channelId: channelId,
          ...values,
          onboardDate: values?.onboardDate && dateFormatInstance.formatDate(values.onboardDate),
          resignDate: values?.resignDate && dateFormatInstance.formatDate(values.resignDate),
        }).then(res => {
          message.success(t('Add successfully'));
          agreementRef.current.saveAgreement(res.staffCode, res.id, channelType);
          onClose();
          search.submit();
        });
      }
      submitReq.catch((error: Error) => message.error(error?.message)).finally(() => setSubmitting(false));
    });
  }, [form, editedStaff, channelId, t, onClose, search, schemaFields]);

  const onDownload = useCallback(() => {
    setDownloading(true);
    ChannelService.download<{
      id: number;
      type: string;
    }>({
      id: channelId,
      type: DownloadOrUploadType.ChannelStaff,
    })
      .then(downloadFile)
      .finally(() => setDownloading(false));
  }, [channelId]);

  const uploadProps = useMemo(
    () =>
      getUploadPropsNew(
        `/api/channel/v2/file/upload/?id=${channelId}&type=${DownloadOrUploadType.ChannelStaff}`,
        () => search.submit(),
        setUploading
      ),
    [channelId, search]
  );

  const columns: ColumnProps<ChannelStaff>[] = useMemo(
    () => [
      {
        dataIndex: 'staffName',
        title: t('Staff Name'),
        fixed: 'left',
      },
      {
        title: t('Staff Code'),
        dataIndex: 'staffCode',
      },
      {
        title: t('E-mail'),
        dataIndex: 'email',
      },
      {
        title: t('Country Code'),
        dataIndex: 'countryCode',
      },
      {
        title: t('Mobile Phone'),
        dataIndex: 'phoneNo',
      },
      {
        title: t('Title'),
        dataIndex: 'title',
      },
      {
        title: t('Actions'),
        key: 'actions',
        fixed: 'right',
        align: 'right',
        width: 100,
        render: (_, staff) => (
          <TableActionsContainer>
            {readonly ? (
              <Tooltip title={t('View')}>
                <Button icon={<ViewSquareOutline />} type="link" onClick={() => onEdit(staff)} />
              </Tooltip>
            ) : (
              <>
                <DeleteConfirm onOk={() => onDelete(staff.id)}>
                  <Tooltip title={t('Delete')}>
                    <Button icon={<DeleteOutline />} disabled={readonly} type="link" />
                  </Tooltip>
                </DeleteConfirm>
                <Tooltip title={t('Edit')}>
                  <Button icon={<EditOutline />} type="link" onClick={() => onEdit(staff)} />
                </Tooltip>
              </>
            )}
          </TableActionsContainer>
        ),
      },
    ],
    [t, readonly, onEdit, onDelete]
  );

  const title = useMemo(() => {
    if (editedStaff) {
      if (readonly) {
        return t('View Staff information');
      }
      return t('Edit Staff information');
    }
    return t('Add New Staff');
  }, [editedStaff, readonly, t]);

  return (
    <div>
      <p className={styles.sectionTitle}>{t('Staff')}</p>
      {!readonly && (
        <div className="flex-between" style={{ marginTop: 16, marginBottom: 16 }}>
          <Button icon={<PlusOutlined />} onClick={() => setVisible(true)} disabled={readonly}>
            {t('Add New')}
          </Button>
          <div>
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />} disabled={readonly} loading={uploading} style={{ marginRight: 6 }}>
                {t('Upload')}
              </Button>
            </Upload>
            <Button icon={<DownloadOutlined />} onClick={onDownload} loading={downloading}>
              {t('Download')}
            </Button>
          </div>
        </div>
      )}
      <Table
        columns={columns}
        rowKey="id"
        scroll={{ x: 'max-content' }}
        bordered={false}
        style={{ marginBottom: 16 }}
        {...tableProps}
        pagination={false}
      />
      <PaginationComponent
        className="margin-top-16 margin-bottom-16"
        total={tableProps?.pagination?.total}
        pagination={tableProps?.pagination}
        handlePaginationChange={(current, pageSize) =>
          tableProps?.onChange({ ...tableProps?.pagination, current, pageSize })
        }
      />

      <DrawerForm
        width={1050}
        title={title}
        visible={visible}
        closable={false}
        maskClosable={false}
        onClose={onClose}
        onSubmit={onSubmit}
        cancelText={t('Cancel')}
        sendText={t('Confirm')}
        submitBtnShow={!readonly}
        submitBtnProps={{ loading: submitting }}
      >
        <CommonForm form={form} fields={[...fields, ...dynamicFields]} />
        <SalesAgreement
          ref={agreementRef}
          readonly={readonly}
          channelCode={editedStaff?.staffCode}
          mode={mode}
          salesType={SalesTypeEnum.STAFF}
          schemaCategory="STAFF_AGREEMENT"
        />
      </DrawerForm>
    </div>
  );
};
