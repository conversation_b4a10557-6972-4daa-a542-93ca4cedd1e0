import { useTranslation } from 'react-i18next';

import { Col, Form, Radio } from 'antd';

import type { BizDictItem } from 'genesis-web-service';
import { ChannelCustomerStatus, YesOrNo } from 'genesis-web-service';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';

interface Props {
  initialValue: any;
  disabled: boolean;
  name: string;
}

/**
 *
 * @param propForm
 * @param initialValue
 * @param disabled
 * @param relyOn
 * @description account模块
 */
export const UserAccount = ({ initialValue, disabled, name }: Props) => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  return (
    <Col span={17}>
      {initialValue?.accountStatus ? (
        <Form.Item
          initialValue={initialValue?.accountStatus || ChannelCustomerStatus.Effective}
          name="accountStatus"
          label={<b>{t('{{name}} Account Status', { name })}</b>}
        >
          <Radio.Group disabled={disabled}>
            <Radio value={ChannelCustomerStatus.Effective}>{t('Effective')}</Radio>
            <Radio value={ChannelCustomerStatus.Frozen}>{t('Frozen')}</Radio>
          </Radio.Group>
        </Form.Item>
      ) : (
        <div>
          <Form.Item
            initialValue={initialValue?.isCreateAccount || YesOrNo.NO}
            name="isCreateAccount"
            label={<b>{t('Whether to create {{name}} account?', { name })}</b>}
          >
            <Radio.Group disabled={disabled}>
              {enums?.yesNo?.map(option => (
                <Radio value={option.value} key={option.value}>
                  {option.label}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
        </div>
      )}
    </Col>
  );
};
