import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Input, Row } from 'antd';
import type { FormInstance } from 'antd/lib/form';

import { Select } from '@zhongan/nagrand-ui';

import { PaymentMethodForm } from 'genesis-web-component/lib/components/PaymentMethodFormV4';
import type { AccountItem, BizDictItem, SchemaDefField } from 'genesis-web-service';

import { FieldNode } from '@/components/CommonForm/FieldNode';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';

import { dealSchemaFieldValueForDisplay } from '../../hooks/useChannelSchemaFieldsByCategory';

const FormItemStyle = {
  width: '240px',
  marginRight: '100px',
};

interface Props {
  form: FormInstance;
  readonly?: boolean;
  initialValues: AccountItem;
  dynamicFields: Record<string, any>[];
  schemaFields: SchemaDefField[];
}

type PaymentMethod = AccountItem & {
  paymentMethod: string;
  cardHolderName: string;
};

/**
 *
 * @param form form实例
 * @param readonly 是否只读
 * @param initialValues 初始值
 *
 */
export const FormContent = ({ form, initialValues, readonly, dynamicFields, schemaFields }: Props) => {
  const { t } = useTranslation('partner');
  const channelOrgAccountTypes = useTenantBizDict('channelOrgAccountType') as BizDictItem[];
  const [paymentMethodData, setPaymentMethodData] = useState<PaymentMethod>();

  const channelOrgAccountTypeOptions = useMemo(
    () =>
      channelOrgAccountTypes?.map(item => ({
        label: item.label || item.dictDesc,
        value: item.value,
      })),
    [channelOrgAccountTypes]
  );

  useEffect(() => {
    form.setFieldsValue(initialValues);
    if (initialValues?.extensions) {
      dealSchemaFieldValueForDisplay(initialValues?.extensions, schemaFields);
    }
    setPaymentMethodData({
      ...initialValues,
      paymentMethod: initialValues?.payMethod,
      cardHolderName: initialValues?.userName,
    });
  }, [form, initialValues, schemaFields]);

  return useMemo(
    () => (
      <Form layout="vertical" form={form}>
        <PaymentMethodForm
          form={form}
          readonly={readonly}
          initialValues={paymentMethodData}
          payMethodLabel={t('Payment Method')}
        />
        <Row>
          <Form.Item name="useType" label={t('Account Usage')}>
            <Select
              placeholder={t('Please Select')}
              options={channelOrgAccountTypeOptions}
              allowClear
              showSearch
              disabled={readonly}
              style={FormItemStyle}
              getPopupContainer={triggerNode => triggerNode.parentNode}
            />
          </Form.Item>
          <Form.Item name="financialCode" label={t('SWIFT Code')}>
            <Input disabled={readonly} placeholder={t('Please Input')} style={FormItemStyle} />
          </Form.Item>
          {dynamicFields.map(({ key, type, col, offset, ctrlProps, customerDom, customerLoadingDom, ...formProp }) => (
            <Col key={key} span={col ?? 8} offset={offset ?? 0}>
              <Form.Item name={key} {...formProp}>
                <FieldNode
                  type={type}
                  {...ctrlProps}
                  onChange={value => {
                    ctrlProps?.onChange?.(value, form);
                  }}
                />
              </Form.Item>
            </Col>
          ))}
        </Row>
      </Form>
    ),
    [channelOrgAccountTypeOptions, form, paymentMethodData, readonly, t]
  );
};
