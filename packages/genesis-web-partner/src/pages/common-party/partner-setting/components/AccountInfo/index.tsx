/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';

import { cloneDeep } from 'lodash-es';

import { DeleteAction, EditAction, Table, ViewAction } from '@zhongan/nagrand-ui';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { AccountItem } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import type { SchemaProps } from '@/types/common';
import { transferSchemaToTableProp } from '@/utils/utils';

import {
  EntityType,
  covertChannelType2SchemaChannelType,
  dealSchemaFieldValueForSubmit,
  useGenericSchemaFormItemFields,
} from '../../../../../hooks/useGenericSchemaFormItemFields';
import { useEnumsMapping } from '../../hooks/useEnumsMapping';
import { ChannelTypeEnum } from '../../models/index.interface';
import { AddNewTitle } from '../AddNewTitle';
import styles from '../ChannelCard/style.scss';
import { FormContent } from './FormContent';

interface Props {
  initialList: AccountItem[];
  channelType: ChannelTypeEnum;
  disabled: boolean;
}

/**
 * @param initialList 初始化列表
 * @param readonly 是否只读
 * @description Account Information信息展示
 */
export const AccountInfo = forwardRef(({ initialList, channelType, disabled }: Props, ref) => {
  const { t } = useTranslation(['partner']);
  const [form] = Form.useForm();
  const accountTypesMapping = useEnumsMapping('accountType');
  const [visible, setVisible] = useState(false);
  const [editedIndex, setEditedIndex] = useState<number>();
  const [editedItem, setEditedItem] = useState<AccountItem>();
  const [shownList, setShownList] = useState<AccountItem[]>();
  const [readonly, setReadonly] = useState(false);

  const { formItems: dynamicFields, schemaFields } = useGenericSchemaFormItemFields({
    staticOrDynamic: 'DYNAMIC',
    category: 'ACCOUNT',
    disabled: readonly,
    type: covertChannelType2SchemaChannelType(channelType),
    entityType: channelType === ChannelTypeEnum.INSURANCE ? EntityType.CHANNEL_INSURANCE : EntityType.CHANNEL,
  });

  useEffect(() => {
    setShownList([...(initialList || [])]);
  }, [initialList]);

  useImperativeHandle(ref, () => {
    return shownList;
  });

  const editClick = useCallback((item: AccountItem, index: number) => {
    setVisible(true);
    setEditedIndex(index);
    setEditedItem(item);
    form.setFieldsValue(item);
  }, []);

  const deleteClick = useCallback(
    (index: number) => {
      const cloneList = cloneDeep(shownList || []);
      cloneList.splice(index, 1);
      setShownList(cloneList);
    },
    [shownList]
  );

  /* ----- 抽屉相关 func----- */
  const onClose = useCallback(() => {
    setVisible(false);
    setEditedIndex(null);
    setEditedItem(null);
    form.resetFields();
  }, [form]);

  const onSubmit = useCallback(() => {
    form.validateFields().then(values => {
      const { data: paymentMethodFormValue, ...restFormValue } = values ?? {};
      const { cardHolderName, paymentMethod, ...resetPaymentMethodFormValue } = paymentMethodFormValue ?? {};
      dealSchemaFieldValueForSubmit(values?.extensions, schemaFields, { dateFormatInstance });
      const result = {
        ...restFormValue,
        ...resetPaymentMethodFormValue,
        userName: cardHolderName,
        payMethod: paymentMethod,
        id: editedItem?.id,
        extensions: values.extensions,
      };
      const cloneList = cloneDeep(shownList || []);
      if (editedItem) {
        cloneList.splice(editedIndex, 1, result);
      } else {
        cloneList.push(result);
      }
      setShownList(cloneList);
      onClose();
    });
  }, [shownList, form, editedItem, editedIndex, onClose, schemaFields]);

  const title = useMemo(() => {
    if (readonly) {
      return t('View Account Information Settings');
    } else if (editedItem) {
      return t('Edit Account Information Settings');
    } else {
      return t('Add Account Information Settings');
    }
  }, [readonly, editedItem, t]);

  return (
    <section className={styles.addressInfo}>
      <AddNewTitle
        title={t('Account')}
        onAddClick={() => {
          setVisible(true);
          setReadonly(false);
        }}
        readonly={disabled}
      />
      <Table
        columns={[
          {
            title: t('Payment Method / Account Type'),
            dataIndex: 'accountType',
            render: text => accountTypesMapping[text],
          },
          {
            title: t('Bank Name'),
            dataIndex: 'bankName',
          },
          {
            title: t('Bank Code'),
            dataIndex: 'bankCode',
          },
          {
            title: t('Account Name'),
            dataIndex: 'userName',
          },
          {
            title: t('Branch'),
            dataIndex: 'branch',
          },
          {
            title: t('Bank Branch Name'),
            dataIndex: 'bankBranchName',
          },
          {
            title: t('Bank Branch Code'),
            dataIndex: 'bankBranchCode',
          },
          {
            title: t('Account No.'),
            dataIndex: 'cardNumber',
          },
          ...transferSchemaToTableProp([...dynamicFields] as SchemaProps[]),
          {
            title: t('Action'),
            render: (_, record, index) => (
              <>
                <ViewAction
                  onClick={() => {
                    editClick(record, index);
                    setReadonly(true);
                  }}
                />
                <EditAction
                  onClick={() => {
                    editClick(record, index);
                    setReadonly(false);
                  }}
                  disabled={disabled}
                />
                <DeleteAction
                  deleteConfirmContent={t('Are you sure to delete?')}
                  onClick={() => deleteClick(index)}
                  disabled={disabled}
                />
              </>
            ),
          },
        ]}
        dataSource={shownList}
        pagination={false}
        scroll={{ x: 'max-content' }}
      />

      <DrawerForm
        title={title}
        visible={visible}
        closable={false}
        onClose={onClose}
        onSubmit={onSubmit}
        bodyStyle={{ width: 1070 }}
        cancelText={t('Cancel')}
        sendText={t('Submit')}
        submitBtnShow={!readonly}
      >
        <FormContent
          form={form}
          initialValues={editedItem}
          schemaFields={schemaFields}
          dynamicFields={dynamicFields}
          readonly={readonly}
        />
      </DrawerForm>
    </section>
  );
});
