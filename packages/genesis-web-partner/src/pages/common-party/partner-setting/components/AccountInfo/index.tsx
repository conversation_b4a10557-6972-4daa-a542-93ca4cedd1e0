/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { Fragment, forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';
import { cloneDeep } from 'lodash-es';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { AccountItem } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { useEnumsMapping } from '@/pages/common-party/partner-setting/hooks/useEnumsMapping';
import type { ModeEnum } from '@/types/common';

import {
  covertChannelType2SchemaChannelType,
  dealSchemaFieldValueForSubmit,
  useSchemaFormItemFieldsByCategory,
} from '../../hooks/useChannelSchemaFieldsByCategory';
import type { ChannelTypeEnum } from '../../models/index.interface';
import CommonStyles from '../../style.scss';
import { ChannelCard } from '../ChannelCard';
import styles from '../ChannelCard/style.scss';
import { FormContent } from './FormContent';

interface Props {
  readonly: boolean;
  initialList: AccountItem[];
  mode: ModeEnum;
  channelType: ChannelTypeEnum;
}

/**
 * @param initialList 初始化列表
 * @param readonly 是否只读
 * @description Account Information信息展示
 */
export const AccountInfo = forwardRef(({ initialList, readonly, mode, channelType }: Props, ref) => {
  const { t } = useTranslation(['partner']);
  const [form] = Form.useForm();
  const accountTypesMapping = useEnumsMapping('accountType');
  const [visible, setVisible] = useState(false);
  const [editedIndex, setEditedIndex] = useState<number>();
  const [editedItem, setEditedItem] = useState<AccountItem>();
  const [shownList, setShownList] = useState<AccountItem[]>();

  const { formItems: dynamicFields, schemaFields } = useSchemaFormItemFieldsByCategory({
    staticOrDynamic: 'DYNAMIC',
    category: 'ACCOUNT',
    disabled: readonly,
    channelType: covertChannelType2SchemaChannelType(channelType),
  });

  useEffect(() => {
    setShownList([...(initialList || [])]);
  }, [initialList]);

  useImperativeHandle(ref, () => {
    return shownList;
  });

  const editClick = useCallback((item: AccountItem, index: number) => {
    setVisible(true);
    setEditedIndex(index);
    setEditedItem(item);
    form.setFieldsValue(item);
  }, []);

  const deleteClick = useCallback(
    (index: number) => {
      const cloneList = cloneDeep(shownList || []);
      cloneList.splice(index, 1);
      setShownList(cloneList);
    },
    [shownList]
  );

  /* ----- 抽屉相关 func----- */
  const onClose = useCallback(() => {
    setVisible(false);
    setEditedIndex(null);
    setEditedItem(null);
    form.resetFields();
  }, [form]);

  const onSubmit = useCallback(() => {
    form.validateFields().then(values => {
      const { data: paymentMethodFormValue, ...restFormValue } = values ?? {};
      const { cardHolderName, paymentMethod, ...resetPaymentMethodFormValue } = paymentMethodFormValue ?? {};
      dealSchemaFieldValueForSubmit(values?.extensions, schemaFields, { dateFormatInstance });
      const result = {
        ...restFormValue,
        ...resetPaymentMethodFormValue,
        userName: cardHolderName,
        payMethod: paymentMethod,
        id: editedItem?.id,
        extensions: values.extensions,
      };
      const cloneList = cloneDeep(shownList || []);
      if (editedItem) {
        cloneList.splice(editedIndex, 1, result);
      } else {
        cloneList.push(result);
      }
      setShownList(cloneList);
      onClose();
    });
  }, [shownList, form, editedItem, editedIndex, onClose, schemaFields]);

  const cardContent = useCallback(
    (item: AccountItem) => {
      return (
        <Fragment>
          <div className={styles.cardTop}>
            <span className={styles.type}>{accountTypesMapping[item.accountType]}</span>
            <span className={styles.name}>{item.bankName || item.bankCode}</span>
          </div>
          <span className={styles.subName}>{item.userName}</span>
          <div className={styles.branch}>
            <p>{item.bankBranchName || item.bankBranchCode}</p>
            <span>{item.cardNumber}</span>
          </div>
        </Fragment>
      );
    },
    [accountTypesMapping]
  );

  const title = useMemo(() => {
    if (readonly) {
      return t('View Account Information Settings');
    } else if (editedItem) {
      return t('Edit Account Information Settings');
    } else {
      return t('Add Account Information Settings');
    }
  }, [readonly, editedItem, t]);

  return (
    <section className={styles.addressInfo}>
      <p className={CommonStyles.sectionTitle}>{t('Account Information Settings')}</p>
      <ChannelCard
        shownList={shownList}
        readonly={readonly}
        mode={mode}
        addText={t('Add New Bank Account')}
        className={styles.account}
        cardContent={cardContent}
        editClick={editClick}
        deleteClick={deleteClick}
        addClick={() => setVisible(true)}
      />
      <DrawerForm
        title={title}
        visible={visible}
        closable={false}
        onClose={onClose}
        onSubmit={onSubmit}
        bodyStyle={{ width: 1070 }}
        cancelText={t('Cancel')}
        sendText={t('Submit')}
        submitBtnShow={!readonly}
      >
        <FormContent
          form={form}
          initialValues={editedItem}
          schemaFields={schemaFields}
          dynamicFields={dynamicFields}
          readonly={readonly}
        />
      </DrawerForm>
    </section>
  );
});
