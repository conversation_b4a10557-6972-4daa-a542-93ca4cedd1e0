import style from '../../style.scss';
import { CommonTable } from './Table';
import { PaginationComponent } from '@/components/Pagination';
import { Structure } from '@/pages/common-party/partner-setting/components/Structure';
import type {
  ChannelRouteState,
  ChannelSearchForm,
} from '@/pages/common-party/partner-setting/models/index.interface';
import type { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import {
  PageSizeOptions,
  CodeColumnTitle,
  NameColumnTitle,
} from '@/pages/common-party/utils/constants';
import { ModeEnum } from '@/types/common';
import { PlusOutlined } from '@ant-design/icons';
import { Form, Button, Divider, message, Modal, Drawer } from 'antd';
import type { ChannelDetail } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';
import type { FC } from 'react';
import {
  Fragment,
  useState,
  useCallback,
  useEffect,
  useRef,
  useMemo,
} from 'react';
import type { PaginationProps } from 'antd/es/pagination';
import { useTranslation } from 'react-i18next';
import { useLocation } from '@umijs/max';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import { useNavigate } from 'react-router-dom';
import qs from 'qs';
import type { FieldDataType } from '@zhongan/nagrand-ui';
import { QueryForm } from '@zhongan/nagrand-ui';

/**
 *
 * @description partner setting agencyCompany/salesChannel/serviceCompany menu component
 */
export const ChannelCard: FC<{ channelType: ChannelTypeEnum }> = ({
  channelType,
}) => {
  const { t } = useTranslation('partner');
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { state } = useLocation() as {
    state: ChannelRouteState<ChannelSearchForm>;
  };

  const { canEdit } = usePartnerSettingPermission();

  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState<number>();
  const [channelList, setChannelList] = useState<ChannelDetail[]>([]);
  const [pagination, setPagination] = useState<PaginationProps>();
  const [structureVisible, setStructureVisible] = useState(false);
  const [selectedChannelId, setSelectedChannelId] = useState<number>();
  const paginationRef = useRef<PaginationProps>(); // 用于同步获取pagination，setState是异步更新，setPagination后立即获取的pagination是旧值

  // 若将handleSearch作为useEffect依赖，handleSearch依赖pagination变化时自动调用，则初始化时会调用两遍（一次是channelType, 一次是pagination）
  // 故在需要的地方手动调用
  const handleSearch = useCallback(() => {
    form.validateFields().then(values => {
      setLoading(true);
      const searchValues = { ...values };
      Object.keys(searchValues).forEach(key => {
        if (searchValues[key] === '') {
          searchValues[key] = undefined;
        }
      });
      ChannelService.getChannelList({
        pageIndex: paginationRef.current?.current - 1,
        pageSize: paginationRef.current?.pageSize,
        type: channelType,
        ...searchValues,
      })
        .then(res => {
          setChannelList(res.data);
          setTotal(res.totalElements);
        })
        .catch(error => message.error(error?.message))
        .finally(() => setLoading(false));
    });
  }, [channelType]);

  // 初始化
  useEffect(() => {
    // 切换menu，只有channelType匹配才会使用state初始化表单和页码信息
    if (state?.channelType === channelType) {
      form.setFieldsValue(state?.searchForm);
      paginationRef.current = state?.pagination;
    } else {
      form.resetFields();
      paginationRef.current = { current: 1, pageSize: 10 };
    }
    setPagination(paginationRef.current);
    handleSearch();
  }, [channelType, form]);

  const handleSearchFieldChange = useCallback(() => {
    paginationRef.current = { current: 1, pageSize: 10 };
    setPagination(paginationRef.current);
    handleSearch();
  }, [handleSearch]);

  const handlePaginationChange = useCallback(
    (current, pageSize) => {
      paginationRef.current = { current, pageSize };
      setPagination(paginationRef.current);
      handleSearch();
    },
    [handleSearch],
  );

  const addChannel = useCallback(
    (parentId: number, level: number) => {
      navigate(
        `/channel?${qs.stringify({
          parentId: parentId && String(parentId),
          type: channelType,
          level: String(level),
          modeType: ModeEnum.ADD,
        })}`,
        {
          state: {
            pagination: pagination,
            searchForm: form.getFieldsValue(),
            channelType: channelType,
          },
        },
      );
    },
    [pagination, form, channelType],
  );

  const viewDetail = useCallback(
    (channelId: number) => {
      navigate(
        `/channel?${qs.stringify({
          id: String(channelId),
          type: channelType,
          modeType: ModeEnum.READ,
        })}`,
        {
          state: {
            pagination: pagination,
            searchForm: form.getFieldsValue(),
            channelType: channelType,
          },
        },
      );
    },
    [pagination, form, channelType],
  );

  const deleteRecord = useCallback(
    (id: number) => {
      Modal.confirm({
        title: t('Delete'),
        className: style.deleteModal,
        content: (
          <p>
            {t(
              'All affiliated partner information will be deleted at the same time. Are you sure to delete this record?',
            )}
          </p>
        ),
        okText: t('Confirm'),
        onOk: () => {
          Modal.destroyAll();
          setLoading(true);
          ChannelService.deleteChannel(id)
            .then(() => {
              message.success(t('Delete successfully'));
              handleSearchFieldChange();
            })
            .catch((error: Error) => {
              message.error(error?.message);
              // 删除后要重新请求数据，故不能放在finally里统一置为false
              setLoading(false);
            });
        },
      });
    },
    [handleSearchFieldChange],
  );

  const viewStructure = useCallback((channelId: number) => {
    setStructureVisible(true);
    setSelectedChannelId(channelId);
  }, []);

  const fields: FieldDataType[] = useMemo(
    () => [
      {
        key: 'name',
        label: NameColumnTitle[channelType],
      },
      {
        key: 'code',
        label: CodeColumnTitle[channelType],
      },
    ],
    [t, channelType],
  );

  return (
    <Fragment>
      <QueryForm
        queryFields={fields}
        onSearch={handleSearchFieldChange}
        formProps={{ form }}
      />
      <Divider dashed />
      <Button
        icon={<PlusOutlined />}
        type="primary"
        style={{ marginBottom: 8 }}
        disabled={!canEdit}
        onClick={() => addChannel(null, 1)}
      >
        {t('Add New')}
      </Button>
      <CommonTable
        loading={loading}
        data={channelList}
        channelType={channelType}
        viewDetail={viewDetail}
        handleDelete={deleteRecord}
        addChannel={addChannel}
        viewStructure={viewStructure}
      />
      <PaginationComponent
        pagination={pagination}
        total={total}
        pageSizeOptions={PageSizeOptions}
        handlePaginationChange={handlePaginationChange}
      />
      <Drawer
        title={t('Structure Details')}
        open={structureVisible}
        maskClosable={false}
        width={641}
        rootClassName={style.structureDrawer}
        onClose={() => setStructureVisible(false)}
      >
        <Structure
          channelId={selectedChannelId}
          channelType={channelType}
          visible={structureVisible}
        />
      </Drawer>
    </Fragment>
  );
};
