import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DeleteOutlined, DownOutlined, DownloadOutlined, UpOutlined, UploadOutlined } from '@ant-design/icons';

import { Button, Modal, Upload, message } from 'antd';
import type { RcFile } from 'antd/lib/upload/interface';
import type { UploadRequestOption } from 'rc-upload/lib/interface';

import type { ChannelDocumentV2 } from 'genesis-web-service';
import { ChannelService, DownloadOrUploadType, UploadTypeEnum } from 'genesis-web-service';
import { security } from 'genesis-web-shared';

import { useFileMap } from '@/pages/common-party/partner-setting-v2/hooks/useFileList';
import { deepCloneMap } from '@/pages/common-party/utils/util';
import { getUrlByFileUniqueCode } from '@/pages/common-party/utils/util';

import styles from './style.scss';
import { UploadFileItem } from '@zhongan/nagrand-ui';

const acceptFileType = '.png, .jpg, .jpeg, .pdf, .doc, .docx, .csv, .xls, .xlsx';
const acceptFileTypeArr = acceptFileType.split(', ');

interface Props {
  savedFileList: ChannelDocumentV2[];
  style?: React.CSSProperties;
  readonly?: boolean;
}

/**
 *
 * @param savedFileList 已保存文档列表
 * @param readonly 是否只读，用于控制是否可删除
 * @description 目前用在institute detail(documents)； 上传文件以及显示，可上传、删除、下载、预览（图片）
 */
export const UploadFile = forwardRef(({ savedFileList, style = {}, readonly }: Props, ref) => {
  const { t } = useTranslation('partner');
  const fileMap = useFileMap(savedFileList);
  const [uploading, setUploading] = useState(false);
  const [isExpand, setIsExpand] = useState(false);
  const [shownFileList, setShownFileList] = useState<ChannelDocumentV2[]>([]); // 用于展示
  const [fileList, setFileList] = useState(new Map<string, ChannelDocumentV2>());
  const [newFileList, setNewFileList] = useState(new Map<string, ChannelDocumentV2>()); // 存放新上传文件
  const [curFile, setCurFile] = useState<RcFile>();

  useEffect(() => {
    setFileList(deepCloneMap(fileMap));
    setNewFileList(new Map<string, ChannelDocumentV2>());
  }, [fileMap]);

  useEffect(() => {
    // 处理用于展示文件列表
    const shownList: ChannelDocumentV2[] = [];
    fileList.forEach((item, key) => {
      if (item.isDeleted !== 'Y') shownList.push({ ...item, id: String(key) });
    });
    newFileList.forEach((item, key) => {
      shownList.unshift({ ...item, id: String(key) });
    });
    setShownFileList(shownList);
  }, [fileList, newFileList]);

  useImperativeHandle(ref, () => {
    const fileValue = Array.from(fileList.values());
    const newFileValue = Array.from(newFileList.values());
    return [...fileValue, ...newFileValue];
  }, [fileList, newFileList]);

  const deleteFile = useCallback(
    (deleteId: string) => {
      // 删除已保存文件，更改标志位isDeleted；新上传文件再删除，不传给后端
      const cloneFileList = deepCloneMap(fileList);
      const cloneNewList = deepCloneMap(newFileList);
      if (cloneFileList.has(deleteId)) {
        const matchFile = cloneFileList.get(deleteId);
        matchFile.isDeleted = 'Y';
      }
      if (cloneNewList.has(deleteId)) {
        cloneNewList.delete(deleteId);
      }

      setFileList(cloneFileList);
      setNewFileList(cloneNewList);
    },
    [fileList, newFileList]
  );

  const preHandleDelete = useCallback(
    (id: string) => {
      Modal.confirm({
        title: t('Delete'),
        className: styles.deleteModal,
        content: <p>{t('Are you sure to delete this record?')}</p>,
        okText: t('Confirm'),
        onOk: () => {
          Modal.destroyAll();
          deleteFile(id);
        },
      });
    },
    [deleteFile, t]
  );

  // 自定义上传
  const handleUpload: (options: UploadRequestOption) => void = useCallback(
    ({ data }: UploadRequestOption) => {
      // upload query 处理
      const formData = new FormData();
      formData.append('file', curFile);
      Object.keys(data).forEach(key => {
        formData.append(key, data[key] as string);
      });
      setUploading(true);

      ChannelService.channelUpload(formData)
        .then(res => {
          const cloneNewList = deepCloneMap(newFileList);
          cloneNewList.set(curFile.uid, {
            ...res.value,
          });
          setNewFileList(cloneNewList);
          message.success(t('Uploaded successfully'));
        })
        .catch(() => {
          message.error(t('Uploaded failed'));
        })
        .finally(() => {
          setUploading(false);
        });
    },
    [curFile, newFileList, t]
  );

  const uploadProps = useMemo(
    () => ({
      data: {
        type: DownloadOrUploadType.COMMON_ENTITY_FILE,
        ext: JSON.stringify({ type: UploadTypeEnum.ChannelDocument }),
      },
      accept: acceptFileType,
      headers: { ...security.csrf() },
      showUploadList: false,
      beforeUpload: (file: RcFile) => {
        const fileType = file.name.split('.').pop();
        if (acceptFileTypeArr.indexOf(`.${fileType}`) === -1) {
          message.error(t('You can only upload  CSV/EXCEL/JPG/WORD/PDF'));
          return false;
        }
        const size = file.size / 1024 / 1024;
        if (size > 20) {
          message.error(t('Upload Size Limit', { size: 20 }));
          return false;
        }
        setCurFile(file);
        return true;
      },
      customRequest: handleUpload,
    }),
    [handleUpload, t]
  );

  return (
    <section className={styles.channelUploadDocument} style={{ ...style }}>
      <p className={styles.sectionTitle}>{t('Document')}</p>
      <Upload {...uploadProps}>
        <Button className="w-100" loading={uploading} icon={<UploadOutlined />} disabled={readonly}>
          {t('Upload')}
        </Button>
      </Upload>
      <p className={styles.uploadTip}>{t('You can only upload  CSV/EXCEL/JPG/WORD/PDF')}</p>
      <div className={styles.docListContainer}>
        {shownFileList.map((item, index) => {
          const url = getUrlByFileUniqueCode('/api/channel/v2/file/download', item.fileUniqueCode);
          return (
            <UploadFileItem
              key={item.fileUniqueCode}
              fileName={item.fileName || ''}
              fileUrl={url}
              style={{
                marginRight: 16,
                marginBottom: 16,
                display: index < 10 || isExpand ? '' : 'none',
              }}
              hoverInfoList={[
                {
                  key: 'downloadBtn',
                  icon: (
                    <a href={url}>
                      <DownloadOutlined style={{ color: '#102A43', marginRight: 16 }} />
                    </a>
                  ),
                  onClick: () => {},
                },
                {
                  key: 'deleteBtn',
                  icon: <DeleteOutlined />,
                  onClick: () => preHandleDelete(String(item.id)),
                  disabled: readonly,
                },
              ]}
            />
          );
        })}
      </div>
      {shownFileList.length > 0 && (
        <div className={styles.uploadBottom}>
          <p>{t('Total:', { total: shownFileList.length })}</p>
          {shownFileList.length > 10 && (
            <div onClick={() => setIsExpand(!isExpand)} className={styles.upDownBtn}>
              {isExpand ? t('Collapse') : t('Expand All')}
              {isExpand ? <UpOutlined /> : <DownOutlined />}
            </div>
          )}
        </div>
      )}
    </section>
  );
});
