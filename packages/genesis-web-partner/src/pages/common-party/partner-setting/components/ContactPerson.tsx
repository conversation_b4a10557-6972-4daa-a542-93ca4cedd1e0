import { Fragment, forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { EnvironmentOutlined, MailOutlined, PhoneOutlined } from '@ant-design/icons';
import { Form } from 'antd';

import cls from 'clsx';
import { cloneDeep } from 'lodash-es';
import moment from 'moment';

import { DeleteAction, EditAction, Table, ViewAction } from '@zhongan/nagrand-ui';

import { ComponentWithFallback } from 'genesis-web-component/lib/components/ComponentWithFallback';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { CustomerItem } from 'genesis-web-service';
import { GenderEnum } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import man from '@/assets/man.svg?asset';
import woman from '@/assets/woman.svg?asset';
import { CommonForm } from '@/components/CommonForm/Form';
import { EntityType, useGenericSchemaFormItemFields } from '@/hooks/useGenericSchemaFormItemFields';
import { useEnumsMapping } from '@/pages/common-party/partner-setting/hooks/useEnumsMapping';
import { useFormFields } from '@/pages/common-party/partner-setting/hooks/useFormFields';
import { ChannelTypeEnum, FormTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import type { SchemaProps } from '@/types/common';
import { transferSchemaToTableProp } from '@/utils/utils';

import {
  covertChannelType2SchemaChannelType,
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
} from '../../../../hooks/useGenericSchemaFormItemFields';
import { AddNewTitle } from './AddNewTitle';
import styles from './ChannelCard/style.scss';

interface PersonProps {
  initialList: CustomerItem[];
  channelType?: ChannelTypeEnum;
  disabled: boolean;
}

/**
 * @param initialList 初始化列表
 * @param channelType ChannelTypeEnum枚举
 * @param readonly 是否只读
 * @description Contact Person信息展示
 */
export const ContactPerson = forwardRef(({ initialList, channelType, disabled }: PersonProps, ref) => {
  const { t } = useTranslation(['partner']);
  const [form] = Form.useForm();
  const channelCustomerTypesMapping = useEnumsMapping('channelCustomerType');
  const [readonly, setReadonly] = useState(false);

  const { formItems: staticFields } = useGenericSchemaFormItemFields({
    staticOrDynamic: 'STATIC',
    category: 'CUSTOMER',
    disabled: readonly,
    type: covertChannelType2SchemaChannelType(channelType),
    entityType: EntityType.CHANNEL,
  });

  const insuranceFields = useFormFields({
    formType: FormTypeEnum.CONTACT_PERSON,
    basicInfoType: channelType,
    channelDetail: {},
    disabled: readonly,
    form,
  });

  const { formItems: dynamicFields, schemaFields } = useGenericSchemaFormItemFields({
    staticOrDynamic: 'DYNAMIC',
    category: 'CUSTOMER',
    disabled: readonly,
    type: covertChannelType2SchemaChannelType(channelType),
    entityType: channelType === ChannelTypeEnum.INSURANCE ? EntityType.CHANNEL_INSURANCE : EntityType.CHANNEL,
  });

  const [visible, setVisible] = useState(false);
  const [editedIndex, setEditedIndex] = useState<number>();
  const [editedItem, setEditedItem] = useState<CustomerItem>();
  const [shownList, setShownList] = useState<CustomerItem[]>();

  useEffect(() => {
    setShownList([...(initialList || [])]);
  }, [initialList]);

  useImperativeHandle(ref, () => {
    return shownList;
  });

  const editClick = useCallback(
    (item: CustomerItem, index: number) => {
      setVisible(true);
      setEditedIndex(index);
      setEditedItem(item);
      dealSchemaFieldValueForDisplay(item?.extensions, schemaFields);
      form.setFieldsValue({
        ...item,
        birthday: item.birthday && moment(item.birthday),
      });
    },
    [form, schemaFields]
  );

  const deleteClick = useCallback(
    (index: number) => {
      const cloneList = cloneDeep(shownList || []);
      cloneList.splice(index, 1);
      setShownList(cloneList);
    },
    [shownList]
  );

  const onClose = useCallback(() => {
    setVisible(false);
    setEditedIndex(null);
    setEditedItem(null);
    form.resetFields();
  }, [form]);

  const onSubmit = useCallback(() => {
    form.validateFields().then(values => {
      const cloneList = cloneDeep(shownList || []);
      values.id = editedItem?.id;
      values.birthday = values.birthday && dateFormatInstance.formatDate(values.birthday);
      dealSchemaFieldValueForSubmit(values.extensions, schemaFields, { dateFormatInstance });
      if (editedItem) {
        cloneList.splice(editedIndex, 1, values);
      } else {
        cloneList.push(values);
      }
      setShownList(cloneList);
      onClose();
    });
  }, [shownList, form, editedItem, editedIndex, schemaFields, onClose, t]);

  const cardContent = useCallback(
    (item: CustomerItem) => {
      return (
        <Fragment>
          <div className={styles.cardTop}>
            <span className={styles.name}>{item.name}</span>
            {item.sex && <img src={item.sex === GenderEnum.Male ? man : woman} />}
            {channelCustomerTypesMapping[item.type] && (
              <span className={styles.type}>{channelCustomerTypesMapping[item.type]}</span>
            )}
          </div>
          <p className={cls(styles.location, !item.phone && styles.gray)}>
            <PhoneOutlined />
            <ComponentWithFallback>{item.phone}</ComponentWithFallback>
          </p>
          <p className={cls(styles.location, !item.email && styles.gray)}>
            <MailOutlined />
            <ComponentWithFallback>{item.email}</ComponentWithFallback>
          </p>
          <p className={cls(styles.location, !item.position && styles.gray)}>
            <EnvironmentOutlined />
            <ComponentWithFallback>{item.position}</ComponentWithFallback>
          </p>
        </Fragment>
      );
    },
    [channelCustomerTypesMapping]
  );

  const title = useMemo(() => {
    if (readonly) {
      return t('View Contact Person Information');
    } else if (editedItem) {
      return t('Edit Contact Person Information');
    } else {
      return t('Add Contact Person Information');
    }
  }, [readonly, editedItem, t]);

  return (
    <section className={styles.addressInfo}>
      <AddNewTitle
        title={t('Contact Person')}
        onAddClick={() => {
          setVisible(true);
          setReadonly(false);
        }}
        readonly={disabled}
      />
      <Table
        columns={[
          ...transferSchemaToTableProp([
            ...(channelType === ChannelTypeEnum.INSURANCE ? insuranceFields : staticFields),
            ...dynamicFields,
          ] as SchemaProps[]),
          {
            title: t('Action'),
            render: (_, record, index) => (
              <>
                <ViewAction
                  onClick={() => {
                    editClick(record, index);
                    setReadonly(true);
                  }}
                />
                <EditAction
                  onClick={() => {
                    editClick(record, index);
                    setReadonly(false);
                  }}
                  disabled={disabled}
                />
                <DeleteAction
                  deleteConfirmContent={t('Are you sure to delete?')}
                  onClick={() => deleteClick(index)}
                  disabled={disabled}
                />
              </>
            ),
          },
        ]}
        dataSource={shownList}
        pagination={false}
        scroll={{ x: 'max-content' }}
      />

      <DrawerForm
        title={title}
        visible={visible}
        closable={false}
        onClose={onClose}
        onSubmit={onSubmit}
        cancelText={t('Cancel')}
        sendText={t('Submit')}
        submitBtnShow={!readonly}
      >
        <CommonForm
          fields={[...(channelType === ChannelTypeEnum.INSURANCE ? insuranceFields : staticFields), ...dynamicFields]}
          form={form}
        />
      </DrawerForm>
    </section>
  );
});
