import { Fragment, forwardRef, useCallback, useEffect, useImperative<PERSON>andle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { EnvironmentOutlined, MailOutlined, PhoneOutlined } from '@ant-design/icons';

import { Form } from 'antd';
import classNames from 'classnames';
import { cloneDeep } from 'lodash-es';
import moment from 'moment';

import { ComponentWithFallback } from 'genesis-web-component/lib/components/ComponentWithFallback';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { CustomerItem } from 'genesis-web-service';
import { GenderEnum } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import man from '@/assets/man.svg?asset';
import woman from '@/assets/woman.svg?asset';
import { CommonForm } from '@/components/CommonForm/Form';
import { ChannelCard } from '@/pages/common-party/partner-setting/components/ChannelCard';
import { useEnumsMapping } from '@/pages/common-party/partner-setting/hooks/useEnumsMapping';
import { useFormFields } from '@/pages/common-party/partner-setting/hooks/useFormFields';
import type { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { FormTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import type { ModeEnum } from '@/types/common';

import {
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
  useSchemaFormItemFieldsByCategory,
} from '../hooks/useChannelSchemaFieldsByCategory';
import CommonStyles from '../style.scss';
import styles from './ChannelCard/style.scss';

interface PersonProps {
  initialList: CustomerItem[];
  channelType?: ChannelTypeEnum;
  readonly: boolean;
  mode: ModeEnum;
}

/**
 * @param initialList 初始化列表
 * @param channelType ChannelTypeEnum枚举
 * @param readonly 是否只读
 * @description Contact Person信息展示
 */
export const ContactPerson = forwardRef(({ initialList, channelType, readonly, mode }: PersonProps, ref) => {
  const { t } = useTranslation(['partner']);
  const [form] = Form.useForm();
  const channelCustomerTypesMapping = useEnumsMapping('channelCustomerType');
  const fields = useFormFields({
    formType: FormTypeEnum.CONTACT_PERSON,
    basicInfoType: channelType,
    disabled: readonly,
  });
  const { formItems: dynamicFields, schemaFields } = useSchemaFormItemFieldsByCategory({
    staticOrDynamic: 'DYNAMIC',
    category: 'CUSTOMER',
    disabled: readonly,
  });

  const [visible, setVisible] = useState(false);
  const [editedIndex, setEditedIndex] = useState<number>();
  const [editedItem, setEditedItem] = useState<CustomerItem>();
  const [shownList, setShownList] = useState<CustomerItem[]>();

  useEffect(() => {
    setShownList([...(initialList || [])]);
  }, [initialList]);

  useImperativeHandle(ref, () => {
    return shownList;
  });

  const editClick = useCallback(
    (item: CustomerItem, index: number) => {
      setVisible(true);
      setEditedIndex(index);
      setEditedItem(item);
      dealSchemaFieldValueForDisplay(item?.extensions, schemaFields);
      form.setFieldsValue({
        ...item,
        birthday: item.birthday && moment(item.birthday),
      });
    },
    [form, schemaFields]
  );

  const deleteClick = useCallback(
    (index: number) => {
      const cloneList = cloneDeep(shownList || []);
      cloneList.splice(index, 1);
      setShownList(cloneList);
    },
    [shownList]
  );

  const onClose = useCallback(() => {
    setVisible(false);
    setEditedIndex(null);
    setEditedItem(null);
    form.resetFields();
  }, [form]);

  const onSubmit = useCallback(() => {
    form.validateFields().then(values => {
      const cloneList = cloneDeep(shownList || []);
      values.id = editedItem?.id;
      values.birthday = values.birthday && dateFormatInstance.formatDate(values.birthday);
      dealSchemaFieldValueForSubmit(values.extensions, schemaFields, { dateFormatInstance });
      if (editedItem) {
        cloneList.splice(editedIndex, 1, values);
      } else {
        cloneList.push(values);
      }
      setShownList(cloneList);
      onClose();
    });
  }, [shownList, form, editedItem, editedIndex, schemaFields, onClose, t]);

  const cardContent = useCallback(
    (item: CustomerItem) => {
      return (
        <Fragment>
          <div className={styles.cardTop}>
            <span className={styles.name}>{item.name}</span>
            {item.sex && <img src={item.sex === GenderEnum.Male ? man : woman} />}
            {channelCustomerTypesMapping[item.type] && (
              <span className={styles.type}>{channelCustomerTypesMapping[item.type]}</span>
            )}
          </div>
          <p className={classNames(styles.location, !item.phone && styles.gray)}>
            <PhoneOutlined />
            <ComponentWithFallback>{item.phone}</ComponentWithFallback>
          </p>
          <p className={classNames(styles.location, !item.email && styles.gray)}>
            <MailOutlined />
            <ComponentWithFallback>{item.email}</ComponentWithFallback>
          </p>
          <p className={classNames(styles.location, !item.position && styles.gray)}>
            <EnvironmentOutlined />
            <ComponentWithFallback>{item.position}</ComponentWithFallback>
          </p>
        </Fragment>
      );
    },
    [channelCustomerTypesMapping]
  );

  const title = useMemo(() => {
    if (readonly) {
      return t('View Contact Person Information');
    } else if (editedItem) {
      return t('Edit Contact Person Information');
    } else {
      return t('Add Contact Person Information');
    }
  }, [readonly, editedItem, t]);

  return (
    <section className={styles.addressInfo}>
      <p className={CommonStyles.sectionTitle}>{t('Contact Person Information')}</p>
      <ChannelCard
        shownList={shownList}
        readonly={readonly}
        mode={mode}
        addText={t('Add New Contacts Person')}
        cardContent={cardContent}
        editClick={editClick}
        deleteClick={deleteClick}
        addClick={() => setVisible(true)}
      />
      <DrawerForm
        title={title}
        visible={visible}
        closable={false}
        onClose={onClose}
        onSubmit={onSubmit}
        cancelText={t('Cancel')}
        sendText={t('Submit')}
        submitBtnShow={!readonly}
      >
        <CommonForm fields={[...fields, ...dynamicFields]} form={form} />
      </DrawerForm>
    </section>
  );
});
