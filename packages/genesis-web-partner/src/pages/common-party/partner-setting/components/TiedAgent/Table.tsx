import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import type { TableProps } from 'antd/es/table';

import type { ColumnsType } from '@zhongan/nagrand-ui';
import { DeleteAction, EditAction, Table, ViewAction } from '@zhongan/nagrand-ui';

import type { ChannelDetail } from 'genesis-web-service';

import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import { ModeEnum } from '@/types/common';

interface TableProp {
  loading: boolean;
  data: ChannelDetail[];
  viewDetail: (type: ModeEnum, id?: number) => void;
  handleDelete: (channelId: number) => void;
}

/**
 *
 * @param loading 是否在加载
 * @param data 表格数据
 * @param viewDetail 跳转method
 * @param handleDelete delete method
 * @description 用于partner setting agencyCompany/salesChannel/serviceCompany menu component； 数据展示
 */
export const CommonTable = ({ loading, data, viewDetail, handleDelete, ...rest }: TableProp & TableProps) => {
  const { t } = useTranslation('partner');
  const { canEdit } = usePartnerSettingPermission();

  const columns: ColumnsType<ChannelDetail> = useMemo(
    () => [
      {
        title: t('Agent Code'),
        dataIndex: 'agentCode',
      },
      {
        title: t('Agent Name'),
        render: item =>
          item?.agentFullName
            ? item?.agentFullName
            : item.firstName && item.lastName && `${item.firstName} ${item.lastName}`,
      },
      {
        title: t('Email'),
        dataIndex: 'email',
      },
      {
        title: t('Phone No.'),
        dataIndex: 'phoneNo',
      },
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (item: ChannelDetail) => (
          <div className="flex items-center gap-[6px] justify-end">
            <ViewAction onClick={() => viewDetail(ModeEnum.READ, item.id)} />
            <EditAction onClick={() => viewDetail(ModeEnum.EDIT, item.id)} disabled={!canEdit} />
            <DeleteAction disabled={!canEdit} onClick={() => handleDelete(item.id)} />
          </div>
        ),
      },
    ],
    [viewDetail, t, handleDelete, canEdit]
  );

  return (
    <Table
      scroll={{ x: 'max-content' }}
      emptyType="icon"
      loading={loading}
      dataSource={data}
      columns={columns}
      rowKey="id"
      pagination={false}
      style={{ marginBottom: 16 }}
      {...rest}
    />
  );
};
