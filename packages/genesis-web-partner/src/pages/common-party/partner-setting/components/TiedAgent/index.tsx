import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useDispatch, useLocation } from '@umijs/max';

import { Button, Divider, Form, message } from 'antd';
import type { PaginationProps } from 'antd/es/pagination';

import type { FieldDataType } from '@zhongan/nagrand-ui';
import { Icon, QueryForm } from '@zhongan/nagrand-ui';

import type { ChannelDetail } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';

import { PaginationComponent } from '@/components/Pagination';
import { ChannelSchemaFieldsContext } from '@/hooks/ChannelSchemaFieldsContext';
import { useChannelSchemaFields } from '@/hooks/useChannelSchemaFields';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import type { ChannelRouteState, ChannelSearchForm } from '@/pages/common-party/partner-setting/models/index.interface';
import { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { OriginType, PageSizeOptions } from '@/pages/common-party/utils/constants';
import { ModeEnum } from '@/types/common';

import { CommonTable } from './Table';
import { TiedAgentDrawer } from './TiedAgentDrawer';

/**
 *
 * @description partner setting bank menu component
 */
export const TiedAgent = () => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const { state } = useLocation<ChannelRouteState<ChannelSearchForm>>();
  const dispatch = useDispatch();
  const { canEdit } = usePartnerSettingPermission();
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState<number>();
  const [channelList, setChannelList] = useState<ChannelDetail[]>([]);
  const [pagination, setPagination] = useState<PaginationProps>();
  const [agentVisible, setAgentVisible] = useState(false);
  const paginationRef = useRef<PaginationProps>(); // 用于同步获取pagination，setState是异步更新，setPagination后立即获取的pagination是旧值
  const [id, setId] = useState<number>();
  const [type, setType] = useState<ModeEnum>();

  const { channelSchemaDefFields, schemaUsedBizDictMap } = useChannelSchemaFields();

  // 若将handleSearch作为useEffect依赖，handleSearch依赖pagination变化时自动调用，则初始化时会调用两遍（一次是channelType, 一次是pagination）
  // 故在需要的地方手动调用
  const handleSearch = useCallback(() => {
    form.validateFields().then(values => {
      setLoading(true);
      const searchValues = { ...values };
      Object.keys(searchValues).forEach(key => {
        if (searchValues[key] === '') {
          searchValues[key] = undefined;
        }
      });
      ChannelService.queryTiedAgentList({
        pageIndex: paginationRef.current?.current - 1,
        pageSize: paginationRef.current?.pageSize,
        ...searchValues,
      })
        .then(res => {
          setChannelList(res.data);
          setTotal(res.totalElements);
        })
        .catch(error => message.error(error?.message))
        .finally(() => setLoading(false));
    });
  }, []);

  // 初始化
  useEffect(() => {
    // 切换menu，只有channelType匹配才会使用state初始化表单和页码信息
    if (state?.channelType === ChannelTypeEnum.TiedAgent) {
      form.setFieldsValue(state?.searchForm);
      paginationRef.current = state?.pagination;
    } else {
      form.resetFields();
      paginationRef.current = { current: 1, pageSize: 10 };
    }
    setPagination(paginationRef.current);
    handleSearch();
  }, [form]);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: ['yesNo', 'certiType', 'country', 'settlementRule', 'salesAgreementStatus', 'salesCategory'],
    });
  }, [dispatch]);

  const handleSearchFieldChange = useCallback(() => {
    paginationRef.current = { current: 1, pageSize: 10 };
    setPagination(paginationRef.current);
    handleSearch();
  }, [handleSearch]);

  const handlePaginationChange = useCallback(
    (current, pageSize) => {
      paginationRef.current = { current, pageSize };
      setPagination(paginationRef.current);
      handleSearch();
    },
    [handleSearch]
  );

  const viewDetail = (handleType: ModeEnum, agentId?: number) => {
    if (agentId) {
      setId(agentId);
    }
    setType(handleType);
    setAgentVisible(true);
  };

  const deleteRecord = useCallback(
    (agentId: number) => {
      setLoading(true);
      ChannelService.deleteTiedAgent(agentId)
        .then(() => {
          message.success(t('Delete successfully'));
          handleSearchFieldChange();
        })
        .catch((error: Error) => {
          message.error(error?.message);
          // 删除后要重新请求数据，故不能放在finally里统一置为false
          setLoading(false);
        });
    },
    [handleSearchFieldChange]
  );

  const onClose = () => {
    setAgentVisible(false);
    setId(null);
    setType(null);
    handleSearchFieldChange();
  };

  const fields: FieldDataType[] = useMemo(
    () => [
      {
        key: 'agentName',
        label: t('Agent Name'),
      },
      {
        key: 'agentCode',
        label: t('Agent Code'),
      },
    ],
    [t]
  );

  return (
    <>
      <QueryForm queryFields={fields} onSearch={handleSearchFieldChange} formProps={{ form }} />
      <Divider dashed />
      <Button
        icon={<Icon type="add" />}
        type="primary"
        disabled={!canEdit}
        onClick={() => viewDetail(ModeEnum.ADD)}
        className="mb-2"
      >
        {t('Add New')}
      </Button>

      <CommonTable
        loading={loading}
        data={channelList}
        viewDetail={viewDetail}
        handleDelete={deleteRecord}
        rowKey="code"
      />
      <PaginationComponent
        pagination={pagination}
        total={total}
        pageSizeOptions={PageSizeOptions}
        handlePaginationChange={handlePaginationChange}
      />
      <ChannelSchemaFieldsContext.Provider
        value={{
          channelSchemaDefFields: channelSchemaDefFields,
          schemaUsedBizDictMap: schemaUsedBizDictMap,
        }}
      >
        <TiedAgentDrawer
          basicInfoType={ChannelTypeEnum.TiedAgent}
          visible={agentVisible}
          id={id}
          type={type}
          origin={OriginType.agent}
          onClose={onClose}
        />
      </ChannelSchemaFieldsContext.Provider>
    </>
  );
};
