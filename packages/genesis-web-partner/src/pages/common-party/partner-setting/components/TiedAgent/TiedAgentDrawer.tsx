import { useCallback, useState, useMemo, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
<<<<<<< HEAD
import { Form, message, Steps, Row, Col, Input, Button, Divider } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
=======

import { Button, Col, Divider, Form, Input, Row, Steps, message } from 'antd';
import type { PaginationProps } from 'antd/es/pagination';
import type { ColumnProps } from 'antd/es/table';

import moment from 'moment';

import { Icon, Table } from '@zhongan/nagrand-ui';

import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import { AddressComponent } from 'genesis-web-component/lib/components/AddressComponent';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { AgentManagementInfo, BizDictItem, EmployeeInfo } from 'genesis-web-service';
import { AgentStatusType, ChannelService, MetadataService, SalesTypeEnum } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { FieldType } from '@/components/CommonForm';
>>>>>>> f617d681332 ([GIS-127704] feat(partner): multi-language support for agent name)
import { CommonForm } from '@/components/CommonForm/Form';
import type { ColumnProps } from 'antd/es/table';
import { PaginationComponent } from '@/components/Pagination';
import { RenderEnums } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
<<<<<<< HEAD
import type {
  AgentManagementInfo,
  BizDictItem,
  EmployeeInfo,
} from 'genesis-web-service';
import {
  ChannelService,
  AgentStatusType,
  SalesTypeEnum,
  MetadataService,
} from 'genesis-web-service';
import type { PaginationProps } from 'antd/es/pagination';
=======
import { SalesAgreement } from '@/pages/common-party/partner-setting/components/SalesAgreement';
import {
  customerDom,
  useFormFields,
  useSalesFormFields,
} from '@/pages/common-party/partner-setting/hooks/useFormFields';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import { ChannelTypeEnum, FormTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { AddressFormConfig, OriginType } from '@/pages/common-party/utils/constants';
import { ModeEnum } from '@/types/common';

>>>>>>> f617d681332 ([GIS-127704] feat(partner): multi-language support for agent name)
import {
  PageSizeOptions,
  AddressFormConfig,
} from '@/pages/common-party/utils/constants';
import {
  useFormFields,
  useSalesFormFields,
} from '@/pages/common-party/partner-setting/hooks/useFormFields';
import { FormTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import type { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { ProcessOutline, WaitOutline, FinishOutline } from '@/components/Icons';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import style from '../../style.scss';
import { ModeEnum } from '@/types/common';
import { OriginType } from '@/pages/common-party/utils/constants';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import moment from 'moment';
import { FieldType } from '@/components/CommonForm';
import { AddressComponent } from 'genesis-web-component/lib/components/AddressComponent';
import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import { SalesAgreement } from '@/pages/common-party/partner-setting/components/SalesAgreement';
import { Table } from '@zhongan/nagrand-ui';
interface RegisterProps {
  basicInfoType: ChannelTypeEnum;
  visible: boolean;
  onClose: () => void;
  id: number;
  origin: string;
  type: ModeEnum;
  agentType?: boolean;
  teamId?: number;
  salesConfigName?: string;
}

enum Step {
  Employee = 0,
  Information = 1,
}

enum AgentType {
  AGENT = 'AGENT',
  MANAGER = 'MANAGER',
}

enum SourceType {
  Agent = 'TIED_AGENT',
  Manager = 'RETAIL_MANAGER',
}

/**
 *
 * @param visible 是否可见
 * @param onClose 关闭抽屉
 * @param salesConfigName sales hierarchy configName
 * @description policy transfer registration 抽屉
 */
export const TiedAgentDrawer = ({
  visible,
  onClose,
  basicInfoType,
  id,
  origin,
  type,
  agentType,
  teamId,
  salesConfigName,
}: RegisterProps) => {
  const { t } = useTranslation('partner');
  const [agentForm] = Form.useForm();
  const firstNameValue = Form.useWatch('firstName', agentForm);
  const lastNameValue = Form.useWatch('lastName', agentForm);
  const [currentStep, setCurrentStep] = useState<Step>();
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationProps>();
  const [employeeList, setEmployeeList] = useState([]);
  const [nameOrCode, setNameOrCode] = useState<string>();
  const [total, setTotal] = useState<number>();
  const [basic, setBasic] = useState(false);
  const [sales, setSales] = useState(true);
  const tenantBizDict = useTenantBizDict() as Record<string, BizDictItem[]>;
  const [agent, setAgent] = useState<AgentManagementInfo | EmployeeInfo>();
  const [channelAgentCategoryEnums, setChannelAgentCategoryEnums] =
    useState<BizDictItem[]>();
  const { canEdit } = usePartnerSettingPermission();
  const paginationRef = useRef<PaginationProps>();
  const agreementRef = useRef(null);
  const isTiedAgent = origin === OriginType.agent;
  const onCloseDrawer = useCallback(() => {
    agentForm.resetFields();
    setNameOrCode(undefined);
    setTotal(0);
    setEmployeeList([]);
    setSelectedRows([]);
    onClose();
  }, [onClose]);

  const onSubmit = () => {
    if (currentStep === Step.Employee) {
      if (selectedRows.length > 0) {
        const employeeItem = employeeList.find(
          item => item.id === selectedRows[0],
        );
        delete employeeItem.department;
        delete employeeItem.isCreateAccount;
        if (teamId) {
          employeeItem.teamId = teamId;
        }
        if (isTiedAgent) {
          employeeItem.agentCode = employeeItem.code;
        }
        agentForm.setFieldsValue({
          ...employeeItem,
          licenseEffectiveDate:
            employeeItem.licenseEffectiveDate &&
            moment(employeeItem.licenseEffectiveDate),
        });
        setAgent(employeeItem);
        setCurrentStep(Step.Information);
      } else {
        message.warning(t('Please select an employee first.'));
      }
    } else {
      agentForm.validateFields().then(values => {
        if (type === ModeEnum.ADD) {
          if (isTiedAgent) {
            ChannelService.addTiedAgent({
              ...agent,
              ...values,
              licenseEffectiveDate:
                values?.licenseEffectiveDate &&
                dateFormatInstance.formatDate(values.licenseEffectiveDate),
              agentType: AgentType.AGENT,
            })
              .then(res => {
                agreementRef.current.saveAgreement(
                  res.agentCode,
                  res.id,
                  SourceType.Agent,
                );
                onCloseDrawer();
              })
              .catch((error: Error) => message.error(error?.message));
          } else {
            ChannelService.addBranchManager(teamId, {
              ...agent,
              ...values,
              licenseEffectiveDate:
                values?.licenseEffectiveDate &&
                dateFormatInstance.formatDate(values.licenseEffectiveDate),
              agentType: AgentType.MANAGER,
            })
              .then(() => {
                onCloseDrawer();
              })
              .catch((error: Error) => message.error(error?.message));
          }
        } else {
          if (isTiedAgent) {
            ChannelService.updateTiedAgent(id, {
              ...agent,
              ...values,
              licenseEffectiveDate:
                values?.licenseEffectiveDate &&
                dateFormatInstance.formatDate(values.licenseEffectiveDate),
            })
              .then(res => {
                agreementRef.current.saveAgreement(
                  res.agentCode,
                  res.id,
                  SourceType.Agent,
                );
                onCloseDrawer();
              })
              .catch((error: Error) => message.error(error?.message));
          } else {
            ChannelService.updateBranchManager({
              ...agent,
              ...values,
              licenseEffectiveDate:
                values?.licenseEffectiveDate &&
                dateFormatInstance.formatDate(values.licenseEffectiveDate),
            })
              .then(() => {
                onCloseDrawer();
              })
              .catch((error: Error) => message.error(error?.message));
          }
        }
      });
    }
  };

  const { title } = useMemo(() => {
    let tiedTitle = '';
    if (id) {
      if (isTiedAgent) {
        if (type === ModeEnum.READ) {
          tiedTitle = t('View Agent');
        } else {
          tiedTitle = t('Edit Agent');
        }
      } else {
        if (type === ModeEnum.READ) {
          tiedTitle = t('View Manager');
        } else {
          tiedTitle = t('Edit Manager');
        }
      }
    } else {
      if (currentStep === Step.Employee) {
        if (isTiedAgent) {
          tiedTitle = t('Employee');
        } else {
          tiedTitle = t('Team Manager', {
            team: salesConfigName,
          });
        }
      } else {
        if (isTiedAgent) {
          tiedTitle = t('Add New Agent');
        } else {
          tiedTitle = t('Add New Manager');
        }
      }
    }

    return {
      title: tiedTitle,
    };
  }, [id, currentStep, origin, salesConfigName]);

  const handleSearch = useCallback(employeeNameOrCode => {
    setLoading(true);
    let request;
    if (isTiedAgent) {
      request = ChannelService.getEmployeeList({
        pageIndex: paginationRef.current?.current - 1,
        pageSize: paginationRef.current?.pageSize,
        employeeNameOrCode,
        sourceType:
          origin === OriginType.agent ? SourceType.Agent : SourceType.Manager,
      });
    } else {
      request = ChannelService.getStaffOrEmployeeList({
        pageIndex: paginationRef.current?.current - 1,
        pageSize: paginationRef.current?.pageSize,
        nameOrCode: employeeNameOrCode,
      });
    }
    request
      .then(res => {
        const data = res.data.filter(
          item => item.status === AgentStatusType.Employed,
        );
        setEmployeeList(data);
        setTotal(res.totalElements);
      })
      .catch(error => message.error(error?.message))
      .finally(() => setLoading(false));
  }, []);

  // 初始化
  useEffect(() => {
    if (visible) {
      setCurrentStep(id ? Step.Information : Step.Employee);
      if (id) {
        let queryInfoById;
        if (isTiedAgent) {
          queryInfoById = ChannelService.queryTiedAgentById(id);
        } else {
          queryInfoById = ChannelService.queryBranchManagerById(teamId, id);
        }

        queryInfoById
          .then(res => {
            agentForm.setFieldsValue({
              ...res,
              licenseEffectiveDate:
                res.licenseEffectiveDate && moment(res.licenseEffectiveDate),
            });
            setAgent(res);
          })
          .catch((error: Error) => message.error(error?.message));
      }
    }
  }, [visible, id]);

  // 获取租户配置的 channelAgentCategory
  useEffect(() => {
    MetadataService.queryBizDictConfigList('channelAgentCategory').then(res => {
      const bizDictConfigList =
        res.tenantBizDictConfig?.channelAgentCategory?.bizDictConfigList || [];
      setChannelAgentCategoryEnums(bizDictConfigList);
    });
  }, []);

  const handleSearchChange = useCallback(() => {
    paginationRef.current = { current: 1, pageSize: 10 };
    setPagination({ current: 1, pageSize: 10 });
    handleSearch(nameOrCode);
  }, [nameOrCode]);

  const handlePaginationChange = useCallback(
    (current, pageSize) => {
      paginationRef.current = { current, pageSize };
      setPagination(paginationRef.current);
      handleSearch(nameOrCode);
    },
    [nameOrCode],
  );
  const columns: ColumnProps<any>[] = useMemo(
    () => [
      {
        title: t('Name'),
        render: item =>
          (item.firstName || item.lastName) &&
          `${item.firstName || ''} ${item.lastName || ''}`,
      },
      {
        title: t('Code'),
        dataIndex: 'code',
      },
      {
        title: t('ID Type'),
        dataIndex: 'idType',
        render: (idType: string) => (
          <ComponentWithFallback>
            {idType && (
              <RenderEnums enums={tenantBizDict?.certiType} keyName={idType} />
            )}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('ID Number'),
        dataIndex: 'idNo',
        render: (idNo: string) => (
          <ComponentWithFallback>{idNo}</ComponentWithFallback>
        ),
      },
    ],
    [t, canEdit],
  );

  const fields = useFormFields({
    formType: FormTypeEnum.BASIC_INFO,
    form: agentForm,
    basicInfoType,
    disabled: true,
  });

  const combinedFields = useMemo(() => {
    let clonedFields = [...fields];
    if (isTiedAgent) {
      clonedFields.splice(2, 0, {
        type: FieldType.Customize,
        customerDom: customerDom({
          label: t('Full Name'),
          name: 'agentFullName',
          disabled: true,
        }),
        rules: [
          {
            required: !(firstNameValue || lastNameValue),
          },
        ],
        ctrlProps: {
          maxLength: 512,
        },
      });
    }
    // Sales Hierarchy Management去除所有必填校验
    if (!isTiedAgent) {
      clonedFields = [...fields]?.map(field => {
        delete field?.rules;
        return field;
      });
    }
    clonedFields.push({
      type: FieldType.Customize,
      customerDom: (
        <AddressComponent
          disabled={true}
          initialValue={agent ?? {}}
          form={agentForm}
          addressFormConfig={AddressFormConfig}
          cascaderCol={24}
          colProps={{ span: 12 }}
          inputWidth="100%"
        />
      ),
    });

    return clonedFields;
  }, [fields, agent, agentForm]);

  const salesFields = useSalesFormFields({
    disabled: type === ModeEnum.READ,
    info: agent,
    id,
    origin,
    agentType,
    channelAgentCategoryEnums,
  });

  return (
    <DrawerForm
      title={title}
      visible={visible}
      maskClosable={false}
      cancelText={t('Cancel')}
      sendText={currentStep === Step.Employee ? t('Next') : t('Submit')}
      onSubmit={onSubmit}
      onClose={onCloseDrawer}
      width={1096}
      backText={currentStep === Step.Information && !id && t('Back')}
      onBack={() => {
        handlePaginationChange(
          paginationRef.current.current,
          paginationRef.current.pageSize,
        );
        setCurrentStep(Step.Employee);
      }}
    >
      <div>
        {!id && (
          <Steps className={style.tiedSteps} current={currentStep}>
            <Steps.Step
              title={
                isTiedAgent
                  ? t('Select Employee')
                  : t('Select Employee or Channel Staff')
              }
              icon={
                currentStep === Step.Employee ? (
                  <ProcessOutline />
                ) : (
                  <FinishOutline />
                )
              }
            />
            <Steps.Step
              title={t('Fill Information')}
              icon={
                currentStep === Step.Employee ? (
                  <WaitOutline />
                ) : (
                  <ProcessOutline />
                )
              }
            />
          </Steps>
        )}

        {currentStep === Step.Employee ? (
          <>
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Input
                  value={nameOrCode}
                  placeholder={t('Please input Employee Code/Name')}
                  allowClear
                  onChange={event => setNameOrCode(event.target.value)}
                />
              </Col>
              <Col span={7}>
                <Button
                  type="primary"
                  ghost
                  onClick={handleSearchChange}
                  loading={loading}
                >
                  {t('Search')}
                </Button>
              </Col>
            </Row>
            <Table
              loading={loading}
              dataSource={employeeList}
              columns={columns}
              pagination={false}
              scroll={{ x: 1030 }}
              style={{ marginBottom: 16, marginTop: 24 }}
              rowKey="id"
              rowSelection={{
                type: 'radio',
                onChange: (selectedRowKeys: React.Key[]) => {
                  setSelectedRows(selectedRowKeys as number[]);
                },
                selectedRowKeys: selectedRows,
                columnWidth: 64,
              }}
            />
            <PaginationComponent
              size="small"
              pagination={pagination}
              total={total}
              pageSizeOptions={PageSizeOptions}
              handlePaginationChange={handlePaginationChange}
            />
          </>
        ) : (
          <>
            <div
              onClick={() => {
                setBasic(!basic);
              }}
              className="text-lg mb-3"
            >
              <span className={style.title}>{t('Basic Information')}</span>
              {basic ? <Icon type="down" /> : <Icon type="up" />}
            </div>
            {basic && <CommonForm fields={combinedFields} form={agentForm} />}

            <Divider />
            {agent?.agentCode && (
              <div
                onClick={() => {
                  setSales(!sales);
                }}
                className="text-lg mb-3"
              >
<<<<<<< HEAD
                <span className={style.title}>
                  {t('Sales Related Information')}
                </span>
                {sales ? <DownOutlined /> : <UpOutlined />}
=======
                <span className={style.title}>{t('Sales Related Information')}</span>
                {sales ? <Icon type="down" /> : <Icon type="up" />}
>>>>>>> f617d681332 ([GIS-127704] feat(partner): multi-language support for agent name)
              </div>
            )}
            {sales && <CommonForm fields={salesFields} form={agentForm} />}
            {isTiedAgent && (
              <SalesAgreement
                ref={agreementRef}
                readonly={type === ModeEnum.READ}
                channelCode={agent?.agentCode}
                mode={type}
                salesType={SalesTypeEnum.AGENT}
              />
            )}
          </>
        )}
      </div>
    </DrawerForm>
  );
};
