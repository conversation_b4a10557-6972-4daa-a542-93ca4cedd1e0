import { forwardRef, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Popover, Skeleton, message } from 'antd';

import { useSelector } from '@umijs/max';

import { uniq } from 'lodash-es';

import { EditableTable, FieldType, Icon, Table } from '@zhongan/nagrand-ui';

import { ComponentWithFallback, convertStatus } from 'genesis-web-component/lib/components';
import type { BizDictItem, CommissionFormulaType, FormulaProps, RelatedType } from 'genesis-web-service';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import type { ConnectState } from '@/models/connect';

import commonStyles from '../../style.scss';

interface CommissionTableProps {
  disabled: boolean;
  relatedInfo: RelatedType;
  setRelatedInfo: (relatedInfo?: RelatedType) => void;
  isTiedAgent?: boolean;
}

// BizTopic.Commission
export const CommissionFormulaCategory = '14';
// BizTopic.CommissionClawback
export const CommissionFormulaClawBackCategory = '34';
export const CommissionSubCategory_GrossCommission = '9';

/**
 * 历史枚举渲染函数，只有使用Edittable组件的时候需要使用
 */
export const renderOldBizdictName = (
  key: string,
  optionList: {
    itemName: string;
    itemExtend1: string | number;
  }[]
): string => {
  const renderEnum = optionList.find(option => `${option.itemExtend1}` === `${key}`);
  if (renderEnum) {
    return renderEnum.itemName;
  }
  return '';
};

export const useCommissionFormulaOptions = (
  formulaList: FormulaProps[],
  bizTopicCode: string,
  formulaSubCategory?: string
) => {
  const commissionFormulaOptions = useMemo(
    () =>
      formulaList
        .filter(formula => {
          // 历史数据没有subcateogry，没有subcategory视为GrossCommission
          if (formulaSubCategory === CommissionSubCategory_GrossCommission) {
            return (
              `${formula.formulaTableTypeId}` === bizTopicCode &&
              (formula.formulaSubCategoryCode === formulaSubCategory || !formula.formulaSubCategoryCode)
            );
          }
          return (
            `${formula.formulaTableTypeId}` === bizTopicCode && formula.formulaSubCategoryCode === formulaSubCategory
          );
        })
        .map(formula => ({
          itemExtend1: formula.formulaCode,
          itemName: formula.formulaName,
        })),
    [formulaSubCategory, formulaList, bizTopicCode]
  );

  return commissionFormulaOptions;
};

export const CommissionTable = forwardRef(
  ({ disabled, relatedInfo, setRelatedInfo, isTiedAgent }: CommissionTableProps) => {
    const { t } = useTranslation('partner');
    const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
    const [editingSubCategory, setEditingSubCategory] = useState<string>();
    const formulaList: FormulaProps[] = useSelector(
      ({ partnerManagement }: ConnectState) => partnerManagement?.formulaList
    );
    const commissionFormulaList: CommissionFormulaType[] = (relatedInfo?.formulas || [])?.filter(
      formula => formula?.formulaCategory === CommissionFormulaCategory
    );
    const commissionClawBackFormulaList: CommissionFormulaType[] = (relatedInfo?.formulas || [])?.filter(
      formula => formula?.formulaCategory === CommissionFormulaClawBackCategory
    );

    const [dataSource, setDataSource] = useState(commissionFormulaList);
    const formulaSubCategoryOptions = useMemo(
      () =>
        enums?.bizTopic
          ?.find(bizdict => bizdict.dictValue === CommissionFormulaCategory)
          ?.childList?.map(bizdict => ({
            itemExtend1: bizdict.dictValue,
            itemName: bizdict.dictValueName,
          })) || [],
      [enums?.bizTopic]
    );

    const formulaOptionsBySubCategory = useCommissionFormulaOptions(
      formulaList,
      CommissionFormulaCategory,
      editingSubCategory
    );

    const salesCategoryOptions = useMemo(
      () =>
        enums?.salesCategory?.map(({ enumItemName, dictValueName }) => ({
          itemExtend1: enumItemName,
          itemName: dictValueName,
        })),
      [enums?.salesCategory]
    );

    const totalCommissionFormulaOptions = useMemo(
      () =>
        formulaList
          .filter(formula => `${formula.formulaTableTypeId}` === CommissionFormulaCategory)
          .map(formula => ({
            itemExtend1: formula.formulaCode,
            itemName: formula.formulaName,
          })),
      [formulaList]
    );

    const renderOrderTooltip = useCallback(
      () => (
        <div>
          <p style={{ marginBottom: 6 }}>
            {t('If Commission GST is configured based on NetCommission, then we could configure as')}
          </p>
          <Table
            columns={[
              {
                title: t('Formula Category'),
                dataIndex: 'formulaType',
              },
              {
                title: t('Formula Sub Category'),
                dataIndex: 'formulaSubCategory',
              },
              {
                title: t('Formula Code'),
                dataIndex: 'formulaCode',
              },
              {
                title: t('Order'),
                dataIndex: 'formulaOrder',
              },
            ]}
            dataSource={[
              {
                formulaType: 'Service Fee & Commission',
                formulaCode: 'commNet',
                formulaOrder: 1,
                formulaSubCategory: 'Net Commission',
                key: 1,
              },
              {
                formulaType: 'Service Fee & Commission',
                formulaCode: 'commGST',
                formulaOrder: 2,
                formulaSubCategory: 'Commission GST',
                key: 2,
              },
            ]}
            pagination={false}
          />
          <p style={{ marginTop: 6 }}>
            {t(
              'and in formula <commGST> we could use schema factor netCommission calculated in order 1 to configure formula'
            )}
          </p>
        </div>
      ),
      [t]
    );

    const columns = useMemo(() => {
      const commissionTypeColumn = {
        title: t('Commission Type'),
        editable: true,
        dataIndex: 'salesCategory',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: salesCategoryOptions?.map(option => ({
              label: option.itemName,
              value: option.itemExtend1,
            })),
            allowClear: false,
          },
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: t('Please select'),
            },
          ],
        },
        width: 240,
        render: (salesCategory: string) => (
          <ComponentWithFallback>{convertStatus(enums, salesCategory, 'salesCategory')}</ComponentWithFallback>
        ),
      };
      const internalColumns = [
        {
          title: t('Formula Category'),
          dataIndex: 'formulaCategory',
          editable: false,
          formItemProps: {
            initialValue: CommissionFormulaCategory,
          },
          width: 240,
          render: () => (
            <ComponentWithFallback>{convertStatus(enums, CommissionFormulaCategory, 'bizTopic')}</ComponentWithFallback>
          ),
        },
        {
          title: t('Formula Sub Category'),
          editable: true,
          dataIndex: 'formulaSubCategory',
          fieldProps: {
            type: FieldType.Select,
            extraProps: {
              options: formulaSubCategoryOptions?.map(option => ({
                label: option.itemName,
                value: option.itemExtend1,
              })),
              allowClear: false,
              onChange: setEditingSubCategory,
            },
          },
          width: 240,
          formItemProps: {
            rules: [
              {
                required: true,
                message: t('Please select'),
              },
            ],
          },
          render: (formulaSubCategory = CommissionSubCategory_GrossCommission): string =>
            renderOldBizdictName(formulaSubCategory, formulaSubCategoryOptions),
        },
        {
          title: t('Formula Name'),
          editable: true,
          dataIndex: 'formulaCode',
          fieldProps: {
            type: FieldType.Select,
            extraProps: {
              options: formulaOptionsBySubCategory?.map(option => ({
                label: option.itemName,
                value: option.itemExtend1,
              })),
              allowClear: false,
              disabled: !editingSubCategory,
            },
          },
          formItemProps: {
            rules: [
              {
                required: true,
                message: t('Please select'),
              },
            ],
          },
          width: 240,
          render: (text: string) => {
            return (
              <ComponentWithFallback>{renderOldBizdictName(text, totalCommissionFormulaOptions)}</ComponentWithFallback>
            );
          },
        },
        {
          title: (
            <span>
              {t('Order')}
              <Popover placement="topRight" content={renderOrderTooltip()} getPopupContainer={() => document.body}>
                <Icon type="exclamation-circle" style={{ marginLeft: '5px', lineHeight: '20px' }} />
              </Popover>
            </span>
          ),
          editable: true,
          dataIndex: 'formulaOrder',
          fieldProps: {
            type: FieldType.InputNumber,
            extraProps: {
              min: 1,
              precision: 0,
              placeholder: t('Please input'),
            },
          },
          width: 160,
          render: (text: string) => <ComponentWithFallback>{text}</ComponentWithFallback>,
        },
      ];
      if (isTiedAgent) {
        internalColumns.splice(1, 0, commissionTypeColumn);
      }
      return internalColumns;
    }, [
      t,
      formulaSubCategoryOptions,
      formulaOptionsBySubCategory,
      editingSubCategory,
      renderOrderTooltip,
      enums,
      totalCommissionFormulaOptions,
    ]);

    const handleSubmit = async (value: CommissionFormulaType, key: string, list: CommissionFormulaType[]) => {
      // 检验formulaOrder是否有重复
      const orderList = list?.filter(item => !!item?.formulaOrder)?.map(item => item?.formulaOrder);
      if (orderList.length > uniq(orderList)?.length) {
        message.error(t('Duplicate Order. Please change.'));
        return Promise.reject();
      }
      // SalesCategory + FormulaSubCategory确定唯一性
      const formulaCategoryList = list?.map(item => item?.salesCategory + item?.formulaSubCategory);
      if (list.length > uniq(formulaCategoryList)?.length) {
        message.error(
          t('Duplicate formula configuration for same formula category and sub category. Please edit original one.')
        );
        return Promise.reject();
      }
      setRelatedInfo({
        ...relatedInfo,
        formulas: [
          ...list?.map(item => ({
            ...item,
            formulaCategory: CommissionFormulaCategory,
          })),
          ...commissionClawBackFormulaList,
        ],
      });
      setDataSource(list);
      setEditingSubCategory(undefined);
    };

    const handleDelete = (index: number) => {
      dataSource?.splice(index, 1);
      setRelatedInfo({
        ...relatedInfo,
        formulas: [...dataSource, ...commissionClawBackFormulaList],
      });
      setDataSource([...dataSource]);
    };

    return (
      <Skeleton loading={false}>
        <h3>{t('Please select related formula for Commission')}</h3>
        <EditableTable<CommissionFormulaType>
          className={commonStyles.commissionTable}
          editBtnProps={{
            disabled: () => disabled,
            handleEdit: record => setEditingSubCategory(record.formulaSubCategory),
          }}
          deleteBtnProps={{
            disabled: () => disabled,
            handleDelete,
          }}
          addBtnProps={{
            disabled,
          }}
          dataSource={dataSource}
          columns={columns}
          handleConfirm={handleSubmit}
          scroll={{ x: 'max-content' }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          setDataSource={setDataSource}
          handleCancel={() => setEditingSubCategory(undefined)}
        />
      </Skeleton>
    );
  }
);
