/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-08-11 17:00:27
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-08-31 11:37:16
 * @Description:
 */
import { forwardRef, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Popover, Skeleton, message } from 'antd';
import { v4 as uuid } from 'uuid';
import { uniq } from 'lodash-es';
import { useSelector } from '@umijs/max';
import type { BizDictItem } from 'genesis-web-service';
import type {
  FormulaProps,
  RelatedType,
  CommissionFormulaType,
} from 'genesis-web-service';
import {
  ComponentWithFallback,
  EditableFormTable as EditableTable,
  convertStatus,
} from 'genesis-web-component/lib/components';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import type { ConnectState } from '@/models/connect';
import EditSquareOutlineSvg from '@/assets/svg/EditSquareOutline.svg';
import DeleteHollowOutlineSvg from '@/assets/svg/DeleteHollowOutline.svg';
import { Icon, Table } from '@zhongan/nagrand-ui';
import commonStyles from '../../style.scss';

interface CommissionTableProps {
  disabled: boolean;
  relatedInfo: RelatedType;
  setRelatedInfo: (relatedInfo?: RelatedType) => void;
  isTiedAgent?: boolean;
}

export type LocalCommissionFormulaType = CommissionFormulaType & {
  tempLocalKey?: string;
};

// BizTopic.Commission
export const CommissionFormulaCategory = '14';
// BizTopic.CommissionClawback
export const CommissionFormulaClawBackCategory = '34';
export const CommissionSubCategory_GrossCommission = '9';

/**
 * 历史枚举渲染函数，只有使用Edittable组件的时候需要使用
 */
export const renderOldBizdictName = (
  key: string,
  optionList: {
    itemName: string;
    itemExtend1: string | number;
  }[],
): string => {
  const renderEnum = optionList.find(
    option => `${option.itemExtend1}` === `${key}`,
  );
  if (renderEnum) {
    return renderEnum.itemName;
  }
  return '';
};

export const useCommissionFormulaOptions = (
  formulaList: FormulaProps[],
  bizTopicCode: string,
  formulaSubCategory?: string,
) => {
  const commissionFormulaOptions = useMemo(
    () =>
      formulaList
        .filter(formula => {
          // 历史数据没有subcateogry，没有subcategory视为GrossCommission
          if (formulaSubCategory === CommissionSubCategory_GrossCommission) {
            return (
              `${formula.formulaTableTypeId}` === bizTopicCode &&
              (formula.formulaSubCategoryCode === formulaSubCategory ||
                !formula.formulaSubCategoryCode)
            );
          }
          return (
            `${formula.formulaTableTypeId}` === bizTopicCode &&
            formula.formulaSubCategoryCode === formulaSubCategory
          );
        })
        .map(formula => ({
          itemExtend1: formula.formulaCode,
          itemName: formula.formulaName,
        })),
    [formulaSubCategory, formulaList, bizTopicCode],
  );

  return commissionFormulaOptions;
};

export const CommissionTable = forwardRef(
  ({
    disabled,
    relatedInfo,
    setRelatedInfo,
    isTiedAgent,
  }: CommissionTableProps) => {
    const { t } = useTranslation('partner');
    const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
    const [editingSubCategory, setEditingSubCategory] = useState<string>();
    const formulaList: FormulaProps[] = useSelector(
      ({ partnerManagement }: ConnectState) => partnerManagement?.formulaList,
    );
    const commissionFormulaList: LocalCommissionFormulaType[] = (
      relatedInfo?.formulas || []
    )?.filter(
      formula => formula?.formulaCategory === CommissionFormulaCategory,
    );
    const commissionClawBackFormulaList: LocalCommissionFormulaType[] = (
      relatedInfo?.formulas || []
    )?.filter(
      formula => formula?.formulaCategory === CommissionFormulaClawBackCategory,
    );

    const formulaSubCategoryOptions = useMemo(
      () =>
        enums?.bizTopic
          ?.find(bizdict => bizdict.dictValue === CommissionFormulaCategory)
          ?.childList?.map(bizdict => ({
            itemExtend1: bizdict.dictValue,
            itemName: bizdict.dictValueName,
          })) || [],
      [enums?.bizTopic],
    );

    const formulaOptionsBySubCategory = useCommissionFormulaOptions(
      formulaList,
      CommissionFormulaCategory,
      editingSubCategory,
    );

    const salesCategoryOptions = useMemo(
      () =>
        enums?.salesCategory.map(({ enumItemName, dictValueName }) => ({
          itemExtend1: enumItemName,
          itemName: dictValueName,
        })),
      [enums?.salesCategory],
    );

    const totalCommissionFormulaOptions = useMemo(
      () =>
        formulaList
          .filter(
            formula =>
              `${formula.formulaTableTypeId}` === CommissionFormulaCategory,
          )
          .map(formula => ({
            itemExtend1: formula.formulaCode,
            itemName: formula.formulaName,
          })),
      [formulaList],
    );

    const renderOrderTooltip = useCallback(
      () => (
        <div>
          <p style={{ marginBottom: 6 }}>
            {t(
              'If Commission GST is configured based on NetCommission, then we could configure as',
            )}
          </p>
          <Table
            columns={[
              {
                title: t('Formula Category'),
                dataIndex: 'formulaType',
              },
              {
                title: t('Formula Sub Category'),
                dataIndex: 'formulaSubCategory',
              },
              {
                title: t('Formula Code'),
                dataIndex: 'formulaCode',
              },
              {
                title: t('Order'),
                dataIndex: 'formulaOrder',
              },
            ]}
            dataSource={[
              {
                formulaType: 'Service Fee & Commission',
                formulaCode: 'commNet',
                formulaOrder: 1,
                formulaSubCategory: 'Net Commission',
                key: 1,
              },
              {
                formulaType: 'Service Fee & Commission',
                formulaCode: 'commGST',
                formulaOrder: 2,
                formulaSubCategory: 'Commission GST',
                key: 2,
              },
            ]}
            pagination={false}
          />
          <p style={{ marginTop: 6 }}>
            {t(
              'and in formula <commGST> we could use schema factor netCommission calculated in order 1 to configure formula',
            )}
          </p>
        </div>
      ),
      [t],
    );

    const columns = useMemo(() => {
      const commissionTypeColumn = {
        title: (
          <span className="ant-form-item-required">{t('Commission Type')}</span>
        ),
        editable: true,
        dataIndex: 'salesCategory',
        inputType: 'select',
        selectOptions: salesCategoryOptions,
        width: '240px',
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => document.body,
        },
        render: (salesCategory: string) => (
          <ComponentWithFallback>
            {convertStatus(enums, salesCategory, 'salesCategory')}
          </ComponentWithFallback>
        ),
      };
      const internalColumns = [
        {
          title: (
            <span className="ant-form-item-required">
              {t('Formula Category')}
            </span>
          ),
          dataIndex: 'formulaCategory',
          editable: false,
          initialValue: CommissionFormulaCategory,
          width: '240px',
          render: () => (
            <ComponentWithFallback>
              {convertStatus(enums, CommissionFormulaCategory, 'bizTopic')}
            </ComponentWithFallback>
          ),
        },
        {
          title: (
            <span className="ant-form-item-required">
              {t('Formula Sub Category')}
            </span>
          ),
          editable: true,
          dataIndex: 'formulaSubCategory',
          inputType: 'select',
          selectOptions: formulaSubCategoryOptions,
          width: '240px',
          controllerprops: {
            required: true,
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => document.body,
            onChange: (formulaSubCategory: string) => {
              setEditingSubCategory(formulaSubCategory);
            },
          },
          render: (
            formulaSubCategory = CommissionSubCategory_GrossCommission,
          ): string =>
            renderOldBizdictName(formulaSubCategory, formulaSubCategoryOptions),
        },
        {
          title: (
            <span className="ant-form-item-required">{t('Formula Name')}</span>
          ),
          editable: true,
          dataIndex: 'formulaCode',
          inputType: 'select',
          selectOptions: formulaOptionsBySubCategory,
          controllerprops: {
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => document.body,
            disabled: !editingSubCategory,
          },
          width: '240px',
          render: (text: string) => (
            <ComponentWithFallback>
              {renderOldBizdictName(text, totalCommissionFormulaOptions)}
            </ComponentWithFallback>
          ),
        },
        {
          title: (
            <span>
              {t('Order')}
              <Popover
                placement="topRight"
                content={renderOrderTooltip()}
                getPopupContainer={() => document.body}
              >
                <Icon
                  type="exclamation-circle"
                  style={{ marginLeft: '5px', lineHeight: '20px' }}
                />
              </Popover>
            </span>
          ),
          editable: true,
          dataIndex: 'formulaOrder',
          inputType: 'number',
          width: '110px',
          controllerprops: {
            required: false,
            style: { width: 160 },
            min: 1,
            precision: 0,
            placeholder: t('Please input'),
          },
          render: (text: string) => (
            <ComponentWithFallback>{text}</ComponentWithFallback>
          ),
        },
      ];
      if (isTiedAgent) {
        internalColumns.splice(1, 0, commissionTypeColumn);
      }
      return internalColumns;
    }, [
      t,
      formulaSubCategoryOptions,
      formulaOptionsBySubCategory,
      editingSubCategory,
      renderOrderTooltip,
      enums,
      totalCommissionFormulaOptions,
      commissionFormulaList,
    ]);

    const handleSubmit = async (
      value: LocalCommissionFormulaType,
      key: string,
      index: number,
    ) => {
      const clonedData: LocalCommissionFormulaType[] = [
        ...commissionFormulaList,
      ];
      clonedData?.splice(index, key === 'add' ? 0 : 1, {
        ...value,
        tempLocalKey: value?.tempLocalKey || uuid(),
        formulaCategory: CommissionFormulaCategory,
      });
      // 检验formulaOrder是否有重复
      const orderList = clonedData
        ?.filter(item => !!item?.formulaOrder)
        ?.map(item => item?.formulaOrder);
      if (orderList.length > uniq(orderList)?.length) {
        message.error(t('Duplicate Order. Please change.'));
        return Promise.reject();
      }
      // FormulaCategory + FormulaSubCategory确定唯一性
      const formulaCategoryList = clonedData?.map(
        item =>
          item?.salesCategory +
          item?.formulaCategory +
          item?.formulaSubCategory,
      );
      if (clonedData.length > uniq(formulaCategoryList)?.length) {
        message.error(
          t(
            'Duplicate formula configuration for same formula category and sub category. Please edit original one.',
          ),
        );
        return Promise.reject();
      }
      setRelatedInfo({
        ...relatedInfo,
        formulas: [...clonedData, ...commissionClawBackFormulaList],
      });
    };

    const handleDelete = (_, index: number) => {
      const clonedData = [...commissionFormulaList];
      clonedData?.splice(index, 1);
      setRelatedInfo({
        ...relatedInfo,
        formulas: [...clonedData, ...commissionClawBackFormulaList],
      });
    };

    return (
      <Skeleton loading={false}>
        <h3>{t('Please select related formula for Commission')}</h3>
        <EditableTable
          className={commonStyles.commissionTable}
          disabled={disabled}
          newAddTop={true}
          editIcon={EditSquareOutlineSvg}
          deleteIcon={DeleteHollowOutlineSvg}
          data={commissionFormulaList?.map(item => ({
            ...item,
            key: item?.id || item?.tempLocalKey || uuid(),
          }))}
          columns={columns}
          onSubmit={handleSubmit}
          onDelete={handleDelete}
          scroll={{ x: 'max-content' }}
          customNoDataFlag={true}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Skeleton>
    );
  },
);
