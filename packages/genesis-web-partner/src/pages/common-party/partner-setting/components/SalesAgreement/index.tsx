/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { PlusOutlined } from '@ant-design/icons';
import { Button, Divider, Form, message } from 'antd';

import { useDispatch } from '@umijs/max';

import { cloneDeep, isEqual, uniqBy } from 'lodash-es';
import type { Moment } from 'moment';
import moment from 'moment';
import { v4 as uuid } from 'uuid';

import { DeleteAction, EditAction, FieldType, Table, ViewAction } from '@zhongan/nagrand-ui';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type { AgreementType, BizDictItem, RelatedType, SelectOptions } from 'genesis-web-service';
import { ChannelService, SalesTypeEnum, YesOrNo } from 'genesis-web-service';
import { YesOrNoDictNumberValue } from 'genesis-web-service/lib/common.interface';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import antdStyles from '@/antd-reset.scss';
import { CommonForm } from '@/components/CommonForm/Form';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { AddNewTitle } from '@/pages/common-party/partner-setting/components/AddNewTitle';
import { useFormFields, useRelatedFormFields } from '@/pages/common-party/partner-setting/hooks/useFormFields';
import { ChannelTypeEnum, FormTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import type { SchemaProps } from '@/types/common';
import { ModeEnum } from '@/types/common';
import { transferSchemaToTableProp } from '@/utils/utils';

import {
  EntityType,
  covertChannelType2SchemaChannelType,
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
  useGenericSchemaFormItemFields,
} from '../../../../../hooks/useGenericSchemaFormItemFields';
import styles from '../ChannelCard/style.scss';
import { CommissionClawBackTable } from './CommissionClawbackTable';
import { CommissionTable } from './CommissionTable';
import { CopySalesAgreementDrawer } from './CopySalesAgreementDrawer';
import { SalesRelatedInfoTable } from './SalesRelatedInfoTable';

interface Props {
  readonly?: boolean;
  channelCode: string;
  renew?: boolean;
  salesType?: SalesTypeEnum;
  schemaCategory?: string;
  channelType: ChannelTypeEnum;
  disabled: boolean;
}

// 本地编辑新增的Agreement时，会给一个临时的localTempKey作为id来增删改查
export type LocalAgreementType = AgreementType & {
  localTempKey?: string;
  effectivePeriod?: [Moment, Moment];
  isCopied?: boolean;
};
type LocalRelatedType = RelatedType & { localTempKey?: string };

export const optionsFormat = (options: Record<string, string>[], label: string | string[], value: string) => {
  return options.map(option => {
    let labelNew: string = '';
    if (typeof label !== 'string') {
      // 拼接成valueA_valueB的格式
      label.forEach(item => {
        labelNew += `${option[item]}_`;
      });
      labelNew = labelNew.slice(0, -1);
    } else {
      labelNew = option[label];
    }
    return {
      label: labelNew,
      value: option[value],
    };
  });
};

/**
 * @param renew 是否需要重新获取list
 * @param channelCode 查询code
 * @param salesType 区分agent/channel/staff
 *
 * @description Agreement信息展示
 */
export const SalesAgreement = forwardRef(
  (
    {
      channelCode,
      renew,
      salesType,
      schemaCategory,
      channelType,
      readonly: defaultReadonly,
      disabled: defaultDisabled,
    }: Props,
    ref
  ) => {
    const { t } = useTranslation(['partner']);
    const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
    const [form] = Form.useForm();
    const [relatedForm] = Form.useForm();
    const teamCode = Form.useWatch('teamCode', form);
    const copySalesAgreementRef = useRef(null);
    const dispatch = useDispatch();
    const isTiedAgent = SalesTypeEnum.AGENT === salesType;
    const [agreementDetailVisible, setAgreementDetailVisible] = useState(false);
    const [salesAgreementList, setSalesAgreementList] = useState<LocalAgreementType[]>();
    const [currentEditingAgreement, setEditingAgreement] = useState<LocalAgreementType>();
    const [relatedInfoDrawerType, setRelatedInfoDrawerType] = useState<ModeEnum>();
    const [relatedInfoVisible, setRelatedInfoVisible] = useState(false);
    const [relatedInfoList, setRelatedInfoList] = useState<LocalRelatedType[]>([]);
    const [currentEditingRelationInfo, setEditingRelateInfo] = useState<LocalRelatedType>();
    const [copyAgreementDrawerVisible, setCopyAgreementDrawerVisible] = useState(false);
    const [teamOptions, setTeamOptions] = useState<Record<string, string>[]>([]);
    const [goodsListData, setGoodsListData] = useState<Record<string, string>[]>([]);
    const [goodsOptions, setGoodsOptions] = useState<SelectOptions[]>([]);
    const [readonly, setReadonly] = useState(false);
    const disabled = defaultReadonly || defaultDisabled;

    const RelatedInfoDrawerTitleEnum: Record<string, string> = {
      EDIT: t('Edit'),
      ADD: t('Add'),
      READ: t('View'),
    };

    const allowEndDateOfSalesAgreementToBeNull = useMemo(
      () => enums.allowEndDateOfSalesAgreementToBeNull?.[0]?.dictValue === YesOrNoDictNumberValue.YES?.toString(),
      [enums]
    );

    const endDateDefaultValue = useMemo(() => enums.allowEndDateOfSalesAgreementToBeNull?.[0]?.itemExtend1, [enums]);

    const fields = useFormFields({
      formType: FormTypeEnum.SALES_AGREEMENT,
      disabled: readonly,
      form,
      editedItem: currentEditingAgreement,
      goodsOptions: teamOptions,
    }) as SchemaProps[];

    const { formItems: staticFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'STATIC',
      category: schemaCategory ?? 'AGREEMENT',
      disabled: readonly,
      type: covertChannelType2SchemaChannelType(channelType),
      entityType: EntityType.CHANNEL,
    });

    const processedStaticFields = useMemo(() => {
      staticFields.splice(
        -2,
        0,
        fields.find(field => field.key === 'effectivePeriod')
      );
      staticFields.forEach(field => {
        const props = field.ctrlProps ?? {};
        switch (field.key) {
          case 'teamCode':
            field.type = FieldType.Select;
            props.options = teamOptions;
            break;
        }
      });
      return channelType === ChannelTypeEnum.INSURANCE ? fields : staticFields;
    }, [fields, staticFields]);

    const { formItems: dynamicFields, schemaFields } = useGenericSchemaFormItemFields({
      staticOrDynamic: 'DYNAMIC',
      category: schemaCategory ?? 'AGREEMENT',
      disabled: readonly,
      type: covertChannelType2SchemaChannelType(channelType),
      entityType: EntityType.CHANNEL,
    });

    const relatedFields = useRelatedFormFields({
      disabled: relatedInfoDrawerType === ModeEnum.READ,
      goodsOptions: uniqBy(goodsOptions, 'value'),
      editedItem: currentEditingRelationInfo,
    });

    const queryAgreementList = () => {
      ChannelService.querySalesAgreementList(channelCode)
        .then(agreementList =>
          setSalesAgreementList(
            agreementList
              ?.filter(info => info?.slaveAgreementRelated === YesOrNo.NO)
              ?.map(info => ({ ...info, localTempKey: uuid() }))
          )
        )
        .catch((error: Error) => message.error(error.message));
    };

    const saveAgreement = (code: string, id: number, type: string) => {
      ChannelService.addSalesAgreement(code, {
        agreements: salesAgreementList,
        salesChannelId: id,
        salesChannelType: type,
      })
        .then(() => {
          if (renew) {
            queryAgreementList();
          }
        })
        .catch((error: Error) => message.error(error.message));
    };

    useImperativeHandle(ref, () => {
      return { saveAgreement };
    });

    useEffect(() => {
      dispatch({
        type: 'global/getTenantBizDict',
        payload: ['bizTopic', 'salesCategory', 'allowEndDateOfSalesAgreementToBeNull'],
      });
      // 获取formula列表
      dispatch({
        type: 'partnerManagement/getFormulaList',
      });
    }, [dispatch]);

    useEffect(() => {
      if (channelCode) {
        queryAgreementList();
      }
      ChannelService.queryTeamList().then(res => {
        setTeamOptions(optionsFormat(res, ['teamName', 'teamCode'], 'teamCode'));
      });
    }, [channelCode]);

    useEffect(() => {
      if (agreementDetailVisible && teamCode) {
        ChannelService.queryGoodsList(teamCode).then(res => {
          setGoodsListData(res);
        });
      }
    }, [teamCode, agreementDetailVisible]);

    const onBasicInfoValuesChange = useCallback(
      (changedValues: Partial<LocalAgreementType>, values: LocalAgreementType) => {
        if (changedValues.teamCode) {
          setRelatedInfoList([]);
        }
        // 不是copy的就不比较了
        if (!copySalesAgreementRef?.current) return;
        // Sales Agreement Code不同不用比较, 删除id
        if (values.agreementCode !== copySalesAgreementRef?.current?.agreementCode) return;

        const comparedKeys: (keyof AgreementType)[] = [
          'agreementName',
          'effectiveStartTime',
          'effectiveEndTime',
          'agreementStatus',
          'zoneId',
          'teamCode',
          'hadUsagePermission',
        ];

        values.effectiveStartTime = dateFormatInstance.formatTz(
          moment(values?.effectivePeriod[0]).startOf('day'),
          values.zoneId
        );
        values.effectiveEndTime = dateFormatInstance.formatTz(
          moment(values?.effectivePeriod[1]).endOf('day'),
          values.zoneId
        );
        comparedKeys.every(key => {
          const isSame = (agreementKey: keyof AgreementType) => {
            if (['effectiveStartTime', 'effectiveEndTime'].includes(agreementKey)) {
              return moment(values[agreementKey]).isSame(copySalesAgreementRef?.current?.[agreementKey]);
            } else {
              return isEqual(values[agreementKey], copySalesAgreementRef?.current?.[agreementKey]);
            }
          };
          if (!isSame(key)) {
            form.setFields([
              {
                name: 'agreementCode',
                errors: [
                  t('Service Agreement information has been updated. Please submit a new service agreement code.'),
                ],
              },
            ]);
          } else {
            form.setFields([
              {
                name: 'agreementCode',
                errors: undefined,
              },
            ]);
          }
          return isSame(key);
        });
      },
      [copySalesAgreementRef?.current, setGoodsListData, setRelatedInfoList, queryAgreementList, form]
    );

    const editClick = useCallback(
      item => {
        item.effectivePeriod = [
          moment.tz(item.effectiveStartTime, item.zoneId),
          moment.tz(item.effectiveEndTime, item.zoneId),
        ];
        setEditingAgreement(item);
        dealSchemaFieldValueForDisplay(item?.extensions, schemaFields);
        form.setFieldsValue(item);
        setRelatedInfoList(
          (item.related || [])?.map((info: LocalRelatedType) => ({
            ...info,
            localTempKey: uuid(),
          }))
        );
        setAgreementDetailVisible(true);
      },
      [form, schemaFields]
    );

    const deleteClick = useCallback(
      (index: number) => {
        const cloneList = cloneDeep(salesAgreementList || []);
        cloneList.splice(index, 1);
        setSalesAgreementList(cloneList);
      },
      [salesAgreementList]
    );

    const onClose = useCallback(() => {
      setAgreementDetailVisible(false);
      setEditingAgreement(null);
      setRelatedInfoList([]);
      form.resetFields();
    }, [form]);

    const onSubmit = useCallback(() => {
      form.validateFields().then(async values => {
        const cloneList = cloneDeep(salesAgreementList || []);

        // 本地编辑的key，用来做增删改查
        values.localTempKey = currentEditingAgreement?.localTempKey || uuid();
        values.related = [...(relatedInfoList || [])];
        values.effectiveStartTime = dateFormatInstance.formatTz(
          moment(allowEndDateOfSalesAgreementToBeNull ? values.effectiveStartTime : values.effectivePeriod[0]).startOf(
            'day'
          ),
          values.zoneId
        );

        values.effectiveEndTime = dateFormatInstance.formatTz(
          moment(allowEndDateOfSalesAgreementToBeNull ? endDateDefaultValue : values.effectivePeriod[1]).endOf('day'),
          values.zoneId
        );
        delete values.effectivePeriod;
        dealSchemaFieldValueForSubmit(values.extensions, schemaFields, { dateFormatInstance });

        const handleSalesAgreementList = () => {
          // 有editedItem代表编辑，根据id+temp key查找替换原来的item，没有代表新增
          const index = currentEditingAgreement
            ? cloneList?.findIndex(
                agreement => `${values?.id}${values?.localTempKey}` === `${agreement?.id}${agreement?.localTempKey}`
              )
            : cloneList?.length;
          cloneList.splice(index, currentEditingAgreement ? 1 : 0, values);
          setSalesAgreementList(cloneList);
          onClose();
        };

        // 新增copy
        if (copySalesAgreementRef?.current) {
          values.isCopied = true;
          values.salesCode = copySalesAgreementRef?.current?.salesCode;
          if (copySalesAgreementRef?.current?.agreementCode === values?.agreementCode) {
            values.id = copySalesAgreementRef?.current?.id;
          } else {
            delete values.id;
          }
          await ChannelService.validateSalesAgreementIsDuplicate(
            copySalesAgreementRef?.current?.salesCode,
            copySalesAgreementRef?.current?.id,
            values
          )
            .then(() => handleSalesAgreementList())
            .catch((err: Error) => {
              form.setFields([
                {
                  name: 'agreementCode',
                  errors: [err?.message],
                },
              ]);
            });
        } else if (currentEditingAgreement?.isCopied && currentEditingAgreement?.id) {
          // 修改copy之后储存在本地，但还未提交
          values.isCopied = true;
          values.salesCode = currentEditingAgreement?.salesCode;
          if (currentEditingAgreement?.agreementCode === values?.agreementCode) {
            values.id = currentEditingAgreement?.id;
          } else {
            delete values.id;
          }
          await ChannelService.validateSalesAgreementIsDuplicate(
            currentEditingAgreement?.salesCode,
            currentEditingAgreement?.id,
            values
          )
            .then(() => handleSalesAgreementList())
            .catch((err: Error) => {
              form.setFields([
                {
                  name: 'agreementCode',
                  errors: [err?.message],
                },
              ]);
            });
        } else {
          values.id = currentEditingAgreement?.id;
          values.isCopied = currentEditingAgreement?.isCopied;
          // 手动新增+修改手动添加+修改已经save过的
          handleSalesAgreementList();
          if (!currentEditingAgreement?.isCopied && currentEditingAgreement?.id) {
            // 如果是修改已经save过的，提示一下用户可能会联动修改别的
            message.warning(
              t(
                'If other sales channels have used this sales agreement, editing it will impact the related sales channels. Please check before making changes.'
              )
            );
          }
        }
      });
    }, [salesAgreementList, form, currentEditingAgreement, onClose, t, relatedInfoList, schemaFields]);

    const title = useMemo(() => {
      if (readonly) {
        return t('View Sales Agreement');
      } else if (currentEditingAgreement) {
        return t('Edit Sales Agreement');
      } else {
        return t('Add New Sales Agreement');
      }
    }, [readonly, currentEditingAgreement, t]);

    // 根据已经添加过的goodsId过滤goods列表
    const filterGoodsListByAddedGoodsId = (goodsListInnerData: Record<string, string>[]) =>
      goodsListInnerData &&
      goodsListInnerData.filter(
        goodsItem => !relatedInfoList.some(tableItem => tableItem.goodsId === goodsItem.goodsId)
      );

    const viewDetail = useCallback(
      (handleType: ModeEnum, item?: LocalRelatedType) => {
        const goodsListDataTrans =
          handleType === ModeEnum.ADD ? filterGoodsListByAddedGoodsId(goodsListData) : goodsListData;
        setGoodsOptions(() => optionsFormat(goodsListDataTrans, 'goodsCode', 'goodsId'));
        setRelatedInfoVisible(true);
        if (item) {
          setEditingRelateInfo(item);
        }
        setRelatedInfoDrawerType(handleType);
        dealSchemaFieldValueForDisplay(item?.extensions, schemaFields);
        relatedForm.setFieldsValue(item);
      },
      [goodsOptions, goodsListData, relatedInfoList, schemaFields]
    );

    const onRelatedClose = () => {
      setRelatedInfoVisible(false);
      setEditingRelateInfo(null);
      relatedForm.resetFields();
    };

    const onConfirm = () => {
      relatedForm.validateFields().then(values => {
        const cloneList = [...relatedInfoList];
        values.id = currentEditingRelationInfo?.id;
        values.localTempKey = currentEditingRelationInfo?.localTempKey || uuid();

        if (currentEditingRelationInfo?.formulas) {
          values.formulas = currentEditingRelationInfo?.formulas;
          delete currentEditingRelationInfo.formulas;
        }
        values.goodsCode = goodsOptions.find(item => item.value === values.goodsId)?.label;
        // add的时候，currentEditingRelationInfo === undefined || {}(添加了formula会是{})
        const isAddRelation =
          currentEditingRelationInfo === undefined || Object.keys(currentEditingRelationInfo || {})?.length === 0;
        // 有editedItem代表编辑，根据id+temp key查找替换原来的index，没有的话在首位新增
        const currentEditingIndex = isAddRelation
          ? 0
          : cloneList?.findIndex(
              relation => `${values?.id}${values?.localTempKey}` === `${relation?.id}${relation?.localTempKey}`
            );
        cloneList.splice(currentEditingIndex, isAddRelation ? 0 : 1, values);
        setRelatedInfoList(cloneList);
        onRelatedClose();
      });
    };

    const onRelatedDelete = useCallback(
      (index: number) => {
        const cloneList = cloneDeep(relatedInfoList || []);
        cloneList.splice(index, 1);
        setRelatedInfoList(cloneList);
      },
      [relatedInfoList]
    );

    const onCopySalesAgreementSubmit = () => {
      const selectedSalesAgreement = copySalesAgreementRef?.current;
      selectedSalesAgreement.effectivePeriod = [
        moment.tz(selectedSalesAgreement.effectiveStartTime, selectedSalesAgreement.zoneId),
        moment.tz(selectedSalesAgreement.effectiveEndTime, selectedSalesAgreement.zoneId),
      ];
      dealSchemaFieldValueForDisplay(selectedSalesAgreement?.extensions, schemaFields);
      form.setFieldsValue(selectedSalesAgreement);
      setRelatedInfoList(
        (selectedSalesAgreement?.related || [])?.map((info: LocalRelatedType) => ({
          ...info,
          localTempKey: uuid(),
        }))
      );
      setCopyAgreementDrawerVisible(false);
    };

    return (
      <section className={styles.addressInfo}>
        <AddNewTitle
          title={t('Sales Agreement')}
          onAddClick={() => {
            setAgreementDetailVisible(true);
            setReadonly(false);
          }}
          readonly={disabled}
        />
        <Table
          columns={[
            ...transferSchemaToTableProp([...processedStaticFields, ...dynamicFields] as SchemaProps[]).map(field => {
              if (field.dataIndex === 'effectivePeriod') {
                field.render = (_, record) =>
                  allowEndDateOfSalesAgreementToBeNull
                    ? dateFormatInstance.getDateTimeString(record.effectiveStartTime, record.zoneId)
                    : dateFormatInstance.getDateRangeString(
                        record.effectiveStartTime,
                        record.effectiveEndTime,
                        record.zoneId
                      );
              }

              return field;
            }),
            {
              title: t('Action'),
              render: (_, record, index) => (
                <>
                  <ViewAction
                    onClick={() => {
                      editClick(record);
                      setReadonly(true);
                    }}
                  />
                  <EditAction
                    disabled={disabled}
                    onClick={() => {
                      editClick(record);
                      setReadonly(false);
                    }}
                  />
                  <DeleteAction
                    disabled={disabled}
                    deleteConfirmContent={t('Are you sure to delete?')}
                    onClick={() => deleteClick(index)}
                  />
                </>
              ),
            },
          ]}
          dataSource={salesAgreementList}
          pagination={false}
          scroll={{ x: 'max-content' }}
        />

        <DrawerForm
          title={title}
          visible={agreementDetailVisible}
          closable={false}
          onClose={onClose}
          onSubmit={onSubmit}
          cancelText={t('Cancel')}
          sendText={t('Submit')}
          submitBtnShow={!readonly}
          className={antdStyles.antdChannelCenter}
        >
          {!readonly && (!currentEditingAgreement?.id || currentEditingAgreement?.isCopied) && (
            <Button className={styles.linkButton} type="link" onClick={() => setCopyAgreementDrawerVisible(true)}>
              {t('Fill in the existing sales agreement')}
            </Button>
          )}
          <CopySalesAgreementDrawer
            salesType={salesType}
            visible={copyAgreementDrawerVisible}
            onSubmit={onCopySalesAgreementSubmit}
            onClose={() => setCopyAgreementDrawerVisible(false)}
            ref={copySalesAgreementRef}
          />
          <div className={styles.agreementTitle}>{t('Basic Information')}</div>
          <CommonForm
            fields={[...processedStaticFields, ...dynamicFields]}
            form={form}
            onValuesChange={onBasicInfoValuesChange}
          />
          <Divider />
          <div className={styles.agreementTitle}>
            <span>{t('Sales Related Information')}</span>
            <Button icon={<PlusOutlined />} disabled={readonly} onClick={() => viewDetail(ModeEnum.ADD)}>
              {t('Add New')}
            </Button>
          </div>
          <SalesRelatedInfoTable
            data={relatedInfoList}
            viewDetail={viewDetail}
            handleDelete={onRelatedDelete}
            readonly={readonly}
          />
        </DrawerForm>
        <DrawerForm
          title={RelatedInfoDrawerTitleEnum[relatedInfoDrawerType]}
          visible={relatedInfoVisible}
          closable={false}
          onClose={onRelatedClose}
          onSubmit={onConfirm}
          cancelText={t('Cancel')}
          sendText={t('Confirm')}
          submitBtnShow={!readonly}
          className={antdStyles.antdChannelCenter}
          width={1000}
        >
          <div className={styles.agreementTitle}>{t('Basic Information')}</div>
          <CommonForm fields={relatedFields} form={relatedForm} />
          <Divider />
          <CommissionTable
            isTiedAgent={isTiedAgent}
            relatedInfo={currentEditingRelationInfo}
            setRelatedInfo={setEditingRelateInfo}
            disabled={relatedInfoDrawerType === ModeEnum.READ}
          />
          <Divider />
          <CommissionClawBackTable
            isTiedAgent={isTiedAgent}
            relatedInfo={currentEditingRelationInfo}
            setRelatedInfo={setEditingRelateInfo}
            disabled={relatedInfoDrawerType === ModeEnum.READ}
          />
        </DrawerForm>
      </section>
    );
  }
);
