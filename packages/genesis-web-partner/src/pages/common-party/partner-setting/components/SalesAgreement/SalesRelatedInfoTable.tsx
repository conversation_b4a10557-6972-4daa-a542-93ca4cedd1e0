import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Tooltip } from 'antd';
import type { ColumnProps } from 'antd/es/table';

import { Icon, Table, TableActionsContainer } from '@zhongan/nagrand-ui';

import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import type { BizDictItem, RelatedType } from 'genesis-web-service';

import { RenderEnums } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import { ModeEnum } from '@/types/common';

interface TableProp {
  loading?: boolean;
  data: RelatedType[];
  viewDetail: (type: ModeEnum, item?: RelatedType, index?: number) => void;
  handleDelete: (channelId: number) => void;
  readonly: boolean;
}

/**
 *
 * @param loading 是否在加载
 * @param data 表格数据
 * @param viewDetail 跳转method
 * @param handleDelete delete method
 * @description 用于partner setting agencyCompany/salesChannel/serviceCompany menu component； 数据展示
 */
export const SalesRelatedInfoTable = ({ loading, data, viewDetail, handleDelete, readonly }: TableProp) => {
  const { t } = useTranslation('partner');
  const { canEdit } = usePartnerSettingPermission();
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const columns: ColumnProps<RelatedType>[] = useMemo(
    () => [
      {
        title: t('Goods'),
        dataIndex: 'goodsCode',
      },
      {
        title: t('Settlement Rule'),
        dataIndex: 'settlementRule',
        render: (settlementRule: string) => <RenderEnums keyName={settlementRule} enums={enums?.settlementRule} />,
      },
      {
        title: t('Accumulated to Premium'),
        dataIndex: 'settlementToPremium',
        render: (settlementToPremium: string) => <RenderEnums keyName={settlementToPremium} enums={enums?.yesNo} />,
      },
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (_, item: RelatedType, index: number) => (
          <div className="table-actions">
            <TableActionsContainer>
              <Tooltip title={t('View')}>
                <Button
                  icon={<Icon type="view" />}
                  type="link"
                  onClick={() => viewDetail(ModeEnum.READ, item, index)}
                />
              </Tooltip>
              <Tooltip title={t('Edit')}>
                <Button
                  icon={<Icon type="edit" />}
                  type="link"
                  disabled={readonly}
                  onClick={() => viewDetail(ModeEnum.EDIT, item, index)}
                />
              </Tooltip>
              <DeleteConfirm onOk={() => handleDelete(index)}>
                <Tooltip title={t('Delete')}>
                  <Button icon={<Icon type="delete" />} disabled={readonly} type="link" />
                </Tooltip>
              </DeleteConfirm>
            </TableActionsContainer>
          </div>
        ),
      },
    ],
    [viewDetail, t, handleDelete, canEdit]
  );

  return (
    <Table
      scroll={{ x: 'max-content' }}
      loading={loading}
      dataSource={data}
      columns={columns}
      rowKey="id"
      pagination={false}
      style={{ marginBottom: 16 }}
    />
  );
};
