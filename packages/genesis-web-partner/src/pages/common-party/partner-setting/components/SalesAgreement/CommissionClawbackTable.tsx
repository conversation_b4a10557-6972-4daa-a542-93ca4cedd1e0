/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-08-11 17:00:27
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-08-31 12:00:12
 * @Description:
 */
import { forwardRef, useCallback, useMemo, useState } from 'react';
import { Skeleton, message, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import { useSelector } from '@umijs/max';
import { uniq } from 'lodash-es';
import type { BizDictItem, FormulaProps } from 'genesis-web-service';
import type { RelatedType } from 'genesis-web-service';
import {
  ComponentWithFallback,
  EditableFormTable as EditableTable,
  convertStatus,
} from 'genesis-web-component/lib/components';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import type { ConnectState } from '@/models/connect';
import { Icon } from '@zhongan/nagrand-ui';
import EditSquareOutlineSvg from '@/assets/svg/EditSquareOutline.svg';
import DeleteHollowOutlineSvg from '@/assets/svg/DeleteHollowOutline.svg';
import type { LocalCommissionFormulaType } from './CommissionTable';
import {
  CommissionFormulaCategory,
  CommissionFormulaClawBackCategory,
  CommissionSubCategory_GrossCommission,
  renderOldBizdictName,
  useCommissionFormulaOptions,
} from './CommissionTable';
import commonStyles from '../../style.scss';

interface CommissionClawBackTableProps {
  disabled: boolean;
  relatedInfo: RelatedType;
  setRelatedInfo: (relatedInfo?: RelatedType) => void;
  isTiedAgent?: boolean;
}

export const CommissionClawBackTable = forwardRef(
  ({
    disabled,
    relatedInfo,
    setRelatedInfo,
    isTiedAgent,
  }: CommissionClawBackTableProps) => {
    const { t } = useTranslation('partner');
    const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
    const [editingSubCategory, setEditingSubCategory] = useState<string>();
    const formulaList: FormulaProps[] = useSelector(
      ({ partnerManagement }: ConnectState) => partnerManagement?.formulaList,
    );
    const commissionFormulaList: LocalCommissionFormulaType[] = (
      relatedInfo?.formulas || []
    )?.filter(
      formula => formula?.formulaCategory === CommissionFormulaCategory,
    );
    const commissionClawBackFormulaList: LocalCommissionFormulaType[] = (
      relatedInfo?.formulas || []
    )?.filter(
      formula => formula?.formulaCategory === CommissionFormulaClawBackCategory,
    );

    const formulaSubCategoryOptions = useMemo(
      () =>
        enums?.bizTopic
          ?.find(
            bizDict => bizDict.dictValue === CommissionFormulaClawBackCategory,
          )
          ?.childList?.map(bizDict => ({
            itemExtend1: bizDict.dictValue,
            itemName: bizDict.dictValueName,
          })) || [],
      [enums?.bizTopic],
    );

    const formulaOptionsBySubCategory = useCommissionFormulaOptions(
      formulaList,
      CommissionFormulaClawBackCategory,
      editingSubCategory,
    );

    const salesCategoryOptions = useMemo(
      () =>
        enums?.salesCategory.map(({ enumItemName, dictValueName }) => ({
          itemExtend1: enumItemName,
          itemName: dictValueName,
        })),
      [enums?.salesCategory],
    );

    const totalCommissionFormulaOptions = useMemo(
      () =>
        formulaList
          .filter(
            formula =>
              `${formula.formulaTableTypeId}` ===
              CommissionFormulaClawBackCategory,
          )
          .map(formula => ({
            itemExtend1: formula.formulaCode,
            itemName: formula.formulaName,
          })),
      [formulaList],
    );

    const renderClawBackFormulaLabel = useCallback(
      () => (
        <span className="ant-form-item-required">
          {t('Formula Name (Clawback)')}
          <Tooltip
            placement="top"
            title={t(
              'Clawback calculation method is configured here while where to trigger clawback calculation is defined in business module configuration such as POS item agreement.',
            )}
          >
            <Icon
              type="exclamation-circle"
              style={{ marginLeft: '5px', lineHeight: '20px' }}
            />
          </Tooltip>
        </span>
      ),
      [t],
    );

    const columns = useMemo(() => {
      const commissionTypeColumn = {
        title: (
          <span className="ant-form-item-required">{t('Commission Type')}</span>
        ),
        editable: true,
        dataIndex: 'salesCategory',
        inputType: 'select',
        selectOptions: salesCategoryOptions,
        width: '240px',
        controllerprops: {
          disabled: false,
          required: true,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => document.body,
        },
        render: (salesCategory: string) => (
          <ComponentWithFallback>
            {convertStatus(enums, salesCategory, 'salesCategory')}
          </ComponentWithFallback>
        ),
      };
      const internalColumns = [
        {
          title: (
            <span className="ant-form-item-required">
              {t('Formula Category (Clawback)')}
            </span>
          ),
          dataIndex: 'formulaCategory',
          editable: false,
          width: '240px',
          render: () => (
            <ComponentWithFallback>
              {convertStatus(
                enums,
                CommissionFormulaClawBackCategory,
                'bizTopic',
              )}
            </ComponentWithFallback>
          ),
        },
        {
          title: (
            <span className="ant-form-item-required">
              {t('Formula Sub Category (Clawback)')}
            </span>
          ),
          editable: true,
          dataIndex: 'formulaSubCategory',
          inputType: 'select',
          selectOptions: formulaSubCategoryOptions,
          width: '240px',
          controllerprops: {
            required: true,
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => document.body,
            onChange: (formulaSubCategory: string) => {
              setEditingSubCategory(formulaSubCategory);
            },
          },
          render: (
            formulaSubCategory = CommissionSubCategory_GrossCommission,
          ): string =>
            renderOldBizdictName(formulaSubCategory, formulaSubCategoryOptions),
        },
        {
          title: renderClawBackFormulaLabel(),
          editable: true,
          dataIndex: 'formulaCode',
          inputType: 'select',
          selectOptions: formulaOptionsBySubCategory,
          controllerprops: {
            allowClear: false,
            style: { width: 240 },
            getPopupContainer: () => document.body,
            disabled: !editingSubCategory,
          },
          width: '240px',
          render: (text: string) => (
            <ComponentWithFallback>
              {renderOldBizdictName(text, totalCommissionFormulaOptions)}
            </ComponentWithFallback>
          ),
        },
      ];
      if (isTiedAgent) {
        internalColumns.splice(1, 0, commissionTypeColumn);
      }
      return internalColumns;
    }, [
      t,
      formulaSubCategoryOptions,
      formulaOptionsBySubCategory,
      editingSubCategory,
      enums?.bizTopic,
      totalCommissionFormulaOptions,
      commissionClawBackFormulaList,
    ]);

    const handleSubmit = async (value: any, key: string, index: number) => {
      const clonedData: LocalCommissionFormulaType[] = [
        ...commissionClawBackFormulaList,
      ];
      clonedData?.splice(index, key === 'add' ? 0 : 1, {
        ...value,
        tempLocalKey: value?.tempLocalKey || uuid(),
        formulaCategory: CommissionFormulaClawBackCategory,
      });
      // FormulaCategory + FormulaSubCategory确定唯一性
      const formulaCategoryList = clonedData?.map(
        item =>
          item?.salesCategory +
          item?.formulaCategory +
          item?.formulaSubCategory,
      );
      if (clonedData.length > uniq(formulaCategoryList)?.length) {
        message.error(
          t(
            'Duplicate formula configuration for same formula category and sub category. Please edit original one.',
          ),
        );
        return Promise.reject();
      }
      setRelatedInfo({
        ...relatedInfo,
        formulas: [...clonedData, ...commissionFormulaList],
      });
    };

    const handleDelete = (_, index: number) => {
      const clonedData = [...commissionClawBackFormulaList];
      clonedData?.splice(index, 1);
      setRelatedInfo({
        ...relatedInfo,
        formulas: [...clonedData, ...commissionFormulaList],
      });
    };

    return (
      <Skeleton loading={false}>
        <h3>{t('Please select related formula for Commission Clawback')}</h3>
        <EditableTable
          className={commonStyles.commissionTable}
          disabled={disabled}
          newAddTop={true}
          editIcon={EditSquareOutlineSvg}
          deleteIcon={DeleteHollowOutlineSvg}
          data={commissionClawBackFormulaList?.map(item => ({
            ...item,
            key: item?.id || item?.tempLocalKey || uuid(),
          }))}
          columns={columns}
          onSubmit={handleSubmit}
          onDelete={handleDelete}
          scroll={{ x: 'max-content' }}
          customNoDataFlag={true}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Skeleton>
    );
  },
);
