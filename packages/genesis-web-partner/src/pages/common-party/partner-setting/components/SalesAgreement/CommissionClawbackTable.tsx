import { forwardRef, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Skeleton, Tooltip, message } from 'antd';

import { useSelector } from '@umijs/max';

import { uniq } from 'lodash-es';

import { EditableTable, FieldType, Icon } from '@zhongan/nagrand-ui';

import { ComponentWithFallback, convertStatus } from 'genesis-web-component/lib/components';
import type { BizDictItem, CommissionFormulaType, FormulaProps, RelatedType } from 'genesis-web-service';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import type { ConnectState } from '@/models/connect';

import commonStyles from '../../style.scss';
import {
  CommissionFormulaCategory,
  CommissionFormulaClawBackCategory,
  CommissionSubCategory_GrossCommission,
  renderOldBizdictName,
  useCommissionFormulaOptions,
} from './CommissionTable';

interface CommissionClawBackTableProps {
  disabled: boolean;
  relatedInfo: RelatedType;
  setRelatedInfo: (relatedInfo?: RelatedType) => void;
  isTiedAgent?: boolean;
}

export const CommissionClawBackTable = forwardRef(
  ({ disabled, relatedInfo, setRelatedInfo, isTiedAgent }: CommissionClawBackTableProps) => {
    const { t } = useTranslation('partner');
    const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
    const [editingSubCategory, setEditingSubCategory] = useState<string>();
    const formulaList: FormulaProps[] = useSelector(
      ({ partnerManagement }: ConnectState) => partnerManagement?.formulaList
    );
    const commissionFormulaList: CommissionFormulaType[] = (relatedInfo?.formulas || [])?.filter(
      formula => formula?.formulaCategory === CommissionFormulaCategory
    );
    const commissionClawBackFormulaList: CommissionFormulaType[] = (relatedInfo?.formulas || [])?.filter(
      formula => formula?.formulaCategory === CommissionFormulaClawBackCategory
    );

    const [dataSource, setDataSource] = useState(commissionClawBackFormulaList);
    const formulaSubCategoryOptions = useMemo(
      () =>
        enums?.bizTopic
          ?.find(bizDict => bizDict.dictValue === CommissionFormulaClawBackCategory)
          ?.childList?.map(bizDict => ({
            itemExtend1: bizDict.dictValue,
            itemName: bizDict.dictValueName,
          })) || [],
      [enums?.bizTopic]
    );

    const formulaOptionsBySubCategory = useCommissionFormulaOptions(
      formulaList,
      CommissionFormulaClawBackCategory,
      editingSubCategory
    );

    const salesCategoryOptions = useMemo(
      () =>
        enums?.salesCategory?.map(({ enumItemName, dictValueName }) => ({
          itemExtend1: enumItemName,
          itemName: dictValueName,
        })),
      [enums?.salesCategory]
    );

    const totalCommissionFormulaOptions = useMemo(
      () =>
        formulaList
          .filter(formula => `${formula.formulaTableTypeId}` === CommissionFormulaClawBackCategory)
          .map(formula => ({
            itemExtend1: formula.formulaCode,
            itemName: formula.formulaName,
          })),
      [formulaList]
    );

    const renderClawBackFormulaLabel = useMemo(
      () => (
        <span>
          {t('Formula Name (Clawback)')}
          <Tooltip
            placement="top"
            title={t(
              'Clawback calculation method is configured here while where to trigger clawback calculation is defined in business module configuration such as POS item agreement.'
            )}
          >
            <Icon type="exclamation-circle" style={{ marginLeft: '5px', lineHeight: '20px' }} />
          </Tooltip>
        </span>
      ),
      [t]
    );

    const columns = useMemo(() => {
      const commissionTypeColumn = {
        title: t('Commission Type'),
        editable: true,
        dataIndex: 'salesCategory',
        width: 240,
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: salesCategoryOptions?.map(option => ({
              label: option.itemName,
              value: option.itemExtend1,
            })),
            allowClear: false,
          },
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: t('Please select'),
            },
          ],
        },
        render: (salesCategory: string) => (
          <ComponentWithFallback>{convertStatus(enums, salesCategory, 'salesCategory')}</ComponentWithFallback>
        ),
      };
      const internalColumns = [
        {
          title: t('Formula Category (Clawback)'),
          dataIndex: 'formulaCategory',
          editable: false,
          width: 240,
          render: () => (
            <ComponentWithFallback>
              {convertStatus(enums, CommissionFormulaClawBackCategory, 'bizTopic')}
            </ComponentWithFallback>
          ),
        },
        {
          title: t('Formula Sub Category (Clawback)'),
          editable: true,
          dataIndex: 'formulaSubCategory',
          fieldProps: {
            type: FieldType.Select,
            extraProps: {
              options: formulaSubCategoryOptions?.map(option => ({
                label: option.itemName,
                value: option.itemExtend1,
              })),
              allowClear: false,
              onChange: setEditingSubCategory,
            },
          },
          width: 240,
          formItemProps: {
            rules: [
              {
                required: true,
                message: t('Please select'),
              },
            ],
          },
          render: (formulaSubCategory = CommissionSubCategory_GrossCommission): string =>
            renderOldBizdictName(formulaSubCategory, formulaSubCategoryOptions),
        },
        {
          title: renderClawBackFormulaLabel,
          editable: true,
          dataIndex: 'formulaCode',
          fieldProps: {
            type: FieldType.Select,
            extraProps: {
              options: formulaOptionsBySubCategory?.map(option => ({
                label: option.itemName,
                value: option.itemExtend1,
              })),
              allowClear: false,
              disabled: !editingSubCategory,
            },
          },
          width: 240,
          render: (text: string) => (
            <ComponentWithFallback>{renderOldBizdictName(text, totalCommissionFormulaOptions)}</ComponentWithFallback>
          ),
        },
      ];
      if (isTiedAgent) {
        internalColumns.splice(1, 0, commissionTypeColumn);
      }
      return internalColumns;
    }, [
      t,
      formulaSubCategoryOptions,
      formulaOptionsBySubCategory,
      editingSubCategory,
      enums?.bizTopic,
      totalCommissionFormulaOptions,
      commissionClawBackFormulaList,
    ]);

    const handleSubmit = async (value: CommissionFormulaType, key: string, list: CommissionFormulaType[]) => {
      // SalesCategory + FormulaSubCategory确定唯一性
      const formulaCategoryList = list?.map(item => item?.salesCategory + item?.formulaSubCategory);
      if (list.length > uniq(formulaCategoryList)?.length) {
        message.error(
          t('Duplicate formula configuration for same formula category and sub category. Please edit original one.')
        );
        return Promise.reject();
      }
      setRelatedInfo({
        ...relatedInfo,
        formulas: [
          ...list?.map(item => ({
            ...item,
            formulaCategory: CommissionFormulaClawBackCategory,
          })),
          ...commissionFormulaList,
        ],
      });
      setDataSource(list);
      setEditingSubCategory(undefined);
    };

    const handleDelete = (index: number) => {
      dataSource?.splice(index, 1);
      setRelatedInfo({
        ...relatedInfo,
        formulas: [...dataSource, ...commissionFormulaList],
      });
      setDataSource([...dataSource]);
    };

    return (
      <Skeleton loading={false}>
        <h3>{t('Please select related formula for Commission Clawback')}</h3>
        <EditableTable<CommissionFormulaType>
          className={commonStyles.commissionTable}
          editBtnProps={{
            disabled: () => disabled,
            handleEdit: record => setEditingSubCategory(record.formulaSubCategory),
          }}
          deleteBtnProps={{
            disabled: () => disabled,
            handleDelete,
          }}
          addBtnProps={{
            disabled,
          }}
          dataSource={dataSource}
          columns={columns}
          handleConfirm={handleSubmit}
          scroll={{ x: 'max-content' }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          setDataSource={setDataSource}
          handleCancel={() => setEditingSubCategory(undefined)}
        />
      </Skeleton>
    );
  }
);
