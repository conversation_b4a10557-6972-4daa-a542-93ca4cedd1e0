/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-08-24 16:13:55
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-09-01 12:03:50
 * @Description:
 */
import {
  useState,
  useCallback,
  useEffect,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Form, Divider, Button, Row, Col, Input, Skeleton } from 'antd';
import { useRequest } from 'ahooks';
import type { AgreementType, SalesTypeEnum } from 'genesis-web-service';
import { ChannelService, AgreementStatus } from 'genesis-web-service';
import { useTranslation } from 'react-i18next';
import { NoData } from 'genesis-web-component/lib/components/NoData';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { StatusTag, Table } from '@zhongan/nagrand-ui';
import type { PaginationConfig } from 'antd/es/pagination';
import type { StatusType } from '@zhongan/nagrand-ui/dist/components/StatusTag';
import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import antdStyles from '@/antd-reset.scss';
import type { LocalAgreementType } from './index';
import CommonStyles from '../../style.scss';

interface CopySalesAgreementDrawerProps {
  salesType: SalesTypeEnum;
  visible: boolean;
  onClose: () => void;
  onSubmit: () => void;
}

export const CopySalesAgreementDrawer = forwardRef(
  (
    { visible, onClose, onSubmit, salesType }: CopySalesAgreementDrawerProps,
    ref,
  ) => {
    const { t } = useTranslation(['partner']);
    const [searchForm] = Form.useForm();
    const [searchValue, setSearchValue] = useState<{
      sales?: string;
      salesAgreement?: string;
    }>();
    const [salesAgreementList, setSalesAgreementList] = useState<
      AgreementType[]
    >([]);
    const [internalSelectedSalesAgreement, setInternalSelectedSalesAgreement] =
      useState<LocalAgreementType>();
    const [selectedSalesAgreement, setSelectedSalesAgreement] =
      useState<LocalAgreementType>();
    const [pagination, setPagination] = useState<PaginationConfig>({
      current: 1,
      pageSize: 10,
      total: 0,
    });

    const agreementStatusMap = {
      [AgreementStatus.Effective]: {
        label: t('Effective'),
        status: 'info',
      },
      [AgreementStatus.Terminated]: {
        label: t('Terminated'),
        status: 'error',
      },
    };

    useImperativeHandle(ref, () => selectedSalesAgreement);

    const { run: querySalesAgreementListByNameOrCode, loading: loading } =
      useRequest(
        params =>
          ChannelService.querySalesAgreementListByNameOrCode({
            ...params,
            salesType,
          }),
        {
          manual: true,
          onSuccess: res => {
            setSalesAgreementList(res?.data);
            setPagination({
              current: res.pageIndex + 1,
              pageSize: res.pageSize,
              total: res.totalElements,
            });
          },
        },
      );

    const { run: querySalesAgreementById } = useRequest(
      () =>
        ChannelService.querySalesAgreementById(
          internalSelectedSalesAgreement?.salesCode,
          internalSelectedSalesAgreement?.id,
        ),
      {
        manual: true,
        onSuccess: res => {
          setSelectedSalesAgreement({
            ...res,
            salesCode: internalSelectedSalesAgreement?.salesCode,
          });
          onSubmit();
          setInternalSelectedSalesAgreement(null);
          searchForm.resetFields();
        },
      },
    );

    useEffect(() => {
      if (visible) {
        querySalesAgreementListByNameOrCode({ pageIndex: 0, pageSize: 10 });
      }
    }, [visible, querySalesAgreementListByNameOrCode]);

    const handleSearchSalesAgreement = useCallback(() => {
      searchForm.validateFields().then(values => {
        // 将 values 的空字符替换为 undefined
        const filterValues = Object.fromEntries(
          Object.entries<string>(values).map(
            ([key, value]: [string, string]) => {
              if (value?.trim() === '') {
                return [key, undefined];
              }
              return [key, value];
            },
          ),
        );
        setSearchValue({ ...filterValues });
        querySalesAgreementListByNameOrCode({
          ...filterValues,
          pageIndex: 0,
          pageSize: 10,
        });
      });
    }, [querySalesAgreementListByNameOrCode]);

    const onPaginationChange = (current: number, pageSize: number) => {
      setPagination({
        current,
        pageSize,
        total: pagination.total,
      });
      querySalesAgreementListByNameOrCode({
        ...searchValue,
        pageIndex: current - 1,
        pageSize: pageSize,
      });
    };

    const columns = useMemo(
      () => [
        {
          title: t('Sales Agreement Name'),
          dataIndex: 'agreementName',
          width: 240,
          render: (text: string) => (
            <ComponentWithFallback>{text}</ComponentWithFallback>
          ),
        },
        {
          title: t('Sales Agreement Code'),
          dataIndex: 'agreementCode',
          width: 240,
          render: (text: string) => (
            <ComponentWithFallback>{text}</ComponentWithFallback>
          ),
        },
        {
          title: t('Sales Channel Name'),
          dataIndex: 'salesName',
          width: 240,
          render: (text: string) => (
            <ComponentWithFallback>{text}</ComponentWithFallback>
          ),
        },
        {
          title: t('Sales Channel Code'),
          dataIndex: 'salesCode',
          width: 240,
          render: (text: string) => (
            <ComponentWithFallback>{text}</ComponentWithFallback>
          ),
        },
        {
          title: t('Status'),
          dataIndex: 'agreementStatus',
          fixed: 'right',
          width: 120,
          render: (status: AgreementStatus) => (
            <ComponentWithFallback>
              {status && (
                <StatusTag
                  statusI18n={<span>{agreementStatusMap[status]?.label}</span>}
                  type={
                    (agreementStatusMap[status]?.status as StatusType) ||
                    'no-status'
                  }
                />
              )}
            </ComponentWithFallback>
          ),
        },
      ],
      [t, agreementStatusMap],
    );

    const rowSelection = {
      onSelect: async (record: AgreementType) => {
        setInternalSelectedSalesAgreement(record);
      },
    };

    const handleClose = () => {
      onClose();
      searchForm.resetFields();
    };

    return (
      <DrawerForm
        title={t('Sales Agreement List')}
        visible={visible}
        closable={false}
        onClose={handleClose}
        onSubmit={() => querySalesAgreementById()}
        okBtnDisabled={!internalSelectedSalesAgreement}
        cancelText={t('Cancel')}
        sendText={t('Confirm')}
        className={antdStyles.antdChannelCenter}
        width={1032}
      >
        <Form form={searchForm} layout="vertical">
          <Row>
            <Col>
              <Form.Item label={t('Sales Channel Name/Code')} name="sales">
                <Input
                  placeholder={t('Please Input')}
                  style={{
                    width: CommonStyles.formItemWidth,
                    marginRight: 100,
                  }}
                  allowClear
                />
              </Form.Item>
            </Col>
            <Col>
              <Form.Item
                label={t('Sales Agreement Name/Code')}
                name="salesAgreement"
              >
                <Input
                  placeholder={t('Please Input')}
                  style={{ width: CommonStyles.formItemWidth }}
                  allowClear
                />
              </Form.Item>
            </Col>
            <Col
              style={{
                marginLeft: 'auto',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Button
                className="margin-right-16"
                onClick={() => searchForm.resetFields()}
              >
                {t('Cancel')}
              </Button>
              <Button
                type="primary"
                style={{ marginLeft: 16 }}
                onClick={handleSearchSalesAgreement}
                loading={loading}
              >
                {t('Search')}
              </Button>
            </Col>
          </Row>
        </Form>
        <Divider dashed style={{ marginTop: 0 }} />
        <p className={CommonStyles.sectionTitle}>{t('Result')}</p>
        <Skeleton loading={loading}>
          <Table
            rowSelection={{
              type: 'radio',
              fixed: true,
              ...rowSelection,
            }}
            columns={columns}
            rowKey={record =>
              record?.id +
              '-' +
              record?.salesCode +
              '-' +
              record?.slaveAgreementRelated
            }
            dataSource={salesAgreementList}
            scroll={{ x: 'max-content' }}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              onChange: onPaginationChange,
              hideOnSinglePage: true,
              showQuickJumper: true,
            }}
          />
        </Skeleton>
      </DrawerForm>
    );
  },
);
