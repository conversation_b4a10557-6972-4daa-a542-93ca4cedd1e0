import { Fragment, useEffect, useState } from 'react';

import type { TableProps } from 'antd/es/table';

import { Table } from '@zhongan/nagrand-ui';

import { NoData } from 'genesis-web-component/lib/components/NoData';
import type { ExperiencedFee } from 'genesis-web-service';

import { PaginationComponent } from '@/components/Pagination';
import type { ColumnEx } from '@/types/common';

import styles from '../style.scss';
import { EditableCell, EditableRow } from './EditableRow';

type EditableProp<T> = TableProps<T> & {
  commonColumns: ColumnEx<T>[];
  currentEditItem?: T;
  handleValueChange: (val: T) => void;
  editAll?: boolean;
};

/**
 *
 * @param commonColumns table columns
 * @param dataSource 表格数据
 * @param currentEditItem 当前编辑item
 * @param handleValueChange edit value change callback
 * @param pagination 分页
 * @description 目前用于institute detail(department、experiencedFee)；可编辑table
 */
export const Editable = <T extends { id: number }>({
  commonColumns,
  dataSource,
  currentEditItem,
  handleValueChange,
  pagination,
  editAll,
  ...props
}: EditableProp<T>) => {
  const [columns, setColumns] = useState<ColumnEx<T>[]>([]);

  useEffect(() => {
    if (!currentEditItem && !editAll) {
      setColumns(commonColumns);
    } else {
      const mappedColumns = commonColumns.map(col => {
        if (!col.editable) {
          return col;
        }

        return {
          ...col,
          onCell: (record: ExperiencedFee) => ({
            record: record.id === currentEditItem?.id ? currentEditItem : record,
            editable: col.editable && editAll ? true : record.id === currentEditItem.id,
            dataIndex: col.dataIndex,
            title: col.title,
            handleValueChange,
            editChildren: col.editChildren,
            placeholder: col.placeholder,
          }),
        };
      });
      setColumns(mappedColumns as ColumnEx<T>[]);
    }
  }, [currentEditItem, commonColumns, handleValueChange]);

  return (
    <Fragment>
      <Table
        rowKey="id"
        className={styles.editTable}
        components={{
          body: {
            row: EditableRow,
            cell: EditableCell,
          },
        }}
        scroll={{ x: 'max-content' }}
        columns={columns}
        dataSource={dataSource}
        rowClassName={() => styles.editableRow}
        pagination={false}
        {...props}
      />
      {pagination && (
        <PaginationComponent
          size="small"
          className="margin-top-16 margin-bottom-16"
          total={pagination?.total}
          pagination={pagination}
          handlePaginationChange={pagination.onChange}
        />
      )}
    </Fragment>
  );
};
