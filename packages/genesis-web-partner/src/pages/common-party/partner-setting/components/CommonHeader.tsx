import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { ApartmentOutlined, EditOutlined, LeftOutlined } from '@ant-design/icons';

import { Breadcrumb, Button } from 'antd';
import qs from 'qs';

import { ListOutline } from '@/components/Icons';
import { usePartnerSettingPermission } from '@/pages/common-party/partner-setting/hooks/usePartnerSettingPermission';
import type { BreadCrumbInfo } from '@/pages/common-party/partner-setting/models/index.interface';
import { ModeEnum } from '@/types/common';
import { useLocation } from '@umijs/max';

import styles from '../style.scss';

interface Props {
  loading: boolean;
  readonly: boolean;
  showCancel: boolean;
  showViewStructure?: boolean;
  name?: string;
  breadCrumbList?: BreadCrumbInfo[];
  handleEdit: (type: ModeEnum) => void;
  handleCancel: () => void;
  handleSave: () => void;
  viewStructure?: () => void;
  extraRightSection?: React.ReactNode;
}

/**
 *
 * @param loading 是否在加载
 * @param readonly 是否只读
 * @param showCancel 是否显示cancel按钮
 * @param showViewStructure 是否显示View Structure按钮
 * @param name name
 * @param handleEdit click edit callback
 * @param handleCancel click cancel callback
 * @param handleSave click save callback
 * @param viewStructure click view Structure callback
 * @description 目前legal service、institute detail页用到；公共header
 */
export const CommonHeader = ({
  loading,
  readonly,
  showCancel,
  showViewStructure,
  name,
  breadCrumbList,
  handleEdit,
  handleCancel,
  handleSave,
  viewStructure,
  extraRightSection = null,
}: Props) => {
  const { t } = useTranslation('partner');
  const { state } = useLocation();
  const { canEdit } = usePartnerSettingPermission();
  const navigate = useNavigate();

  const goBack = useCallback(() => {
    navigate('/partner-setting', {
      state,
    });
  }, [state]);

  const jump = useCallback(
    ({ pathname, query }: BreadCrumbInfo) => {
      navigate(`${pathname}?${qs.stringify(query)}`, {
        state,
      });
    },
    [state]
  );

  return (
    <div className={styles.contentHeader}>
      <div className={styles.flex}>
        <Button icon={<LeftOutlined />} onClick={goBack}>
          {t('Back')}
        </Button>
        {breadCrumbList?.length > 1 && (
          <Breadcrumb separator="/" className={styles.breadCrumb}>
            {breadCrumbList.map((breadCrumb, index) => {
              const active = index < breadCrumbList.length - 1;
              return (
                <Breadcrumb.Item
                  key={breadCrumb.id}
                  className={active ? styles.jumpBreadCrumb : null}
                  onClick={() => active && jump(breadCrumb)}
                >
                  {active && <ListOutline />}
                  {breadCrumb.name}
                </Breadcrumb.Item>
              );
            })}
          </Breadcrumb>
        )}
        <div className={styles.right}>
          {extraRightSection}
          {showViewStructure && (
            <Button
              style={{ marginRight: 16 }}
              icon={<ApartmentOutlined />}
              onClick={() => viewStructure && viewStructure()}
            >
              {t('View Structure')}
            </Button>
          )}
          {!canEdit ? null : readonly ? (
            <Button icon={<EditOutlined />} onClick={() => handleEdit(ModeEnum.EDIT)}>
              {t('Edit')}
            </Button>
          ) : (
            showCancel && (
              <span>
                <Button style={{ marginRight: 16 }} onClick={handleCancel}>
                  {t('Cancel')}
                </Button>
                <Button type="primary" onClick={handleSave} loading={loading}>
                  {t('Save')}
                </Button>
              </span>
            )
          )}
        </div>
      </div>
      {!!name && <div className={styles.headerName}>{name}</div>}
    </div>
  );
};
