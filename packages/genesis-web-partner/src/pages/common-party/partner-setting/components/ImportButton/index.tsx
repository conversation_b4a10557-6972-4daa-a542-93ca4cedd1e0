import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { ButtonProps, UploadFile, UploadProps } from 'antd';
import { Button, Modal, Upload, message } from 'antd';
import type { UploadChangeParam } from 'antd/es/upload';

import { Icon } from '@zhongan/nagrand-ui';

import { DMSService } from 'genesis-web-service/lib/data-mgmt-service/data-mgmt-service.service';

import DataImport from '@/assets/data-import.svg';

import type { ValidateObj, ValidateResponse } from './utils';
import { calcWarningCount, warningKeyRender } from './utils';

export const ImportButton = (props: { uploadProps?: UploadProps; buttonProps?: ButtonProps }) => {
  const [validating, setValidating] = useState(false);
  const { t } = useTranslation('partner');
  const uploadFile = useCallback((file: any) => {
    const formData = new FormData();
    formData.append('file', file);

    return DMSService.bizImport(formData)
      .then(key => {
        message.success(t('Import Successful!'));
      })
      .catch(() => {
        message.error(t('Import Failed!'));
      });
  }, []);

  const renderWarningList = useCallback(
    (orderKeys: string[], validateMap: Record<string, ValidateObj[]>) =>
      orderKeys
        .filter(orderKey => !!validateMap[orderKey])
        .map(orderKey => {
          const validateList = validateMap[orderKey];

          return (
            <li key={orderKey}>
              <div>
                {t('Channel')}: {validateList.map(item => warningKeyRender[orderKey]?.(item)).join('; ')}
              </div>
            </li>
          );
        }),
    []
  );

  const showWarningModal = useCallback(({ orderKeys, validateMap }: ValidateResponse, onOk: () => void) => {
    Modal.confirm({
      title: <span className="pmc-font-bold">{t('Warning')}</span>,
      icon: <Icon type="exclamation-circle" />,
      content: (
        <div>
          <div className="pmc-font-bold pmc-mb-2">
            {t(`System has ${calcWarningCount(validateMap)} data with the same codes：`)}
          </div>
          <ul className="pmc-pl-[14px]">{renderWarningList(orderKeys, validateMap)}</ul>
        </div>
      ),
      cancelText: t('Cancel'),
      okText: t('Replace'),
      onOk: onOk,
    });
  }, []);

  const uploadProps: UploadProps = useMemo(
    () => ({
      action: DMSService.ValidateApi,
      multiple: false,
      name: 'file',
      accept: '.zip',
      maxCount: 1,
      showUploadList: false,
      disabled: validating,
      onChange: (
        info: UploadChangeParam<
          UploadFile<
            | {
                message: string;
              }
            | ValidateResponse
          >
        >
      ) => {
        if (info.file.status === 'uploading') {
          setValidating(true);
        } else if (info.file.status === 'done') {
          const { validateMap } = info.file.response as ValidateResponse;

          setValidating(false);
          if (Object.values(validateMap).every(item => item.length === 0)) {
            uploadFile(info.file.originFileObj);
            return;
          }
          showWarningModal(info.file.response as ValidateResponse, () => uploadFile(info.file.originFileObj));
        } else if (info.file.status === 'error') {
          setValidating(false);
          Modal.error({
            title: t('Import Operation Fail'),
            content: (info.file.response as { message: string })?.message,
          });
        }
      },
    }),
    [validating, uploadFile]
  );

  return (
    <Upload {...uploadProps} {...props.uploadProps}>
      <Button icon={<DataImport />} style={{ marginLeft: 8 }} {...props.buttonProps}>
        {t('Import')}
      </Button>
    </Upload>
  );
};
