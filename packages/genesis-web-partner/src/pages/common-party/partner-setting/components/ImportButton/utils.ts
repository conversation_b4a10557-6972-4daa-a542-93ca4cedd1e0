export interface ValidateResponse {
  orderKeys: string[];
  validateMap: Record<string, ValidateObj[]>;
}

export interface ValidateObj {
  name: string;
  code: string;
  version: string;
}

export const calcWarningCount = (validateMap: Record<string, ValidateObj[]>) =>
  Object.values(validateMap).reduce((count, item) => count + item.length, 0);

export const renderCodeVersion = (obj: ValidateObj) => `${obj.code}( ${obj.version} )`;

export const renderCodeName = (obj: ValidateObj) => `${obj.code} | ${obj.name}`;

export const renderCode = (obj: ValidateObj) => `${obj.code}`;

export const warningKeyRender: Record<string, (obj: ValidateObj) => string> = {
  GOODS: renderCodeVersion,
  PACKAGE: renderCode,
  PRODUCT: renderCodeVersion,
  CHANNEL: renderCodeName,
  RULE: renderCode,
  FORMULA: renderCode,
  RATE_TABLE: renderCodeName,
  MATRIX_TABLE: renderCodeName,
  NOTIFICATION: renderCode,
  POS: renderCode,
};
