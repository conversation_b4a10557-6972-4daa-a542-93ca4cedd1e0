import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Layout } from 'antd';

import { useDispatch, useSelector } from '@umijs/max';

import { keyBy } from 'lodash-es';

import type { TreeDataItem } from '@zhongan/nagrand-ui';
import { BackBtn, SimplePageHeader, TextEllipsisDetect, Tree } from '@zhongan/nagrand-ui';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import type { BizDictItem } from 'genesis-web-service/lib/foundation/foundation.interface';

import { useTenant } from '@/hooks/useUserInfo';
import type { ConnectState } from '@/models/connect';
import { BankCard } from '@/pages/common-party/partner-setting/components/BankCard';
import { BrokerCompanyCard } from '@/pages/common-party/partner-setting/components/BrokerCompanyCard';
import { ChannelCard } from '@/pages/common-party/partner-setting/components/CommonCard';
import { LeasingCompanyCard } from '@/pages/common-party/partner-setting/components/LeasingCompanyCard';
import { TiedAgent } from '@/pages/common-party/partner-setting/components/TiedAgent';
import { SalesChannelManagementMenuList, keyMapping } from '@/pages/common-party/utils/constants';
import { ModeEnum } from '@/types/common';
import type { LocationQueryParam } from '@/types/common-party';

import BankDetail from './Bank';
import BrokerCompanyDetail from './BrokerCompany';
import ChannelDetail from './Channel/Detail';
import FrontLineChannelDetail from './FrontLineChannel/FrontLineChannel';
import { FrontLineChannelCard } from './FrontLineChannel/FrontLineChannelCard';
import KeyAccountChannelDetail from './KeyAccountChannel/KeyAccountChannel';
import { KeyAccountChannelCard } from './KeyAccountChannel/KeyAccountChannelCard';
import LeasingCompanyDetail from './LeasingCompany';
import type { ChannelRouteState, ChannelSearchForm } from './models/index.interface';
import { ChannelTypeEnum } from './models/index.interface';
import styles from './style.scss';

export type PartnerSettingSearchQuery = Partial<LocationQueryParam & ChannelRouteState<ChannelSearchForm>> & {
  visible: boolean;
};

const PartnerSettingFC = () => {
  const dispatch = useDispatch();
  const [searchQuery, setSearchQuery] = useRouterState<PartnerSettingSearchQuery>();

  useEffect(() => {
    setSearchQuery(() => ({
      visible: false,
      pagination: { current: 1, pageSize: 10 },
    }));
  }, []);

  const { t } = useTranslation('partner');

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: ['channelType'],
    });
  }, [dispatch]);
  const tenant = useTenant();
  const [selectedMenu, setSelectedMenu] = useState<TreeDataItem>();

  // 获取tenantConfigInfo的orgType来获取tenantInfo；
  // tenantInfo为列表数据，包含channelId、name和logoUrl
  const { tenantConfigInfo, tenantInfo } = useSelector(({ partnerManagement }: ConnectState) => ({
    tenantConfigInfo: partnerManagement.tenantConfigInfo,
    tenantInfo: partnerManagement.tenantInfo,
  }));

  const tenantBizDict: Record<string, BizDictItem[]> = useSelector(({ global }: ConnectState) => global.tenantBizDict);

  const salesChannelTypeMap = useMemo(
    () => keyBy(tenantBizDict?.channelType || [], 'dictValue'),
    [tenantBizDict?.channelType]
  );

  const visibleMenuList = useMemo(
    () =>
      SalesChannelManagementMenuList.filter(menu => {
        if (!tenantInfo && !menu.children) {
          return;
        }
        return {
          ...menu,
          children: menu.children?.filter(child => {
            return !!salesChannelTypeMap[keyMapping[child.key]];
          }),
        };
      }),
    [salesChannelTypeMap, tenantInfo]
  );

  useEffect(() => {
    if (searchQuery?.channelType) {
      let keyList: TreeDataItem[] = [];
      visibleMenuList.forEach(item => {
        keyList = [...keyList, ...(item.children ?? [])];
      });
      setSelectedMenu(keyList.find(menu => menu.key === searchQuery?.channelType));
    } else {
      setSelectedMenu(visibleMenuList[0].children?.[0]);
    }
  }, [visibleMenuList]);

  useEffect(() => {
    if (tenant) {
      dispatch({
        type: 'partnerManagement/getTenantConfigInfo',
        payload: tenant,
      });
    }
  }, [tenant]);

  useEffect(() => {
    dispatch({
      type: 'partnerManagement/getTenantInfo',
    });
  }, [dispatch]);

  const ChannelCardRenderer = useCallback(
    (rest: Record<string, any>) => {
      const channelType = selectedMenu?.key;
      switch (channelType) {
        case ChannelTypeEnum.AGENCY:
          return <ChannelCard channelType={channelType} {...rest} />;
        case ChannelTypeEnum.SALE_CHANNEL:
          return <ChannelCard channelType={channelType} {...rest} />;
        case ChannelTypeEnum.LeaseChannel:
          return <LeasingCompanyCard {...rest} />;
        case ChannelTypeEnum.BrokerCompany:
          return <BrokerCompanyCard {...rest} />;
        case ChannelTypeEnum.Bank:
          return <BankCard {...rest} />;
        case ChannelTypeEnum.TiedAgent:
          return <TiedAgent {...rest} />;
        case ChannelTypeEnum.FRONT_LINE_CHANNEL:
          return <FrontLineChannelCard {...rest} />;
        case ChannelTypeEnum.KEY_ACCOUNT_CHANNEL:
          return <KeyAccountChannelCard {...rest} />;
        case ChannelTypeEnum.INSURANCE:
          return <ChannelDetail />;

        default:
          break;
      }
    },
    [selectedMenu, tenantInfo]
  );

  const ChannelDetailRenderer = useCallback(() => {
    const channelType = selectedMenu?.key;
    switch (channelType) {
      case ChannelTypeEnum.AGENCY:
        return <ChannelDetail />;
      case ChannelTypeEnum.SALE_CHANNEL:
        return <ChannelDetail />;
      case ChannelTypeEnum.LeaseChannel:
        return <LeasingCompanyDetail />;
      case ChannelTypeEnum.BrokerCompany:
        return <BrokerCompanyDetail />;
      case ChannelTypeEnum.Bank:
        return <BankDetail />;
      case ChannelTypeEnum.FRONT_LINE_CHANNEL:
        return <FrontLineChannelDetail />;
      case ChannelTypeEnum.KEY_ACCOUNT_CHANNEL:
        return <KeyAccountChannelDetail />;
      default:
        break;
    }
  }, [selectedMenu]);

  return (
    <Layout hasSider className="h-screen">
      <div className="flex flex-col border-r-[1px] border-[var(--border-line-color3)] border-solid border-y-0 border-l-0">
        <SimplePageHeader>
          <TextEllipsisDetect
            text={t('Sales Channel Management')}
            width={236}
            tooltipProps={{
              placement: 'bottomRight',
            }}
          />
        </SimplePageHeader>

        <Tree
          className="flex-1 !pt-4"
          onSelect={(_, { selectedNodes }) => {
            if (!selectedNodes[0]) {
              return;
            }
            setSelectedMenu(selectedNodes[0] as unknown as TreeDataItem);
            if (selectedNodes[0]?.key === ChannelTypeEnum.INSURANCE) {
              setSearchQuery({
                id: tenantInfo?.id,
                channelType: tenantConfigInfo?.orgType as unknown as ChannelTypeEnum,
                modeType: ModeEnum.EDIT,
                visible: false,
              });
            } else {
              setSearchQuery({
                visible: false,
              });
            }
          }}
          defaultExpandAll
          selectedKeys={[selectedMenu?.key]}
          treeData={visibleMenuList}
        />
      </div>

      <div className="h-screen flex-1 overflow-auto relative">
        <div className="border-solid border-x-0 border-t-0 border-[var(--border-line-color3)] h-[48px] border-b-[1px] flex items-center bg-white">
          {searchQuery?.visible && (
            <>
              <BackBtn
                onClick={() =>
                  setSearchQuery({
                    visible: false,
                  })
                }
                className="ml-0"
              />
              <span className="text-[var(--border-line-color3)] text-xl">|</span>
            </>
          )}
          <span className="font-bold ml-4">{selectedMenu?.title as string}</span>
        </div>
        <div
          className={`${styles.container} ${searchQuery?.visible || selectedMenu?.key === ChannelTypeEnum.INSURANCE ? '!h-[calc(100%-120px)]' : ''}`}
        >
          {searchQuery?.visible ? <ChannelDetailRenderer /> : <ChannelCardRenderer setSearchQuery={setSearchQuery} />}
        </div>
      </div>
    </Layout>
  );
};

export default PartnerSettingFC;
