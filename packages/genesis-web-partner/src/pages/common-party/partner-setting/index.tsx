import { use<PERSON>allback, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import type { MenuProps } from 'antd';
import { Layout, Menu, Tooltip } from 'antd';
import { keyBy } from 'lodash-es';
import qs from 'qs';

import type { BizDictItem } from 'genesis-web-service/lib/foundation/foundation.interface';

import { useTenant } from '@/hooks/useUserInfo';
import type { ConnectState } from '@/models/connect';
import { BankCard } from '@/pages/common-party/partner-setting/components/BankCard';
import { BrokerCompanyCard } from '@/pages/common-party/partner-setting/components/BrokerCompanyCard';
import { ChannelCard } from '@/pages/common-party/partner-setting/components/CommonCard';
import { LeasingCompanyCard } from '@/pages/common-party/partner-setting/components/LeasingCompanyCard';
import { TiedAgent } from '@/pages/common-party/partner-setting/components/TiedAgent';
import { Channel<PERSON><PERSON>List, MenuList, keyMapping } from '@/pages/common-party/utils/constants';
import { getUrlByFileUniqueCode } from '@/pages/common-party/utils/util';
import { ModeEnum } from '@/types/common';
import { useDispatch, useLocation, useSelector } from '@umijs/max';

import { FrontLineChannelCard } from './FrontLineChannel/FrontLineChannelCard';
import { KeyAccountChannelCard } from './KeyAccountChannel/KeyAccountChannelCard';
import { ChannelTypeEnum } from './models/index.interface';
import styles from './style.scss';

interface KeyItem {
  label: string;
  type: string;
  key?: ChannelTypeEnum;
  children: {
    label: string;
    key: ChannelTypeEnum;
  }[];
}

const PartnerSettingFC = () => {
  const dispatch = useDispatch();
  const { state } = useLocation() as {
    state: { channelType: ChannelTypeEnum };
  };
  const navigate = useNavigate();
  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: ['channelType'],
    });
  }, [dispatch]);
  const tenant = useTenant();
  const [selectedMenu, setSelectedMenu] = useState<ChannelTypeEnum>();

  // 获取tenantConfigInfo的orgType来获取tenantInfo；
  // tenantInfo为列表数据，包含channelId、name和logoUrl
  const { tenantConfigInfo, tenantInfo } = useSelector(({ partnerManagement }: ConnectState) => ({
    tenantConfigInfo: partnerManagement.tenantConfigInfo,
    tenantInfo: partnerManagement.tenantInfo,
  }));

  const tenantBizDict: Record<string, BizDictItem[]> = useSelector(({ global }: ConnectState) => global.tenantBizDict);

  const salesChannelTypeMap = useMemo(
    () => keyBy(tenantBizDict?.channelType || [], 'dictValue'),
    [tenantBizDict?.channelType]
  );

  const visibleMenuList = useMemo(
    () =>
      MenuList.map(menu => {
        return {
          ...menu,
          children: menu.children.filter(child => {
            return !!salesChannelTypeMap[keyMapping[child.key]];
          }),
        };
      }),
    [salesChannelTypeMap, MenuList]
  );

  useEffect(() => {
    if (state?.channelType) {
      let keyList: KeyItem[] = [];
      visibleMenuList.forEach(item => {
        keyList = [...keyList, ...item.children];
      });
      setSelectedMenu(keyList.find(menu => menu.key === state?.channelType)?.key);
    } else {
      setSelectedMenu(visibleMenuList[0].children?.[0]?.key);
    }
  }, [state?.channelType, visibleMenuList]);

  const goDetail = useCallback(() => {
    navigate(
      `/channel?${qs.stringify({
        id: tenantInfo?.id,
        type: tenantConfigInfo?.orgType,
        modeType: ModeEnum.READ,
      })}`
    );
  }, [tenantInfo?.id, tenantConfigInfo?.orgType]);

  useEffect(() => {
    if (tenant) {
      dispatch({
        type: 'partnerManagement/getTenantConfigInfo',
        payload: tenant,
      });
    }
  }, [tenant]);

  useEffect(() => {
    dispatch({
      type: 'partnerManagement/getTenantInfo',
    });
  }, [dispatch]);

  return (
    <Layout style={{ height: '94vh' }} className={styles.partnerSetting}>
      <Layout.Sider theme="light">
        {tenantInfo && (
          <div className={styles.header}>
            <img src={getUrlByFileUniqueCode('/api/channel/v2/file/download', tenantInfo.logoUrl)} />
            <Tooltip title={tenantInfo.name}>
              <span style={{ cursor: 'pointer' }} onClick={() => goDetail()}>
                {tenantInfo.name}
              </span>
            </Tooltip>
          </div>
        )}
        <Menu
          selectedKeys={[selectedMenu]}
          items={visibleMenuList}
          mode="inline"
          onSelect={(item: MenuProps) => {
            setSelectedMenu(item.key);
          }}
        />
      </Layout.Sider>
      <Layout.Content>
        {ChannelCardList.includes(selectedMenu) && <ChannelCard channelType={selectedMenu} />}
        {selectedMenu === ChannelTypeEnum.LeaseChannel && <LeasingCompanyCard />}
        {selectedMenu === ChannelTypeEnum.BrokerCompany && <BrokerCompanyCard />}
        {selectedMenu === ChannelTypeEnum.Bank && <BankCard />}
        {selectedMenu === ChannelTypeEnum.TiedAgent && <TiedAgent />}
        {selectedMenu === ChannelTypeEnum.FRONT_LINE_CHANNEL && <FrontLineChannelCard />}
        {selectedMenu === ChannelTypeEnum.KEY_ACCOUNT_CHANNEL && <KeyAccountChannelCard />}
      </Layout.Content>
    </Layout>
  );
};

export default PartnerSettingFC;
