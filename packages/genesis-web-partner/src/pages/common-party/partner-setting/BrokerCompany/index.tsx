import styles from './style.scss';
import commonStyles from '../style.scss';
import { BasicInfo } from '@/pages/common-party/partner-setting/components/BasicInfo';
import { Address } from '@/pages/common-party/partner-setting/components/Address';
import { <PERSON><PERSON>erson } from '@/pages/common-party/partner-setting/components/ContactPerson';
import { AccountInfo } from '@/pages/common-party/partner-setting/components/AccountInfo';
import { CommonHeader } from '@/pages/common-party/partner-setting/components/CommonHeader';
import { Structure } from '@/pages/common-party/partner-setting/components/Structure';
import { Staff } from '@/pages/common-party/partner-setting/components/ChannelStaff';
import { SalesAgreement } from '@/pages/common-party/partner-setting/components/SalesAgreement';
import { ModeEnum } from '@/types/common';
import {
  Card,
  Layout,
  message,
  Skeleton,
  Form,
  Drawer,
  Divider,
  Button,
} from 'antd';
import type { ChannelDetail } from 'genesis-web-service';
import { ChannelService, SalesTypeEnum, YesOrNo } from 'genesis-web-service';
import { useCallback, useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import type { LocationQueryParam } from '@/types/common-party';
import { useDispatch } from '@umijs/max';
import { BasicInfoTip } from '@/pages/common-party/partner-setting/components/BasicInfoTip';
import { useBreadCrumbNav } from '../hooks/useBreadCrumb';
import qs from 'qs';
import { useSearchParams } from 'react-router-dom';
import { ChannelSchemaFieldsContext } from '@/hooks/ChannelSchemaFieldsContext';
import { useChannelSchemaFields } from '@/hooks/useChannelSchemaFields';
import {
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
} from '../hooks/useChannelSchemaFieldsByCategory';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

/**
 * broker company detail页面
 * @constructor
 */
const BrokerCompanyDetail = () => {
  const [searchParams] = useSearchParams();
  const { id, type, modeType, parentId, level } = qs.parse(
    searchParams.toString(),
  ) as unknown as LocationQueryParam;

  const dispatch = useDispatch();
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const basicInfoRef = useRef(null);
  const addressRef = useRef(null);
  const agreementRef = useRef(null);
  const concatPersonRef = useRef(null);
  const accountInfoRef = useRef(null);

  const { channelSchemaDefFields, schemaUsedBizDictMap } =
    useChannelSchemaFields();

  const [brokerCompanyData, setBrokerCompanyData] = useState<ChannelDetail>();
  const [mode, setMode] = useState<ModeEnum>();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [structureVisible, setStructureVisible] = useState(false);

  const { breadCrumbList = [], setBreadCrumbList } = useBreadCrumbNav({
    id: id || parentId,
    type,
  });

  const getBrokerCompanyData = useCallback(() => {
    setLoading(true);
    ChannelService.queryChannelDetail(id)
      .then(setBrokerCompanyData)
      .catch((error: Error) => message.error(error.message))
      .finally(() => setLoading(false));
  }, [id]);

  useEffect(() => {
    if (id) {
      getBrokerCompanyData();
    }
  }, [id]);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'country',
        'organizationIdType',
        'companyAddressType',
        'gender',
        'channelCustomerType',
        'certiType',
        'channelOrgAccountType',
        'accountSubType',
        'accountType',
        'yesNo',
        'settlementRule',
        'salesAgreementStatus',
        'agentStatus',
      ],
    });
  }, [dispatch]);

  useEffect(() => {
    if (modeType) {
      setMode(modeType);
    } else if (id) {
      setMode(ModeEnum.READ);
    }
  }, [modeType, id]);

  useEffect(() => {
    if (brokerCompanyData) {
      const basicInfodynamicFields =
        channelSchemaDefFields
          .find(item => item.category === 'BASE')
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForDisplay(
        brokerCompanyData?.extensions,
        basicInfodynamicFields,
      );
      form.setFieldsValue({
        ...brokerCompanyData,
      });
    }
  }, [brokerCompanyData, form]);

  const handleCancel = useCallback(() => {
    form.resetFields();
    setMode(ModeEnum.READ);
    setBrokerCompanyData({
      ...brokerCompanyData,
      addressList: [...(brokerCompanyData.addressList ?? [])],
      customerList: [...(brokerCompanyData.customerList ?? [])],
      accountList: [...(brokerCompanyData.accountList ?? [])],
    });
  }, [form, brokerCompanyData]);

  const saveBrokerCompany = useCallback(() => {
    form?.validateFields().then(values => {
      setSaving(true);
      const logoUrl = basicInfoRef.current || '';
      const addressList = addressRef.current || [];
      const customerList = concatPersonRef.current || [];
      const accountList = accountInfoRef.current || [];

      const basicInfodynamicFields =
        channelSchemaDefFields
          .find(item => item.category === 'BASE')
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForSubmit(values.extensions, basicInfodynamicFields, {
        dateFormatInstance,
      });

      let submitReq: Promise<ChannelDetail>;
      if (brokerCompanyData) {
        submitReq = ChannelService.updateChannel(brokerCompanyData.id, {
          ...values,
          type,
          parentId: brokerCompanyData.parentId,
          level: brokerCompanyData.level,
          addressList,
          accountList,
          customerList,
          logoUrl,
          teamId: brokerCompanyData.teamId,
          dataPathCode: brokerCompanyData.dataPathCode,
        });
      } else {
        submitReq = ChannelService.addChannel({
          ...values,
          type,
          parentId,
          level,
          addressList,
          accountList,
          customerList,
          logoUrl,
        });
      }
      submitReq
        .then(resp => {
          message.success(t('Save successfully'));
          setBrokerCompanyData(resp);
          setMode(ModeEnum.READ);
          if (resp) {
            const clonedBreadList = [...breadCrumbList];
            if (clonedBreadList.length > 1) {
              // 保存成功，更新最后一条name
              clonedBreadList[clonedBreadList.length - 1].name = resp.name;
            }
            agreementRef.current.saveAgreement(resp.code, resp.id, type);
            setBreadCrumbList(clonedBreadList);
          }
        })
        .catch((error: Error) => message.error(error.message))
        .finally(() => setSaving(false));
    });
  }, [form, brokerCompanyData, type, parentId, level, breadCrumbList, t]);

  return (
    <ChannelSchemaFieldsContext.Provider
      value={{
        channelSchemaDefFields: channelSchemaDefFields,
        schemaUsedBizDictMap: schemaUsedBizDictMap,
      }}
    >
      <Skeleton loading={loading} active>
        <Layout.Content className={styles.channelDetail}>
          <CommonHeader
            loading={saving}
            readonly={mode === ModeEnum.READ}
            showCancel={!!brokerCompanyData}
            showViewStructure={!!brokerCompanyData}
            handleEdit={setMode}
            handleCancel={handleCancel}
            handleSave={saveBrokerCompany}
            viewStructure={() => setStructureVisible(true)}
            name={brokerCompanyData?.name}
            breadCrumbList={breadCrumbList}
          />
          <div className={styles.section}>
            <Card bordered={false}>
              <div className={commonStyles.basicInfoSection}>
                <BasicInfo
                  ref={basicInfoRef}
                  form={form}
                  readonly={mode === ModeEnum.READ}
                  channelDetail={brokerCompanyData}
                  fileUniqueCode={brokerCompanyData?.logoUrl}
                  basicInfoType={type}
                />
                {!brokerCompanyData && (
                  <Button
                    type="primary"
                    loading={saving}
                    onClick={() => saveBrokerCompany()}
                  >
                    {t('Create')}
                  </Button>
                )}
              </div>
              <div className={commonStyles.extraInfoSection}>
                {!brokerCompanyData && <BasicInfoTip />}
                <SalesAgreement
                  ref={agreementRef}
                  readonly={mode === ModeEnum.READ || !brokerCompanyData}
                  channelCode={brokerCompanyData?.code}
                  mode={mode}
                  renew={true}
                  salesType={SalesTypeEnum.CHANNEL}
                />
                <Divider dashed />
                <Address
                  ref={addressRef}
                  readonly={mode === ModeEnum.READ || !brokerCompanyData}
                  initialList={brokerCompanyData?.addressList}
                  mode={mode}
                />
                <Divider dashed />
                <ContactPerson
                  ref={concatPersonRef}
                  channelType={type}
                  readonly={mode === ModeEnum.READ || !brokerCompanyData}
                  initialList={brokerCompanyData?.customerList}
                  mode={mode}
                />
                <Divider dashed />
                <AccountInfo
                  ref={accountInfoRef}
                  readonly={mode === ModeEnum.READ || !brokerCompanyData}
                  initialList={brokerCompanyData?.accountList}
                  mode={mode}
                />
                <Divider dashed />
                <Staff
                  readonly={mode === ModeEnum.READ || !brokerCompanyData}
                  channelId={brokerCompanyData?.id}
                  channelType={type}
                  mode={mode}
                />
              </div>
              <Drawer
                title={t('Structure Details')}
                open={structureVisible}
                maskClosable={false}
                width={641}
                rootClassName={commonStyles.structureDrawer}
                onClose={() => setStructureVisible(false)}
              >
                <Structure
                  channelId={brokerCompanyData?.id}
                  channelType={type}
                  visible={structureVisible}
                />
              </Drawer>
            </Card>
          </div>
        </Layout.Content>
      </Skeleton>
    </ChannelSchemaFieldsContext.Provider>
  );
};

export default BrokerCompanyDetail;
