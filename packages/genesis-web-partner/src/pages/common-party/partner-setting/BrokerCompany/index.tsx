import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Drawer, Form, Skeleton, message } from 'antd';

import { useDispatch } from '@umijs/max';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import type { ChannelDetail } from 'genesis-web-service';
import { ChannelService, SalesTypeEnum, YesOrNo } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { ChannelSchemaFieldsContext } from '@/hooks/ChannelSchemaFieldsContext';
import { useChannelSchemaFields } from '@/hooks/useGenericChannelSchemaFields';
import type { PartnerSettingSearchQuery } from '@/pages/common-party/partner-setting';
import { ChannelDetailFooter } from '@/pages/common-party/partner-setting/Channel/Footer/index';
import { AccountInfo } from '@/pages/common-party/partner-setting/components/AccountInfo';
import { Address } from '@/pages/common-party/partner-setting/components/Address';
import { BasicInfo } from '@/pages/common-party/partner-setting/components/BasicInfo';
import { BasicInfoTip } from '@/pages/common-party/partner-setting/components/BasicInfoTip';
import { Staff } from '@/pages/common-party/partner-setting/components/ChannelStaff';
import { ContactPerson } from '@/pages/common-party/partner-setting/components/ContactPerson';
import { SalesAgreement } from '@/pages/common-party/partner-setting/components/SalesAgreement';
import { Structure } from '@/pages/common-party/partner-setting/components/Structure';
import { ModeEnum } from '@/types/common';

import {
  covertChannelType2SchemaChannelType,
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
} from '../../../../hooks/useGenericSchemaFormItemFields';
import { useBreadCrumbNav } from '../hooks/useBreadCrumb';
import commonStyles from '../style.scss';

/**
 * broker company detail页面
 * @constructor
 */
const BrokerCompanyDetail = () => {
  const [searchParams] = useRouterState<PartnerSettingSearchQuery>();
  const { id, channelType: type, modeType, parentId, level } = searchParams;
  const dispatch = useDispatch();
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const basicInfoRef = useRef(null);
  const addressRef = useRef(null);
  const agreementRef = useRef(null);
  const concatPersonRef = useRef(null);
  const accountInfoRef = useRef(null);

  const { schemaDefFields, schemaUsedBizDictMap } = useChannelSchemaFields();

  const [brokerCompanyData, setBrokerCompanyData] = useState<ChannelDetail>();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [structureVisible, setStructureVisible] = useState(false);
  const disabled = modeType === ModeEnum.READ;

  const { breadCrumbList = [], setBreadCrumbList } = useBreadCrumbNav({
    id: id || parentId,
    type,
  });

  const getBrokerCompanyData = useCallback(() => {
    setLoading(true);
    ChannelService.queryChannelDetail(id)
      .then(setBrokerCompanyData)
      .catch((error: Error) => message.error(error.message))
      .finally(() => setLoading(false));
  }, [id]);

  useEffect(() => {
    if (id) {
      getBrokerCompanyData();
    }
  }, [id]);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'country',
        'organizationIdType',
        'companyAddressType',
        'gender',
        'channelCustomerType',
        'certiType',
        'channelOrgAccountType',
        'accountSubType',
        'accountType',
        'yesNo',
        'settlementRule',
        'salesAgreementStatus',
        'agentStatus',
      ],
    });
  }, [dispatch]);

  useEffect(() => {
    if (brokerCompanyData) {
      const basicInfodynamicFields =
        schemaDefFields
          .find(item => item.category === 'BASE' && item.channelType === covertChannelType2SchemaChannelType(type))
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForDisplay(brokerCompanyData?.extensions, basicInfodynamicFields);
      form.setFieldsValue({
        ...brokerCompanyData,
      });
    }
  }, [brokerCompanyData, form, type, schemaDefFields]);

  const saveBrokerCompany = useCallback(() => {
    form?.validateFields().then(values => {
      setSaving(true);
      const logoUrl = basicInfoRef.current || '';
      const addressList = addressRef.current || [];
      const customerList = concatPersonRef.current || [];
      const accountList = accountInfoRef.current || [];

      const basicInfodynamicFields =
        schemaDefFields
          .find(item => item.category === 'BASE' && item.channelType === covertChannelType2SchemaChannelType(type))
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForSubmit(values.extensions, basicInfodynamicFields, {
        dateFormatInstance,
      });

      let submitReq: Promise<ChannelDetail>;
      if (brokerCompanyData) {
        submitReq = ChannelService.updateChannel(brokerCompanyData.id, {
          ...values,
          type,
          parentId: brokerCompanyData.parentId,
          level: brokerCompanyData.level,
          addressList,
          accountList,
          customerList,
          logoUrl,
          teamId: brokerCompanyData.teamId,
          dataPathCode: brokerCompanyData.dataPathCode,
        });
      } else {
        submitReq = ChannelService.addChannel({
          ...values,
          type,
          parentId,
          level,
          addressList,
          accountList,
          customerList,
          logoUrl,
        });
      }
      submitReq
        .then(resp => {
          message.success(t('Save successfully'));
          setBrokerCompanyData(resp);
          if (resp) {
            const clonedBreadList = [...breadCrumbList];
            if (clonedBreadList.length > 1) {
              // 保存成功，更新最后一条name
              clonedBreadList[clonedBreadList.length - 1].name = resp.name;
            }
            agreementRef.current.saveAgreement(resp.code, resp.id, type);
            setBreadCrumbList(clonedBreadList);
          }
        })
        .catch((error: Error) => message.error(error.message))
        .finally(() => setSaving(false));
    });
  }, [form, brokerCompanyData, type, parentId, level, breadCrumbList, t]);

  return (
    <ChannelSchemaFieldsContext.Provider
      value={{
        channelSchemaDefFields: schemaDefFields,
        schemaUsedBizDictMap: schemaUsedBizDictMap,
      }}
    >
      <Skeleton loading={loading} active>
        <div className={commonStyles.basicInfoSection}>
          <BasicInfo
            ref={basicInfoRef}
            form={form}
            disabled={disabled}
            channelDetail={brokerCompanyData}
            fileUniqueCode={brokerCompanyData?.logoUrl}
            basicInfoType={type}
          />
          {!brokerCompanyData && (
            <Button type="primary" loading={saving} onClick={() => saveBrokerCompany()}>
              {t('Create')}
            </Button>
          )}
        </div>
        <div>
          {!brokerCompanyData && <BasicInfoTip />}
          <SalesAgreement
            ref={agreementRef}
            channelCode={brokerCompanyData?.code}
            renew={true}
            salesType={SalesTypeEnum.CHANNEL}
            channelType={type}
            disabled={disabled}
          />
        </div>
        <Address ref={addressRef} initialList={brokerCompanyData?.addressList} channelType={type} disabled={disabled} />
        <ContactPerson
          ref={concatPersonRef}
          channelType={type}
          initialList={brokerCompanyData?.customerList}
          disabled={disabled}
        />
        <AccountInfo
          ref={accountInfoRef}
          initialList={brokerCompanyData?.accountList}
          channelType={type}
          disabled={disabled}
        />
        <Staff channelId={brokerCompanyData?.id} channelType={type} disabled={disabled} />
        <ChannelDetailFooter
          handleSave={saveBrokerCompany}
          showViewStructure={!!brokerCompanyData}
          viewStructure={() => setStructureVisible(true)}
          channelType={type}
          disabled={disabled}
        />
        <Drawer
          title={t('Structure Details')}
          open={structureVisible}
          maskClosable={false}
          width={641}
          rootClassName={commonStyles.structureDrawer}
          onClose={() => setStructureVisible(false)}
        >
          <Structure channelId={brokerCompanyData?.id} channelType={type} visible={structureVisible} />
        </Drawer>
      </Skeleton>
    </ChannelSchemaFieldsContext.Provider>
  );
};

export default BrokerCompanyDetail;
