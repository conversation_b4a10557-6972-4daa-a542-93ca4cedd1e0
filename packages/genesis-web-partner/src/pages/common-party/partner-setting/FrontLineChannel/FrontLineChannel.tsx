import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Drawer, Form, Skeleton, message } from 'antd';

import { useDispatch } from '@umijs/max';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import type { ChannelDetail } from 'genesis-web-service';
import { ChannelService, SalesTypeEnum, YesOrNo } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { ChannelSchemaFieldsContext } from '@/hooks/ChannelSchemaFieldsContext';
import { useChannelSchemaFields } from '@/hooks/useGenericChannelSchemaFields';
import type { PartnerSettingSearchQuery } from '@/pages/common-party/partner-setting';
import { ChannelDetailFooter } from '@/pages/common-party/partner-setting/Channel/Footer/index';
import { AccountInfo } from '@/pages/common-party/partner-setting/components/AccountInfo';
import { Address } from '@/pages/common-party/partner-setting/components/Address';
import { BasicInfo } from '@/pages/common-party/partner-setting/components/BasicInfo';
import { BasicInfoTip } from '@/pages/common-party/partner-setting/components/BasicInfoTip';
import { Staff } from '@/pages/common-party/partner-setting/components/ChannelStaff';
import { ContactPerson } from '@/pages/common-party/partner-setting/components/ContactPerson';
import { SalesAgreement } from '@/pages/common-party/partner-setting/components/SalesAgreement';
import { Structure } from '@/pages/common-party/partner-setting/components/Structure';
import { ModeEnum } from '@/types/common';

import {
  covertChannelType2SchemaChannelType,
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
} from '../../../../hooks/useGenericSchemaFormItemFields';
import { useBreadCrumbNav } from '../hooks/useBreadCrumb';
import commonStyles from '../style.scss';

/**
 * FrontLine Channel detail页面
 * @constructor
 */
const FrontLineChannelDetail = () => {
  const [searchParams] = useRouterState<PartnerSettingSearchQuery>();
  const { id, channelType: type, modeType, parentId, level } = searchParams;
  const dispatch = useDispatch();
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const basicInfoRef = useRef(null);
  const addressRef = useRef(null);
  const agreementRef = useRef(null);
  const concatPersonRef = useRef(null);
  const accountInfoRef = useRef(null);

  const { schemaDefFields, schemaUsedBizDictMap } = useChannelSchemaFields();

  const [frontLineChannelData, setFrontLineChannelData] = useState<ChannelDetail>();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [structureVisible, setStructureVisible] = useState(false);
  const disabled = modeType === ModeEnum.READ;

  const { breadCrumbList = [], setBreadCrumbList } = useBreadCrumbNav({
    id: id || parentId,
    type,
  });

  const getDetailData = useCallback(() => {
    setLoading(true);
    ChannelService.queryChannelDetail(id)
      .then(setFrontLineChannelData)
      .catch((error: Error) => message.error(error.message))
      .finally(() => setLoading(false));
  }, [id]);

  useEffect(() => {
    if (id) {
      getDetailData();
    }
  }, [id]);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'country',
        'organizationIdType',
        'companyAddressType',
        'gender',
        'channelCustomerType',
        'certiType',
        'channelOrgAccountType',
        'accountSubType',
        'accountType',
        'yesNo',
        'settlementRule',
        'salesAgreementStatus',
        'agentStatus',
      ],
    });
  }, [dispatch]);

  useEffect(() => {
    if (frontLineChannelData) {
      const basicInfodynamicFields =
        schemaDefFields
          .find(item => item.category === 'BASE' && item.channelType === covertChannelType2SchemaChannelType(type))
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForDisplay(frontLineChannelData?.extensions, basicInfodynamicFields);
      form.setFieldsValue({
        ...frontLineChannelData,
      });
    }
  }, [frontLineChannelData, form, type, schemaDefFields]);

  const save = useCallback(() => {
    form?.validateFields().then(values => {
      setSaving(true);
      const logoUrl = basicInfoRef.current || '';
      const addressList = addressRef.current || [];
      const customerList = concatPersonRef.current || [];
      const accountList = accountInfoRef.current || [];

      const basicInfodynamicFields =
        schemaDefFields
          .find(item => item.category === 'BASE' && item.channelType === covertChannelType2SchemaChannelType(type))
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForSubmit(values.extensions, basicInfodynamicFields, {
        dateFormatInstance,
      });

      let submitReq: Promise<ChannelDetail>;
      if (frontLineChannelData) {
        submitReq = ChannelService.updateChannel(frontLineChannelData.id, {
          ...values,
          type,
          parentId: frontLineChannelData.parentId,
          level: frontLineChannelData.level,
          addressList,
          accountList,
          customerList,
          logoUrl,
          teamId: frontLineChannelData.teamId,
          dataPathCode: frontLineChannelData.dataPathCode,
        });
      } else {
        submitReq = ChannelService.addChannel({
          ...values,
          type,
          parentId,
          level,
          addressList,
          accountList,
          customerList,
          logoUrl,
        });
      }
      submitReq
        .then(resp => {
          message.success(t('Save successfully'));
          setFrontLineChannelData(resp);
          if (resp) {
            const clonedBreadList = [...breadCrumbList];
            if (clonedBreadList.length > 1) {
              // 保存成功，更新最后一条name
              clonedBreadList[clonedBreadList.length - 1].name = resp.name;
            }
            agreementRef.current.saveAgreement(resp.code, resp.id, type);
            setBreadCrumbList(clonedBreadList);
          }
        })
        .catch((error: Error) => message.error(error.message))
        .finally(() => setSaving(false));
    });
  }, [form, frontLineChannelData, type, parentId, level, breadCrumbList, t]);

  return (
    <ChannelSchemaFieldsContext.Provider
      value={{
        channelSchemaDefFields: schemaDefFields,
        schemaUsedBizDictMap: schemaUsedBizDictMap,
      }}
    >
      <Skeleton loading={loading} active>
        <div className={commonStyles.basicInfoSection}>
          <BasicInfo
            ref={basicInfoRef}
            form={form}
            channelDetail={frontLineChannelData}
            fileUniqueCode={frontLineChannelData?.logoUrl}
            basicInfoType={type}
            disabled={disabled}
          />
          {!frontLineChannelData && (
            <Button type="primary" loading={saving} onClick={() => save()}>
              {t('Create')}
            </Button>
          )}
        </div>
        <div>
          {!frontLineChannelData && <BasicInfoTip />}
          <SalesAgreement
            ref={agreementRef}
            channelCode={frontLineChannelData?.code}
            renew={true}
            salesType={SalesTypeEnum.CHANNEL}
            channelType={type}
            disabled={disabled}
          />
        </div>
        <Address
          ref={addressRef}
          initialList={frontLineChannelData?.addressList}
          channelType={type}
          disabled={disabled}
        />
        <ContactPerson
          ref={concatPersonRef}
          channelType={type}
          initialList={frontLineChannelData?.customerList}
          disabled={disabled}
        />
        <AccountInfo
          ref={accountInfoRef}
          initialList={frontLineChannelData?.accountList}
          channelType={type}
          disabled={disabled}
        />
        <Staff channelId={frontLineChannelData?.id} channelType={type} disabled={disabled} />
        <ChannelDetailFooter
          handleSave={save}
          showViewStructure={!!frontLineChannelData}
          viewStructure={() => setStructureVisible(true)}
          channelType={type}
          disabled={disabled}
        />
        <Drawer
          title={t('Structure Details')}
          open={structureVisible}
          maskClosable={false}
          width={641}
          rootClassName={commonStyles.structureDrawer}
          onClose={() => setStructureVisible(false)}
        >
          <Structure channelId={frontLineChannelData?.id} channelType={type} visible={structureVisible} />
        </Drawer>
      </Skeleton>
    </ChannelSchemaFieldsContext.Provider>
  );
};

export default FrontLineChannelDetail;
