import type { FC } from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useAntdTable } from 'ahooks';
import { Button, Col, Form, Row, Steps, Tooltip, Upload, message } from 'antd';
import type { PaginationProps } from 'antd/lib/pagination';
import type { ColumnProps } from 'antd/lib/table';
import moment from 'moment';

import { Input, Table, TableActionsContainer } from '@zhongan/nagrand-ui';

import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import type { BizDictItem, ChannelDetail, ChannelStaff, EmployeeInfo } from 'genesis-web-service';
import {
  AgentStatusType,
  ChannelService,
  DownloadOrUploadType,
  MetadataService,
  SalesTypeEnum,
} from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { CommonForm } from '@/components/CommonForm/Form';
import {
  DeleteOutline,
  EditOutline,
  FinishOutline,
  ProcessOutline,
  ViewSquareOutline,
  WaitOutline,
} from '@/components/Icons';
import { PaginationComponent } from '@/components/Pagination';
import { RenderEnums } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { SalesAgreement } from '@/pages/common-party/partner-setting/components/SalesAgreement';
import { useStaffFormFields } from '@/pages/common-party/partner-setting/hooks/useFormFields';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';
import type { ModeEnum } from '@/types/common';
import { DownloadOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';

import { PageSizeOptions } from '../../utils/constants';
import {
  covertChannelType2SchemaChannelType,
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
  useSchemaFormItemFieldsByCategory,
} from '../hooks/useChannelSchemaFieldsByCategory';
import { ChannelTypeEnum } from '../models/index.interface';
import styles from '../style.scss';

// 左边是staff的字段key，右边是agent的字段key
// 需要将agent的字段自动复制给staff，并且禁止更改
const staffAndAgentKeyMapping: Record<string, keyof EmployeeInfo> = {
  staffName: 'name',
  salesFirstName: 'firstName',
  salesLastName: 'lastName',
  email: 'email',
  countryCode: 'countryCode',
  phoneNo: 'phoneNo',
  title: 'title',
  issueWithoutPayment: 'issueWithoutPayment',
  category: 'agentCategory',
  idType: 'idType',
  idNo: 'idNo',
  status: 'status',
  onboardDate: 'onboardDate',
  resignDate: 'resignDate',
};

// 日期类型的字段，用于转日期格式
const agentDateKeys = ['onboardDate', 'resignDate'];

/**
 * Sales Channel的staff信息
 * 目前用在Agency Company，Leasing Channel，Broker Company，Bank四个外部销售渠道中
 */
export const KeyAccountStaff: FC<{
  readonly: boolean;
  channelId: number;
  channelCode: string;
  channelType: ChannelTypeEnum;
  mode: ModeEnum;
}> = ({ channelId, readonly, mode, channelType, channelCode }) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();

  const [visible, setVisible] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [editedStaff, setEditedStaff] = useState<ChannelStaff>();
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [pagination, setPagination] = useState<PaginationProps>();
  const [employeeList, setEmployeeList] = useState([]);
  const [nameOrCode, setNameOrCode] = useState<string>();
  const [total, setTotal] = useState<number>();
  const [loading, setLoading] = useState(false);
  const tenantBizDict = useTenantBizDict() as Record<string, BizDictItem[]>;
  const paginationRef = useRef<PaginationProps>();
  const [channelAgentCategoryEnums, setChannelAgentCategoryEnums] = useState<BizDictItem[]>();
  const [agent, setAgent] = useState<EmployeeInfo>();
  const fields = useStaffFormFields(form, readonly, editedStaff, channelAgentCategoryEnums);
  const { formItems: dynamicFields, schemaFields } = useSchemaFormItemFieldsByCategory({
    staticOrDynamic: 'DYNAMIC',
    category: 'STAFF',
    disabled: readonly,
    channelType: covertChannelType2SchemaChannelType(channelType),
  });
  const agreementRef = useRef(null);

  if (agent) {
    fields.forEach(field => {
      if (field.key === 'countryCode') {
        field.customerDomOption.disabled = true;
        return;
      }
      if (field.key === 'resignDate') {
        field.ctrlProps.disabled = true;
        return;
      }
      if (agent[staffAndAgentKeyMapping[field.key]]) {
        field.ctrlProps.disabled = true;
      }
    });
  }

  // 获取租户配置的 channelAgentCategory
  useEffect(() => {
    MetadataService.queryBizDictConfigList('channelAgentCategory').then(res => {
      const bizDictConfigList = res.tenantBizDictConfig?.channelAgentCategory?.bizDictConfigList || [];
      setChannelAgentCategoryEnums(bizDictConfigList);
    });
  }, []);

  const getTableData = useCallback(
    async ({ current, pageSize }: PaginationProps) => {
      if (channelId) {
        const staffs = await ChannelService.queryChannelStaffs(channelId, {
          pageIndex: current - 1,
          pageSize: pageSize,
        });
        return {
          total: staffs?.totalElements,
          list: staffs?.data,
        };
      } else {
        return {
          total: 0,
          list: [],
        };
      }
    },
    [channelId]
  );

  const { tableProps, search } = useAntdTable(getTableData, {
    onError: error => message.error(error?.message),
    refreshDeps: [channelId],
  });

  const onEdit = useCallback(
    staff => {
      ChannelService.queryTiedAgentById(staff.employeeId)
        .then(agentRes => {
          setAgent(agentRes as EmployeeInfo);
        })
        .catch(() => {
          setAgent({} as EmployeeInfo);
        })
        .finally(() => {
          setCurrentStep(1);
          setVisible(true);
          dealSchemaFieldValueForDisplay(staff?.extensions, schemaFields);
          const formattedStaffInfo = {
            ...staff,
            onboardDate: staff?.onboardDate && moment(staff.onboardDate),
            resignDate: staff?.resignDate && moment(staff.resignDate),
          };
          setEditedStaff(formattedStaffInfo);
          form.setFieldsValue(formattedStaffInfo);
        });
    },
    [form, schemaFields]
  );

  const onDelete = useCallback(
    (staffId: number) => {
      return ChannelService.deleteChannelStaff(channelId, staffId)
        .then(() => {
          message.success(t('Delete successfully'));
          search.submit();
        })
        .catch((error: Error) => {
          message.error(error?.message);
        });
    },
    [channelId, search, t]
  );

  const onClose = useCallback(() => {
    form.resetFields();
    setVisible(false);
    setEditedStaff(undefined);
    setNameOrCode(undefined);
    setTotal(0);
    setEmployeeList([]);
    setSelectedRows([]);
    setCurrentStep(0);
  }, [form]);

  const onSubmit = useCallback(() => {
    if (currentStep === 0) {
      if (selectedRows.length > 0) {
        const employeeItem = employeeList.find(item => item.id === selectedRows[0]);
        delete employeeItem.department;
        delete employeeItem.isCreateAccount;
        employeeItem.agentCode = employeeItem.code;
        form.setFieldsValue({
          // row 1
          staffName: employeeItem[staffAndAgentKeyMapping.staffName],
          salesFirstName: employeeItem[staffAndAgentKeyMapping.salesFirstName],
          salesLastName: employeeItem[staffAndAgentKeyMapping.salesLastName],
          // row 2

          email: employeeItem[staffAndAgentKeyMapping.email],
          countryCode: employeeItem[staffAndAgentKeyMapping.countryCode],
          // row 3
          phoneNo: employeeItem[staffAndAgentKeyMapping.phoneNo],
          title: employeeItem[staffAndAgentKeyMapping.title],
          issueWithoutPayment: employeeItem[staffAndAgentKeyMapping.issueWithoutPayment],
          // row 4
          category: employeeItem[staffAndAgentKeyMapping.category],
          idType: employeeItem[staffAndAgentKeyMapping.idType],
          idNo: employeeItem[staffAndAgentKeyMapping.idNo],
          // row 5
          status: employeeItem[staffAndAgentKeyMapping.status],
          onboardDate:
            employeeItem[staffAndAgentKeyMapping.onboardDate] &&
            moment(employeeItem[staffAndAgentKeyMapping.onboardDate]),
          resignDate:
            employeeItem[staffAndAgentKeyMapping.resignDate] &&
            moment(employeeItem[staffAndAgentKeyMapping.resignDate]),
        });
        setAgent(employeeItem);
        setCurrentStep(1);
      } else {
        message.warning(t('Please select an employee first.'));
      }
    } else {
      form.validateFields().then(values => {
        setSubmitting(true);
        let submitReq: Promise<void>;
        dealSchemaFieldValueForSubmit(values.extensions, schemaFields, {
          dateFormatInstance,
        });
        if (editedStaff) {
          submitReq = ChannelService.updateChannelStaff(channelId, editedStaff.id, {
            channelId: channelId,
            channelCustomerId: editedStaff.channelCustomerId,
            isCreateAccount: editedStaff.isCreateAccount,
            ...values,
            onboardDate: values?.onboardDate && dateFormatInstance.formatDate(values.onboardDate),
            resignDate: values?.resignDate && dateFormatInstance.formatDate(values.resignDate),
            employeeId: editedStaff.employeeId,
          }).then(res => {
            message.success(t('Edited successfully'));
            agreementRef.current.saveAgreement(res.staffCode, res.id, channelType);
            onClose();
            search.submit();
          });
        } else {
          submitReq = ChannelService.addChannelStaff(channelId, {
            channelId: channelId,
            ...values,
            onboardDate: values?.onboardDate && dateFormatInstance.formatDate(values.onboardDate),
            resignDate: values?.resignDate && dateFormatInstance.formatDate(values.resignDate),
            employeeId: agent.id,
          }).then(res => {
            message.success(t('Add successfully'));
            agreementRef.current.saveAgreement(res.staffCode, res.id, channelType);
            onClose();
            search.submit();
          });
        }
        submitReq.catch((error: Error) => message.error(error?.message)).finally(() => setSubmitting(false));
      });
    }
  }, [form, editedStaff, channelId, t, onClose, search, schemaFields, agent]);

  const columns: ColumnProps<ChannelStaff>[] = useMemo(
    () => [
      {
        dataIndex: 'staffName',
        title: t('Staff Name'),
        fixed: 'left',
      },
      {
        title: t('Staff Code'),
        dataIndex: 'staffCode',
      },
      {
        title: t('E-mail'),
        dataIndex: 'email',
      },
      {
        title: t('Country Code'),
        dataIndex: 'countryCode',
      },
      {
        title: t('Mobile Phone'),
        dataIndex: 'phoneNo',
      },
      {
        title: t('Title'),
        dataIndex: 'title',
      },
      {
        title: t('Actions'),
        key: 'actions',
        fixed: 'right',
        align: 'right',
        width: 100,
        render: (_, staff) => (
          <TableActionsContainer>
            {readonly ? (
              <Tooltip title={t('View')}>
                <Button icon={<ViewSquareOutline />} type="link" onClick={() => onEdit(staff)} />
              </Tooltip>
            ) : (
              <>
                <DeleteConfirm onOk={() => onDelete(staff.id)}>
                  <Tooltip title={t('Delete')}>
                    <Button icon={<DeleteOutline />} disabled={readonly} type="link" />
                  </Tooltip>
                </DeleteConfirm>
                <Tooltip title={t('Edit')}>
                  <Button icon={<EditOutline />} type="link" onClick={() => onEdit(staff)} />
                </Tooltip>
              </>
            )}
          </TableActionsContainer>
        ),
      },
    ],
    [t, readonly, onEdit, onDelete]
  );

  const title = useMemo(() => {
    if (editedStaff) {
      if (readonly) {
        return t('View Staff information');
      }
      return t('Edit Staff information');
    }
    return t('Add New Staff');
  }, [editedStaff, readonly, t]);

  const handleSearch = useCallback(employeeNameOrCode => {
    setLoading(true);
    ChannelService.getEmployeeList({
      pageIndex: paginationRef.current?.current - 1,
      pageSize: paginationRef.current?.pageSize,
      employeeNameOrCode,
      sourceType: 'TIED_AGENT',
    })
      .then(res => {
        const data = res.data.filter(item => item.status === AgentStatusType.Employed);
        setEmployeeList(data);
        setTotal(res.totalElements);
      })
      .catch(error => message.error(error?.message))
      .finally(() => setLoading(false));
  }, []);

  const handleSearchChange = useCallback(() => {
    paginationRef.current = { current: 1, pageSize: 10 };
    setPagination({ current: 1, pageSize: 10 });
    handleSearch(nameOrCode);
  }, [nameOrCode]);

  const handlePaginationChange = useCallback(
    (current, pageSize) => {
      paginationRef.current = { current, pageSize };
      setPagination(paginationRef.current);
      handleSearch(nameOrCode);
    },
    [nameOrCode]
  );

  const employeeColumns: ColumnProps<any>[] = useMemo(
    () => [
      {
        title: t('Name'),
        render: item => (item.firstName || item.lastName) && `${item.firstName || ''} ${item.lastName || ''}`,
      },
      {
        title: t('Code'),
        dataIndex: 'code',
      },
      {
        title: t('ID Type'),
        dataIndex: 'idType',
        render: (idType: string) => (
          <ComponentWithFallback>
            {idType && <RenderEnums enums={tenantBizDict?.certiType} keyName={idType} />}
          </ComponentWithFallback>
        ),
      },
      {
        title: t('ID Number'),
        dataIndex: 'idNo',
        render: (idNo: string) => <ComponentWithFallback>{idNo}</ComponentWithFallback>,
      },
    ],
    [t]
  );

  return (
    <div>
      <p className={styles.sectionTitle}>{t('Staff')}</p>
      {!readonly && (
        <div className="flex-between" style={{ marginTop: 16, marginBottom: 16 }}>
          <Button icon={<PlusOutlined />} onClick={() => setVisible(true)} disabled={readonly}>
            {t('Add New')}
          </Button>
        </div>
      )}
      <Table
        columns={columns}
        rowKey="id"
        scroll={{ x: 'max-content' }}
        bordered={false}
        style={{ marginBottom: 16 }}
        {...tableProps}
        pagination={false}
      />
      <PaginationComponent
        className="margin-top-16 margin-bottom-16"
        total={tableProps?.pagination?.total}
        pagination={tableProps?.pagination}
        handlePaginationChange={(current, pageSize) =>
          tableProps?.onChange({ ...tableProps?.pagination, current, pageSize })
        }
      />

      <DrawerForm
        width={1050}
        title={title}
        visible={visible}
        closable={false}
        maskClosable={false}
        onClose={onClose}
        onSubmit={onSubmit}
        cancelText={t('Cancel')}
        backText={currentStep === 1 && !editedStaff && t('Back')}
        sendText={currentStep === 0 ? t('Next') : t('Confirm')}
        submitBtnShow={!readonly}
        submitBtnProps={{ loading: submitting }}
        onBack={() => {
          handlePaginationChange(paginationRef.current.current, paginationRef.current.pageSize);
          setCurrentStep(0);
        }}
      >
        <div style={{ marginTop: 24 }}>
          {!editedStaff && (
            <Steps className={styles.tiedSteps} current={currentStep}>
              <Steps.Step
                title={t('Select Employee')}
                icon={currentStep === 0 ? <ProcessOutline /> : <FinishOutline />}
              />
              <Steps.Step
                title={t('Fill Information')}
                icon={currentStep === 0 ? <WaitOutline /> : <ProcessOutline />}
              />
            </Steps>
          )}
          {currentStep === 0 ? (
            <>
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Input
                    value={nameOrCode}
                    placeholder={t('Please input Employee Code/Name')}
                    allowClear
                    onChange={event => setNameOrCode(event.target.value)}
                  />
                </Col>
                <Col span={7}>
                  <Button type="primary" ghost onClick={handleSearchChange} loading={loading}>
                    {t('Search')}
                  </Button>
                </Col>
              </Row>
              <Table
                loading={loading}
                dataSource={employeeList}
                columns={employeeColumns}
                pagination={false}
                scroll={{ x: 1030 }}
                style={{ marginBottom: 16, marginTop: 24 }}
                rowKey="id"
                rowSelection={{
                  type: 'radio',
                  onChange: (selectedRowKeys: React.Key[]) => {
                    setSelectedRows(selectedRowKeys as number[]);
                  },
                  selectedRowKeys: selectedRows,
                  columnWidth: 64,
                }}
              />
              <PaginationComponent
                size="small"
                pagination={pagination}
                total={total}
                pageSizeOptions={PageSizeOptions}
                handlePaginationChange={handlePaginationChange}
              />
            </>
          ) : (
            <>
              <CommonForm form={form} fields={[...fields, ...dynamicFields]} />
              <SalesAgreement
                ref={agreementRef}
                readonly={readonly}
                channelCode={editedStaff?.staffCode}
                mode={mode}
                salesType={SalesTypeEnum.STAFF}
                schemaCategory="STAFF_AGREEMENT"
                channelType={ChannelTypeEnum.KEY_ACCOUNT_CHANNEL}
              />
            </>
          )}
        </div>
      </DrawerForm>
    </div>
  );
};
