import type { ChangeEvent } from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { FormInstance } from 'antd';
import { Col, Form, Tooltip } from 'antd';

import { useSelector } from '@umijs/max';

import type { Moment } from 'moment';
import moment from 'moment';

import { Icon } from '@zhongan/nagrand-ui';

import { AddressComponent } from 'genesis-web-component/lib/components';
import FTimezonePicker from 'genesis-web-component/lib/components/deprecated/F-Timezone-Picker-v4';
import type {
  AgentManagementInfo,
  AgreementType,
  BizDictItem,
  ChannelStaff,
  EmployeeInfo,
  RelatedType,
  SelectOptions,
} from 'genesis-web-service';
import {
  AgencyExclusiveCategoryEnum,
  AgentStatusType,
  AgreementStatus,
  CustomerTypeCode,
  SubType,
  YesOrNo,
  YesOrNoDictNumberValue,
} from 'genesis-web-service';
import { useUniqueIdentification } from 'genesis-web-shared/lib/hook/useUniqueIdentification';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import { selectDateFormat } from 'genesis-web-shared/lib/redux';
import { CodePattern } from 'genesis-web-shared/lib/util/validator';

import type { LabeledValue, QueryFieldsType } from '@/components/CommonForm';
import { FieldType } from '@/components/CommonForm';
import { CountryCode } from '@/components/CountryCode';
import { I18nLanguage } from '@/components/I18nLanguage';
import {
  EntityType,
  covertChannelType2SchemaChannelType,
  useGenericSchemaFormItemFields,
} from '@/hooks/useGenericSchemaFormItemFields';
import { useNationality, useTenantBizDict } from '@/hooks/useTenantBizDict';
import { useTimezoneList } from '@/hooks/useTenantConfig';
import type { ConnectState } from '@/models/connect';
import { ChannelTypeEnum, FormTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { OriginType, SubTypeList } from '@/pages/common-party/utils/constants';
import { CustomerType } from '@/utils/constants';
import { EMAIL_PATTERN, MailMaxLength, getNameGroupsFormat } from '@/utils/utils';

import { UserAccount } from '../components/ChannelStaff/UserAccount';

enum AddressModelEnums {
  Dropdown = 'dropdown',
  FreeText = 'freeText',
}

interface Props<T> {
  formType: FormTypeEnum;
  basicInfoType?: ChannelTypeEnum;
  disabled?: boolean;
  channelDetail?: T;
  form?: FormInstance;
  editedItem?: AgreementType & { isCopied?: boolean };
  goodsOptions?: Record<string, string>[];
  setAddressResult?: (result: Record<string, string>) => void;
}

/**
 *
 * @description 判断addressModel配置是否是dropdown
 */
export const useAddressModelIsDropdown = () => {
  const addressModelEnums = useTenantBizDict('addressModel') as BizDictItem[];
  const addressModelValue = addressModelEnums?.[0].dictValue;
  return useMemo(() => addressModelValue === AddressModelEnums.Dropdown, [addressModelValue]);
};

export const customerDom = ({
  label,
  name,
  disabled,
  onChange,
}: {
  label: string;
  name: string;
  disabled?: boolean;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
}) => (
  <Col span={8}>
    <I18nLanguage
      label={label}
      name={name}
      disabled={disabled}
      className="w-[240px]"
      onChange={event => onChange?.(event)}
    />
  </Col>
);

/**
 *
 * @param formType 需要哪种类型的表单
 * @param basicInfoType basicInfo细分type
 * @param disabled 是否禁用
 * @param channelDetail response detail，部分fields有值时禁用判断需要用到
 * @description 生成form所需fields
 */
export const useFormFields = <T extends object>({
  basicInfoType,
  formType,
  disabled,
  channelDetail,
  form,
  editedItem,
  goodsOptions,
  setAddressResult,
}: Props<T>) => {
  const { t } = useTranslation(['partner']);
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const nationalityEnum = useNationality();
  const addressModelIsDropdown = useAddressModelIsDropdown();
  const { uniqConfig: uniqueIdentifications } = useUniqueIdentification(CustomerTypeCode.organization);
  const { loadingEnums } = useSelector(({ loading }: ConnectState) => ({
    loadingEnums: !!loading.effects['global/getTenantBizDict'],
  }));
  const dateFormat = useSelector(selectDateFormat);
  const timeZoneList = useTimezoneList();
  let fields = [];
  const [statusDisabledFlag, setStatusDisabledFlag] = useState<boolean>();
  const agentFullNameValue = Form.useWatch('agentFullName', form);

  const agencyExclusiveCategoryValue = Form.useWatch('agencyExclusiveCategory', form);
  const onChangeStatus = (date: Moment[]) => {
    const startTime = moment(date[0]).startOf('day');
    const endTime = moment(date[1]).endOf('day');
    const current = moment();
    if (current < startTime || current > endTime) {
      setStatusDisabledFlag(true);
      form?.setFieldValue('agreementStatus', AgreementStatus.Terminated);
    } else {
      setStatusDisabledFlag(false);
    }
  };
  useEffect(() => {
    onChangeStatus([moment(editedItem?.effectiveStartTime), moment(editedItem?.effectiveEndTime)]);
  }, [editedItem]);

  const allowEndDateOfSalesAgreementToBeNull = useMemo(
    () => enums?.allowEndDateOfSalesAgreementToBeNull?.[0]?.dictValue === YesOrNoDictNumberValue.YES?.toString(),
    [enums]
  );

  const handleBasicInfoForm = useCallback(() => {
    switch (basicInfoType) {
      case ChannelTypeEnum.INSURANCE:
        return [
          {
            key: 'name',
            label: t('Insurance Company Name'),
            type: FieldType.Input,
            col: 24,
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Insurance Company Name'),
                }),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'name2',
            label: t('Abbreviation'),
            type: FieldType.Input,
            rules: [],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'code',
            label: t('Insurance Company Code'),
            type: FieldType.Input,
            ctrlProps: {
              disabled: disabled || !!channelDetail?.code,
              allowClear: true,
            },
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Insurance Company Code'),
                }),
              },
              {
                pattern: CodePattern,
                message: t('Please input in correct format'),
              },
            ],
          },
          {
            key: 'organizationIdType',
            label: t('Organization ID Type'),
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.organizationIdType,
              disabled: disabled || !!channelDetail?.organizationIdType,
              loading: loadingEnums,
            },
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Organization ID Type'),
                }),
              },
            ],
          },
          {
            key: 'businessLicenseNumber',
            label: t('Organization ID No.'),
            ctrlProps: {
              disabled: disabled || !!channelDetail?.businessLicenseNumber,
              allowClear: true,
            },
            type: FieldType.Input,
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Organization ID No.'),
                }),
              },
            ],
          },
          {
            key: 'hotLine',
            label: t('Hotline'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 20,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'email',
            label: t('Email'),
            type: FieldType.Input,
            rules: [
              {
                pattern: EMAIL_PATTERN,
                message: t('Please input correct format'),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'officeHour',
            label: t('Office Hour'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 200,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'website',
            label: t('Website'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 200,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'fax',
            label: t('Fax Number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'taxpayerIdNumber',
            label: t('Taxpayer ID number'),
            col: 8,
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'introduction',
            label: t('Introduction'),
            type: FieldType.TextArea,
            col: 16,
            ctrlProps: {
              maxLength: 100,
              allowClear: true,
              disabled,
            },
          },
        ];
      case ChannelTypeEnum.AGENCY:
        return [
          {
            key: 'name',
            label: t('Agency Name'),
            type: FieldType.Input,
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Agency Name'),
                }),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'subType',
            label: t('Self-agency'),
            type: FieldType.Radio,
            col: 16,
            initialValue: SubType.Other,
            ctrlProps: {
              options: SubTypeList,
              disabled,
            },
          },
          {
            key: 'name2',
            label: t('Abbreviation'),
            type: FieldType.Input,
            rules: [],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'code',
            label: t('Agency Code'),
            type: FieldType.Input,
            ctrlProps: {
              disabled: disabled || !!channelDetail?.code,
              allowClear: true,
            },
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Agency Code'),
                }),
              },
              {
                pattern: CodePattern,
                message: t('Please input in correct format'),
              },
            ],
          },
          {
            key: 'organizationIdType',
            label: t('Organization ID Type'),
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.organizationIdType,
              disabled: disabled || !!channelDetail?.organizationIdType,
              loading: loadingEnums,
            },
            rules: [
              {
                required: !!uniqueIdentifications?.organizationIdType,
                message: t('channel.common.required', {
                  label: t('Organization ID Type'),
                }),
              },
            ],
          },
          {
            key: 'businessLicenseNumber',
            label: t('Organization ID No.'),
            ctrlProps: {
              disabled: disabled || !!channelDetail?.businessLicenseNumber,
              allowClear: true,
            },
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications.organizationIdNo,
                message: t('channel.common.required', {
                  label: t('Organization ID No.'),
                }),
              },
            ],
          },
          {
            key: 'hotLine',
            label: t('Hotline'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 20,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'email',
            label: t('Email'),
            type: FieldType.Input,
            rules: [
              {
                pattern: EMAIL_PATTERN,
                message: t('Please input correct format'),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'officeHour',
            label: t('Office Hour'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 200,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'website',
            label: t('Website'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 200,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'branchCode',
            label: t('Branch Code'),
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications?.branchCode,
                message: t('channel.common.required', {
                  label: t('Branch Code'),
                }),
              },
            ],
            ctrlProps: {
              maxLength: 200,
              allowClear: true,
              disabled: disabled || (!!uniqueIdentifications?.branchCode && !!channelDetail?.branchCode),
            },
          },
          {
            key: 'issueWithoutPayment',
            label: t('Issue without payment'),
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.yesNo as unknown as LabeledValue<string>[],
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'fax',
            label: t('Fax Number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'taxpayerIdNumber',
            label: t('Taxpayer ID number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'registrationDate',
            label: t('Registration Date'),
            type: FieldType.DatePicker,
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'agencyIoType',
            label: t('Individual or Organization Agency'),
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.agencyIOType as unknown as LabeledValue<string>[],
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'agencyExclusiveCategory',
            label: t('Exclusive Agency or Not'),
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.agencyExclusiveCategory as unknown as LabeledValue<string>[],
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'otherInsCompany',
            label: t('Other Insurance Company Name'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
            // 和上面agencyExclusiveCategory字段联动显示
            hidden: agencyExclusiveCategoryValue !== AgencyExclusiveCategoryEnum.ProxyByOtherInsCos,
          },
          {
            key: 'saleableProductType',
            label: t('Saleable Insurance Product Type'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'otherRelatedBusiness',
            label: t('Other Related Business'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'introduction',
            label: t('Introduction'),
            type: FieldType.TextArea,
            col: 16,
            ctrlProps: {
              maxLength: 100,
              allowClear: true,
              disabled,
            },
          },
        ];
      case ChannelTypeEnum.SALE_CHANNEL:
        return [
          {
            key: 'name',
            label: t('Channel Name'),
            type: FieldType.Input,
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Channel Name'),
                }),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'subType',
            label: t('Self-agency'),
            type: FieldType.Radio,
            col: 16,
            initialValue: SubType.Other,
            ctrlProps: {
              options: SubTypeList,
              disabled,
            },
          },
          {
            key: 'name2',
            label: t('Abbreviation'),
            type: FieldType.Input,
            rules: [],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'code',
            label: t('Channel Code'),
            type: FieldType.Input,
            ctrlProps: {
              disabled: disabled || !!channelDetail?.code,
              allowClear: true,
            },
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Channel Code'),
                }),
              },
              {
                pattern: CodePattern,
                message: t('Please input in correct format'),
              },
            ],
          },
          {
            key: 'organizationIdType',
            label: t('Organization ID Type'),
            dependencies: ['businessLicenseNumber'],
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.organizationIdType,
              disabled: disabled || !!channelDetail?.organizationIdType,
              loading: loadingEnums,
              allowClear: true,
            },
            rules: [
              {
                required: !!uniqueIdentifications?.organizationIdType,
                message: t('channel.common.required', {
                  label: t('Organization ID Type'),
                }),
              },
            ],
          },
          {
            key: 'businessLicenseNumber',
            label: t('Organization ID No.'),
            dependencies: ['organizationIdType'],
            ctrlProps: {
              disabled: disabled || !!channelDetail?.businessLicenseNumber,
              allowClear: true,
            },
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications.organizationIdNo,
                message: t('channel.common.required', {
                  label: t('Organization ID No.'),
                }),
              },
            ],
          },
          {
            key: 'hotLine',
            label: t('Hotline'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 20,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'email',
            label: t('Email'),
            type: FieldType.Input,
            rules: [
              {
                pattern: EMAIL_PATTERN,
                message: t('Please input correct format'),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'officeHour',
            label: t('Office Hour'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 200,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'website',
            label: t('Website'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 200,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'branchCode',
            label: t('Branch Code'),
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications?.branchCode,
                message: t('channel.common.required', {
                  label: t('Branch Code'),
                }),
              },
            ],
            ctrlProps: {
              maxLength: 200,
              allowClear: true,
              disabled: disabled || (!!uniqueIdentifications?.branchCode && !!channelDetail?.branchCode),
            },
          },
          {
            key: 'issueWithoutPayment',
            label: t('Issue without payment'),
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.yesNo as unknown as LabeledValue<string>[],
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'fax',
            label: t('Fax Number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'taxpayerIdNumber',
            label: t('Taxpayer ID number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'introduction',
            label: t('Introduction'),
            type: FieldType.TextArea,
            col: 16,
            ctrlProps: {
              maxLength: 100,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'isPremiumsForCustomer',
            label: (
              <span
                style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                {t('Premium Collection and Refund for Customer')}
                <Tooltip
                  title={t('If the value is equal to Yes, the premium collection or refund will through Claim.')}
                >
                  <Icon style={{ marginLeft: 5, cursor: 'pointer' }} type="info-fill" />
                </Tooltip>
              </span>
            ),
            type: FieldType.Radio,
            col: 16,
            ctrlProps: {
              options: enums?.yesNo,
              disabled,
            },
          },
        ];
      case ChannelTypeEnum.LeaseChannel:
        return [
          {
            key: 'name',
            label: t('Leasing Channel Name'),
            type: FieldType.Input,
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Leasing Channel Name'),
                }),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'name2',
            label: t('Abbreviation'),
            type: FieldType.Input,
            rules: [],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'code',
            label: t('Leasing Channel Code'),
            type: FieldType.Input,
            ctrlProps: {
              disabled: disabled || !!channelDetail?.code,
              allowClear: true,
            },
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Leasing Channel Code'),
                }),
              },
              {
                pattern: CodePattern,
                message: t('Please input in correct format'),
              },
            ],
          },
          {
            key: 'organizationIdType',
            label: t('Organization ID Type'),
            dependencies: ['businessLicenseNumber'],
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.organizationIdType,
              disabled: disabled || !!channelDetail?.organizationIdType,
              loading: loadingEnums,
              allowClear: true,
            },
            rules: [
              {
                required: !!uniqueIdentifications?.organizationIdType,
                message: t('channel.common.required', {
                  label: t('Organization ID Type'),
                }),
              },
            ],
          },
          {
            key: 'businessLicenseNumber',
            label: t('Organization ID No.'),
            dependencies: ['organizationIdType'],
            ctrlProps: {
              disabled: disabled || !!channelDetail?.businessLicenseNumber,
              allowClear: true,
            },
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications.organizationIdNo,
                message: t('channel.common.required', {
                  label: t('Organization ID No.'),
                }),
              },
            ],
          },
          {
            type: FieldType.Customize,
            customerDom: (
              <Col span={8}>
                <Form.Item name="countryCode" label={t('Country Code')}>
                  <CountryCode disabled={disabled} />
                </Form.Item>
              </Col>
            ),
          },
          {
            key: 'hotLine',
            label: t('Hotline'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 20,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'email',
            label: t('Email'),
            type: FieldType.Input,
            rules: [
              {
                pattern: EMAIL_PATTERN,
                message: t('Please input correct format'),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'branchCode',
            label: t('Branch Code'),
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications?.branchCode,
                message: t('channel.common.required', {
                  label: t('Branch Code'),
                }),
              },
            ],
            ctrlProps: {
              maxLength: 200,
              allowClear: true,
              disabled: disabled || (!!uniqueIdentifications?.branchCode && !!channelDetail?.branchCode),
            },
          },
          {
            key: 'issueWithoutPayment',
            label: t('Issue without payment'),
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.yesNo as unknown as LabeledValue<string>[],
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'fax',
            label: t('Fax Number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'taxpayerIdNumber',
            label: t('Taxpayer ID number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'introduction',
            label: t('Introduction'),
            type: FieldType.TextArea,
            col: 16,
            ctrlProps: {
              maxLength: 100,
              allowClear: true,
              disabled,
            },
          },
        ];
      case ChannelTypeEnum.Bank:
        return [
          {
            key: 'name',
            label: t('Bank Name'),
            type: FieldType.Input,
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Bank Name'),
                }),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'code',
            label: t('Bank Code'),
            type: FieldType.Input,
            ctrlProps: {
              disabled: disabled || !!channelDetail?.code,
              allowClear: true,
            },
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Bank Code'),
                }),
              },
              {
                pattern: CodePattern,
                message: t('Please input in correct format'),
              },
            ],
          },
          {
            key: 'organizationIdType',
            label: t('Organization ID Type'),
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.organizationIdType,
              disabled: disabled || !!channelDetail?.organizationIdType,
              loading: loadingEnums,
            },
            rules: [
              {
                required: !!uniqueIdentifications?.organizationIdType,
                message: t('channel.common.required', {
                  label: t('Organization ID Type'),
                }),
              },
            ],
          },
          {
            key: 'businessLicenseNumber',
            label: t('Organization ID No.'),
            ctrlProps: {
              disabled: disabled || !!channelDetail?.businessLicenseNumber,
              allowClear: true,
            },
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications.organizationIdNo,
                message: t('channel.common.required', {
                  label: t('Organization ID No.'),
                }),
              },
            ],
          },
          {
            key: 'abbreviation',
            label: t('Abbreviation'),
            type: FieldType.Input,
            rules: [],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            type: FieldType.Customize,
            customerDom: (
              <Col span={8}>
                <Form.Item name="countryCode" label={t('Country Code')}>
                  <CountryCode disabled={disabled} />
                </Form.Item>
              </Col>
            ),
          },
          {
            key: 'hotLine',
            label: t('Hotline'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 20,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'email',
            label: t('Email'),
            type: FieldType.Input,
            rules: [
              {
                pattern: EMAIL_PATTERN,
                message: t('Please input correct format'),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'branchCode',
            label: t('Branch Code'),
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications?.branchCode,
                message: t('channel.common.required', {
                  label: t('Branch Code'),
                }),
              },
            ],
            ctrlProps: {
              maxLength: 200,
              allowClear: true,
              disabled: disabled || (!!uniqueIdentifications?.branchCode && !!channelDetail?.branchCode),
            },
          },
          {
            key: 'issueWithoutPayment',
            label: t('Issue without payment'),
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.yesNo as unknown as LabeledValue<string>[],
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'fax',
            label: t('Fax Number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'taxpayerIdNumber',
            label: t('Taxpayer ID number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'introduction',
            label: t('Introduction'),
            type: FieldType.TextArea,
            col: 16,
            ctrlProps: {
              maxLength: 100,
              allowClear: true,
              disabled,
            },
          },
        ];
      case ChannelTypeEnum.BrokerCompany:
        return [
          {
            key: 'name',
            label: t('Broker Company Name'),
            type: FieldType.Input,
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Broker Company Name'),
                }),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'code',
            label: t('Broker Company Code'),
            type: FieldType.Input,
            ctrlProps: {
              disabled: disabled || !!channelDetail?.code,
              allowClear: true,
            },
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Broker Company Code'),
                }),
              },
              {
                pattern: CodePattern,
                message: t('Please input in correct format'),
              },
            ],
          },
          {
            key: 'organizationIdType',
            label: t('Organization ID Type'),
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.organizationIdType,
              disabled: disabled || !!channelDetail?.organizationIdType,
              loading: loadingEnums,
            },
            rules: [
              {
                required: !!uniqueIdentifications?.organizationIdType,
                message: t('channel.common.required', {
                  label: t('Organization ID Type'),
                }),
              },
            ],
          },
          {
            key: 'businessLicenseNumber',
            label: t('Organization ID No.'),
            ctrlProps: {
              disabled: disabled || !!channelDetail?.businessLicenseNumber,
              allowClear: true,
            },
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications.organizationIdNo,
                message: t('channel.common.required', {
                  label: t('Organization ID No.'),
                }),
              },
            ],
          },
          {
            key: 'abbreviation',
            label: t('Abbreviation'),
            type: FieldType.Input,
            rules: [],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            type: FieldType.Customize,
            customerDom: (
              <Col span={8}>
                <Form.Item name="countryCode" label={t('Country Code')}>
                  <CountryCode disabled={disabled} />
                </Form.Item>
              </Col>
            ),
          },
          {
            key: 'hotLine',
            label: t('Hotline'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 20,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'email',
            label: t('Email'),
            type: FieldType.Input,
            rules: [
              {
                pattern: EMAIL_PATTERN,
                message: t('Please input correct format'),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'branchCode',
            label: t('Branch Code'),
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications?.branchCode,
                message: t('channel.common.required', {
                  label: t('Branch Code'),
                }),
              },
            ],
            ctrlProps: {
              maxLength: 200,
              allowClear: true,
              disabled: disabled || (!!uniqueIdentifications?.branchCode && !!channelDetail?.branchCode),
            },
          },
          {
            key: 'issueWithoutPayment',
            label: t('Issue without payment'),
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.yesNo as unknown as LabeledValue<string>[],
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'fax',
            label: t('Fax Number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'taxpayerIdNumber',
            label: t('Taxpayer ID number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'introduction',
            label: t('Introduction'),
            type: FieldType.TextArea,
            col: 16,
            ctrlProps: {
              maxLength: 100,
              allowClear: true,
              disabled,
            },
          },
        ];
      case ChannelTypeEnum.TiedAgent:
        return [
          {
            type: FieldType.Customize,
            ctrlProps: {
              disabled,
            },
            customerDom: customerDom({
              label: t('First Name'),
              name: 'firstName',
              disabled,
            }),
            rules: [
              {
                required: !agentFullNameValue,
              },
            ],
          },
          {
            type: FieldType.Customize,
            ctrlProps: {
              disabled,
            },
            customerDom: customerDom({
              label: t('Last Name'),
              name: 'lastName',
              disabled,
            }),
            rules: [
              {
                required: !agentFullNameValue,
              },
            ],
          },
          {
            key: 'code',
            label: t('Code'),
            type: FieldType.Input,
            ctrlProps: {
              disabled,
            },
            rules: [
              {
                required: true,
              },
            ],
          },
          {
            key: 'idType',
            label: t('ID Type'),
            type: FieldType.Select,
            ctrlProps: {
              disabled,
              options: enums?.certiType,
            },
          },
          {
            key: 'idNo',
            label: t('ID Number'),
            type: FieldType.Input,
            ctrlProps: {
              disabled,
            },
          },
          {
            key: 'status',
            label: t('Status'),
            type: FieldType.Input,
            ctrlProps: {
              disabled,
            },
          },
          {
            key: 'onboardDate',
            label: t('Onboard Date'),
            type: FieldType.Input,
            ctrlProps: {
              disabled,
            },
          },
          {
            key: 'resignDate',
            label: t('Resign Date'),
            type: FieldType.Input,
            ctrlProps: {
              disabled,
            },
          },
          {
            key: 'email',
            label: t('Email'),
            type: FieldType.Input,
            ctrlProps: {
              disabled,
            },
            rules: [
              {
                required: true,
              },
            ],
          },
          {
            type: FieldType.Customize,
            customerDom: (
              <Col span={8}>
                <Form.Item name="countryCode" label={t('Country Code')}>
                  <CountryCode disabled={disabled} />
                </Form.Item>
              </Col>
            ),
          },
          {
            key: 'phoneNo',
            label: t('Mobile Phone'),
            ctrlProps: {
              disabled,
            },
          },
          {
            key: 'fax',
            label: t('Fax Number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'taxpayerIdNumber',
            label: t('Taxpayer ID number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
        ];
      case ChannelTypeEnum.FRONT_LINE_CHANNEL:
        return [
          {
            key: 'name',
            label: t('Front Line Channel Name'),
            type: FieldType.Input,
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Front Line Channel Name'),
                }),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'name2',
            label: t('Abbreviation'),
            type: FieldType.Input,
            rules: [],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'code',
            label: t('Front Line Code'),
            type: FieldType.Input,
            ctrlProps: {
              disabled: disabled || !!channelDetail?.code,
              allowClear: true,
            },
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Front Line Code'),
                }),
              },
              {
                pattern: CodePattern,
                message: t('Please input in correct format'),
              },
            ],
          },
          {
            key: 'organizationIdType',
            label: t('Organization ID Type'),
            dependencies: ['businessLicenseNumber'],
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.organizationIdType,
              disabled: disabled || !!channelDetail?.organizationIdType,
              loading: loadingEnums,
              allowClear: true,
            },
            rules: [
              {
                required: !!uniqueIdentifications?.organizationIdType,
                message: t('channel.common.required', {
                  label: t('Organization ID Type'),
                }),
              },
            ],
          },
          {
            key: 'businessLicenseNumber',
            label: t('Organization ID No.'),
            dependencies: ['organizationIdType'],
            ctrlProps: {
              disabled: disabled || !!channelDetail?.businessLicenseNumber,
              allowClear: true,
            },
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications.organizationIdNo,
                message: t('channel.common.required', {
                  label: t('Organization ID No.'),
                }),
              },
            ],
          },
          {
            type: FieldType.Customize,
            customerDom: (
              <Col span={8}>
                <Form.Item name="countryCode" label={t('Country Code')}>
                  <CountryCode disabled={disabled} />
                </Form.Item>
              </Col>
            ),
          },
          {
            key: 'hotLine',
            label: t('Hotline'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 20,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'email',
            label: t('Email'),
            type: FieldType.Input,
            rules: [
              {
                pattern: EMAIL_PATTERN,
                message: t('Please input correct format'),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'branchCode',
            label: t('Branch Code'),
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications?.branchCode,
                message: t('channel.common.required', {
                  label: t('Branch Code'),
                }),
              },
            ],
            ctrlProps: {
              maxLength: 200,
              allowClear: true,
              disabled: disabled || (!!uniqueIdentifications?.branchCode && !!channelDetail?.branchCode),
            },
          },
          {
            key: 'issueWithoutPayment',
            label: t('Issue without payment'),
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.yesNo as unknown as LabeledValue<string>[],
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'fax',
            label: t('Fax Number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'taxpayerIdNumber',
            label: t('Taxpayer ID number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'introduction',
            label: t('Introduction'),
            type: FieldType.TextArea,
            col: 16,
            ctrlProps: {
              maxLength: 100,
              allowClear: true,
              disabled,
            },
          },
        ];
      case ChannelTypeEnum.KEY_ACCOUNT_CHANNEL:
        return [
          {
            key: 'name',
            label: t('Key Account Channel Name'),
            type: FieldType.Input,
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Key Account Channel Name'),
                }),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'name2',
            label: t('Abbreviation'),
            type: FieldType.Input,
            rules: [],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'code',
            label: t('Key Account Channel Code'),
            type: FieldType.Input,
            ctrlProps: {
              disabled: disabled || !!channelDetail?.code,
              allowClear: true,
            },
            rules: [
              {
                required: true,
                message: t('channel.common.required', {
                  label: t('Key Account Channel Code'),
                }),
              },
              {
                pattern: CodePattern,
                message: t('Please input in correct format'),
              },
            ],
          },
          {
            key: 'organizationIdType',
            label: t('Organization ID Type'),
            dependencies: ['businessLicenseNumber'],
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.organizationIdType,
              disabled: disabled || !!channelDetail?.organizationIdType,
              loading: loadingEnums,
              allowClear: true,
            },
            rules: [
              {
                required: !!uniqueIdentifications?.organizationIdType,
                message: t('channel.common.required', {
                  label: t('Organization ID Type'),
                }),
              },
            ],
          },
          {
            key: 'businessLicenseNumber',
            label: t('Organization ID No.'),
            dependencies: ['organizationIdType'],
            ctrlProps: {
              disabled: disabled || !!channelDetail?.businessLicenseNumber,
              allowClear: true,
            },
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications.organizationIdNo,
                message: t('channel.common.required', {
                  label: t('Organization ID No.'),
                }),
              },
            ],
          },
          {
            type: FieldType.Customize,
            customerDom: (
              <Col span={8}>
                <Form.Item name="countryCode" label={t('Country Code')}>
                  <CountryCode disabled={disabled} />
                </Form.Item>
              </Col>
            ),
          },
          {
            key: 'hotLine',
            label: t('Hotline'),
            type: FieldType.Input,
            ctrlProps: {
              maxLength: 20,
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'email',
            label: t('Email'),
            type: FieldType.Input,
            rules: [
              {
                pattern: EMAIL_PATTERN,
                message: t('Please input correct format'),
              },
            ],
            ctrlProps: {
              allowClear: true,
              disabled,
            },
          },
          {
            key: 'branchCode',
            label: t('Branch Code'),
            type: FieldType.Input,
            rules: [
              {
                required: !!uniqueIdentifications?.branchCode,
                message: t('channel.common.required', {
                  label: t('Branch Code'),
                }),
              },
            ],
            ctrlProps: {
              maxLength: 200,
              allowClear: true,
              disabled: disabled || (!!uniqueIdentifications?.branchCode && !!channelDetail?.branchCode),
            },
          },
          {
            key: 'issueWithoutPayment',
            label: t('Issue without payment'),
            type: FieldType.Select,
            ctrlProps: {
              options: enums?.yesNo as unknown as LabeledValue<string>[],
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'fax',
            label: t('Fax Number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'taxpayerIdNumber',
            label: t('Taxpayer ID number'),
            ctrlProps: {
              disabled,
              allowClear: true,
            },
          },
          {
            key: 'introduction',
            label: t('Introduction'),
            type: FieldType.TextArea,
            col: 16,
            ctrlProps: {
              maxLength: 100,
              allowClear: true,
              disabled,
            },
          },
        ];
      default:
        return [];
    }
  }, [
    enums,
    disabled,
    loadingEnums,
    addressModelIsDropdown,
    basicInfoType,
    t,
    form,
    agentFullNameValue,
    agencyExclusiveCategoryValue,
  ]);

  switch (formType) {
    case FormTypeEnum.BASIC_INFO:
      return handleBasicInfoForm().filter(item => !item.hidden);
    case FormTypeEnum.SALES_AGREEMENT:
      return [
        {
          key: 'agreementName',
          label: t('Sales Agreement Name'),
          rules: [
            {
              required: true,
              message: t('channel.common.required', {
                label: t('Sales Agreement Name'),
              }),
            },
          ],
          ctrlProps: {
            allowClear: true,
            disabled,
          },
        },
        {
          key: 'agreementCode',
          label: t('Sales Agreement Code'),
          rules: [
            {
              required: true,
              message: t('channel.common.required', {
                label: t('Sales Agreement Code'),
              }),
            },
          ],
          ctrlProps: {
            allowClear: true,
            disabled: disabled || (editedItem?.id && !editedItem?.isCopied),
          },
        },
        {
          key: 'teamCode',
          label: t('Sales Agreement Signing Party'),
          type: FieldType.Select,
          rules: [
            {
              required: true,
              message: t('channel.common.required', {
                label: t('Sales Agreement Signing Party'),
              }),
            },
          ],
          ctrlProps: {
            options: goodsOptions,
            allowClear: true,
            disabled: disabled || (editedItem?.id && !editedItem?.isCopied),
          },
        },
        {
          key: 'effectivePeriod',
          label: t('Effective Period'),
          type: FieldType.Customize,
          customerDom: (
            <Col span={16}>
              <Form.Item label={t('Effective Period')} required>
                <FTimezonePicker
                  form={form}
                  selectItem={{
                    key: 'zoneId',
                    rules: [
                      {
                        required: true,
                        message: t('Please select'),
                      },
                    ],
                  }}
                  pickerItem={{
                    key: allowEndDateOfSalesAgreementToBeNull ? 'effectiveStartTime' : 'effectivePeriod',
                    rules: [
                      {
                        required: true,
                        message: t('Please select'),
                      },
                    ],
                  }}
                  showError
                  format={dateFormat || 'YYYY-MM-DD'}
                  pickerType={allowEndDateOfSalesAgreementToBeNull ? 'date' : 'range'}
                  selectObj={{
                    list: timeZoneList,
                    placeholder: 'Please select',
                    allowClear: false,
                    disabled,
                  }}
                  customizeSize={[200, 360]}
                  disabled={disabled}
                  onCalendarChange={onChangeStatus}
                />
              </Form.Item>
            </Col>
          ),
        },
        {
          key: 'agreementStatus',
          label: t('Sales Agreement Status'),
          type: FieldType.Select,
          rules: [
            {
              required: true,
              message: t('channel.common.required', {
                label: t('Sales Agreement Status'),
              }),
            },
          ],
          ctrlProps: {
            options: enums?.salesAgreementStatus,
            allowClear: true,
            disabled: disabled || statusDisabledFlag,
          },
        },
      ];
    case FormTypeEnum.SERVICE_AGREEMENT:
      return [
        {
          key: 'agreementName',
          label: t('Service Agreement Name'),
          rules: [
            {
              required: true,
              message: t('channel.common.required', {
                label: t('Service Agreement Name'),
              }),
            },
          ],
          ctrlProps: {
            allowClear: true,
            disabled,
          },
        },
        {
          key: 'agreementCode',
          label: t('Service Agreement Code'),
          rules: [
            {
              required: true,
              message: t('channel.common.required', {
                label: t('Service Agreement Code'),
              }),
            },
          ],
          ctrlProps: {
            allowClear: true,
            disabled: disabled || editedItem?.id,
          },
        },
        {
          key: 'effectivePeriod',
          type: FieldType.Customize,
          label: t('Effective Period'),
          customerDom: (
            <Col span={16}>
              <Form.Item label={t('Effective Period')} required>
                <FTimezonePicker
                  form={form}
                  selectItem={{
                    key: 'zoneId',
                    rules: [
                      {
                        required: true,
                        message: t('Please select'),
                      },
                    ],
                  }}
                  pickerItem={{
                    key: 'effectivePeriod',
                    rules: [
                      {
                        required: true,
                        message: t('Please select'),
                      },
                    ],
                  }}
                  showError
                  format={dateFormat || 'YYYY-MM-DD'}
                  pickerType="range"
                  selectObj={{
                    list: timeZoneList,
                    placeholder: t('Please select'),
                    allowClear: false,
                    disabled,
                  }}
                  customizeSize={[200, 360]}
                  disabled={disabled}
                  onCalendarChange={onChangeStatus}
                />
              </Form.Item>
            </Col>
          ),
        },
        {
          key: 'agreementStatus',
          label: t('Service Agreement Status'),
          type: FieldType.Select,
          rules: [
            {
              required: true,
              message: t('channel.common.required', {
                label: t('Service Agreement Status'),
              }),
            },
          ],
          ctrlProps: {
            options: enums?.salesAgreementStatus as LabeledValue<string>[],
            allowClear: true,
            disabled: disabled || statusDisabledFlag,
          },
        },
      ];
    case FormTypeEnum.ADDRESS:
      return [
        {
          key: 'addressType',
          label: t('Address Type'),
          type: FieldType.Select,
          rules: [
            {
              required: true,
              message: t('channel.common.required', {
                label: t('Address Type'),
              }),
            },
          ],
          ctrlProps: {
            options: enums?.companyAddressType,
            allowClear: true,
            loading: loadingEnums,
            disabled,
          },
        },
        {
          type: FieldType.Customize,
          customerDom: (
            <AddressComponent
              disabled={disabled}
              onCascaderValueChange={setAddressResult}
              form={form}
              initialValue={editedItem ?? {}}
              customerType={CustomerType.COMPANY}
            />
          ),
        },
      ];
    case FormTypeEnum.CONTACT_PERSON:
      fields = [
        {
          key: 'name',
          label: t('Contacts Name'),
          type: FieldType.Input,
          rules: [
            {
              required: true,
              message: t('channel.common.required', {
                label: t('Contacts Name'),
              }),
            },
          ],
          ctrlProps: {
            disabled,
          },
        },
        {
          key: 'birthday',
          label: t('Contacts Person Birthday'),
          type: FieldType.DatePicker,
          ctrlProps: {
            disabled,
          },
        },
        {
          key: 'name2',
          label: t('Abbreviation'),
          type: FieldType.Input,
          ctrlProps: {
            disabled,
          },
        },
        {
          key: 'sex',
          label: t('Gender'),
          type: FieldType.Select,
          ctrlProps: {
            options: enums?.gender,
            allowClear: true,
            loading: loadingEnums,
            disabled,
          },
        },
        {
          key: 'type',
          label: t('Contacts Person Type'),
          type: FieldType.Select,
          ctrlProps: {
            options: enums?.channelCustomerType,
            allowClear: true,
            loading: loadingEnums,
            disabled,
          },
        },
        {
          key: 'certType',
          label: t('Contact Person ID Type'),
          type: FieldType.Select,
          ctrlProps: {
            options: enums?.certiType,
            allowClear: true,
            loading: loadingEnums,
            disabled,
          },
        },
        {
          key: 'certNo',
          label: t('Contact Person ID'),
          type: FieldType.Input,
          ctrlProps: {
            disabled,
          },
        },
        {
          key: 'phone',
          label: t('Telephone'),
          type: FieldType.Input,
          rules: [
            {
              pattern: /^[0-9]+$/,
              message: t('Please input in correct format'),
            },
          ],
          ctrlProps: {
            disabled,
          },
        },
        {
          key: 'position',
          label: t('Position'),
          type: FieldType.Input,
          ctrlProps: {
            disabled,
          },
        },
        {
          key: 'email',
          label: t('Email'),
          type: FieldType.Input,
          rules: [
            {
              pattern: EMAIL_PATTERN,
              message: t('Please input in correct format'),
            },
          ],
          ctrlProps: {
            maxLength: MailMaxLength,
            disabled,
          },
        },
      ];

      // 对于Agency Company和Sales Channel，增加一个国籍的可选下拉框
      if (basicInfoType === ChannelTypeEnum.AGENCY || basicInfoType === ChannelTypeEnum.SALE_CHANNEL) {
        fields.push({
          key: 'nationality',
          label: t('Nationality'),
          type: FieldType.Select,
          ctrlProps: {
            options: nationalityEnum,
            allowClear: true,
            loading: loadingEnums,
            disabled,
          },
        });
      }
      return fields;
    default:
      return [];
  }
};

export const useDoctorFormFields = ({ disabled }: { disabled: boolean }) => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  return useMemo(
    () => [
      {
        key: 'doctorName',
        label: t('Doctor Name'),
        type: FieldType.Input,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Doctor Name'),
            }),
          },
        ],
        ctrlProps: {
          allowClear: true,
          disabled,
        },
      },
      {
        key: 'title',
        label: t('Title'),
        type: FieldType.Input,
        ctrlProps: {
          allowClear: true,
          disabled,
        },
      },
      {
        key: 'department',
        label: t('Department'),
        type: FieldType.Input,
        ctrlProps: {
          allowClear: true,
          disabled,
        },
      },
      {
        key: 'idType',
        label: t('Doctor ID type'),
        type: FieldType.Select,
        dependencies: ['idNo'],
        ctrlProps: {
          options: enums?.certiType,
          allowClear: true,
          disabled,
        },
        rules: [
          ({ getFieldValue }) => ({
            validator: (_, value) => {
              if (!value && getFieldValue('idNo')) {
                return Promise.reject(
                  new Error(
                    t('channel.common.required', {
                      label: t('Doctor ID type'),
                    })
                  )
                );
              }
              return Promise.resolve();
            },
          }),
        ],
      },
      {
        key: 'idNo',
        label: t('ID No'),
        type: FieldType.Input,
        dependencies: ['idType'],
        ctrlProps: {
          allowClear: true,
          disabled,
        },
        rules: [
          ({ getFieldValue }) => ({
            validator: (_, value) => {
              if (!value && getFieldValue('idType')) {
                return Promise.reject(
                  new Error(
                    t('channel.common.required', {
                      label: t('ID No'),
                    })
                  )
                );
              }
              return Promise.resolve();
            },
          }),
        ],
      },
      {
        key: 'phoneNo',
        label: t('Phone No.'),
        type: FieldType.Input,
        ctrlProps: {
          allowClear: true,
          disabled,
        },
      },
      {
        key: 'email',
        label: t('E-mail'),
        type: FieldType.Input,
        rules: [
          {
            pattern: EMAIL_PATTERN,
            message: t('Please input in correct format'),
          },
        ],
        ctrlProps: {
          allowClear: true,
          disabled,
        },
      },
      {
        key: 'certificateType',
        label: t('Certificate Type'),
        type: FieldType.Select,
        ctrlProps: {
          options: enums?.doctorCertificateType,
          allowClear: true,
          showSearch: true,
          disabled,
        },
      },
      {
        key: 'certificateNo',
        label: t('Certificate No'),
        type: FieldType.Input,
        ctrlProps: {
          allowClear: true,
          disabled,
        },
      },
    ],
    [disabled, enums]
  );
};

export const useStaffFormFields = (
  form: FormInstance,
  disabled: boolean,
  staff: ChannelStaff,
  channelAgentCategoryEnums: BizDictItem[],
  channelType: ChannelTypeEnum
): Partial<QueryFieldsType>[] => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const [status, setStatus] = useState<AgentStatusType>();
  

  useEffect(() => {
    setStatus(staff?.status as AgentStatusType);
  }, [staff]);

  const onChangeStatus = useCallback((selectedStatus: AgentStatusType) => {
    setStatus(selectedStatus);
    // 状态改为employed时，清空resignDate
    if (selectedStatus === AgentStatusType.Employed) {
      form?.resetFields(['resignDate']);
    }
  }, []);
  const onboardDateChange = useCallback((currentDate: Moment) => {
    if (currentDate > moment(form.getFieldValue('resignDate'))) {
      form?.resetFields(['resignDate']);
    }
  }, []);

  const handleNameChange = useCallback((nameList: string[]) => {
    const fullNameValue = nameList.filter(Boolean)?.join(' ');
    form?.setFields([
      {
        name: 'staffName',
        value: fullNameValue,
      },
    ]);
  }, []);

  const fields = useMemo(
    (): Partial<QueryFieldsType>[] => [
      {
        key: 'staffName',
        label: t('Staff Name'),
        type: FieldType.Input,
        ctrlProps: {
          allowClear: true,
          disabled,
        },
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Staff Name'),
            }),
          },
        ],
      },
      {
        key: 'salesFirstName',
        label: t('First Name'),
        type: FieldType.Input,
        ctrlProps: {
          allowClear: true,
          disabled,
          onChange: (event: ChangeEvent<HTMLInputElement>) =>
            handleNameChange([event.target.value, form.getFieldValue('salesLastName')]),
        },
      },
      {
        key: 'salesLastName',
        label: t('Last Name'),
        type: FieldType.Input,
        ctrlProps: {
          allowClear: true,
          disabled,
          onChange: (event: ChangeEvent<HTMLInputElement>) =>
            handleNameChange([form.getFieldValue('salesFirstName'), event.target.value]),
        },
      },
      {
        key: 'staffCode',
        label: t('Staff Code'),
        type: FieldType.Input,
        ctrlProps: {
          allowClear: true,
          disabled: disabled || !!staff?.staffCode,
        },
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Staff Code'),
            }),
          },
        ],
      },
      {
        key: 'email',
        label: t('Email'),
        type: FieldType.Input,
        ctrlProps: {
          allowClear: true,
          disabled,
        },
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Email'),
            }),
          },
        ],
      },
      {
        type: FieldType.Customize,
        key: 'countryCode',
        customerDomOption: {},
        customerDom: config => {
          return (
            <Col span={8}>
              <Form.Item name="countryCode" label={t('Country Code')}>
                <CountryCode style={{ width: 240 }} disabled={disabled || config.disabled} />
              </Form.Item>
            </Col>
          );
        },
      },
      {
        key: 'phoneNo',
        label: t('Mobile Phone'),
        type: FieldType.Input,
        ctrlProps: {
          maxLength: 20,
          allowClear: true,
          disabled,
        },
      },
      {
        key: 'title',
        label: t('Title'),
        type: FieldType.Input,
        ctrlProps: {
          allowClear: true,
          disabled,
        },
      },
      {
        key: 'issueWithoutPayment',
        label: t('Issue without payment'),
        type: FieldType.Select,
        ctrlProps: {
          options: enums?.yesNo as unknown as LabeledValue<string>[],
          disabled,
        },
      },
      {
        key: 'category',
        label: t('Category'),
        type: FieldType.Select,
        ctrlProps: {
          allowClear: true,
          options: channelAgentCategoryEnums?.map(options => ({
            key: options?.dictValue,
            label: options?.dictValueName,
            value: options?.dictValue,
          })),
          disabled,
        },
      },
      {
        key: 'idType',
        label: t('ID Type'),
        type: FieldType.Select,
        ctrlProps: {
          options: enums?.certiType as unknown as LabeledValue<string>[],
          disabled: disabled || !!staff?.idType,
          allowClear: true,
        },
      },
      {
        key: 'idNo',
        label: t('ID Number'),
        ctrlProps: {
          disabled: disabled || !!staff?.idNo,
          allowClear: true,
        },
      },
      {
        key: 'status',
        label: t('Status'),
        type: FieldType.Select,
        ctrlProps: {
          options: enums?.agentStatus as unknown as LabeledValue<string>[],
          disabled: disabled || staff?.status === AgentStatusType.Resigned, // 已保存的status为resigned时，不能再修改
          onChange: onChangeStatus,
          allowClear: true,
        },
      },
      {
        key: 'onboardDate',
        label: t('Onboard Date'),
        type: FieldType.DatePicker,
        ctrlProps: {
          disabled,
          allowClear: true,
          onChange: onboardDateChange,
        },
      },
      {
        key: 'resignDate',
        label: t('Resign Date'),
        type: FieldType.DatePicker,
        ctrlProps: {
          disabled: disabled || status === AgentStatusType.Employed,
          disabledDate: (currentDate: Moment) => currentDate && currentDate < moment(form.getFieldValue('onboardDate')),
        },
      },
      {
        type: FieldType.Customize,
        customerDom: <UserAccount initialValue={staff} disabled={disabled} name="Agent Portal" />,
      },
    ],
    [disabled, staff, t, channelAgentCategoryEnums, status]
  );
  const { formItems: staticFields } = useGenericSchemaFormItemFields({
    staticOrDynamic: 'STATIC',
    category: 'STAFF',
    disabled,
    type: covertChannelType2SchemaChannelType(channelType),
    entityType: EntityType.CHANNEL,
  });

  const processedStaticFields = useMemo(() => {
    staticFields.forEach(async field => {
      const props = field.ctrlProps ?? {};

      const aaa = await getNameGroupsFormat();

      console.log(aaa, 'aaa');

      switch (field.key) {
        case 'staffName':
          field.type = FieldType.Customize;
          field.customerDom = customerDom({
            label: field.label,
            name: field.key,
          });
          break;
        case 'salesFirstName':
          field.type = FieldType.Customize;
          field.customerDom = customerDom({
            label: field.label,
            name: field.key,
            onChange: (event: ChangeEvent<HTMLInputElement>) =>
              handleNameChange([event.target.value, form.getFieldValue('salesLastName')]),
          });
          break;
        case 'salesLastName':
          field.type = FieldType.Customize;
          field.customerDom = customerDom({
            label: field.label,
            name: field.key,
            onChange: (event: ChangeEvent<HTMLInputElement>) =>
              handleNameChange([form.getFieldValue('salesFirstName'), event.target.value]),
          });
          break;
        case 'status':
          props.onChange = onChangeStatus;
          break;
        case 'onboardDate':
          props.onChange = onboardDateChange;
          break;
        case 'resignDate':
          props.disabledDate = (currentDate: Moment) =>
            currentDate && currentDate < moment(form.getFieldValue('onboardDate'));
          props.disabled = props.disabled || status === AgentStatusType.Employed;
          break;
        case 'isCreateAccount':
          field.type = FieldType.Customize;
          field.customerDom = <UserAccount initialValue={staff} disabled={props.disabled} name="Agent Portal" />;
          break;

        default:
          break;
      }
    });
    return [...staticFields];
  }, [staticFields]);

  return channelType === ChannelTypeEnum.INSURANCE ? fields : processedStaticFields;
};

export const useSalesFormFields = ({
  disabled,
  info,
  id,
  origin,
  agentType,
  channelAgentCategoryEnums,
}: {
  disabled: boolean;
  info: AgentManagementInfo | EmployeeInfo;
  id: number;
  origin: string;
  agentType?: boolean;
  channelAgentCategoryEnums?: BizDictItem[];
}) => {
  const isTiedAgent = origin === OriginType.agent;
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const agent = useMemo(
    () => [
      {
        key: 'agentCode',
        label: isTiedAgent ? t('Agent Code') : t('Sales Channel Code'),
        type: FieldType.Input,
        ctrlProps: {
          disabled: disabled || id || !isTiedAgent,
        },
        rules: [
          {
            required: isTiedAgent,
            message: t('channel.common.required', {
              label: t('Agent Code'),
            }),
          },
        ],
      },
      {
        key: 'licenseNumber',
        label: t('License No.'),
        type: FieldType.Input,
        ctrlProps: {
          disabled: disabled || !isTiedAgent,
        },
      },
      {
        key: 'licenseEffectiveDate',
        label: t('License Effective Date'),
        type: FieldType.DatePicker,
        ctrlProps: {
          disabled: disabled || !isTiedAgent,
          format: dateFormatInstance.dateFormat,
        },
      },
      {
        key: 'selfAgent',
        label: t('Self Agent'),
        type: FieldType.Select,
        ctrlProps: {
          options: enums?.yesNo,
          disabled: disabled || !isTiedAgent,
        },
        rules: [
          {
            required: isTiedAgent,
            message: t('channel.common.required', {
              label: t('Self Agent'),
            }),
          },
        ],
      },
      {
        key: 'issueWithoutPayment',
        label: t('Issue without payment'),
        type: FieldType.Select,
        ctrlProps: {
          options: enums?.yesNo,
          disabled: disabled || !isTiedAgent,
        },
      },
      {
        key: 'agentCategory',
        label: t('Category'),
        type: FieldType.Select,
        ctrlProps: {
          allowClear: true,
          options: channelAgentCategoryEnums?.map(options => ({
            key: options?.dictValue,
            label: options?.dictValueName,
            value: options?.dictValue,
          })),
          disabled: disabled || !isTiedAgent,
        },
      },
    ],
    [disabled, enums, info, origin, id, channelAgentCategoryEnums]
  );
  let newAgent: Partial<QueryFieldsType>[] = [];
  if ((info as AgentManagementInfo)?.agentCode) {
    newAgent = [...agent];
  }
  if (isTiedAgent) {
    newAgent.push({
      type: FieldType.Customize,
      customerDom: (
        <UserAccount
          initialValue={info}
          disabled={disabled || !isTiedAgent || info?.status === AgentStatusType.Resigned}
          name="Agent Portal"
        />
      ),
    });
  }
  if ((info as EmployeeInfo)?.isEmployee === YesOrNo.YES && agentType) {
    newAgent.push({
      key: 'overridingCommission',
      label: <b>{t('Overriding Commission')}</b>,
      type: FieldType.Radio,
      initialValue: YesOrNo.NO,
      col: 24,
      ctrlProps: {
        options: enums?.yesNo,
        disabled,
      },
    });
  }

  return newAgent;
};

export const useRelatedFormFields = ({
  disabled,
  goodsOptions,
  editedItem,
}: {
  disabled: boolean;
  goodsOptions: SelectOptions[];
  editedItem: RelatedType;
}) => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const settlementRuleOptions = enums?.settlementRule?.filter(item => item.dictValue !== '3') || [];
  return useMemo(
    () => [
      {
        key: 'goodsId',
        label: t('Goods'),
        type: FieldType.Select,
        ctrlProps: {
          options: goodsOptions,
          disabled: disabled || !!editedItem?.id,
        },
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Goods'),
            }),
          },
        ],
      },
      {
        key: 'settlementRule',
        label: t('Settlement Rule'),
        type: FieldType.Select,
        ctrlProps: {
          options: settlementRuleOptions,
          disabled,
        },
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Settlement Rule'),
            }),
          },
        ],
      },
      {
        key: 'settlementToPremium',
        label: t('Accumulated to Premium'),
        type: FieldType.Select,
        ctrlProps: {
          options: enums?.yesNo,
          disabled,
        },
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Accumulated to Premium'),
            }),
          },
        ],
      },
    ],
    [disabled, enums, goodsOptions]
  );
};
