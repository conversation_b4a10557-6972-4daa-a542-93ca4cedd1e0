import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

import moment from 'moment';

import type { SchemaDefField } from 'genesis-web-service';
import { SchemaDataType, SchemaDefType, YesOrNo } from 'genesis-web-service';
import type { DateFormat } from 'genesis-web-shared/lib/l10n';

import { FieldType } from '@/components/CommonForm';
import { ChannelSchemaFieldsContext } from '@/hooks/ChannelSchemaFieldsContext';

interface Option {
  staticOrDynamic?: 'STATIC' | 'DYNAMIC' | 'ALL';
  category: string;
  disabled?: boolean;
  fieldKeyPrefix?: string;
}
export const useChannelSchemaFieldsByCategory = ({ staticOrDynamic = 'ALL', category }: Option) => {
  const { channelSchemaDefFields } = useContext(ChannelSchemaFieldsContext);
  const [params] = useSearchParams();
  const isInsurance = params.get('type') === 'INSURANCE';

  const salesAgreementFields =
    channelSchemaDefFields.find(
      item =>
        item.category === category &&
        (isInsurance
          ? item.schemaDefType === SchemaDefType.CHANNEL_INSURANCE
          : item.schemaDefType === SchemaDefType.CHANNEL)
    )?.fields || [];

  const renderedFields = salesAgreementFields.filter(field => {
    if (field.displayOnTanentLevel === YesOrNo.NO) {
      return false;
    }
    if (staticOrDynamic === 'STATIC') {
      return field.isExtension === YesOrNo.NO;
    }
    if (staticOrDynamic === 'DYNAMIC') {
      return field.isExtension === YesOrNo.YES;
    }
    return true;
  });

  return renderedFields;
};

export const useSchemaFormItemFieldsByCategory = ({
  staticOrDynamic = 'ALL',
  category,
  disabled,
  fieldKeyPrefix = 'extensions',
}: Option) => {
  const { t } = useTranslation(['partner']);
  const { schemaUsedBizDictMap } = useContext(ChannelSchemaFieldsContext);

  const renderedFields = useChannelSchemaFieldsByCategory({
    staticOrDynamic,
    category,
  });

  const formItems = renderedFields.map(field => {
    const basicFieldInfo: Record<string, any> = {
      key: [fieldKeyPrefix, field.code],
      label: field.name,
      type: FieldType.Input,
      rules:
        field.required === YesOrNo.YES
          ? [
              {
                required: true,
                message: t('channel.common.required', {
                  label: field.name,
                }),
              },
            ]
          : [],
      ctrlProps: {
        allowClear: true,
        disabled,
      },
    };
    if (field.dataType === SchemaDataType.INPUT) {
      return basicFieldInfo;
    }

    if (field.dataType === SchemaDataType.SELECT) {
      basicFieldInfo.type = FieldType.Select;
      basicFieldInfo.ctrlProps.options = schemaUsedBizDictMap[field.bizDictKey!] || [];
    }

    if (field.dataType === SchemaDataType.DATETIME) {
      basicFieldInfo.type = FieldType.DatePicker;
      basicFieldInfo.ctrlProps.showTime = true;
    }

    if (field.dataType === SchemaDataType.DATE) {
      basicFieldInfo.type = FieldType.DatePicker;
    }

    if (field.dataType === SchemaDataType.NUMBER) {
      basicFieldInfo.type = FieldType.InputNumber;
      basicFieldInfo.ctrlProps.style = {
        width: 240,
      };
    }

    return basicFieldInfo;
  });

  return {
    formItems,
    schemaFields: renderedFields,
  };
};

export const dealSchemaFieldValueForDisplay = (values: Record<string, any>, schemaFields: SchemaDefField[]) => {
  if (!values) {
    return;
  }
  const dateFieldCodes = schemaFields
    .filter(field => field.dataType === SchemaDataType.DATETIME || field.dataType === SchemaDataType.DATE)
    .map(field => field.code);

  for (const key in values) {
    if (dateFieldCodes.includes(key) && values[key]) {
      values[key] = moment(values[key]);
    }
  }
};

export const dealSchemaFieldValueForSubmit = (
  values: Record<string, any>,
  schemaFields: SchemaDefField[],
  options: {
    dateFormatInstance: DateFormat;
  }
) => {
  if (!values) {
    return;
  }
  const { dateFormatInstance } = options;
  const dateFieldCodes = schemaFields
    .filter(field => field.dataType === SchemaDataType.DATETIME || field.dataType === SchemaDataType.DATE)
    .map(field => field.code);

  for (const key in values) {
    if (dateFieldCodes.includes(key) && values[key]) {
      values[key] = dateFormatInstance.formatTz(values[key]);
    }
  }
};
