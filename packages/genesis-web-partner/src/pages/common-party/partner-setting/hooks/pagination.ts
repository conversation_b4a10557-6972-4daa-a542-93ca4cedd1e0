import { useState } from 'react';

import type { PaginationProps } from 'antd/lib/pagination/Pagination';

export const usePagination = (
  initialValue: PaginationProps = { current: 1, pageSize: 10, total: 0 }
): [page: PaginationProps, pageChangeVal: (total: number) => void] => {
  const [pagination, setPaginationValue] = useState<PaginationProps>({
    ...initialValue,
    showSizeChanger: true,
    showQuickJumper: true,
    onChange: (page: number, pageSize?: number) => {
      setPaginationValue({
        ...pagination,
        current: page,
        pageSize: pageSize || 10,
      });
    },
    onShowSizeChange: (page: number, pageSize?: number) => {
      setPaginationValue({
        ...pagination,
        current: page,
        pageSize: pageSize || 10,
      });
    },
  });

  const setPagination = (total: number) => {
    setPaginationValue({ ...pagination, total });
  };

  return [pagination, setPagination];
};
