import { useState } from 'react';

import type { PaginationProps } from 'antd/lib/pagination/Pagination';

export const usePagination = (
  initialValue: PaginationProps = { current: 1, pageSize: 10, total: 0 }
): [page: PaginationProps, pageChangeVal: (total: number) => void] => {
  const [current, setCurrent] = useState<number>(initialValue.current);
  const [pageSize, setPageSize] = useState<number>(initialValue.pageSize);
  const [total, setTotal] = useState<number>(initialValue.total);

  const onChange = (currentPage: number, currentPageSize?: number) => {
    setCurrent(currentPage);
    setPageSize(currentPageSize);
  };

  const setPagination = (currentTotal: number) => {
    setTotal(currentTotal);
  };

  return [
    {
      current,
      pageSize,
      total,
      onChange,
      onShowSizeChange: onChange,
      showSizeChanger: true,
      showQuickJumper: true,
    },
    setPagination,
  ];
};
