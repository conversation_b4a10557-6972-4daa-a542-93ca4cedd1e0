import type { BizDictItem } from 'genesis-web-service';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';

/**
 *
 * @param dictKey bizDictKey
 * @description 目前address、contactPerson、account用到；enums mapping处理
 */
export const useEnumsMapping = (dictKey: string): Record<string, string> => {
  const enums = useTenantBizDict(dictKey) as BizDictItem[];
  return (
    enums?.reduce(
      (out, enumItem) => ({
        ...out,
        [enumItem.value]: enumItem.label || enumItem.dictDesc,
      }),
      {}
    ) ?? {}
  );
};
