import { useCallback, useEffect, useState } from 'react';

import { useSearchParams } from '@umijs/max';

import qs from 'qs';

import type { StructureDetail } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service';

import { ChannelTitleMap } from '@/pages/common-party/utils/constants';
import { ModeEnum } from '@/types/common';
import type { LocationQueryParam } from '@/types/common-party';

import type { BreadCrumbInfo } from '../models/index.interface';
import { ChannelTypeEnum } from '../models/index.interface';

interface Props {
  id: string;
  type: ChannelTypeEnum;
}

/**
 *
 * @param id id
 * @param type 当前页面所属type
 * @description 获取面包屑
 */
export const useBreadCrumbNav = ({ id, type }: Props) => {
  const [breadCrumbList, setBreadCrumbList] = useState<BreadCrumbInfo[]>();
  const [searchParams] = useSearchParams();
  const { modeType } = qs.parse(searchParams.toString()) as unknown as LocationQueryParam;
  const queryStructure = useCallback(() => {
    ChannelService.getStructure(Number(id), { type }).then(res => {
      const tempBreadList: BreadCrumbInfo[] = [];
      const loop = (structureList: StructureDetail[]) => {
        structureList.forEach(structure => {
          tempBreadList.push({
            id: structure.id,
            name: structure.name,
            pathname: type === ChannelTypeEnum.Bank ? '/bank' : '/channel',
            query: {
              id: String(structure.id),
              type: structure.type,
              modeType: ModeEnum.READ,
            },
          });
          if (structure.childList?.length && String(structure.id) !== id) {
            // structure接口会返回当前id所处层级的下一层，但是面包屑只需展示到当前层级，估当匹配到id时，不继续往下
            loop(structure.childList);
          }
        });
      };

      loop(res ?? []);
      if (modeType === ModeEnum.ADD) {
        // 新增往最后添加一条
        tempBreadList.push({
          name: ChannelTitleMap[type],
          pathname: type === ChannelTypeEnum.Bank ? '/bank' : '/channel',
        });
      }
      setBreadCrumbList(tempBreadList);
    });
  }, [id, type, modeType]);

  useEffect(() => {
    if (id) {
      queryStructure();
    }
  }, [queryStructure]);

  return {
    breadCrumbList,
    setBreadCrumbList,
  };
};
