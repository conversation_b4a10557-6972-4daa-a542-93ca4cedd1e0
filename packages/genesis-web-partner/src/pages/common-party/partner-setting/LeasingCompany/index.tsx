import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

import { Button, Card, Divider, Drawer, Form, Layout, Skeleton, message } from 'antd';
import qs from 'qs';

import type { ChannelDetail } from 'genesis-web-service';
import { ChannelService, SalesTypeEnum, YesOrNo } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { ChannelSchemaFieldsContext } from '@/hooks/ChannelSchemaFieldsContext';
import { useChannelSchemaFields } from '@/hooks/useChannelSchemaFields';
import { AccountInfo } from '@/pages/common-party/partner-setting/components/AccountInfo';
import { Address } from '@/pages/common-party/partner-setting/components/Address';
import { BasicInfo } from '@/pages/common-party/partner-setting/components/BasicInfo';
import { BasicInfoTip } from '@/pages/common-party/partner-setting/components/BasicInfoTip';
import { Staff } from '@/pages/common-party/partner-setting/components/ChannelStaff';
import { CommonHeader } from '@/pages/common-party/partner-setting/components/CommonHeader';
import { ContactPerson } from '@/pages/common-party/partner-setting/components/ContactPerson';
import { SalesAgreement } from '@/pages/common-party/partner-setting/components/SalesAgreement';
import { Structure } from '@/pages/common-party/partner-setting/components/Structure';
import { ModeEnum } from '@/types/common';
import type { LocationQueryParam } from '@/types/common-party';
import { useDispatch } from '@umijs/max';

import { useBreadCrumbNav } from '../hooks/useBreadCrumb';
import {
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
} from '../hooks/useChannelSchemaFieldsByCategory';
import commonStyles from '../style.scss';
import styles from './style.scss';

const LeasingCompanyDetail = () => {
  const [searchParams] = useSearchParams();
  const { id, type, modeType, parentId, level } = qs.parse(searchParams.toString()) as unknown as LocationQueryParam;
  const dispatch = useDispatch();
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const basicInfoRef = useRef(null);
  const addressRef = useRef(null);
  const agreementRef = useRef(null);
  const concatPersonRef = useRef(null);
  const accountInfoRef = useRef(null);

  const { channelSchemaDefFields, schemaUsedBizDictMap } = useChannelSchemaFields();

  const [leasingCompanyData, setLeasingCompanyData] = useState<ChannelDetail>();
  const [mode, setMode] = useState<ModeEnum>();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [structureVisible, setStructureVisible] = useState(false);

  const { breadCrumbList = [], setBreadCrumbList } = useBreadCrumbNav({
    id: id || parentId,
    type,
  });

  const getLeasingCompanyData = useCallback(() => {
    setLoading(true);
    ChannelService.queryChannelDetail(id)
      .then(setLeasingCompanyData)
      .catch((error: Error) => message.error(error.message))
      .finally(() => setLoading(false));
  }, [id]);

  useEffect(() => {
    if (id) {
      getLeasingCompanyData();
    }
  }, [id]);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'country',
        'organizationIdType',
        'companyAddressType',
        'gender',
        'channelCustomerType',
        'certiType',
        'channelOrgAccountType',
        'accountSubType',
        'accountType',
        'yesNo',
        'settlementRule',
        'salesAgreementStatus',
        'agentStatus',
      ],
    });
  }, [dispatch]);

  useEffect(() => {
    if (modeType) {
      setMode(modeType);
    } else if (id) {
      setMode(ModeEnum.READ);
    }
  }, [modeType, id]);

  useEffect(() => {
    if (leasingCompanyData) {
      const basicInfodynamicFields =
        channelSchemaDefFields
          .find(item => item.category === 'BASE')
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForDisplay(leasingCompanyData?.extensions, basicInfodynamicFields);
      form.setFieldsValue({
        ...leasingCompanyData,
      });
    }
  }, [leasingCompanyData, form]);

  const handleCancel = useCallback(() => {
    form.resetFields();
    setMode(ModeEnum.READ);
    setLeasingCompanyData({
      ...leasingCompanyData,
      addressList: [...(leasingCompanyData.addressList ?? [])],
      customerList: [...(leasingCompanyData.customerList ?? [])],
      accountList: [...(leasingCompanyData.accountList ?? [])],
    });
  }, [form, leasingCompanyData]);

  const saveleasingCompany = useCallback(() => {
    form?.validateFields().then(values => {
      setSaving(true);
      const logoUrl = basicInfoRef.current || '';
      const addressList = addressRef.current || [];
      const customerList = concatPersonRef.current || [];
      const accountList = accountInfoRef.current || [];

      const basicInfodynamicFields =
        channelSchemaDefFields
          .find(item => item.category === 'BASE')
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForSubmit(values.extensions, basicInfodynamicFields, {
        dateFormatInstance,
      });

      let submitReq: Promise<ChannelDetail>;
      if (leasingCompanyData) {
        submitReq = ChannelService.updateChannel(leasingCompanyData.id, {
          ...values,
          type,
          parentId: leasingCompanyData.parentId,
          level: leasingCompanyData.level,
          addressList,
          accountList,
          customerList,
          logoUrl,
          teamId: leasingCompanyData.teamId,
          dataPathCode: leasingCompanyData.dataPathCode,
        });
      } else {
        submitReq = ChannelService.addChannel({
          ...values,
          type,
          parentId,
          level,
          addressList,
          accountList,
          customerList,
          logoUrl,
        });
      }
      submitReq
        .then(resp => {
          message.success(t('Save successfully'));
          setLeasingCompanyData(resp);
          setMode(ModeEnum.READ);
          if (resp) {
            const clonedBreadList = [...breadCrumbList];
            if (clonedBreadList.length > 1) {
              // 保存成功，更新最后一条name
              clonedBreadList[clonedBreadList.length - 1].name = resp.name;
            }
            agreementRef.current.saveAgreement(resp.code, resp.id, type);
            setBreadCrumbList(clonedBreadList);
          }
        })
        .catch((error: Error) => message.error(error.message))
        .finally(() => setSaving(false));
    });
  }, [form, leasingCompanyData, type, parentId, level, breadCrumbList, t]);

  return (
    <ChannelSchemaFieldsContext.Provider
      value={{
        channelSchemaDefFields: channelSchemaDefFields,
        schemaUsedBizDictMap: schemaUsedBizDictMap,
      }}
    >
      <Skeleton loading={loading} active>
        <Layout.Content className={styles.channelDetail}>
          <CommonHeader
            loading={saving}
            readonly={mode === ModeEnum.READ}
            showCancel={!!leasingCompanyData}
            showViewStructure={!!leasingCompanyData}
            handleEdit={setMode}
            handleCancel={handleCancel}
            handleSave={saveleasingCompany}
            viewStructure={() => setStructureVisible(true)}
            name={leasingCompanyData?.name}
            breadCrumbList={breadCrumbList}
          />
          <div className={styles.section}>
            <Card bordered={false}>
              <div className={commonStyles.basicInfoSection}>
                <BasicInfo
                  ref={basicInfoRef}
                  form={form}
                  readonly={mode === ModeEnum.READ}
                  channelDetail={leasingCompanyData}
                  fileUniqueCode={leasingCompanyData?.logoUrl}
                  basicInfoType={type}
                />
                {!leasingCompanyData && (
                  <Button type="primary" loading={saving} onClick={() => saveleasingCompany()}>
                    {t('Create')}
                  </Button>
                )}
              </div>
              <div className={commonStyles.extraInfoSection}>
                {!leasingCompanyData && <BasicInfoTip />}
                <SalesAgreement
                  ref={agreementRef}
                  readonly={mode === ModeEnum.READ || !leasingCompanyData}
                  channelCode={leasingCompanyData?.code}
                  mode={mode}
                  renew={true}
                  salesType={SalesTypeEnum.CHANNEL}
                />
                <Divider dashed />
                <Address
                  ref={addressRef}
                  readonly={mode === ModeEnum.READ || !leasingCompanyData}
                  initialList={leasingCompanyData?.addressList}
                  mode={mode}
                />
                <Divider dashed />
                <ContactPerson
                  ref={concatPersonRef}
                  channelType={type}
                  readonly={mode === ModeEnum.READ || !leasingCompanyData}
                  initialList={leasingCompanyData?.customerList}
                  mode={mode}
                />
                <Divider dashed />
                <AccountInfo
                  ref={accountInfoRef}
                  readonly={mode === ModeEnum.READ || !leasingCompanyData}
                  initialList={leasingCompanyData?.accountList}
                  mode={mode}
                />
                <Divider dashed />
                <Staff
                  readonly={mode === ModeEnum.READ || !leasingCompanyData}
                  channelId={leasingCompanyData?.id}
                  channelType={type}
                  mode={mode}
                />
              </div>
              <Drawer
                title={t('Structure Details')}
                open={structureVisible}
                maskClosable={false}
                width={641}
                rootClassName={commonStyles.structureDrawer}
                onClose={() => setStructureVisible(false)}
              >
                <Structure channelId={leasingCompanyData?.id} channelType={type} visible={structureVisible} />
              </Drawer>
            </Card>
          </div>
        </Layout.Content>
      </Skeleton>
    </ChannelSchemaFieldsContext.Provider>
  );
};

export default LeasingCompanyDetail;
