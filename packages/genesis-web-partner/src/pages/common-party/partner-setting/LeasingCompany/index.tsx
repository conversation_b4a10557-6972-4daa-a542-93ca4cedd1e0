import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Drawer, Form, Skeleton, message } from 'antd';

import { useDispatch } from '@umijs/max';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import type { ChannelDetail } from 'genesis-web-service';
import { ChannelService, SalesTypeEnum, YesOrNo } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { ChannelSchemaFieldsContext } from '@/hooks/ChannelSchemaFieldsContext';
import { useChannelSchemaFields } from '@/hooks/useGenericChannelSchemaFields';
import { ChannelDetailFooter } from '@/pages/common-party/partner-setting/Channel/Footer';
import { AccountInfo } from '@/pages/common-party/partner-setting/components/AccountInfo';
import { Address } from '@/pages/common-party/partner-setting/components/Address';
import { BasicInfo } from '@/pages/common-party/partner-setting/components/BasicInfo';
import { BasicInfoTip } from '@/pages/common-party/partner-setting/components/BasicInfoTip';
import { Staff } from '@/pages/common-party/partner-setting/components/ChannelStaff';
import { ContactPerson } from '@/pages/common-party/partner-setting/components/ContactPerson';
import { SalesAgreement } from '@/pages/common-party/partner-setting/components/SalesAgreement';
import { Structure } from '@/pages/common-party/partner-setting/components/Structure';
import { ModeEnum } from '@/types/common';

import type { PartnerSettingSearchQuery } from '..';
import {
  covertChannelType2SchemaChannelType,
  dealSchemaFieldValueForDisplay,
  dealSchemaFieldValueForSubmit,
} from '../../../../hooks/useGenericSchemaFormItemFields';
import { useBreadCrumbNav } from '../hooks/useBreadCrumb';
import commonStyles from '../style.scss';

const LeasingCompanyDetail = () => {
  const [searchParams] = useRouterState<PartnerSettingSearchQuery>();
  const { id, channelType: type, modeType, parentId, level } = searchParams;
  const dispatch = useDispatch();
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const basicInfoRef = useRef(null);
  const addressRef = useRef(null);
  const agreementRef = useRef(null);
  const concatPersonRef = useRef(null);
  const accountInfoRef = useRef(null);

  const { schemaDefFields, schemaUsedBizDictMap } = useChannelSchemaFields();

  const [leasingCompanyData, setLeasingCompanyData] = useState<ChannelDetail>();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [structureVisible, setStructureVisible] = useState(false);
  const disabled = modeType === ModeEnum.READ;

  const { breadCrumbList = [], setBreadCrumbList } = useBreadCrumbNav({
    id: id || parentId,
    type,
  });

  const getLeasingCompanyData = useCallback(() => {
    setLoading(true);
    ChannelService.queryChannelDetail(id)
      .then(setLeasingCompanyData)
      .catch((error: Error) => message.error(error.message))
      .finally(() => setLoading(false));
  }, [id]);

  useEffect(() => {
    if (id) {
      getLeasingCompanyData();
    }
  }, [id]);

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: [
        'country',
        'organizationIdType',
        'companyAddressType',
        'gender',
        'channelCustomerType',
        'certiType',
        'channelOrgAccountType',
        'accountSubType',
        'accountType',
        'yesNo',
        'settlementRule',
        'salesAgreementStatus',
        'agentStatus',
      ],
    });
  }, [dispatch]);

  useEffect(() => {
    if (leasingCompanyData) {
      const basicInfodynamicFields =
        schemaDefFields
          .find(item => item.category === 'BASE' && item.channelType === covertChannelType2SchemaChannelType(type))
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForDisplay(leasingCompanyData?.extensions, basicInfodynamicFields);
      form.setFieldsValue({
        ...leasingCompanyData,
      });
    }
  }, [leasingCompanyData, form, type, schemaDefFields]);

  const saveLeasingCompany = useCallback(() => {
    form?.validateFields().then(values => {
      setSaving(true);
      const logoUrl = basicInfoRef.current || '';
      const addressList = addressRef.current || [];
      const customerList = concatPersonRef.current || [];
      const accountList = accountInfoRef.current || [];

      const basicInfodynamicFields =
        schemaDefFields
          .find(item => item.category === 'BASE' && item.channelType === covertChannelType2SchemaChannelType(type))
          ?.fields?.filter(field => field.isExtension === YesOrNo.YES) ?? [];
      dealSchemaFieldValueForSubmit(values.extensions, basicInfodynamicFields, {
        dateFormatInstance,
      });

      let submitReq: Promise<ChannelDetail>;
      if (leasingCompanyData) {
        submitReq = ChannelService.updateChannel(leasingCompanyData.id, {
          ...values,
          type,
          parentId: leasingCompanyData.parentId,
          level: leasingCompanyData.level,
          addressList,
          accountList,
          customerList,
          logoUrl,
          teamId: leasingCompanyData.teamId,
          dataPathCode: leasingCompanyData.dataPathCode,
        });
      } else {
        submitReq = ChannelService.addChannel({
          ...values,
          type,
          parentId,
          level,
          addressList,
          accountList,
          customerList,
          logoUrl,
        });
      }
      submitReq
        .then(resp => {
          message.success(t('Save successfully'));
          setLeasingCompanyData(resp);
          if (resp) {
            const clonedBreadList = [...breadCrumbList];
            if (clonedBreadList.length > 1) {
              // 保存成功，更新最后一条name
              clonedBreadList[clonedBreadList.length - 1].name = resp.name;
            }
            agreementRef.current.saveAgreement(resp.code, resp.id, type);
            setBreadCrumbList(clonedBreadList);
          }
        })
        .catch((error: Error) => message.error(error.message))
        .finally(() => setSaving(false));
    });
  }, [form, leasingCompanyData, type, parentId, level, breadCrumbList, t]);

  return (
    <ChannelSchemaFieldsContext.Provider
      value={{
        channelSchemaDefFields: schemaDefFields,
        schemaUsedBizDictMap: schemaUsedBizDictMap,
      }}
    >
      <Skeleton loading={loading} active>
        <div>
          <BasicInfo
            ref={basicInfoRef}
            form={form}
            disabled={disabled}
            channelDetail={leasingCompanyData}
            fileUniqueCode={leasingCompanyData?.logoUrl}
            basicInfoType={type}
          />
          {!leasingCompanyData && (
            <Button type="primary" loading={saving} onClick={() => saveLeasingCompany()}>
              {t('Create')}
            </Button>
          )}
        </div>
        <div>
          {!leasingCompanyData && <BasicInfoTip />}
          <SalesAgreement
            ref={agreementRef}
            channelCode={leasingCompanyData?.code}
            renew={true}
            salesType={SalesTypeEnum.CHANNEL}
            channelType={type}
            disabled={disabled}
          />
        </div>
        <Address
          ref={addressRef}
          initialList={leasingCompanyData?.addressList}
          channelType={type}
          disabled={disabled}
        />
        <ContactPerson
          ref={concatPersonRef}
          channelType={type}
          initialList={leasingCompanyData?.customerList}
          disabled={disabled}
        />
        <AccountInfo
          ref={accountInfoRef}
          initialList={leasingCompanyData?.accountList}
          channelType={type}
          disabled={disabled}
        />
        <Staff channelId={leasingCompanyData?.id} channelType={type} disabled={disabled} />
        <ChannelDetailFooter
          handleSave={saveLeasingCompany}
          showViewStructure={!!leasingCompanyData}
          viewStructure={() => setStructureVisible(true)}
          channelType={type}
          disabled={disabled}
        />
        <Drawer
          title={t('Structure Details')}
          open={structureVisible}
          maskClosable={false}
          width={641}
          rootClassName={commonStyles.structureDrawer}
          onClose={() => setStructureVisible(false)}
        >
          <Structure channelId={leasingCompanyData?.id} channelType={type} visible={structureVisible} />
        </Drawer>
      </Skeleton>
    </ChannelSchemaFieldsContext.Provider>
  );
};

export default LeasingCompanyDetail;
