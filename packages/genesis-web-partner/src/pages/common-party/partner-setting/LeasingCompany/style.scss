@import '@/variables.scss';

.channel-detail {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 50px);
  background-color: var(--layout-body-background);

  .section {
    flex: 1;
    margin: 16px;
    overflow: auto;
    :global {
      .#{$antd-prefix}-card {
        background-color: transparent;
        .#{$antd-prefix}-card-body {
          padding: 0;
        }
      }
    }
  }
}
