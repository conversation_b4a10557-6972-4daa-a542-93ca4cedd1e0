import type { PaginationProps } from 'antd/es/pagination';

import type { ListShownType, ModeEnum } from '@/types/common';

export enum ChannelTypeEnum {
  INSTITUTE = 'INSTITUTE',
  INSURANCE = 'INSURANCE',
  SALE_CHANNEL = 'SALE_CHANNEL',
  AGENCY = 'AGENCY',
  SERVICE = 'SERVICE',
  SERVICE_COMPANY = 'SERVICE_COMPANY',
  LEGAL_SERVICE = 'LEGAL_SERVICE',
  Investigator = 'INVESTIGATOR',
  Bank = 'BANK',
  AssessmentCompany = 'ASSESSMENT_COMPANY',
  ExternalInsuranceCompany = 'EXTERNAL_INSURANCE_COMPANY',
  BrokerCompany = 'BROKER_COMPANY',
  LeaseChannel = 'LEASE_CHANNEL',
  TiedAgent = 'TIED_AGENT',
  FRONT_LINE_CHANNEL = 'FRONT_LINE_CHANNEL',
  KEY_ACCOUNT_CHANNEL = 'KEY_ACCOUNT_CHANNEL',
  PARTNERSHIP_DISTRIBUTION = 'PARTNERSHIP_DISTRIBUTION',
  RETAIL_FINANCIAL_SERVICES = 'RETAIL_FINANCIAL_SERVICES',
  IN_FA = 'IN_FA',
  DIRECT = 'DIRECT',
  Default = 'default',
}

export const ChannelTypeCodeMap = {
  [ChannelTypeEnum.INSURANCE]: '1',
  [ChannelTypeEnum.SALE_CHANNEL]: '2',
  [ChannelTypeEnum.AGENCY]: '3',
  [ChannelTypeEnum.SERVICE]: '4',
  [ChannelTypeEnum.INSTITUTE]: '5',
  [ChannelTypeEnum.TiedAgent]: '6',
  [ChannelTypeEnum.Bank]: '7',
  [ChannelTypeEnum.LEGAL_SERVICE]: '8',
  [ChannelTypeEnum.Investigator]: '9',
  [ChannelTypeEnum.BrokerCompany]: '10',
  [ChannelTypeEnum.LeaseChannel]: '11',
  [ChannelTypeEnum.PARTNERSHIP_DISTRIBUTION]: '12',
  [ChannelTypeEnum.RETAIL_FINANCIAL_SERVICES]: '13',
  [ChannelTypeEnum.FRONT_LINE_CHANNEL]: '14',
  [ChannelTypeEnum.KEY_ACCOUNT_CHANNEL]: '15',
};

export interface ChannelSearchForm {
  code: string;
  name: string;
}
export interface ChannelRouteState<T> {
  pagination: PaginationProps;
  searchForm: T;
  channelType: ChannelTypeEnum;
  currentMode: ListShownType;
}

export enum FormTypeEnum {
  BASIC_INFO = 'basicInfo',
  ADDRESS = 'address',
  ACCOUNT_INFO = 'accountInfo',
  CONTACT_PERSON = 'contactPerson',
  SALES_AGREEMENT = 'salesAgreement',
  SERVICE_AGREEMENT = 'serviceAgreement',
}

export interface BreadCrumbInfo {
  id?: number;
  name: string;
  pathname?: string;
  query?: {
    id: string;
    parentId?: string;
    type?: string;
    modeType: ModeEnum;
  };
}
