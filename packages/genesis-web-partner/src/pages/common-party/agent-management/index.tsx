import { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { EllipsisOutlined, FileAddOutlined, FileTextOutlined } from '@ant-design/icons';
import { Button, Dropdown, Input, Layout, Modal, Skeleton, Spin, Tree, Typography, message } from 'antd';
import type { DataNode, EventDataNode } from 'antd/es/tree';
import type { Key } from 'antd/lib/table/interface';

import { Outlet, useDispatch, useNavigate, useSearchParams, useSelector } from '@umijs/max';

import { useDebounceFn } from 'ahooks';
import { find, remove } from 'lodash-es';
import qs from 'qs';

import { Icon, SimpleSectionHeader } from '@zhongan/nagrand-ui';

import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import { NoData } from 'genesis-web-component/lib/components/NoData';
import type { ChannelConfigItem, ChannelTeamInfo } from 'genesis-web-service';
import {
  AgentSalesHierarchyConfiguration,
  ChannelService,
  TeamBusinessType,
  ChannelTeamType as TeamType,
  YesOrNo,
} from 'genesis-web-service';

import { usePermission } from '@/hooks/usePermissions';
import type { ConnectState } from '@/models/connect';
import { AddNewSalesHierarchy } from '@/pages/common-party/agent-management/components/AddNewSalesHierarchy';
import { AgentContext } from '@/pages/common-party/agent-management/hooks/useAgentManagement';
import {
  AgentManagementPrefix,
  DeletedContentMap,
  RootLevel,
  RouterMap,
  VirtualRootLevel,
} from '@/pages/common-party/agent-management/util/constants';
import type { LocationQueryParam } from '@/types/common-party';

import styles from './index.scss';

interface TeamsMapProps {
  teamNames: string[];
  teamInfo: ChannelTeamInfo;
  maxLevel: number;
  originMaxLevel: number;
}

const AgentManagement = () => {
  const { t } = useTranslation('partner');
  const navigate = useNavigate();
  const canEdit = usePermission('channel.agent-management.edit');
  const [searchParams] = useSearchParams();
  const { id, parentId: routerParentId } = qs.parse(searchParams.toString()) as unknown as LocationQueryParam;
  const dispatch = useDispatch();

  const [teams, setTeams] = useState<ChannelTeamInfo[]>();
  const [autoExpandParent, setAutoExpandParent] = useState(false); // 是否自动展开父节点
  const [expandedKeys, setExpandedKeys] = useState<number[]>(); // 展开的节点key
  const [selectedKey, setSelectedKey] = useState<number>(); // 选择的节点key
  const [hoveredId, setHoveredId] = useState<number>();
  const [loading, setLoading] = useState(false);
  const [teamsLoading, setTeamsLoading] = useState(false);
  const [isSearchNoData, setIsSearchNoData] = useState(false);
  const teamsMapRef = useRef<Record<number, TeamsMapProps>>();
  const [config, setConfig] = useState<ChannelConfigItem>();

  useEffect(() => {
    dispatch({
      type: 'global/getTenantBizDict',
      payload: ['agentStatus', 'yesNo', 'country', 'officeCity', 'certiType', 'channelType'],
    });
  }, []);

  const { teamInfo, teamOrgStructureId } = useSelector(({ partnerManagement }: ConnectState) => ({
    teamInfo: partnerManagement?.teamInfo,
    teamOrgStructureId: partnerManagement?.teamOrgStructureId,
  }));
  // 切换前是否需要拦截
  const isNeedIntercept = useMemo(
    () => (teamOrgStructureId && !teamInfo) || (!!teamInfo && teamInfo.level === RootLevel && !teamOrgStructureId),
    [teamInfo, teamOrgStructureId]
  );

  // 循环遍历处理teams
  const loopTeams = useCallback((teamsTree: ChannelTeamInfo[], channelConfig: ChannelConfigItem) => {
    const teamsMap: Record<number, TeamsMapProps> = {};
    const loop = (searchedTeams: ChannelTeamInfo[], maxLevel?: number) => {
      searchedTeams?.forEach(team => {
        // 面包屑数组
        let teamNames = teamsMap[team.parentId]?.teamNames ?? [];

        // virtual节点只展示第一层和最后一层
        if (!team.isVirtual || team.level === VirtualRootLevel || !team.children.length) {
          teamNames = teamNames.concat(team.teamName);
        }
        const teamProps = {
          teamNames: teamNames, // 面包屑数组
          teamInfo: team,
          maxLevel: maxLevel ?? team.maxLevel ?? +channelConfig?.dictValue,
          originMaxLevel: team.maxLevel,
        };
        teamsMap[team.id] = teamProps;
        if (team.children?.length) {
          loop(team.children, teamProps.maxLevel);
        }
      });
    };
    loop(teamsTree);
    teamsMapRef.current = teamsMap;
  }, []);

  // 获取teams tree
  const queryTeams = useCallback(
    (channelConfigItem?: ChannelConfigItem) => {
      setTeamsLoading(true);
      return new Promise((resolve, reject) => {
        ChannelService.queryTeamTree(TeamBusinessType.Agent.toLowerCase())
          .then(res => {
            setTeams(res);
            loopTeams(res, channelConfigItem ?? config);
            resolve(res);
          })
          .catch((error: Error) => {
            message.error(error.message);
            reject();
          })
          .finally(() => setTeamsLoading(false));
      });
    },
    [config, loopTeams]
  );

  // 防抖搜索
  const { run: onSearch } = useDebounceFn(
    async (searchContent: string) => {
      setTeamsLoading(true);
      if (searchContent === '') {
        // 清除搜索内容时回到初始状态
        await queryTeams();
        setIsSearchNoData(false);
        setAutoExpandParent(false);
        setExpandedKeys([]);
        setSelectedKey(null);
        return;
      }
      ChannelService.searchTeamTree(TeamBusinessType.Agent.toLowerCase(), searchContent)
        .then(res => {
          setTeams(res);
          loopTeams(res);
          if (!res?.length) {
            setIsSearchNoData(true);
            return;
          }
          // 拿到结果后全部展开
          setAutoExpandParent(true);
          const keys: number[] = Object.keys(teamsMapRef.current)?.map(key => +key);
          setExpandedKeys(keys);
        })
        .catch((error: Error) => message.error(error.message))
        .finally(() => setTeamsLoading(false));
    },
    { wait: 200 }
  );

  // 拦截弹窗
  const onIntercept = useCallback(() => {
    Modal.warning({
      title: t('warning'),
      content: t(
        'There is still required information that has not been saved, please fill in all required information first!'
      ),
      maskClosable: false,
    });
  }, []);

  // 展开收起
  const onExpand = useCallback((keys: Key[], info?: { expanded: boolean; node: EventDataNode<DataNode> }) => {
    const deleteChildIdInSelectedKeys = (node?: DataNode) => {
      if (keys?.includes(node?.key)) {
        remove(keys, key => key === node?.key);
      }
      if (node?.children?.length) {
        node?.children?.forEach(childNode => {
          deleteChildIdInSelectedKeys(childNode);
        });
      }
    };
    // expanded === false 代表折叠，需要遍历展开的子节点从队列删除
    if (info?.expanded === false) {
      deleteChildIdInSelectedKeys(info?.node);
    }
    setExpandedKeys(keys as number[]);
    setAutoExpandParent(false);
  }, []);

  // 选择，根据type跳转不同页面
  const onSelect = useCallback(
    (team: ChannelTeamInfo, isCloseIntercept?: boolean, defaultConfig?: ChannelConfigItem) => {
      if (!isCloseIntercept && isNeedIntercept) {
        onIntercept();
        return;
      }
      const configData = find((config || defaultConfig)?.itemList, item => item.orderNo === team.level);
      setSelectedKey(team.id);
      const teamProps = teamsMapRef.current?.[team.id];
      navigate(
        `/${AgentManagementPrefix}/${RouterMap[team.level > 0 ? TeamType.Branch : TeamType.Headquarter]}?${qs.stringify(
          {
            id: String(team.id),
            level: String(team.level),
            configLevel: teamProps.maxLevel,
            originMaxLevel: teamProps?.originMaxLevel,
            configName: configData?.dictValueName || configData?.dictValue,
            isVirtual: team.isVirtual ? YesOrNo.YES : YesOrNo.NO,
          }
        )}`,
        {
          state: {
            teamNames: teamProps?.teamNames,
          },
        }
      );
    },
    [isNeedIntercept, onIntercept, config]
  );

  // 删除
  const onDelete = useCallback(
    (team: ChannelTeamInfo) => {
      setTeamsLoading(true);
      ChannelService.removeTeamInfo(team.id)
        .then(async () => {
          message.success(t('Delete successfully'));
          try {
            await queryTeams();
            // 删除成功后，region/branch跳转至上一层级，retail删除后展示初始无内容状态
            if (team.level === 0) {
              setSelectedKey(null);
              navigate(`/${AgentManagementPrefix}/index`);
            } else {
              const parentTeam = teamsMapRef.current?.[team.parentId]?.teamInfo;
              // 删除时，切换不拦截
              onSelect(parentTeam, true);
            }
          } catch {
            return;
          }
        })
        .catch((error: Error) => {
          message.error(error.message);
          setTeamsLoading(false);
        });
    },
    [queryTeams, onSelect]
  );

  // 新增，根据type跳转不同页面
  const onAdd = useCallback(
    (parentId: number, level: number, maxLevel?: number) => {
      if (level !== 0 && isNeedIntercept) {
        // 新增Retail时，不需要拦截
        onIntercept();
        return;
      }
      const configData = find(config?.itemList, item => item.orderNo === level);
      const teamProps = teamsMapRef.current?.[parentId];
      navigate(
        `/${AgentManagementPrefix}/${RouterMap[level > 0 ? TeamType.Branch : TeamType.Headquarter]}?${qs.stringify({
          parentId: parentId?.toString(),
          level: level?.toString(),
          configLevel: (maxLevel ?? teamProps?.maxLevel)?.toString(),
          originMaxLevel: (maxLevel ?? teamProps?.originMaxLevel)?.toString(),
          configName: configData?.dictValueName || configData?.dictValue,
        })}`,
        {
          state: {
            teamNames: (teamProps?.teamNames ?? []).concat(configData?.dictValueName || configData?.dictValue),
          },
        }
      );
    },
    [isNeedIntercept, onIntercept, config]
  );

  // 编辑/新增成功后更新teams tree
  const updateTeamTree = useCallback(
    async (team: ChannelTeamInfo) => {
      try {
        await queryTeams();
        // 新增成功后，左侧焦点自动选中新增
        if (!id) {
          let clonedExpandedKeys: number[] = [];
          if (routerParentId) {
            clonedExpandedKeys = expandedKeys?.concat([Number(routerParentId), team.id]);
          } else {
            // 新增时没有parentId表示新增Retail，只需展开retail
            clonedExpandedKeys = [team.id];
          }
          setExpandedKeys(clonedExpandedKeys);
          setAutoExpandParent(true);
          onSelect(team, true);
        }
      } catch {
        return;
      }
    },
    [id, routerParentId, expandedKeys, queryTeams, onSelect]
  );

  // 页面初始化，默认展开且选中第一个
  const initPage = useCallback(async () => {
    setLoading(true);
    try {
      const channelConfig = await ChannelService.queryChannelConfig(
        AgentSalesHierarchyConfiguration.AgentSalesHierarchy
      );
      setConfig(channelConfig);
      const channelTeamInfo = (await queryTeams(channelConfig)) as ChannelTeamInfo[];
      const defaultTeam = channelTeamInfo?.[0];
      if (defaultTeam) {
        setAutoExpandParent(true);
        onExpand([defaultTeam?.id]);
        // 初始化时切换不拦截
        onSelect(defaultTeam, true, channelConfig);
      }
      setLoading(false);
    } catch {
      setLoading(false);
    }
  }, [queryTeams, onExpand, onSelect]);

  // 若使用useEffect，会闪一下No Data样式
  useLayoutEffect(() => {
    initPage();
  }, []);

  // 获取dropdown menu；branch只能删除，不能新增子节点
  const getMenuItems = useCallback(
    (team: ChannelTeamInfo) => {
      const configData = find(config?.itemList, item => item.orderNo === team.level + 1);

      return [
        {
          key: 'add',
          label: (
            <Button
              icon={<FileAddOutlined style={{ fontSize: 18 }} />}
              type="link"
              disabled={!canEdit || team.isVirtual}
            >
              {`${t('Add')} ${configData?.dictValueName || configData?.dictValue}`}
            </Button>
          ),
          onClick: ({ domEvent }) => {
            onAdd(team.id, team.level + 1);
            domEvent.stopPropagation();
            domEvent.preventDefault();
          },
          hidden: !(team.level < teamsMapRef.current?.[team.id]?.maxLevel),
        },
        {
          key: 'delete',
          label: (
            <DeleteConfirm
              content={DeletedContentMap[team.level > 0 ? TeamType.Branch : TeamType.Headquarter]}
              onOk={() => onDelete(team)}
            >
              <Button
                icon={<Icon type="delete" style={{ fontSize: 18 }} />}
                type="link"
                disabled={!canEdit || team.isVirtual}
              >
                {t('Delete')}
              </Button>
            </DeleteConfirm>
          ),
        },
      ].filter(({ hidden }) => !hidden);
    },
    [canEdit, onAdd, onDelete, config]
  );

  // 处理tree node title
  const getTreeNodeTitle = useCallback(
    (team: ChannelTeamInfo) => (
      <span
        className={styles.treeNodeTitle}
        onMouseEnter={() => setHoveredId(team.id)}
        onMouseLeave={() => setHoveredId(null)}
        onClick={() => onSelect(team)}
      >
        <FileTextOutlined style={{ marginRight: 10 }} />
        <span className={styles.teamName}>
          <Typography.Paragraph ellipsis={{ tooltip: true, rows: 1 }}>{team.teamName}</Typography.Paragraph>
        </span>
        {hoveredId === team.id && canEdit && !team.isVirtual && (
          <div className={styles.hoverBox}>
            <Dropdown menu={{ items: getMenuItems(team) }} placement="bottomLeft">
              <EllipsisOutlined />
            </Dropdown>
          </div>
        )}
      </span>
    ),
    [hoveredId, canEdit, getMenuItems, onSelect]
  );

  // 处理tree
  // TODO: 后续需要优化
  const treeData = useMemo(() => {
    const loop = (teamList: ChannelTeamInfo[], rootVirtualNode?: DataNode): DataNode[] =>
      teamList?.map(team => {
        const dataNode: DataNode = {
          key: team.id,
          title: getTreeNodeTitle(team),
          children: [],
        };

        if (team.isVirtual) {
          loop(team.children, team.level === VirtualRootLevel ? dataNode : rootVirtualNode);
          if (!team.children.length) {
            rootVirtualNode?.children?.push(dataNode);
          }
        } else {
          dataNode.children = loop(team.children);
        }
        return dataNode;
      });
    return loop(teams);
  }, [teams, getTreeNodeTitle]);

  return (
    <>
      {config && (
        <AgentContext.Provider
          value={{
            updateTeamTree,
          }}
        >
          <Skeleton loading={loading} active>
            <Layout style={{ height: '94vh' }} className={styles.agentManagement}>
              <Layout.Sider theme="light" width="274" className={styles.left}>
                <div className={styles.siderContainer}>
                  <div className={styles.siderContent}>
                    <SimpleSectionHeader className="mb-md" weight="bold" type="h4">
                      {t('Sales Hierarchy Management')}
                    </SimpleSectionHeader>
                    <Input
                      prefix={<Icon type="search" />}
                      placeholder={t('Search')}
                      size="large"
                      allowClear
                      style={{ marginBottom: 21, marginRight: 10 }}
                      onChange={event => onSearch(event.target.value)}
                    />
                    <Spin spinning={teamsLoading}>
                      {treeData?.length ? (
                        <>
                          <Tree
                            expandedKeys={expandedKeys}
                            selectedKeys={[selectedKey]}
                            autoExpandParent={autoExpandParent}
                            onExpand={onExpand}
                            treeData={treeData}
                          />
                        </>
                      ) : (
                        // 搜索导致无结果
                        <div className={styles.treeNoData}>
                          <NoData emptyText={isSearchNoData ? t('No search results found') : undefined} />
                        </div>
                      )}
                    </Spin>
                  </div>
                  <div className={styles.siderAddNew}>
                    <AddNewSalesHierarchy
                      disabled={teamsLoading}
                      onAdd={maxLevel => onAdd(null, 0, maxLevel, maxLevel)}
                    />
                  </div>
                </div>
              </Layout.Sider>
              <Layout.Content>
                <Outlet />
              </Layout.Content>
            </Layout>
          </Skeleton>
        </AgentContext.Provider>
      )}
    </>
  );
};

export default AgentManagement;
