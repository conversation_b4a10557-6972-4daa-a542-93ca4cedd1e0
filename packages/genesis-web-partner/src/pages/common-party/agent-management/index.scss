@import '../../../variables';

.agent-management {
  flex-direction: row;
  .left {
    .sider-container {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .sider-content {
      flex: 1;
      min-width: 0;
      padding: $gap-md $gap-sm;
      overflow: auto;

      .title {
        margin-bottom: 18px;
      }
      :global {
        .#{$antd-prefix}-input-prefix {
          color: var(--text-color-quaternary);
        }
        .#{$antd-prefix}-tree {
          .#{$antd-prefix}-tree-treenode {
            width: 100%;
          }
          .#{$antd-prefix}-tree-node-content-wrapper {
            flex: 1;
            min-height: 36px;
            line-height: 36px;
            overflow: hidden;
          }
          .#{$antd-prefix}-tree-switcher {
            line-height: 36px;
          }
          .#{$antd-prefix}-typography {
            margin-bottom: 0;
          }
        }
      }
      .tree-node-title {
        display: flex;
        align-items: center;
        .team-name {
          flex: 1;
          overflow: hidden;
        }
        .hover-box {
          width: 24px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          border-radius: 2px;
          font-size: 18px;
          &:hover {
            background-color: $hover-bg;
          }
        }
      }
      .tree-no-data {
        text-align: center;
        margin-top: 42px;
        :global {
          .#{$antd-prefix}-btn {
            margin-top: 20px;
          }
        }
      }
    }

    .sider-add-new {
      flex: 0 0 auto;
      padding: $gap-lg;
    }
  }
  .loading {
    width: 100%;
    min-height: 500px;
    text-align: center;
    line-height: 500px;
  }
  :global {
    .#{$antd-prefix}-layout-content {
      margin-top: $gap-md;
      margin-left: $gap-md;
      overflow: auto;
    }
  }
  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--white);
    height: 100% !important;
  }
}
