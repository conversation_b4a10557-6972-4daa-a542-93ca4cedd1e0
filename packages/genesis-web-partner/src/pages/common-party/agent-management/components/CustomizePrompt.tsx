import { useTranslation } from 'react-i18next';

import { Modal } from 'antd';
import type { ModalFuncProps } from 'antd/lib/modal';
import type * as History from 'history';

import { Prompt } from '@umijs/max';

interface Props extends ModalFuncProps {
  isOpenPrompt?: boolean;
  onOk?: (location?: History.Location) => void | Promise<unknown>;
}

/**
 *
 * @param isOpenPrompt 是否开启Prompt
 * @param onOk Modal onOk
 * @description 自定义Prompt，用于页面离开时进行拦截
 */
export const CustomizePrompt = ({ isOpenPrompt, onOk, ...resetProps }: Props) => {
  const { t } = useTranslation('partner');

  return (
    <Prompt
      when={isOpenPrompt}
      message={location => {
        if (!isOpenPrompt) {
          return true;
        }
        Modal.warning({
          title: t('warning'),
          content: t(
            'There is still required information that has not been saved, please fill in all required information first!'
          ),
          maskClosable: false,
          okText: t('OK'),
          onOk: () => {
            if (typeof onOk === 'function') {
              const result = onOk(location);
              /**
               * 如果onOk返回的是Promise，则在Promise resolve时候关闭
               * 如果onOk返回的不是Primose，则函数执行完就关闭
               */
              if (result instanceof Promise) {
                return result;
              }
            }
            return Promise.resolve();
          },
          ...resetProps,
        });
        return false;
      }}
    />
  );
};
