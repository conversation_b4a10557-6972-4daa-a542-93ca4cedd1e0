import type { FC } from 'react';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { PlusOutlined } from '@ant-design/icons';

import { useRequest } from 'ahooks';
import type { SelectProps } from 'antd';
import { Button, Form, message } from 'antd';

import { ModalConfirm, Select } from '@zhongan/nagrand-ui';

import { AgentSalesHierarchyConfiguration, ChannelService } from 'genesis-web-service';

export const AddNewSalesHierarchy: FC<{
  disabled?: boolean;
  onAdd: (maxLevel: number) => void;
}> = ({ disabled, onAdd }) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();

  const [levelOptions, setLevelOptions] = useState<SelectProps['options']>();

  useRequest(
    () =>
      Promise.all([
        ChannelService.queryChannelConfigDefault(AgentSalesHierarchyConfiguration.AgentSalesHierarchy),
        ChannelService.queryChannelConfig(AgentSalesHierarchyConfiguration.AgentSalesHierarchy),
      ]),
    {
      onSuccess: ([configDefault, config]) => {
        const maxLevel = +config.dictValue;
        setLevelOptions(
          configDefault.itemList
            .filter(({ dictValue }) => +dictValue <= maxLevel)
            .map(({ dictValue, dictValueName }) => ({
              label: dictValueName,
              value: dictValue,
            }))
        );
      },
      onError: error => message.error(error.message),
    }
  );

  const onConfirm = useCallback(
    () =>
      new Promise((resolve, reject) => {
        form
          .validateFields()
          .then((params: { maxLevel: number }) => {
            form.resetFields();
            onAdd(params.maxLevel);
            resolve(true);
          })
          .catch(reject);
      }),
    [form, onAdd]
  );

  const confirmContent = useMemo(
    () => (
      <Form form={form} layout="vertical">
        <Form.Item
          name="maxLevel"
          label={t('Please select the number of sales hierarchy')}
          rules={[
            {
              required: true,
              message: t('Please select the number of sales hierarchy'),
            },
          ]}
        >
          <Select
            style={{ width: 240 }}
            allowClear
            showSearch
            optionFilterProp="label"
            options={levelOptions}
            placeholder={t('Please select')}
          />
        </Form.Item>
      </Form>
    ),
    [form, levelOptions, t]
  );

  return (
    <ModalConfirm
      title={t('Add New')}
      content={confirmContent}
      onOk={onConfirm}
      width={560}
      onCancel={() => form.resetFields()}
    >
      <Button block size="large" icon={<PlusOutlined />} disabled={disabled}>
        {t('Add New')}
      </Button>
    </ModalConfirm>
  );
};
