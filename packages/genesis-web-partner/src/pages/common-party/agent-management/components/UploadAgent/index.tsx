import { useState, useCallback } from 'react';
import { Button, Upload, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { RcFile } from 'antd/lib/upload/interface';
import { getUploadPropsNew } from '@/pages/common-party/utils/util';

interface UploadAgentProps {
  disabled?: boolean;
  teamId: number;
  type: string;
  handleUploadSuccess: () => void;
}

export const UploadAgent = ({
  disabled,
  teamId,
  type,
  handleUploadSuccess,
}: UploadAgentProps) => {
  const { t } = useTranslation();
  const [uploading, setUploading] = useState(false);
  const uploadProps = getUploadPropsNew(
    `/api/channel/v2/file/upload/?id=${teamId}&type=${type}`,
    handleUploadSuccess,
    setUploading,
  );

  const handleBeforeUpload = useCallback(
    (file: RcFile) => {
      const fileNameLower = file.name.toLowerCase();
      if (!fileNameLower.endsWith('.xls') && !fileNameLower.endsWith('.xlsx')) {
        message.error(t('Only xls and xlsx files can be uploaded.'));
        setUploading(false);
        return false;
      }
      return true;
    },
    [t],
  );

  return (
    <Upload
      {...uploadProps}
      accept=".xls, .xlsx"
      beforeUpload={handleBeforeUpload}
    >
      <Button icon={<UploadOutlined />} disabled={disabled} loading={uploading}>
        {t('Upload')}
      </Button>
    </Upload>
  );
};
