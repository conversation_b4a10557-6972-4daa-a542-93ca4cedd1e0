import { Breadcrumb } from 'antd';

import styles from './style.scss';

export const AgentBreadcrumb: React.FC<{ teamNames: string[] }> = ({ teamNames }) => {
  return (
    <div className={styles.breadCrumb}>
      <Breadcrumb separator=">">
        {teamNames?.map((nav: string) => {
          return <Breadcrumb.Item key={`${nav}`}>{nav}</Breadcrumb.Item>;
        })}
      </Breadcrumb>
    </div>
  );
};
