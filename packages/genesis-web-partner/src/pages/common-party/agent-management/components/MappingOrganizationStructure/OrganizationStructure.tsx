import type { FC } from 'react';
import type { TreeDataNode } from 'antd';
import { Input, Skeleton, Tree, Button } from 'antd';
import { Icon } from '@zhongan/nagrand-ui';
import { useDebounceFn, useRequest } from 'ahooks';
import type { ChannelTeamInfo } from 'genesis-web-service';
import { ChannelService, TeamBusinessType } from 'genesis-web-service';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { NoData } from 'genesis-web-component/lib/components/NoData';

import styles from './index.scss';

interface OrganizationStructureProps {
  structureId?: number;
  onSelect: (id: number) => void;
}

export const OrganizationStructure: FC<OrganizationStructureProps> = ({
  structureId,
  onSelect,
}) => {
  const { t } = useTranslation('partner');
  const [searchValue, setSearchValue] = useState<string>();
  const { data: teams, loading } = useRequest(
    (): Promise<ChannelTeamInfo[]> =>
      (searchValue
        ? ChannelService.searchTeamTree(
          TeamBusinessType.Employee.toLowerCase(),
          searchValue,
        )
        : ChannelService.queryTeamTree(TeamBusinessType.Employee.toLowerCase())),
    { refreshDeps: [searchValue] },
  );

  const [treeData, defaultExpandKey] = useMemo(() => {
    let expandKey: number;
    const loop = (teamList: ChannelTeamInfo[]): TreeDataNode[] => {
      return teamList?.map(({ id, parentId, hadBind, teamName, children }) => {
        if (id === structureId) {
          expandKey = parentId;
        }
        return {
          key: id,
          title: (
            <Button
              type="text"
              size="small"
              disabled={structureId !== id && hadBind}
              style={{
                padding: '1px 0',
              }}
              onClick={() => onSelect(id)}
            >
              {teamName}
            </Button>
          ),
          children: loop(children),
        };
      });
    };
    return [loop(teams), expandKey];
  }, [teams, structureId, onSelect]);

  const { run: onSearch } = useDebounceFn(
    async (value: string) => {
      setSearchValue(value);
    },
    { wait: 200 },
  );

  return (
    <>
      <Input
        className={styles.searchInput}
        suffix={<Icon type="search" className={styles.searchIcon} />}
        placeholder={t('Search')}
        size="large"
        allowClear
        onChange={event => onSearch(event.target.value)}
      />
      <Skeleton loading={loading}>
        {treeData?.length ? (
          <Tree
            defaultExpandAll={!!searchValue}
            defaultExpandedKeys={[treeData?.[0]?.key, defaultExpandKey]}
            defaultSelectedKeys={[structureId]}
            treeData={treeData}
          />
        ) : (
          <div className={styles.noData}>
            <NoData
              emptyText={searchValue ? t('No search results found') : undefined}
            />
          </div>
        )}
      </Skeleton>
    </>
  );
};
