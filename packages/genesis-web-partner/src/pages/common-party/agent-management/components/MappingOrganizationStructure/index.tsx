import type { FC } from 'react';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Drawer } from 'antd';
import cls from 'classnames';

import { Icon } from '@zhongan/nagrand-ui';

import { OrganizationStructure } from '@/pages/common-party/agent-management/components/MappingOrganizationStructure/OrganizationStructure';

import styles from './index.scss';

export const MappingOrganizationStructure: FC<{
  className?: string;
  hiddenConfirm?: boolean;
  loading?: boolean;
  structureId?: number;
  onSubmit: (orgStructureId: number) => void;
}> = ({ className, structureId, hiddenConfirm, loading, onSubmit }) => {
  const { t } = useTranslation('partner');
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedId, setSelectedId] = useState<number>();

  const onConfirm = useCallback(() => {
    onSubmit?.(selectedId);
    setDrawerOpen(false);
  }, [onSubmit, selectedId]);

  const afterOpenChange = useCallback(
    open => {
      if (!open) {
        setSelectedId(structureId);
      }
    },
    [structureId]
  );

  return (
    <>
      <div className={cls('flex-between', className)}>
        {!structureId ? (
          <Icon className={styles.infoIcon} type="info-fill" />
        ) : (
          <Icon className={styles.infoIcon} type="done" />
        )}
        <Button loading={loading} icon={<Icon type="tree-list" />} onClick={() => setDrawerOpen(true)}>
          {t('Mapping with Organization')}
        </Button>
      </div>
      <Drawer
        open={drawerOpen}
        width={408}
        title={t('Mapping with Organization')}
        destroyOnClose
        maskClosable={false}
        onClose={() => setDrawerOpen(false)}
        afterOpenChange={afterOpenChange}
        footer={[
          <Button key="cancel" size="large" onClick={() => setDrawerOpen(false)}>
            {t('Cancel')}
          </Button>,
          !hiddenConfirm && (
            <Button key="confirm" type="primary" size="large" onClick={onConfirm} disabled={!selectedId}>
              {t('Confirm')}
            </Button>
          ),
        ]}
      >
        <OrganizationStructure structureId={structureId} onSelect={setSelectedId} />
      </Drawer>
    </>
  );
};
