import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button } from 'antd';
import type { PaginationConfig } from 'antd/es/pagination';
import type { ColumnsType, TableProps } from 'antd/es/table';
import type { SortOrder } from 'antd/es/table/interface';

import { useRequest } from 'ahooks';

import { Drawer, Icon, StatusTag, Table, TextEllipsisDetect } from '@zhongan/nagrand-ui';
import type { StatusType } from '@zhongan/nagrand-ui/dist/components/StatusTag';

import type { SalesTransferRecordResponse } from 'genesis-web-service/service-types/channel-types/package';
import { Direction } from 'genesis-web-service/service-types/channel-types/package';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { useHistoryStatus } from '@/pages/common-party/policy-transfer/history/index';
import { NewChannelService } from '@/services/channel/channel.service.new';

export const AgentTransferHistory: React.FC = () => {
  const { t } = useTranslation('partner');
  const { dateFormat } = useL10n();
  const [agentTransferHistoryList, setAgentTransferHistoryList] = useState<SalesTransferRecordResponse[]>([]);
  const [total, setTotal] = useState(0);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [pagination, setPagination] = useState<
    PaginationConfig & {
      sortOrder: Direction;
    }
  >({
    current: 1,
    pageSize: 10,
    sortOrder: Direction.DESC,
  });
  const historyStatus = useHistoryStatus();

  const { loading } = useRequest(
    async () => {
      if (!drawerVisible) {
        return;
      }
      return await NewChannelService.SalesHierarchiesService.queryTransferRecords({
        pageIndex: pagination.current - 1,
        pageSize: pagination.pageSize,
        operationTimeSort: pagination.sortOrder,
      });
    },
    {
      onSuccess: res => {
        setAgentTransferHistoryList(res?.data ?? []);
        setTotal(res?.totalElements ?? 0);
      },
      refreshDeps: [pagination, drawerVisible],
    }
  );

  const onTableChange: TableProps<SalesTransferRecordResponse>['onChange'] = (pages, filters, sorter, extra) => {
    const sort = pagination.sortOrder === Direction.DESC ? Direction.ASC : Direction.DESC;
    if (extra?.action === 'sort') {
      setPagination(old => ({
        ...old,
        sortOrder: sort,
        current: 1,
      }));
    }
  };

  const columns: ColumnsType<SalesTransferRecordResponse> = useMemo(
    () => [
      {
        title: t('Operation Time'),
        dataIndex: 'operationTime',
        render: (text: string) => {
          return dateFormat?.getDateTimeString(text);
        },
        sortOrder: pagination.sortOrder as unknown as SortOrder,
        sorter: true,
        width: 180,
      },
      {
        title: t('Sales Channel'),
        dataIndex: 'salesName',
        width: 200,
      },
      {
        title: t('Sales Agreement'),
        dataIndex: 'salesAgreementName',
        width: 200,
      },
      {
        title: t('Sales Agreement Signing Party (Before Change)'),
        dataIndex: 'beforeTeamName',
        width: 250,
      },
      {
        title: t('Sales Agreement Signing Party (After Change)'),
        dataIndex: 'afterTeamName',
        width: 250,
      },
      {
        title: t('Operator'),
        dataIndex: 'operator',
        width: 120,
      },
      {
        title: t('Error Message'),
        dataIndex: 'errorMessage',
        render: (text: string) => <TextEllipsisDetect text={text} width={200} />,
        width: 200,
      },
      {
        title: t('Status'),
        dataIndex: 'status',
        align: 'center',
        fixed: 'right',
        width: 120,
        render: (status: string) => {
          if (!status) {
            return;
          }
          const { name, type } = (status && historyStatus[status?.split(' ')?.pop()?.toUpperCase()]) ?? {};
          return <StatusTag type={type as StatusType} statusI18n={name} />;
        },
      },
    ],
    [dateFormat, t, pagination.sortOrder]
  );

  return (
    <React.Fragment>
      <Button icon={<Icon type="time" />} onClick={() => setDrawerVisible(true)}>
        {t('History')}
      </Button>
      <Drawer
        open={drawerVisible}
        onClose={() => {
          setDrawerVisible(false);
        }}
        width={1200}
        title={t('Agent Transfer History')}
        destroyOnClose
        maskClosable
      >
        <Table
          scroll={{ x: 'max-content' }}
          columns={columns}
          loading={loading}
          dataSource={agentTransferHistoryList}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: total,
            onChange: (current: number, pageSize: number) => {
              setPagination(old => ({
                ...old,
                current,
                pageSize,
              }));
            },
          }}
          onChange={onTableChange}
          rowKey="id"
        />
      </Drawer>
    </React.Fragment>
  );
};
