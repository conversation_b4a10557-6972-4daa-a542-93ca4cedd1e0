import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, message } from 'antd';
import type { PaginationConfig } from 'antd/es/pagination';
import type { ColumnsType, TableProps } from 'antd/es/table';

import type moment from 'moment';

import type { ConditionFieldsConfig } from '@zhongan/nagrand-ui';
import { ConditionFilter, Drawer, Icon, StatusTag, Table } from '@zhongan/nagrand-ui';

import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import { ChannelService, DownloadOrUploadType, ReflectSortOrder, SortOrder } from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { FieldType } from '@/components/CommonForm';
import { useHistoryStatus } from '@/pages/common-party/policy-transfer/history/index';
import { handleDate } from '@/pages/common-party/utils/util';

import styles from './index.scss';

// 临时类型定义，实际应该从 genesis-web-service 导入
interface AgentTransferHistoryDataType {
  id: number;
  modifiedTime: string;
  salesChannelName: string;
  salesChannelCode: string;
  salesAgreementName: string;
  beforeTeamName: string;
  beforeTeamCode: string;
  afterTeamName: string;
  afterTeamCode: string;
  modifiedBy: string;
  errorMsg?: string;
  status: string;
}

interface FilterParamsProps {
  modifiedTime?: [moment.Moment, moment.Moment];
  salesChannelCode?: string;
  beforeTeamCode?: string;
  afterTeamCode?: string;
}

export const AgentTransferHistory: React.FC = () => {
  const { t } = useTranslation('partner');
  const { dateFormat } = useL10n();
  const [loading, setLoading] = useState(false);
  const [agentTransferHistoryList, setAgentTransferHistoryList] = useState<AgentTransferHistoryDataType[]>([]);
  const [total, setTotal] = useState(0);
  const [sortOrder, setSortOrder] = useState<SortOrder>(SortOrder.DESC);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [filterParams, setFilterParams] = useState<FilterParamsProps>({});
  const [pagination, setPagination] = useState<PaginationConfig>({
    current: 1,
    pageSize: 10,
  });
  const historyStatus = useHistoryStatus();

  const onChangePagination = useCallback((current: number, pageSize: number) => {
    setPagination(old => ({
      ...old,
      current,
      pageSize,
    }));
  }, []);

  const queryAgentTransferHistoryList = useCallback(async () => {
    setLoading(true);

    const uploadDateObj = handleDate('startDate', 'endDate', filterParams?.modifiedTime, 'YYYY-MM-DD HH:mm:ss');

    const params = {
      sort: ReflectSortOrder[sortOrder as keyof typeof ReflectSortOrder],
      pageIndex: pagination.current - 1,
      pageSize: pagination.pageSize,
      ...uploadDateObj,
      salesChannelCode: filterParams.salesChannelCode,
      beforeTeamCode: filterParams.beforeTeamCode,
      afterTeamCode: filterParams.afterTeamCode,
    };

    try {
      // 临时实现：如果 queryAgentTransferHistoryList 不存在，使用模拟数据
      let res;
      if (typeof ChannelService.queryAgentTransferHistoryList === 'function') {
        res = await ChannelService.queryAgentTransferHistoryList(params);
      } else {
        // 模拟数据，实际开发中应该删除
        res = {
          data: [],
          totalElements: 0,
        };
        console.warn('ChannelService.queryAgentTransferHistoryList not implemented yet');
      }
      setAgentTransferHistoryList(res?.data ?? []);
      setTotal(res?.totalElements ?? 0);
    } catch (error: any) {
      message.error(error?.message || t('Failed to load history'));
    } finally {
      setLoading(false);
    }
  }, [filterParams, pagination, sortOrder, t]);

  useEffect(() => {
    if (drawerVisible) {
      queryAgentTransferHistoryList();
    }
  }, [queryAgentTransferHistoryList, drawerVisible]);

  const onTableChange: TableProps<AgentTransferHistoryDataType>['onChange'] = useCallback(
    (pages, filters, sorter, extra) => {
      const sort = sortOrder === SortOrder.DESC ? SortOrder.ASC : sortOrder === undefined ? SortOrder.DESC : undefined;
      if (extra?.action === 'sort') {
        setSortOrder(sort);
        setPagination(old => ({
          ...old,
          current: 1,
        }));
      }
    },
    [sortOrder]
  );

  const columns: ColumnsType<AgentTransferHistoryDataType> = useMemo(
    () => [
      {
        title: t('Operation Time'),
        dataIndex: 'modifiedTime',
        render: (text: string) => {
          return dateFormat?.getDateTimeString(text);
        },
        sortOrder: sortOrder,
        sorter: true,
        width: 180,
      },
      {
        title: t('Sales Channel'),
        dataIndex: 'salesChannelName',
        render: (text: string) => <ComponentWithFallback>{text}</ComponentWithFallback>,
        width: 200,
      },
      {
        title: t('Sales Agreement'),
        dataIndex: 'salesAgreementName',
        render: (text: string) => <ComponentWithFallback>{text}</ComponentWithFallback>,
        width: 200,
      },
      {
        title: t('Sales Agreement Signing Party (Before Change)'),
        dataIndex: 'beforeTeamName',
        render: (text: string) => <ComponentWithFallback>{text}</ComponentWithFallback>,
        width: 250,
      },
      {
        title: t('Sales Agreement Signing Party (After Change)'),
        dataIndex: 'afterTeamName',
        render: (text: string) => <ComponentWithFallback>{text}</ComponentWithFallback>,
        width: 250,
      },
      {
        title: t('Operator'),
        dataIndex: 'modifiedBy',
        render: (text: string) => <ComponentWithFallback>{text}</ComponentWithFallback>,
        width: 120,
      },
      {
        title: t('Error Message'),
        dataIndex: 'errorMsg',
        render: (text: string) => <ComponentWithFallback>{text}</ComponentWithFallback>,
        width: 200,
      },
      {
        title: t('Status'),
        dataIndex: 'status',
        align: 'center',
        fixed: 'right',
        width: 120,
        render: (status: string) => {
          const statusConfig = historyStatus[status];
          return <StatusTag type={statusConfig?.type}>{statusConfig?.name || status}</StatusTag>;
        },
      },
    ],
    [dateFormat, t, sortOrder]
  );

  const panelConditions: ConditionFieldsConfig[] = useMemo(
    () => [
      {
        label: t('Operation Time'),
        fieldKey: 'modifiedTime',
        type: FieldType.RangePicker,
      },
      {
        label: t('Sales Channel Code'),
        fieldKey: 'salesChannelCode',
        type: FieldType.Input,
      },
      {
        label: t('Before Team Code'),
        fieldKey: 'beforeTeamCode',
        type: FieldType.Input,
      },
      {
        label: t('After Team Code'),
        fieldKey: 'afterTeamCode',
        type: FieldType.Input,
      },
    ],
    [t]
  );

  const rowSelection = {
    onChange: (newSelectedRowKeys: number[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    selectedRowKeys,
    preserveSelectedRowKeys: true,
  };

  const handleDownload = () => {
    ChannelService.batchDownload({
      type: DownloadOrUploadType.AGENT_TRANSFER_HISTORY,
      ids: selectedRowKeys,
    })
      .then(downloadFile)
      .catch((error: Error) => message.error(error?.message || t('Download failed')));
  };

  const handleChangeFilter = useCallback((value: FilterParamsProps) => {
    setFilterParams(value);
    setPagination(old => ({
      ...old,
      current: 1,
    }));
  }, []);

  return (
    <React.Fragment>
      <Button icon={<Icon ty/>}>
        {t('History')}
      </Button>
      <div onClick={() => setDrawerVisible(true)}>
        <span className={styles.historyText}>{t('History')}</span>
      </div>
      <Drawer
        open={drawerVisible}
        onClose={() => {
          setDrawerVisible(false);
          setSelectedRowKeys([]);
          setFilterParams({});
        }}
        width={1200}
        title={t('Agent Transfer History')}
        destroyOnClose
        maskClosable
      >
        <div className={styles.historyDrawerOperation}>
          <ConditionFilter
            mode="simple"
            panelConditions={panelConditions}
            appliedCondition={filterParams}
            onChange={handleChangeFilter}
            optionalProps={{
              dateTimeRender: (date: moment.Moment) => dateFormat.getDateString(date)!,
            }}
            className="margin-bottom-8"
          />

          <Button type="primary" ghost onClick={handleDownload} disabled={selectedRowKeys.length === 0}>
            {t('export')}
          </Button>
        </div>

        <Table
          loading={loading}
          scroll={{ x: 'max-content' }}
          columns={columns}
          dataSource={agentTransferHistoryList}
          pagination={pagination}
          style={{ marginBottom: 16 }}
          onChange={onTableChange}
          rowKey="id"
          preserveSelectedRowKeys
          rowSelection={rowSelection}
        />
        {/* <PaginationComponent
          size="small"
          className="margin-left-16 margin-right-16"
          total={total || 0}
          pagination={pagination}
          handlePaginationChange={onChangePagination}
          pageSizeOptions={pagination?.pageSizeOptions as string[]}
          showQuickJumper={false}
        /> */}
      </Drawer>
    </React.Fragment>
  );
};
