@import '@/variables.scss';

.manager-container {
  margin-top: 16px;
  padding: 24px 32px;
  background-color: var(--white);

  .page-title {
    font-size: 16px;
    line-height: 24px;
    font-weight: 700;
    margin-bottom: 11px;
  }

  .add-rule-btn {
    margin-bottom: 16px;
  }

  .actions-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;

    > div > button {
      margin-left: 22px;
    }
  }

  :global {
    .#{$antd-prefix}-btn {
      border-radius: var(--border-radius-base);
    }
  }
}
