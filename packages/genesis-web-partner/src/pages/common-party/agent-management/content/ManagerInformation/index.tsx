import { useMemo, useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, message, Tooltip } from 'antd';
import type { ColumnProps } from 'antd/es/table';
import { PlusOutlined, DownloadOutlined } from '@ant-design/icons';
import {
  EditOutline,
  DeleteOutline,
  ViewSquareOutline,
} from '@/components/Icons';
import { PaginationComponent } from '@/components/Pagination';
import { ModeEnum } from '@/types/common';
import type { LocationQueryParam } from '@/types/common-party';
import {
  ChannelService,
  AgentType,
  EmployeeAndRelativeEnum,
  YesOrNo,
} from 'genesis-web-service';
import {
  ChannelAddressFieldCodeMap,
  OriginType,
} from '@/pages/common-party/utils/constants';
import type { ChannelTeamInfo, AgentManagementInfo } from 'genesis-web-service';
import type { AddressDisplayConfig } from 'genesis-web-component/lib/components/Address';
import { useAddressConfig } from 'genesis-web-component/lib/components/Address';
import { DeleteConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import { UploadAgent } from '@/pages/common-party/agent-management/components/UploadAgent';
import { useEditPermission } from '@/pages/common-party/agent-management/hooks/useAgentManagement';
import { useColumns } from '@/pages/common-party/agent-management/hooks/useColumns';
import { AgentFormColumnType } from '@/types/common-party';
import { DefaultTablePagination } from '@/utils/constants';
import { AddressFormConfig } from '@/pages/common-party/utils/constants';
import type { ConnectState } from '@/models/connect';
import { transferSchemaToTableProp } from '@/utils/utils';

import { useRequest } from 'ahooks';
import { TiedAgentDrawer } from '../../../partner-setting/components/TiedAgent/TiedAgentDrawer';
import styles from './index.scss';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';
import { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { useSearchParams } from 'react-router-dom';
import qs from 'qs';
import { useSelector } from '@umijs/max';
import { Table, TableActionsContainer } from '@zhongan/nagrand-ui';
import { useAddressI18nList } from '@/hooks/useAddressI18nList';

const AddressKeys = Object.keys(ChannelAddressFieldCodeMap);

/**
 *
 * @param agentType
 * @description manager/agent信息；manager信息和branch agent信息 展示字段 & 接口一样，故用同一套代码；但是由于有些title展示区别，和接口传参需要agentType来判断是manager还是agent，故需要传递参数agentType
 */
export const ManagerInformation: React.FC<{ agentType: AgentType }> = ({
  agentType,
}) => {
  const { t } = useTranslation('partner');

  const [searchParams] = useSearchParams();
  const { configLevel, level, id, configName, isVirtual } = qs.parse(
    searchParams.toString(),
  ) as unknown as LocationQueryParam;
  const { canEdit, canView } = useEditPermission();

  const [drawerMode, setDrawerMode] = useState(ModeEnum.EDIT);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [editedManger, setEditedManager] = useState<AgentManagementInfo>();
  const [pagination, setPagination] = useState(DefaultTablePagination);
  const [teamId, setTeamId] = useState<number>();

  const { formItems } = useAddressConfig();

  const { teamInfo }: { teamInfo: ChannelTeamInfo } = useSelector(
    ({ partnerManagement }: ConnectState) => ({
      teamInfo: partnerManagement.teamInfo,
    }),
  );

  useEffect(() => {
    // id变化时pagination回到初始状态
    setTeamId((id && Number(id)) ?? teamInfo?.id);
    setPagination(DefaultTablePagination);
  }, [id, teamInfo?.id]);

  // 查询
  const {
    data: agentList,
    loading: teamInfoLoading,
    run: queryAgentList,
  } = useRequest(
    () => {
      if (teamId)
        if (agentType === AgentType.Manager) {
          return ChannelService.getBranchManagerList({
            teamId: teamId,
            pageIndex: pagination.current - 1,
            pageSize: pagination.pageSize,
            agentType,
          });
        } else {
          return ChannelService.querySalesChannelList({
            teamId: teamId,
            pageIndex: pagination.current - 1,
            pageSize: pagination.pageSize,
          });
        }
    },
    {
      refreshDeps: [teamId, pagination],
    },
  );

  // address1-8 => address11-18 用于国际化
  const agentListWithAddress = useMemo(
    () =>
      agentList?.data?.map(addressItem => {
        const newAddressValue: Record<string, string> = {};
        AddressKeys?.forEach(addressKey => {
          const addressValue =
            addressItem?.[ChannelAddressFieldCodeMap[addressKey]];
          if (addressValue) {
            newAddressValue[addressKey] = addressValue;
          }
        });

        return { ...addressItem, ...newAddressValue };
      }),
    [agentList?.data],
  );

  const addressI18nList = useAddressI18nList(agentListWithAddress);

  // branch才展示address & zipCode
  const addressAndZipCodeTableColumns = useMemo(() => {
    if (!formItems?.length) {
      return [];
    }
    return transferSchemaToTableProp(formItems, addressI18nList);
  }, [formItems, agentList?.data, addressI18nList]);

  // 关闭抽屉
  const onCloseDrawer = useCallback(() => {
    setDrawerMode(ModeEnum.EDIT);
    setEditedManager(undefined);
    setDrawerVisible(false);
    if (!editedManger) {
      //新增成功后，回到初始页码条件
      setPagination({ ...DefaultTablePagination });
    } else {
      // 编辑成功，以当前条件请求
      queryAgentList();
    }
  }, [queryAgentList]);

  // 删除
  const removeAgent = useCallback(
    (val: number) => {
      return ChannelService.removeBranchManager(teamId, val)
        .then(() => {
          setPagination({ ...DefaultTablePagination });
          message.success(t('Delete successfully'));
        })
        .catch((error: Error) => message.error(error.message));
    },
    [teamId, agentType],
  );

  // 下载
  const { loading: downloadLoading, runAsync: downloadAgentList } = useRequest(
    () =>
      ChannelService.download<{
        id: number;
        type: EmployeeAndRelativeEnum;
      }>({
        id: teamId,
        type: EmployeeAndRelativeEnum.Manager,
      }),
    {
      manual: true,
      onSuccess: res => downloadFile(res),
      onError: (error: Error) =>
        message.error(error.message || t('Download failed')),
    },
  );

  const onChangePagination = useCallback(
    (current: number, pageSize: number) => {
      setPagination(old => ({
        ...old,
        current,
        pageSize,
      }));
    },
    [],
  );

  // 查看/编辑
  const onViewOrEditDetail = useCallback(
    (manager: AgentManagementInfo, mode: ModeEnum) => {
      setDrawerMode(mode);
      setDrawerVisible(true);
      setEditedManager(manager);
    },
    [],
  );

  const { title, addBtnText, addBtnDisabled, actionBtnDisabled } =
    useMemo(() => {
      let tempTitle = '';
      let tempAddBtnText = '';
      const tempAddBtnDisabled = !canEdit || !teamInfo?.id;
      const tempActionBtnDisabled = !canEdit;
      if (agentType === AgentType.Manager) {
        tempTitle = t('Team Manager Information', {
          team: configName,
        });
        tempAddBtnText = t('Add New Team Manager', {
          team: configName,
        });
      } else {
        tempTitle = t('Sales Channel Information');
      }
      return {
        title: tempTitle,
        addBtnText: tempAddBtnText,
        addBtnDisabled: tempAddBtnDisabled,
        actionBtnDisabled: tempActionBtnDisabled,
      };
    }, [agentType, configName, canEdit, teamInfo]);

  const toolTipText = useMemo(() => {
    if (!teamInfo) {
      return t('Please complete Team Information first', {
        team: configName?.toLowerCase(),
      });
    }
    // agent 只要有了组织信息就不需要展示任何tooltip
    //  manager isVirtual时不显示tooltip，没有在职manager时需要展示tooltip
    if (agentType === AgentType.Agent || teamInfo.isVirtual) {
      return null;
    }
  }, [teamInfo, configName, agentType, t]);

  const columnsData = {
    formType: AgentFormColumnType.ManagerInfo,
    configName,
  };
  if (agentType === AgentType.Agent) {
    columnsData.formType = AgentFormColumnType.AgentInfo;
  }

  const columns = useColumns(columnsData);
  const combinedColumns = useMemo((): ColumnProps<AgentManagementInfo>[] => {
    let columnsNew = [...columns];
    if (agentType === AgentType.Manager) {
      columnsNew = [...columns, ...addressAndZipCodeTableColumns];
      columnsNew.push({
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (_, record) => (
          <div className="table-actions">
            <TableActionsContainer>
              <DeleteConfirm
                onOk={() => removeAgent(record.id)}
                disabled={actionBtnDisabled}
              >
                <Tooltip title={t('Delete')}>
                  <Button
                    disabled={actionBtnDisabled}
                    type="link"
                    icon={<DeleteOutline />}
                  />
                </Tooltip>
              </DeleteConfirm>
              <Tooltip title={t('Edit')}>
                <Button
                  disabled={actionBtnDisabled}
                  type="link"
                  icon={<EditOutline />}
                  onClick={() => onViewOrEditDetail(record, ModeEnum.EDIT)}
                />
              </Tooltip>
              <Tooltip title={t('View')}>
                <Button
                  disabled={!canView}
                  type="link"
                  icon={<ViewSquareOutline />}
                  onClick={() => onViewOrEditDetail(record, ModeEnum.READ)}
                />
              </Tooltip>
            </TableActionsContainer>
          </div>
        ),
      });
    }

    return columnsNew;
  }, [
    columns,
    addressAndZipCodeTableColumns,
    actionBtnDisabled,
    canView,
    removeAgent,
    onViewOrEditDetail,
    agentType,
  ]);

  const onAdd = () => {
    setDrawerMode(ModeEnum.ADD);
    setDrawerVisible(true);
  };

  return (
    <div className={styles.managerContainer}>
      <p className={styles.pageTitle}>{title}</p>
      <div className={styles.actionsContainer}>
        {agentType !== AgentType.Agent && (
          <>
            <Tooltip title={toolTipText}>
              <Button
                className={styles.addRuleBtn}
                icon={<PlusOutlined />}
                type="primary"
                disabled={addBtnDisabled}
                onClick={onAdd}
              >
                {addBtnText}
              </Button>
            </Tooltip>
            <div>
              <UploadAgent
                disabled={addBtnDisabled}
                teamId={teamId}
                type={EmployeeAndRelativeEnum.Manager}
                handleUploadSuccess={() =>
                  setPagination({ ...DefaultTablePagination })
                }
              />
              <Button
                disabled={addBtnDisabled}
                icon={<DownloadOutlined />}
                loading={downloadLoading}
                onClick={downloadAgentList}
              >
                {t('Download')}
              </Button>
            </div>
          </>
        )}
      </div>

      <Table
        scroll={{ x: 'max-content' }}
        rowKey="id"
        columns={combinedColumns}
        loading={teamInfoLoading}
        dataSource={agentList?.data}
        pagination={false}
      />
      <PaginationComponent
        className="margin-top-16 margin-bottom-16"
        total={agentList?.totalElements || 0}
        pagination={pagination}
        handlePaginationChange={onChangePagination}
      />
      {agentType === AgentType.Manager && (
        <TiedAgentDrawer
          basicInfoType={ChannelTypeEnum.TiedAgent}
          visible={drawerVisible}
          id={editedManger?.id}
          teamId={teamId}
          type={drawerMode}
          origin={OriginType.sales}
          agentType={level === configLevel && isVirtual === YesOrNo.NO}
          salesConfigName={configName}
          onClose={onCloseDrawer}
        />
      )}
    </div>
  );
};
