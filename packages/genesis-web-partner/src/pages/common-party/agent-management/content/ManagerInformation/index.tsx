import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DownloadOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Form, Spin, Tooltip, message } from 'antd';
import type { ColumnProps } from 'antd/es/table';
import { useForm } from 'antd/lib/form/Form';

import { useSearchParams, useSelector } from '@umijs/max';

import { useRequest } from 'ahooks';
import qs from 'qs';

import {
  DeleteAction,
  EditAction,
  Icon,
  Modal,
  Select,
  Table,
  TableActionsContainer,
  ViewAction,
} from '@zhongan/nagrand-ui';

import { useAddressConfig } from 'genesis-web-component/lib/components/Address';
import type { AgentManagementInfo, ChannelTeamInfo } from 'genesis-web-service';
import { AgentType, ChannelService, EmployeeAndRelativeEnum, YesOrNo } from 'genesis-web-service';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { useAddressI18nList } from '@/hooks/useAddressI18nList';
import type { ConnectState } from '@/models/connect';
import { UploadAgent } from '@/pages/common-party/agent-management/components/UploadAgent';
import { useEditPermission } from '@/pages/common-party/agent-management/hooks/useAgentManagement';
import { useColumns } from '@/pages/common-party/agent-management/hooks/useColumns';
import { optionsFormat } from '@/pages/common-party/partner-setting/components/SalesAgreement';
import { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import { ChannelAddressFieldCodeMap, OriginType } from '@/pages/common-party/utils/constants';
import { ModeEnum } from '@/types/common';
import type { LocationQueryParam } from '@/types/common-party';
import { AgentFormColumnType } from '@/types/common-party';
import { DefaultTablePagination } from '@/utils/constants';
import { transferSchemaToTableProp } from '@/utils/utils';

import { TiedAgentDrawer } from '../../../partner-setting/components/TiedAgent/TiedAgentDrawer';
import styles from './index.scss';

const AddressKeys = Object.keys(ChannelAddressFieldCodeMap);

/**
 *
 * @param agentType
 * @description manager/agent信息；manager信息和branch agent信息 展示字段 & 接口一样，故用同一套代码；但是由于有些title展示区别，和接口传参需要agentType来判断是manager还是agent，故需要传递参数agentType
 */
export const ManagerInformation: React.FC<{ agentType: AgentType }> = ({ agentType }) => {
  const { t } = useTranslation('partner');

  const [searchParams] = useSearchParams();
  const { configLevel, level, id, configName, isVirtual } = qs.parse(
    searchParams.toString()
  ) as unknown as LocationQueryParam;
  const { canEdit, canView } = useEditPermission();

  const [drawerMode, setDrawerMode] = useState(ModeEnum.EDIT);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [editedManger, setEditedManager] = useState<AgentManagementInfo>();
  const [pagination, setPagination] = useState(DefaultTablePagination);
  const [teamId, setTeamId] = useState<number>();

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>();
  const [isTransfer, setIsTransfer] = useState<boolean>();
  const [teamTransferModalVisible, setTeamTransferModalVisible] = useState<boolean>();
  const { formItems } = useAddressConfig();
  const { teamInfo }: { teamInfo: ChannelTeamInfo } = useSelector(({ partnerManagement }: ConnectState) => ({
    teamInfo: partnerManagement.teamInfo,
  }));
  const { data: teamOptions } = useRequest(() => ChannelService.queryTeamList());

  const [form] = useForm();
  const [transferConfirmLoading, setTransferConfirmLoading] = useState(false);

  useEffect(() => {
    // id变化时pagination回到初始状态
    setTeamId((id && Number(id)) ?? teamInfo?.id);
    setPagination(DefaultTablePagination);

    setSelectedRowKeys(undefined);
    setIsTransfer(false);
  }, [id, teamInfo?.id]);

  // 查询
  const {
    data: agentList,
    loading: teamInfoLoading,
    run: queryAgentList,
  } = useRequest(
    () => {
      if (teamId)
        if (agentType === AgentType.Manager) {
          return ChannelService.getBranchManagerList({
            teamId: teamId,
            pageIndex: pagination.current - 1,
            pageSize: pagination.pageSize,
            agentType,
          });
        } else {
          return ChannelService.querySalesChannelList({
            teamId: teamId,
            pageIndex: pagination.current - 1,
            pageSize: pagination.pageSize,
          });
        }
    },
    {
      refreshDeps: [teamId, pagination],
    }
  );

  // address1-8 => address11-18 用于国际化
  const agentListWithAddress = useMemo(
    () =>
      agentList?.data?.map(addressItem => {
        const newAddressValue: Record<string, string> = {};
        AddressKeys?.forEach(addressKey => {
          const addressValue = addressItem?.[ChannelAddressFieldCodeMap[addressKey]];
          if (addressValue) {
            newAddressValue[addressKey] = addressValue;
          }
        });

        return { ...addressItem, ...newAddressValue };
      }),
    [agentList?.data]
  );

  const addressI18nList = useAddressI18nList(agentListWithAddress);

  // branch才展示address & zipCode
  const addressAndZipCodeTableColumns = useMemo(() => {
    if (!formItems?.length) {
      return [];
    }
    return transferSchemaToTableProp(formItems, addressI18nList);
  }, [formItems, agentList?.data, addressI18nList]);

  // 关闭抽屉
  const onCloseDrawer = useCallback(() => {
    setDrawerMode(ModeEnum.EDIT);
    setEditedManager(undefined);
    setDrawerVisible(false);
    if (!editedManger) {
      //新增成功后，回到初始页码条件
      setPagination({ ...DefaultTablePagination });
    } else {
      // 编辑成功，以当前条件请求
      queryAgentList();
    }
  }, [queryAgentList]);

  // 删除
  const removeAgent = useCallback(
    (val: number) => {
      return ChannelService.removeBranchManager(teamId, val)
        .then(() => {
          setPagination({ ...DefaultTablePagination });
          message.success(t('Delete successfully'));
        })
        .catch((error: Error) => message.error(error.message));
    },
    [teamId, agentType]
  );

  // 下载
  const { loading: downloadLoading, runAsync: downloadAgentList } = useRequest(
    () =>
      ChannelService.download<{
        id: number;
        type: EmployeeAndRelativeEnum;
      }>({
        id: teamId,
        type: EmployeeAndRelativeEnum.Manager,
      }),
    {
      manual: true,
      onSuccess: res => downloadFile(res),
      onError: (error: Error) => message.error(error.message || t('Download failed')),
    }
  );

  const onChangePagination = useCallback((current: number, pageSize: number) => {
    setPagination(old => ({
      ...old,
      current,
      pageSize,
    }));
  }, []);

  // 查看/编辑
  const onViewOrEditDetail = useCallback((manager: AgentManagementInfo, mode: ModeEnum) => {
    setDrawerMode(mode);
    setDrawerVisible(true);
    setEditedManager(manager);
  }, []);

  const { title, addBtnText, addBtnDisabled, actionBtnDisabled } = useMemo(() => {
    let tempTitle = '';
    let tempAddBtnText = '';
    const tempAddBtnDisabled = !canEdit || !teamInfo?.id;
    const tempActionBtnDisabled = !canEdit;
    if (agentType === AgentType.Manager) {
      tempTitle = t('Team Manager Information', {
        team: configName,
      });
      tempAddBtnText = t('Add New Team Manager', {
        team: configName,
      });
    } else {
      tempTitle = t('Sales Channel Information');
    }
    return {
      title: tempTitle,
      addBtnText: tempAddBtnText,
      addBtnDisabled: tempAddBtnDisabled,
      actionBtnDisabled: tempActionBtnDisabled,
    };
  }, [agentType, configName, canEdit, teamInfo]);

  const toolTipText = useMemo(() => {
    if (!teamInfo) {
      return t('Please complete Team Information first', {
        team: configName?.toLowerCase(),
      });
    }
    // agent 只要有了组织信息就不需要展示任何tooltip
    //  manager isVirtual时不显示tooltip，没有在职manager时需要展示tooltip
    if (agentType === AgentType.Agent || teamInfo.isVirtual) {
      return null;
    }
  }, [teamInfo, configName, agentType, t]);

  const columnsData = {
    formType: AgentFormColumnType.ManagerInfo,
    configName,
  };
  if (agentType === AgentType.Agent) {
    columnsData.formType = AgentFormColumnType.AgentInfo;
  }

  const columns = useColumns(columnsData);
  const combinedColumns = useMemo((): ColumnProps<AgentManagementInfo>[] => {
    let columnsNew = [...columns];
    if (agentType === AgentType.Manager) {
      columnsNew = [...columns, ...addressAndZipCodeTableColumns];
      columnsNew.push({
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (_, record) => (
          <TableActionsContainer>
            <DeleteAction
              disabled={actionBtnDisabled}
              onClick={() => removeAgent(record.id)}
              doubleConfirmType="modal"
            />
            <EditAction disabled={actionBtnDisabled} onClick={() => onViewOrEditDetail(record, ModeEnum.EDIT)} />
            <ViewAction disabled={!canView} onClick={() => onViewOrEditDetail(record, ModeEnum.READ)} />
          </TableActionsContainer>
        ),
      });
    }

    return columnsNew;
  }, [columns, addressAndZipCodeTableColumns, actionBtnDisabled, canView, removeAgent, onViewOrEditDetail, agentType]);

  const onAdd = () => {
    setDrawerMode(ModeEnum.ADD);
    setDrawerVisible(true);
  };

  const onCloseTeamTransferModal = () => {
    setTeamTransferModalVisible(false);
    form.resetFields();
  };

  const handleTeamTransferModalConfirm = async () => {
    setTransferConfirmLoading(true);
    await form.validateFields();

    const params = {
      beforeTeamCode: teamInfo.teamCode,
      afterTeamCode: form.getFieldValue('afterTeamCode'),
      salesCodes: selectedRowKeys,
    };

    await ChannelService.transferTeam(params);
    setTransferConfirmLoading(false);
    queryAgentList();
    message.success(t('Operated successfully'));

    setSelectedRowKeys(undefined);
    setIsTransfer(false);
    onCloseTeamTransferModal();
  };

  return (
    <div className={styles.managerContainer}>
      <p className={styles.pageTitle}>
        {title}
        {agentType === AgentType.Agent &&
          (isTransfer ? (
            <div>
              <Button onClick={() => setIsTransfer(false)}>{t('Cancel')}</Button>
              <Button
                onClick={() => setTeamTransferModalVisible(true)}
                disabled={!selectedRowKeys?.length}
                type="primary"
              >
                {t('Confirm')}
              </Button>
            </div>
          ) : (
            <Button onClick={() => setIsTransfer(true)} type="primary">
              {t('Transfer')}
            </Button>
          ))}
      </p>
      {agentType !== AgentType.Agent && (
        <div className={styles.actionsContainer}>
          <Tooltip title={toolTipText}>
            <Button
              className={styles.addRuleBtn}
              icon={<PlusOutlined />}
              type="primary"
              disabled={addBtnDisabled}
              onClick={onAdd}
            >
              {addBtnText}
            </Button>
          </Tooltip>
          <div>
            <UploadAgent
              disabled={addBtnDisabled}
              teamId={teamId}
              type={EmployeeAndRelativeEnum.Manager}
              handleUploadSuccess={() => setPagination({ ...DefaultTablePagination })}
            />
            <Button
              disabled={addBtnDisabled}
              icon={<DownloadOutlined />}
              loading={downloadLoading}
              onClick={downloadAgentList}
            >
              {t('Download')}
            </Button>
          </div>
        </div>
      )}
      <Table
        scroll={{ x: 'max-content' }}
        rowKey="salesChannelCode"
        columns={combinedColumns}
        loading={teamInfoLoading}
        dataSource={agentList?.data}
        pagination={{
          total: agentList?.totalElements || 0,
          current: pagination.current,
          pageSize: pagination.pageSize,
          onChange: onChangePagination,
        }}
        rowSelection={
          agentType === AgentType.Agent && isTransfer
            ? {
                onChange: currentSelectedRowKeys => {
                  setSelectedRowKeys([
                    ...new Set([
                      ...(selectedRowKeys?.filter(
                        key => !agentList?.data?.map(item => item.salesChannelCode)?.includes(key)
                      ) ?? []),
                      ...((currentSelectedRowKeys ?? []) as string[]),
                    ]),
                  ]);
                },
                selectedRowKeys,
              }
            : undefined
        }
      />
      {agentType === AgentType.Manager && (
        <TiedAgentDrawer
          basicInfoType={ChannelTypeEnum.TiedAgent}
          visible={drawerVisible}
          id={editedManger?.id}
          teamId={teamId}
          type={drawerMode}
          origin={OriginType.sales}
          agentType={level === configLevel && isVirtual === YesOrNo.NO}
          salesConfigName={configName}
          onClose={onCloseDrawer}
        />
      )}
      <Modal
        open={teamTransferModalVisible}
        title={
          <>
            <Icon
              type="warning"
              style={{
                marginRight: '4px',
              }}
            />
            {t('Confirmation Required')}
          </>
        }
        footer={null}
        onClose={onCloseTeamTransferModal}
      >
        <Spin spinning={transferConfirmLoading}>
          <div>
            <div style={{ fontWeight: 500, marginBottom: '16px' }}>
              {t(
                'Are you sure you want to transfer these selected sales channels to the new team? This transfer will also move all policies managed by these sales channels. If some policies need to remain with the original team or be transferred to another team, please complete the policy transfer first.'
              )}
            </div>
            <Form layout="vertical" form={form}>
              <Form.Item
                label={t('Sales Agreement Signing Party')}
                name="afterTeamCode"
                rules={[
                  {
                    required: true,
                    message: t('Please select'),
                  },
                ]}
              >
                <Select
                  showSearch
                  options={optionsFormat(
                    teamOptions?.filter(option => option.teamCode !== teamInfo?.teamCode) ?? [],
                    ['teamName', 'teamCode'],
                    'teamCode'
                  )}
                />
              </Form.Item>
            </Form>
          </div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'end',
              gap: '16px',
            }}
          >
            <Button onClick={onCloseTeamTransferModal}>{t('Cancel')}</Button>
            <Button onClick={handleTeamTransferModalConfirm} type="primary">
              {t('Confirm')}
            </Button>
          </div>
        </Spin>
      </Modal>
    </div>
  );
};
