import { TeamInformation } from '@/pages/common-party/agent-management/content/TeamInformation';
import { ManagerInformation } from '@/pages/common-party/agent-management/content/ManagerInformation';
import { AgentType } from 'genesis-web-service';
import type { LocationQueryParam } from '@/types/common-party';
import { useSearchParams } from 'react-router-dom';
import qs from 'qs';

const Content = () => {
  const [searchParams] = useSearchParams();
  const { configLevel, level } = qs.parse(
    searchParams.toString(),
  ) as unknown as LocationQueryParam;

  return (
    <div>
      <TeamInformation />
      <ManagerInformation agentType={AgentType.Manager} />
      {level === configLevel && (
        <ManagerInformation agentType={AgentType.Agent} />
      )}
    </div>
  );
};

export default Content;
