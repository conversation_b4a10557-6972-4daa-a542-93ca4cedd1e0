import { useSearchParams } from '@umijs/max';

import qs from 'qs';

import { AgentType } from 'genesis-web-service';

import { ManagerInformation } from '@/pages/common-party/agent-management/content/ManagerInformation';
import { TeamInformation } from '@/pages/common-party/agent-management/content/TeamInformation';
import type { LocationQueryParam } from '@/types/common-party';

const Content = () => {
  const [searchParams] = useSearchParams();
  const { configLevel, level } = qs.parse(searchParams.toString()) as unknown as LocationQueryParam;

  return (
    <div>
      <TeamInformation />
      <ManagerInformation agentType={AgentType.Manager} />
      {level === configLevel && <ManagerInformation agentType={AgentType.Agent} />}
    </div>
  );
};

export default Content;
