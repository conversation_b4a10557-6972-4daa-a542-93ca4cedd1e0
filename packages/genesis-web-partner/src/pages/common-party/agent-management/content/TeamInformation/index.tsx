import { useMemo, useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Form, message, Tooltip } from 'antd';
import type { ColumnProps } from 'antd/es/table';
import { useDispatch } from '@umijs/max';
import cls from 'classnames';
import { PlusOutlined } from '@ant-design/icons';
import { EditOutline, ViewSquareOutline } from '@/components/Icons';
import { AgentBreadcrumb } from '../../components/agent-breadcrumb';
import { ModeEnum } from '@/types/common';
import type { LocationQueryParam } from '@/types/common-party';
import { ChannelService, TeamBusinessType } from 'genesis-web-service';
import type { ChannelTeamInfo } from 'genesis-web-service';
import { CommonForm } from '@/components/CommonForm/Form';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import {
  useEditPermission,
  useAgentContext,
} from '@/pages/common-party/agent-management/hooks/useAgentManagement';
import { useFormFields } from '@/pages/common-party/agent-management/hooks/useFormFields';
import { useColumns } from '@/pages/common-party/agent-management/hooks/useColumns';
import { AgentFormColumnType } from '@/types/common-party';
import { RootLevel } from '@/pages/common-party/agent-management/util/constants';

import { useLocation } from '@umijs/max';
import { useRequest } from 'ahooks';

import styles from './index.scss';
import { MappingOrganizationStructure } from '@/pages/common-party/agent-management/components/MappingOrganizationStructure';
import { useSearchParams } from 'react-router-dom';
import qs from 'qs';
import { Table, TableActionsContainer } from '@zhongan/nagrand-ui';

/**
 *
 * @description 组织信息；只会有一条，新增成功后，Add New button隐藏，表格只支持查看和编辑，删除功能在左侧树状结构里
 */
export const TeamInformation: React.FC = () => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();

  const { state } = useLocation();
  const [searchParams] = useSearchParams();
  const {
    parentId,
    level,
    configLevel,
    originMaxLevel,
    id: teamId,
    configName,
  } = qs.parse(searchParams.toString()) as unknown as LocationQueryParam;
  const dispatch = useDispatch();
  const { canEdit, canView } = useEditPermission();
  const { updateTeamTree } = useAgentContext();

  const [teamNames, setTeamNames] = useState<string[]>();
  const [editing, setEditing] = useState(false);
  const [drawerMode, setDrawerMode] = useState(ModeEnum.EDIT);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [teamInfo, setTeamInfo] = useState<ChannelTeamInfo>();
  const [teamOrgStructureId, setTeamOrgStructureId] = useState<number>();

  const isHeadquarter = +level === RootLevel;

  useEffect(() => {
    setTeamOrgStructureId(teamInfo?.orgStructureId);
  }, [teamInfo]);

  useEffect(() => {
    dispatch({
      type: 'partnerManagement/saveTeamOrgStructureId',
      payload: teamOrgStructureId,
    });
  }, [dispatch, teamOrgStructureId]);

  const { loading: teamInfoLoading } = useRequest(
    () => {
      if (teamId) return ChannelService.queryTeamInfo(teamId);
    },
    {
      refreshDeps: [teamId],
      onSuccess: res => setTeamInfo(res),
      onError: (error: Error) => {
        message.error(error?.message);
        setTeamInfo(undefined);
      },
    },
  );

  const onCloseDrawer = useCallback(
    (updated?: boolean, options?: ChannelTeamInfo) => {
      setDrawerMode(ModeEnum.EDIT);
      setDrawerVisible(false);
      setEditing(false);
      form.resetFields();
      if (updated) {
        updateTeamTree?.(options);
        setTeamInfo(options);
      }
    },
    [updateTeamTree],
  );

  const { run: saveTeamInfo, loading: updating } = useRequest(
    (team: ChannelTeamInfo) =>
      (!teamId
        ? ChannelService.addTeamInfo({
          ...team,
          parentId,
          level: +level,
          maxLevel: originMaxLevel && +originMaxLevel,
          teamBusinessType: TeamBusinessType.Agent,
        })
        : ChannelService.updateTeamInfo(teamId, {
          ...teamInfo,
          ...team,
        })),
    {
      manual: true,
      onSuccess: res => onCloseDrawer(true, res),
      onError: (error: Error) => message.error(error?.message),
    },
  );

  const onSave = useCallback(() => {
    form.validateFields().then(values => {
      if (teamId || !isHeadquarter || (values.teamCode && teamOrgStructureId)) {
        saveTeamInfo({
          ...values,
          orgStructureId: teamOrgStructureId,
        });
        return;
      }
      setTeamInfo({
        ...values,
        level: +level,
      });
      onCloseDrawer(false);
    });
  }, [
    form,
    isHeadquarter,
    level,
    onCloseDrawer,
    saveTeamInfo,
    teamId,
    teamOrgStructureId,
  ]);

  const onChangeOrgStructureId = useCallback(
    (orgStructureId: number) => {
      setTeamOrgStructureId(orgStructureId);
      if (teamInfo) {
        saveTeamInfo({
          ...teamInfo,
          orgStructureId,
        } as ChannelTeamInfo);
      }
    },
    [saveTeamInfo, teamInfo],
  );

  const onViewDetail = useCallback(() => {
    setEditing(true);
    setDrawerMode(ModeEnum.READ);
    setDrawerVisible(true);
  }, []);

  useEffect(() => {
    setDrawerVisible(editing);
    if (teamInfo && editing) {
      form.setFieldsValue(teamInfo);
    }
  }, [teamInfo, editing]);

  const fields = useFormFields({
    disabled: drawerMode === ModeEnum.READ,
    information: teamInfo,
    formType: AgentFormColumnType.TeamInfo,
    level,
    configLevel,
    configName,
  });

  const columns =
    useColumns({
      formType: AgentFormColumnType.TeamInfo,
      level,
      configLevel,
      configName,
    }) ?? [];

  const combinedColumns = useMemo(
    (): ColumnProps<ChannelTeamInfo>[] => [
      ...columns,
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: () => (
          <div className="table-actions">
            <TableActionsContainer>
              <Tooltip title={t('Edit')}>
                <Button
                  disabled={!!teamInfo?.isVirtual || !canEdit}
                  type="link"
                  icon={<EditOutline />}
                  onClick={() => setEditing(true)}
                />
              </Tooltip>
              <Tooltip title={t('View')}>
                <Button
                  disabled={!canView}
                  type="link"
                  icon={<ViewSquareOutline />}
                  onClick={() => onViewDetail()}
                />
              </Tooltip>
            </TableActionsContainer>
          </div>
        ),
      },
    ],
    [canEdit, teamInfo?.isVirtual, t, columns, canView, onViewDetail],
  );

  const teamInfoDrawerTitle = useMemo(() => {
    return !teamInfo
      ? t('Add New Team', {
        team: configName,
      })
      : drawerMode === ModeEnum.READ
        ? t('View Team', {
          team: configName,
        })
        : t('Edit Team', {
          team: configName,
        });
  }, [teamInfo, drawerMode, configName, t]);

  useEffect(() => {
    dispatch({
      type: 'partnerManagement/saveTeamInfo',
      payload: teamInfo,
    });
  }, [dispatch, teamInfo]);

  // 面包屑处理，最后一个需要根据组织信息来替换
  useEffect(() => {
    const clonedTeamNames = [...(state?.teamNames ?? [])];
    if (teamInfo?.teamName) {
      clonedTeamNames?.splice(
        clonedTeamNames.length - 1,
        1,
        teamInfo?.teamName,
      );
    }
    setTeamNames(clonedTeamNames);
  }, [state?.teamNames, teamInfo]);

  return (
    <div
      className={cls(
        styles.teamInfoAndBread,
        isHeadquarter && styles.headquarter,
      )}
    >
      <AgentBreadcrumb teamNames={teamNames} />
      <div className={styles.teamInfo}>
        <div className="flex-between">
          <p className={styles.pageTitle}>
            {t('Team Information', { team: configName })}
          </p>
          {isHeadquarter && (
            <MappingOrganizationStructure
              className={styles.mappingButton}
              hiddenConfirm={!!teamId && !!teamOrgStructureId}
              loading={teamInfoLoading}
              structureId={teamOrgStructureId}
              onSubmit={onChangeOrgStructureId}
            />
          )}
        </div>
        {!!canEdit && !teamInfo && (
          <Button
            className={styles.addRuleBtn}
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => setDrawerVisible(true)}
          >
            {t('Add {{Team}} Information', {
              team: configName,
            })}
          </Button>
        )}
        <Table
          scroll={{ x: 'max-content' }}
          rowKey="id"
          columns={combinedColumns}
          loading={teamInfoLoading}
          dataSource={teamInfo ? [teamInfo] : []}
          pagination={false}
        />
        <DrawerForm
          className={styles.agentEditDrawer}
          visible={drawerVisible}
          disabled={!canEdit || drawerMode === ModeEnum.READ}
          onClose={onCloseDrawer}
          maskClosable={false}
          width={752}
          title={teamInfoDrawerTitle}
          cancelText={t('Cancel')}
          sendText={t('Submit')}
          onSubmit={onSave}
          submitBtnProps={{ loading: updating }}
        >
          <CommonForm form={form} fields={fields} />
        </DrawerForm>
      </div>
    </div>
  );
};
