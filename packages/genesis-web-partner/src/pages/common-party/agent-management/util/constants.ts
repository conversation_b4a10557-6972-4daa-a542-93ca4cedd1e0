import { ChannelTeamType as TeamType } from 'genesis-web-service';

import i18nInstance from '@/utils/i18n';

// 删除文案
export const DeletedContentMap = {
  [TeamType.Headquarter]: i18nInstance.t(
    'Are you sure to delete this headquarter and all relevant branch information?',
    { ns: 'partner' }
  ),
  [TeamType.Branch]: i18nInstance.t('Are you sure to delete this branch?', {
    ns: 'partner',
  }),
};

export const AgentManagementPrefix = 'agent-management';

export const RouterMap = {
  [TeamType.Headquarter]: 'headquarter',
  [TeamType.Branch]: 'branch',
};

export const RootLevel = 0;
export const VirtualRootLevel = 1;
