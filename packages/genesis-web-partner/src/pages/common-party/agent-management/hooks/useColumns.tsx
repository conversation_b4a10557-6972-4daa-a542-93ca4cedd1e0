import { useTranslation } from 'react-i18next';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import type { BizDictItem, AgentManagementInfo } from 'genesis-web-service';
import { AgentStatusType } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import { AgentFormColumnType } from '@/types/common-party';
import { RenderEnums } from '@/components/RenderEnums';
import { StatusTag } from '@/components/StatusTag';
import { TagsShape, TagsType } from '@/utils/constants';
import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import { Tooltip } from 'antd';

interface Props {
  formType: AgentFormColumnType;
  configName: string;
}
export const useColumns = ({ formType, configName }: Props) => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  if (formType === AgentFormColumnType.TeamInfo) {
    const teamCodeI18n = t('Team-Code', { team: configName });
    const teamNameI18n = t('Team-Name', { team: configName });

    return [
      {
        title: teamNameI18n,
        dataIndex: 'teamName',
        fixed: 'left',
      },
      {
        title: teamCodeI18n,
        dataIndex: 'teamCode',
        render: (text: string) => <Tooltip title={text}>{text}</Tooltip>
      },
      {
        title: t('Introduction'),
        dataIndex: 'introduction',
      },
      {
        title: t('Email'),
        dataIndex: 'email',
      },
      {
        title: t('Country Code'),
        dataIndex: 'countryCode',
      },
      {
        title: t('Phone Number'),
        dataIndex: 'phoneNum',
      },
      {
        title: t('Office City'),
        dataIndex: 'officeCity',
        render: (officeCity: string) => (
          <RenderEnums enums={enums?.officeCity} keyName={officeCity} />
        ),
      },
    ];
  }

  if (formType === AgentFormColumnType.ManagerInfo) {
    const commonColumnsTop = [
      {
        title: t('Name'),
        fixed: 'left',
        dataIndex: 'agentName',
        render: (_, record: AgentManagementInfo) => (
          <>
            {record.firstName} {record.lastName}
          </>
        ),
      },
      {
        title: t('Code'),
        dataIndex: 'code',
      },
    ];

    const commonColumnsBottom = [
      {
        title: t('ID Type'),
        dataIndex: 'idType',
        render: (idType: string) => (
          <RenderEnums enums={enums?.certiType} keyName={idType} />
        ),
      },
      {
        title: t('ID Number'),
        dataIndex: 'idNo',
      },
      {
        title: t('Status'),
        dataIndex: 'status',
        render: (status: AgentStatusType) => {
          const tagType =
            status === AgentStatusType.Resigned
              ? TagsType.Default
              : TagsType.Active;
          if (!status) {
            return (
              <ComponentWithFallback>
                <></>
              </ComponentWithFallback>
            );
          }
          return (
            <StatusTag shape={TagsShape.Round} type={tagType} hasborder={false}>
              <RenderEnums enums={enums?.agentStatus} keyName={status} />
            </StatusTag>
          );
        },
      },
      {
        title: t('Onboard Date'),
        dataIndex: 'onboardDate',
        render: (onboardDate: string) =>
          dateFormatInstance?.getDateString(onboardDate),
      },
      {
        title: t('Resign Date'),
        dataIndex: 'resignDate',
        render: (resignDate: string) =>
          dateFormatInstance?.getDateString(resignDate),
      },
      {
        title: t('Email'),
        dataIndex: 'email',
      },
      {
        title: t('Country Code'),
        dataIndex: 'countryCode',
      },
      {
        title: t('Mobile Phone'),
        dataIndex: 'phoneNo',
      },
    ];

    const branchSpecialColumns = [
      {
        title: t('Sales License Type'),
        dataIndex: 'salesLicenseType',
      },
      {
        title: t('License No.'),
        dataIndex: 'licenseNumber',
      },
      {
        title: t('License Effective Date'),
        dataIndex: 'licenseEffectiveDate',
        render: (licenseEffectiveDate: string) =>
          dateFormatInstance?.getDateString(licenseEffectiveDate),
      },
      {
        title: t('Self Agent'),
        dataIndex: 'selfAgent',
        render: (selfAgent: string) => (
          <RenderEnums enums={enums?.yesNo} keyName={selfAgent} />
        ),
      },
      {
        title: t('Overriding Commission'),
        dataIndex: 'overridingCommission',
        render: (overridingCommission: string) => (
          <RenderEnums enums={enums?.yesNo} keyName={overridingCommission} />
        ),
      },
    ];

    if (formType === AgentFormColumnType.ManagerInfo) {
      return commonColumnsTop.concat(commonColumnsBottom, branchSpecialColumns);
    }
  }
  if (formType === AgentFormColumnType.AgentInfo) {
    const salesChannelColumns = [
      {
        title: t('Sales Channel Name'),
        dataIndex: 'salesChannelName',
      },
      {
        title: t('Sales Channel Code'),
        dataIndex: 'salesChannelCode',
      },
      {
        title: t('Sales Channel Type'),
        dataIndex: 'salesChannelType',
        render: (salesChannelType: string) => (
          <RenderEnums enums={enums?.channelType} keyName={salesChannelType} />
        ),
      },
      {
        title: t('Sales Agreement Name'),
        dataIndex: 'agreementNameList',
        render: (agreementNameList: string[]) => agreementNameList?.join(','),
      },
      {
        title: t('Sales Agreement Code'),
        dataIndex: 'agreementCodeList',
        render: (agreementCodeList: string[]) => agreementCodeList?.join(','),
      },
      {
        title: t('Email'),
        dataIndex: 'email',
      },
      {
        title: t('Country Code'),
        dataIndex: 'countryCode',
      },
      {
        title: t('Mobile Phone'),
        dataIndex: 'phoneNo',
      },
    ];
    return salesChannelColumns;
  }
};

export const useSalesChannelColumns = () => {
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  const { t } = useTranslation('partner');
  return [
    {
      title: t('Sales Channel Name'),
      dataIndex: 'salesChannelName',
    },
    {
      title: t('Sales Channel Code'),
      dataIndex: 'salesChannelCode',
    },
    {
      title: t('Sales Channel Type'),
      dataIndex: 'salesChannelType',
      render: (salesChannelType: string) => (
        <RenderEnums enums={enums?.channelType} keyName={salesChannelType} />
      ),
    },
  ];
};
