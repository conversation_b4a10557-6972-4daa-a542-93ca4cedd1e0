import { createContext, useContext } from 'react';

import { usePermission } from '@/hooks/usePermissions';
import type { AgentContextProps } from '@/types/common-party';

export const AgentContext = createContext<AgentContextProps>({
  updateTeamTree: () => {},
});

export const useAgentContext: () => AgentContextProps = () => useContext(AgentContext);

export const useEditPermission = (): Record<string, boolean> => {
  const canEdit = usePermission('channel.agent-management.edit');

  const canView = usePermission('channel.agent-management.view');

  return {
    canEdit,
    canView,
  };
};
