import { createContext, useContext } from 'react';
import type { AgentContextProps } from '@/types/common-party';
import { usePermission } from '@/hooks/usePermissions';

export const AgentContext = createContext<AgentContextProps>({
  updateTeamTree: () => {},
});

export const useAgentContext: () => AgentContextProps = () =>
  useContext(AgentContext);

export const useEditPermission = (): Record<string, boolean> => {
  const canEdit = usePermission('channel.agent-management.edit');

  const canView = usePermission('channel.agent-management.view');

  return {
    canEdit,
    canView,
  };
};
