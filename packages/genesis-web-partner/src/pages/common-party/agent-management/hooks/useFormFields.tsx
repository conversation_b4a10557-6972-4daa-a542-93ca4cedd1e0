import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import moment from 'moment';
import type { Moment } from 'moment';

import type { BizDictItem } from 'genesis-web-service';
import { AgentStatusType } from 'genesis-web-service';

import { FieldType } from '@/components/CommonForm';
import { CountryCode } from '@/components/CountryCode';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { AgentFormColumnType } from '@/types/common-party';

interface FieldsProps {
  form: FormInstance;
  formType: AgentFormColumnType;
  level?: string;
  configLevel: string;
  configName: string;
  disabled: boolean;
  information?: Record<string, string>;
  goodsListOptions?: { label: string; value: number }[];
}

export const useFormFields = ({
  form,
  formType,
  disabled,
  information,
  level,
  configLevel,
  configName,
  goodsListOptions,
}: FieldsProps) => {
  const { t } = useTranslation('partner');
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  const [status, setStatus] = useState<AgentStatusType>();

  useEffect(() => {
    setStatus(information?.status as AgentStatusType);
  }, [information]);

  const onChangeStatus = useCallback((selectedStatus: AgentStatusType) => {
    setStatus(selectedStatus);
    // 状态改为employed时，清空resignDate
    if (selectedStatus === AgentStatusType.Employed) {
      form?.resetFields(['resignDate']);
    }
  }, []);

  if (formType === AgentFormColumnType.TeamInfo) {
    const teamNameI18n = t('Team-Name', { team: configName });
    const teamCodeI18n = t('Team-Code', { team: configName });

    return [
      {
        key: 'teamName',
        label: teamNameI18n,
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: teamNameI18n,
            }),
          },
        ],
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'teamCode',
        label: teamCodeI18n,
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: teamCodeI18n,
            }),
          },
        ],
        ctrlProps: {
          disabled: disabled || (!!information?.id && !!information?.teamCode),
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'introduction',
        label: t('Introduction'),
        col: 12,
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'email',
        label: t('Email'),
        col: 12,
        rules: [
          {
            type: 'email',
            message: t('Please input correct format'),
          },
        ],
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        type: FieldType.Customize,
        customerDom: (
          <Col span={12}>
            <Form.Item name="countryCode" label={t('Country Code')}>
              <CountryCode disabled={disabled} />
            </Form.Item>
          </Col>
        ),
      },
      {
        key: 'phoneNum',
        label: t('Phone Number'),
        col: 12,
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'officeCity',
        label: t('Office City'),
        col: 12,
        type: FieldType.Select,
        ctrlProps: {
          options: enums?.officeCity,
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
    ];
  }

  if (formType === AgentFormColumnType.AgentInfo || formType === AgentFormColumnType.ManagerInfo) {
    const commonFieldsTop = [
      {
        key: 'firstName',
        label: t('First Name'),
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('First Name'),
            }),
          },
        ],
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'lastName',
        label: t('Last Name'),
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Last Name'),
            }),
          },
        ],
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'agentCode',
        label: t('Code'),
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Code'),
            }),
          },
        ],
        ctrlProps: {
          disabled: disabled || !!information?.agentCode,
          style: { width: '100%' },
          allowClear: true,
        },
      },
    ];

    const commonFieldsBottom = [
      {
        key: 'idType',
        label: t('ID Type'),
        col: 12,
        type: FieldType.Select,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('ID Type'),
            }),
          },
        ],
        ctrlProps: {
          options: enums?.certiType,
          disabled: disabled || !!information?.idType,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'idNo',
        label: t('ID Number'),
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('ID Number'),
            }),
          },
        ],
        ctrlProps: {
          disabled: disabled || !!information?.idNo,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'status',
        label: t('Status'),
        col: 12,
        type: FieldType.Select,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Status'),
            }),
          },
        ],
        ctrlProps: {
          options: enums?.agentStatus,
          disabled: disabled || information?.status === AgentStatusType.Resigned, // 已保存的status为resigned时，不能再修改
          onChange: onChangeStatus,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'onboardDate',
        label: t('Onboard Date'),
        type: FieldType.DatePicker,
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Onboard Date'),
            }),
          },
        ],
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'resignDate',
        label: t('Resign Date'),
        col: 12,
        type: FieldType.DatePicker,
        rules: [
          {
            required: status === AgentStatusType.Resigned,
            message: t('channel.common.required', {
              label: t('Resign Date'),
            }),
          },
        ],
        ctrlProps: {
          disabled: disabled || status === AgentStatusType.Employed,
          style: { width: '100%' },
          disabledDate: (currentDate: Moment) => currentDate && currentDate < moment(form.getFieldValue('onboardDate')),
        },
      },
      {
        key: 'email',
        label: t('Email'),
        col: 12,
        rules: [
          {
            type: 'email',
            message: t('Please input correct format'),
          },
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Email'),
            }),
          },
        ],
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        type: FieldType.Customize,
        customerDom: (
          <Col span={12}>
            <Form.Item
              name="countryCode"
              label={t('Country Code')}
              rules={[
                {
                  required: true,
                  message: t('channel.common.required', {
                    label: t('Country Code'),
                  }),
                },
              ]}
            >
              <CountryCode disabled={disabled} />
            </Form.Item>
          </Col>
        ),
      },
      {
        key: 'phoneNo',
        label: t('Mobile Phone'),
        col: 12,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Mobile Phone'),
            }),
          },
        ],
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
    ];

    const branchSpecialFields = [
      {
        key: 'salesLicenseTypeList',
        label: t('Sales License Type'),
        type: FieldType.Select,
        col: 12,
        ctrlProps: {
          disabled,
          options: goodsListOptions,
          mode: 'multiple',
          showArrow: true,
          allowClear: true,
          style: { width: '100%' },
        },
      },
      {
        key: 'licenseNumber',
        label: t('License No.'),
        col: 12,
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'licenseEffectiveDate',
        label: t('License Effective Date'),
        col: 12,
        type: FieldType.DatePicker,
        ctrlProps: {
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        key: 'selfAgent',
        label: t('Self Agent'),
        col: 12,
        type: FieldType.Select,
        rules: [
          {
            required: true,
            message: t('channel.common.required', {
              label: t('Self Agent'),
            }),
          },
        ],
        ctrlProps: {
          options: enums?.yesNo,
          disabled,
          style: { width: '100%' },
          allowClear: true,
        },
      },
    ];
    if (
      formType === AgentFormColumnType.AgentInfo ||
      (formType === AgentFormColumnType.ManagerInfo && level === configLevel)
    ) {
      // branch manager 和agent form表单内容一样
      return commonFieldsTop.concat(branchSpecialFields, commonFieldsBottom);
    } else {
      return commonFieldsTop.concat(commonFieldsBottom);
    }
  }
};
