import i18nInstance from '@/utils/i18n';
import { Menu } from 'antd';
import type { MenuInfo } from 'rc-menu/lib/interface';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Commission } from '@/pages/agent-configuration/Commission';
import { Hierarchy } from '@/pages/agent-configuration/Hierarchy';
import { AgentType } from '@/pages/agent-configuration/AgentType';
import { TabEnum } from '@/types/agent-configuration';
import { usePermissionMap } from '@/hooks/usePermissions';
import styles from './index.scss';

const MenuItems = [
  {
    key: TabEnum.Commission,
    viewPermission: 'configuration.agent.view',
    title: i18nInstance.t('Commission&Commission Clawback Rule Configuration', {
      ns: 'partner',
    }),
  },
  {
    key: TabEnum.Hierarchy,
    viewPermission: 'configuration.agent.view',
    title: i18nInstance.t('Agent Sales Hierarchy Configuration', {
      ns: 'partner',
    }),
  },
  {
    key: TabEnum.AgentType,
    viewPermission: 'configuration.agent.view',
    title: i18nInstance.t('Agent Type Configuration', {
      ns: 'partner',
    }),
  },
];

export default () => {
  const { t } = useTranslation('partner');
  const [activeMenu, setActiveMenu] = useState<TabEnum>();
  const permissionMap = usePermissionMap();
  // 过滤掉没有查看权限的菜单
  const showMenuItems = useMemo(
    () =>
      MenuItems.filter(({ viewPermission }) => permissionMap?.[viewPermission]),
    [permissionMap],
  );
  useEffect(() => {
    // 默认选中第一项展示
    setActiveMenu(showMenuItems?.[0]?.key);
  }, [showMenuItems]);

  return (
    <div className={styles.rootContainer}>
      <div className={styles.sider}>
        <p className={styles.siderTitle}>{t('Agent Configuration')}</p>
        <Menu
          mode="inline"
          selectedKeys={[activeMenu]}
          onClick={({ key }: MenuInfo) => setActiveMenu(key)}
        >
          {showMenuItems.map(({ key, title }) => (
            <Menu.Item key={key} className={styles.siderMenuItem}>
              {title}
            </Menu.Item>
          ))}
        </Menu>
      </div>
      <div className={styles.contentContainer}>
        {activeMenu === TabEnum.Commission && <Commission />}
        {activeMenu === TabEnum.Hierarchy && <Hierarchy />}
        {activeMenu === TabEnum.AgentType && <AgentType />}
      </div>
    </div>
  );
};
