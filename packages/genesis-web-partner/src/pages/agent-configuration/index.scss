@import '../../variables';

.root-container {
  display: flex;
  height: calc(100vh - 50px);

  .sider {
    flex: 0 0 240px;
    height: 100%;
    background-color: var(--white);

    .sider-title {
      margin: 24px;
      font-size: 20px;
      line-height: 23px;
      font-weight: 700;
    }

    .sider-menu-item {
      height: auto;
      padding: 14px 15px 14px 24px;
      margin: 0;
      font-size: 14px;
      line-height: 22px;
      font-weight: 700;

      > span {
        white-space: break-spaces !important;
      }
    }
  }

  .content-container {
    min-width: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  :global .#{$antd-prefix}-btn {
    border-radius: var(--border-radius-base) !important;
  }
}
