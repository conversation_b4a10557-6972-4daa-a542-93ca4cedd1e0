import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Card, Form, Skeleton, message } from 'antd';

import { slice } from 'lodash-es';

import { EditableTable, FieldType, Select } from '@zhongan/nagrand-ui';

import { AgentSalesHierarchyConfiguration, ChannelService } from 'genesis-web-service';

import { usePermission } from '@/hooks/usePermissions';

import styles from './index.scss';

export const Hierarchy = () => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const [selectOptions, setSelectOptions] = useState([]);
  const [tableConfig, setTableConfig] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [isEdit, setIsEdit] = useState(false);
  const [loading, setLoading] = useState(false);
  const hasEditAuth = usePermission('configuration.agent.edit');

  // 获取下拉选择列表 以及默认值
  const queryHierarchyConfig = useCallback(() => {
    setLoading(true);
    Promise.all([
      ChannelService.queryChannelConfigDefault(AgentSalesHierarchyConfiguration.AgentSalesHierarchy),
      ChannelService.queryChannelConfigDefault(AgentSalesHierarchyConfiguration.AgentSalesHierarchyContent),
      ChannelService.queryChannelConfig(AgentSalesHierarchyConfiguration.AgentSalesHierarchy),
    ]).then(res => {
      const AgentSalesHierarchyOptions = res[0].itemList;
      const AgentSalesHierarchyContent = res[1].itemList;
      form.setFieldValue('hierarchy', res[2].dictValue);
      setTableConfig(AgentSalesHierarchyContent);
      setDataSource(res[2].itemList);
      setSelectOptions(AgentSalesHierarchyOptions);
      setLoading(false);
    });
  }, []);

  // 提交事件
  const handleSubmit = useCallback(() => {
    const params = {
      id: form.getFieldValue('hierarchy'),
      isDefault: 'YES',
      itemList: [...dataSource],
      dictKey: AgentSalesHierarchyConfiguration.AgentSalesHierarchy,
    };
    ChannelService.updateChannelConfig(
      AgentSalesHierarchyConfiguration.AgentSalesHierarchy,
      form.getFieldValue('hierarchy'),
      params
    )
      .then(() => {
        message.success(t('Save Success'));
        // 重新获取页面初始化配置
        queryHierarchyConfig();
      })
      .catch((error: Error) => {
        message.error(error.message);
        // 重新获取页面初始化配置
        queryHierarchyConfig();
      });

    setIsEdit(false);
  }, [t, queryHierarchyConfig, dataSource]);

  useEffect(() => {
    queryHierarchyConfig();
  }, [queryHierarchyConfig]);

  const columns = [
    {
      title: t('Level'),
      dataIndex: 'dictValue',
      width: '200px',
    },
    {
      title: t('Display Name'),
      dataIndex: 'dictValueName',
      editable: true,
      fieldProps: {
        type: FieldType.Input,
      },
    },
  ];

  const handleSelect = (val: string) => {
    message.warning(
      t('It should be noted that switching the agent sales hierarchy will cause the agent information to be cleared.')
    );
    let dataSourceNew = [];
    if (dataSource.length > Number(val) + 1) {
      dataSourceNew = slice(dataSource, 0, Number(val) + 1);
    } else {
      const addNew = slice(tableConfig, dataSource.length, Number(val) + 1);
      dataSourceNew = [...dataSource, ...addNew];
    }
    setDataSource(dataSourceNew);
  };

  const handleCancel = () => {
    queryHierarchyConfig();
    setIsEdit(false);
  };

  return (
    <div className={styles.channelAgentConfigurationAccount}>
      <Card title={t('Agent Sales Hierarchy Configuration')} bordered={false} className={styles.content}>
        <Skeleton loading={loading} active>
          <Form layout="vertical" form={form}>
            <Form.Item required shouldUpdate label={t('Agent Sales Hierarchy')} name="hierarchy">
              <Select className={styles.w240} disabled={!isEdit} onChange={handleSelect}>
                {selectOptions.map(item => (
                  <Select.Option value={item.dictValue} key={item.dictValue}>
                    {item.dictValueName}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Form>
          <EditableTable
            columns={columns}
            dataSource={dataSource}
            setDataSource={setDataSource}
            pagination={false}
            addBtnProps={{
              visible: false,
            }}
            deleteBtnProps={{
              visible: false,
            }}
            editBtnProps={{
              disabled: () => !isEdit,
            }}
            rowKey="id"
            addBtnLeftSection={t('Content')}
          />
        </Skeleton>
      </Card>
      <div className={styles.footer}>
        {hasEditAuth && (
          <>
            {isEdit ? (
              <>
                <Button className={styles.saveButton} onClick={handleCancel}>
                  {t('Cancel')}
                </Button>
                <Button className={styles.saveButton} type="primary" onClick={handleSubmit}>
                  {t('Submit')}
                </Button>
              </>
            ) : (
              <Button className={styles.saveButton} type="primary" onClick={() => setIsEdit(true)}>
                {t('Edit')}
              </Button>
            )}
          </>
        )}
      </div>
    </div>
  );
};
