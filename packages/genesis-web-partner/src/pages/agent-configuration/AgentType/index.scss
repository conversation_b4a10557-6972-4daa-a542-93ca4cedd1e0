@import '../../../variables';

.channel-agent-configuration-type {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .content {
    flex: 1;
    margin: 16px;
  }
  .footer {
    width: 100%;
    height: 72px;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .save-button {
      margin: 16px 16px 16px 0;
    }
  }

  .connector {
    margin: 0 8px;
    line-height: 12px;
    height: 32px;
  }
  .button {
    padding: 9px 16px;
  }

  .w220 {
    width: 220px;
  }

  .w60 {
    width: 60px;
  }
  .w280 {
    width: 280px;
  }
  .mr8 {
    margin-right: 8px;
  }
  .flexStart {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  :global {
    .#{$antd-prefix}-form-item
      .#{$antd-prefix}-input-group
      .#{$antd-prefix}-select {
      width: 220px;
    }
    .#{$antd-prefix}-input-group.#{$antd-prefix}-input-group-compact
      .#{$antd-prefix}-select:not(.#{$antd-prefix}-select-customize-input)
      .#{$antd-prefix}-select-selector {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}
