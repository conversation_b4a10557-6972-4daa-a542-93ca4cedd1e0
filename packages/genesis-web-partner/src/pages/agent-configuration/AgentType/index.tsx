import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON>, Card, message, Skeleton, Form } from 'antd';
import type { BizDictItem } from 'genesis-web-service';
import { MetadataService, YesOrNo } from 'genesis-web-service';
import { useState, useCallback, useEffect, useMemo } from 'react';
import { usePermission } from '@/hooks/usePermissions';
import { ModalConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import styles from './index.scss';
import { Select } from '@zhongan/nagrand-ui';

export const AgentType = () => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const [selectedAgentTypeConfig, setSelectedAgentTypeConfig] =
    useState<string>(); // 用来恢复默认配置
  const [whetherToEnableAgentTypeEnums, setWhetherToEnableAgentTypeEnums] =
    useState<BizDictItem[]>([]);
  const [currentSelectedAgentType, setCurrentSelectedAgentType] =
    useState<string>(); // 动态选择 用来切换表单布局
  const [editedDisabled, setEditedDisabled] = useState(false);
  const [formLabel, setFormLabel] = useState('');
  const [isEdit, setIsEdit] = useState(false);
  const [loading, setLoading] = useState(false);
  const { setFieldsValue, validateFields } = form;
  const hasEditAuth = usePermission('configuration.agent.edit');

  const queryWhetherToEnableAgentTypeConfig = useCallback(() => {
    setLoading(true);
    MetadataService.queryBizDictConfigList('whetherToEnableAgentType')
      .then(res => {
        setWhetherToEnableAgentTypeEnums(
          res?.commonBizDictConfig?.whetherToEnableAgentType
            ?.bizDictConfigList ?? [],
        );
        const responseTenantHistoryConfig =
          res?.tenantBizDictConfig?.whetherToEnableAgentType
            ?.bizDictConfigList?.[0]?.dictValueName;
        // 默认值 用来取消保存时恢复数据使用
        setSelectedAgentTypeConfig(responseTenantHistoryConfig);
        setCurrentSelectedAgentType(responseTenantHistoryConfig);
        setFieldsValue({ tenantConfig: responseTenantHistoryConfig });
        setFormLabel(res?.dictKeyI18nMap?.whetherToEnableAgentType);
        // 如果返回的有默认值说明之前已经保存过值  那么就不允许编辑了
        if (responseTenantHistoryConfig) {
          setEditedDisabled(true);
        } else {
          setEditedDisabled(false);
        }
      })
      .catch((error: Error) => {
        message.error(error.message);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [setFieldsValue]);

  useEffect(() => {
    queryWhetherToEnableAgentTypeConfig();
  }, [queryWhetherToEnableAgentTypeConfig]);

  const handleSubmit = useCallback(() => {
    validateFields().then(values => {
      const params = {
        bizDictConfigs: {
          whetherToEnableAgentType: {
            selectFlag: YesOrNo.NO,
            tenantCustomized: YesOrNo.NO,
            tenantBizDictConfigValues: whetherToEnableAgentTypeEnums?.filter(
              whetherToEnableAgentType =>
                whetherToEnableAgentType?.dictValueName ===
                values?.tenantConfig,
            ),
          },
        },
      };
      MetadataService.updateBizDictData(params)
        .then(() => {
          message.success(t('Save Success'));
          // 重新获取页面初始化配置
          queryWhetherToEnableAgentTypeConfig();
          setEditedDisabled(true);
          setIsEdit(false);
        })
        .catch((error: Error) => {
          message.error(error.message);
        });
    });
  }, [
    validateFields,
    whetherToEnableAgentTypeEnums,
    queryWhetherToEnableAgentTypeConfig,
  ]);

  const cancelEdit = useCallback(() => {
    setIsEdit(false);
    setFieldsValue({
      tenantConfig: selectedAgentTypeConfig,
    });
    setCurrentSelectedAgentType(selectedAgentTypeConfig);
  }, [selectedAgentTypeConfig, setFieldsValue]);

  // 根据是否为禁用状态判断当前是否处于edit状态
  const actionButton = useMemo(() => {
    // 通过判断是否有编辑权限 如果没有不展示保存按钮
    if (!hasEditAuth) {
      return null;
    }
    if (isEdit) {
      return (
        <>
          <Button
            className={styles.saveButton}
            disabled={editedDisabled}
            onClick={() => cancelEdit()}
          >
            {t('Cancel')}
          </Button>
          <ModalConfirm
            title={t('Confirm')}
            content={t(
              'After the agent type is configured, it can not be modified. Please confirm.',
            )}
            okText={t('Submit')}
            onOk={handleSubmit}
          >
            <Button
              type="primary"
              className={styles.saveButton}
              disabled={editedDisabled}
            >
              {t('Submit')}
            </Button>
          </ModalConfirm>
        </>
      );
    }
    // 根据是否有默认值来判断是否允许保存
    return (
      <Button
        type="primary"
        className={styles.saveButton}
        disabled={editedDisabled}
        onClick={() => setIsEdit(true)}
      >
        {t('Edit')}
      </Button>
    );
  }, [editedDisabled, cancelEdit, handleSubmit, hasEditAuth, isEdit, t]);

  const formContent = useMemo(() => {
    return (
      <Form.Item
        required
        label={<span className={styles.mr8}>{formLabel}</span>}
      >
        <div className={styles.flexStart}>
          <Form.Item
            name="tenantConfig"
            rules={[
              {
                required: true,
                message: t('Please Select', {
                  ns: 'partner',
                }),
              },
            ]}
          >
            <Select
              className={styles.w280}
              disabled={editedDisabled || !isEdit}
              onChange={element =>
                setCurrentSelectedAgentType(element as string)
              }
            >
              {whetherToEnableAgentTypeEnums?.map(whetherToEnableAgentType => (
                <Select.Option
                  value={whetherToEnableAgentType?.dictValueName}
                  key={whetherToEnableAgentType?.id}
                >
                  {whetherToEnableAgentType?.dictValueName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </div>
      </Form.Item>
    );
  }, [formLabel, currentSelectedAgentType, editedDisabled, t, isEdit]);

  return (
    <div className={styles.channelAgentConfigurationType}>
      <Card
        title={t('Agent Type Configuration')}
        bordered={false}
        className={styles.content}
      >
        <Skeleton loading={loading} active>
          <Form layout="vertical" form={form}>
            {formContent}
          </Form>
        </Skeleton>
      </Card>
      <div className={styles.footer}>{actionButton}</div>
    </div>
  );
};
