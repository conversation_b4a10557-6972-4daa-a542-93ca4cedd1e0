import { useTranslation } from 'react-i18next';
import { Button, Card, message, Input, Skeleton, Form, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import type { BizDictItem } from 'genesis-web-service';
import { MetadataService, YesOrNo } from 'genesis-web-service';
import { useState, useCallback, useEffect, useMemo } from 'react';
import { usePermission } from '@/hooks/usePermissions';
import { ModalConfirm } from 'genesis-web-component/lib/components/ModalConfirm';
import styles from './index.scss';
import { Select } from '@zhongan/nagrand-ui';

const DecideMonthly = 'MONTHLY';

export const Commission = () => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const [selectedCommissionBasisConfig, setSelectedCommissionBasisConfig] =
    useState<string>(); // 用来恢复默认配置
  const [currentSelectedCommissionBasis, setCurrentSelectedCommissionBasis] =
    useState<string>(); // 动态选择 用来切换表单布局
  const [selectedCommissionDayConfig, setSelectedCommissionDayConfig] =
    useState<string>(); // 用来恢复默认配置
  const [selectedStrategy, setSelectedStrategy] = useState<string>(); // 用来恢复默认配置
  const [commissionCalculationBasisEnums, setCommissionCalculationBasisEnums] =
    useState<BizDictItem[]>([]);
  const [commissionCalculationDayEnums, setCommissionCalculationDayEnums] =
    useState<BizDictItem[]>([]);
  const [
    historicalCommissionClawbackStrategyEnums,
    setHistoricalCommissionClawbackStrategyEnums,
  ] = useState<BizDictItem[]>([]);
  const [editedDisabled, setEditedDisabled] = useState(false);
  const [formLabel, setFormLabel] = useState('');
  const [historicalCommissionFormLabel, setHistoricalCommissionFormLabel] =
    useState('');
  const [monthlyTitle, setMonthlyTitle] = useState(''); // 为Monthly的时候日期选择的title
  const [isEdit, setIsEdit] = useState(false);
  const [loading, setLoading] = useState(false);
  const { setFieldsValue, validateFields } = form;
  const hasEditAuth = usePermission('configuration.agent.edit');

  const queryCommissionCalculationBasisConfig = useCallback(() => {
    setLoading(true);
    MetadataService.queryBizDictConfigList('commissinCalculationBasis')
      .then(res => {
        setCommissionCalculationBasisEnums(
          res?.commonBizDictConfig?.commissinCalculationBasis
            ?.bizDictConfigList ?? [],
        );
        const responseTenantHistoryConfig =
          res?.tenantBizDictConfig?.commissinCalculationBasis
            ?.bizDictConfigList?.[0]?.dictValueName;
        // 默认值 用来取消保存时恢复数据使用
        setSelectedCommissionBasisConfig(responseTenantHistoryConfig);
        setCurrentSelectedCommissionBasis(responseTenantHistoryConfig);
        setFieldsValue({ tenantConfig: responseTenantHistoryConfig });
        setFormLabel(res?.dictKeyI18nMap?.commissinCalculationBasis);
        // 如果返回的有默认值说明之前已经保存过值  那么就不允许编辑了
        if (responseTenantHistoryConfig) {
          setEditedDisabled(true);
        } else {
          setEditedDisabled(false);
        }
      })
      .catch((error: Error) => {
        message.error(error.message);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [setFieldsValue]);

  // 获取days
  const queryCommissionCalculationDayConfig = useCallback(() => {
    setLoading(true);
    MetadataService.queryBizDictConfigList('commissinCalculationDay')
      .then(res => {
        setCommissionCalculationDayEnums(
          res?.commonBizDictConfig?.commissinCalculationDay?.bizDictConfigList?.sort(
            (prevDay, nextDay) => prevDay?.orderNo - nextDay?.orderNo,
          ) ?? [],
        );
        const responseTenantHistoryConfigSelectedDay =
          res?.tenantBizDictConfig?.commissinCalculationDay
            ?.bizDictConfigList?.[0]?.dictValueName;
        // 默认值 用来取消保存时恢复数据使用
        setSelectedCommissionDayConfig(responseTenantHistoryConfigSelectedDay);
        setFieldsValue({ day: responseTenantHistoryConfigSelectedDay });
        setMonthlyTitle(res?.dictKeyI18nMap?.commissinCalculationDay);
      })
      .catch((error: Error) => {
        message.error(error.message);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [setFieldsValue]);

  const queryHistoricalCommissionClawbackStrategyConfig = useCallback(() => {
    setLoading(true);
    MetadataService.queryBizDictConfigList(
      'historicalCommissionClawbackStrategy',
    )
      .then(res => {
        setHistoricalCommissionClawbackStrategyEnums(
          res?.commonBizDictConfig?.historicalCommissionClawbackStrategy
            ?.bizDictConfigList ?? [],
        );
        setHistoricalCommissionFormLabel(
          res?.dictKeyI18nMap?.historicalCommissionClawbackStrategy,
        );
        const responseTenantHistoryConfigSelectedStrategy =
          res?.tenantBizDictConfig?.historicalCommissionClawbackStrategy
            ?.bizDictConfigList?.[0]?.dictValueName;

        // 默认值 用来取消保存时恢复数据使用
        setSelectedStrategy(responseTenantHistoryConfigSelectedStrategy);

        setFieldsValue({
          strategy: responseTenantHistoryConfigSelectedStrategy,
        });
      })
      .catch((error: Error) => {
        message.error(error.message);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [setFieldsValue]);

  useEffect(() => {
    queryCommissionCalculationBasisConfig();
    queryCommissionCalculationDayConfig();
    queryHistoricalCommissionClawbackStrategyConfig();
  }, [
    queryCommissionCalculationBasisConfig,
    queryCommissionCalculationDayConfig,
    queryHistoricalCommissionClawbackStrategyConfig,
  ]);

  const handleSubmit = useCallback(() => {
    validateFields().then(values => {
      const params = {
        bizDictConfigs: {
          commissinCalculationBasis: {
            selectFlag: YesOrNo.NO,
            tenantCustomized: YesOrNo.NO,
            tenantBizDictConfigValues: commissionCalculationBasisEnums?.filter(
              commissinCalculationBasis =>
                commissinCalculationBasis?.dictValueName ===
                values?.tenantConfig,
            ),
          },
          commissinCalculationDay: {
            selectFlag: YesOrNo.NO,
            tenantCustomized: YesOrNo.NO,
            tenantBizDictConfigValues: commissionCalculationDayEnums?.filter(
              day => day?.dictValueName === values?.day,
            ),
          },
          historicalCommissionClawbackStrategy: {
            selectFlag: YesOrNo.NO,
            tenantCustomized: YesOrNo.NO,
            tenantBizDictConfigValues:
              historicalCommissionClawbackStrategyEnums?.filter(
                strategy => strategy?.dictValueName === values?.strategy,
              ),
          },
        },
      };
      if (values?.tenantConfig !== DecideMonthly) {
        delete params.bizDictConfigs.commissinCalculationDay;
      }
      MetadataService.updateBizDictData(params)
        .then(() => {
          message.success(t('Save Success'));
          // 重新获取页面初始化配置
          queryCommissionCalculationBasisConfig();
          queryCommissionCalculationDayConfig();
          setEditedDisabled(true);
          setIsEdit(false);
        })
        .catch((error: Error) => {
          message.error(error.message);
        });
    });
  }, [
    validateFields,
    commissionCalculationBasisEnums,
    commissionCalculationDayEnums,
    historicalCommissionClawbackStrategyEnums,
    queryCommissionCalculationBasisConfig,
    queryCommissionCalculationDayConfig,
  ]);

  const cancelEdit = useCallback(() => {
    setIsEdit(false);
    setFieldsValue({
      tenantConfig: selectedCommissionBasisConfig,
      day: selectedCommissionDayConfig,
      strategy: selectedStrategy,
    });
    setCurrentSelectedCommissionBasis(selectedCommissionBasisConfig);
  }, [
    selectedCommissionBasisConfig,
    selectedCommissionDayConfig,
    selectedStrategy,
    setFieldsValue,
  ]);

  // 根据是否为禁用状态判断当前是否处于edit状态
  const actionButton = useMemo(() => {
    // 通过判断是否有编辑权限 如果没有不展示保存按钮
    if (!hasEditAuth) {
      return null;
    }
    if (isEdit) {
      return (
        <>
          <Button className={styles.saveButton} onClick={() => cancelEdit()}>
            {t('Cancel')}
          </Button>
          <ModalConfirm
            title={t('Confirm')}
            content={t(
              'After the commission/commission clawback calculation range is configured, it cannot be modified. Please confirm.',
            )}
            okText={t('Submit')}
            onOk={handleSubmit}
          >
            <Button type="primary" className={styles.saveButton}>
              {t('Submit')}
            </Button>
          </ModalConfirm>
        </>
      );
    }
    // 根据是否有默认值来判断是否允许保存
    return (
      <Button
        type="primary"
        className={styles.saveButton}
        onClick={() => setIsEdit(true)}
      >
        {t('Edit')}
      </Button>
    );
  }, [cancelEdit, handleSubmit, hasEditAuth, isEdit, t]);

  const formContent = useMemo(() => {
    const isSelectedMonthly =
      currentSelectedCommissionBasis ===
      commissionCalculationBasisEnums?.find(
        item => item.enumItemName === DecideMonthly,
      )?.dictValueName;
    return (
      <>
        <Form.Item
          required
          label={
            <>
              <span className={styles.mr8}>{formLabel}</span>
              {isSelectedMonthly && (
                <Tooltip
                  title={t(
                    'If some months do not have this date, the configured date automatically becomes the last day of the month.',
                  )}
                >
                  <QuestionCircleOutlined />
                </Tooltip>
              )}
            </>
          }
        >
          <div className={styles.flexStart}>
            <Form.Item
              name="tenantConfig"
              rules={[
                {
                  required: true,
                  message: t('Please Select', {
                    ns: 'partner',
                  }),
                },
              ]}
            >
              <Select
                className={styles.w280}
                disabled={editedDisabled || !isEdit}
                onChange={element =>
                  setCurrentSelectedCommissionBasis(element as string)
                }
              >
                {commissionCalculationBasisEnums?.map(
                  commissinCalculationBasis => (
                    <Select.Option
                      value={commissinCalculationBasis?.dictValueName}
                      key={commissinCalculationBasis?.id}
                    >
                      {commissinCalculationBasis?.dictValueName}
                    </Select.Option>
                  ),
                )}
              </Select>
            </Form.Item>
            {isSelectedMonthly && (
              <>
                <span className={styles.connector}>-</span>
                <Input.Group compact className={styles.w290}>
                  <Input className={styles.w60} value={monthlyTitle} disabled />
                  <Form.Item
                    name="day"
                    rules={[
                      {
                        required: true,
                        message: t('Please select a specific date first.'),
                      },
                    ]}
                  >
                    <Select
                      className={styles.w220}
                      disabled={editedDisabled || !isEdit}
                    >
                      {commissionCalculationDayEnums?.map(dictMonthsEnum => (
                        <Select.Option
                          value={dictMonthsEnum?.dictValueName}
                          key={dictMonthsEnum?.id}
                        >
                          {dictMonthsEnum?.dictValueName}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Input.Group>
              </>
            )}
          </div>
        </Form.Item>

        <Form.Item
          label={historicalCommissionFormLabel}
          name="strategy"
          rules={[
            {
              required: true,
              message: t('Please Select', {
                ns: 'partner',
              }),
            },
          ]}
        >
          <Select className={styles.w280} disabled={!isEdit}>
            {historicalCommissionClawbackStrategyEnums?.map(strategy => (
              <Select.Option value={strategy?.dictValueName} key={strategy?.id}>
                {strategy?.dictValueName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </>
    );
  }, [
    formLabel,
    historicalCommissionFormLabel,
    currentSelectedCommissionBasis,
    editedDisabled,
    monthlyTitle,
    commissionCalculationBasisEnums,
    t,
    commissionCalculationDayEnums,
    isEdit,
  ]);

  return (
    <div className={styles.channelAgentConfigurationCommission}>
      <Card
        title={t('Commission&Commission Clawback Rule Configuration')}
        bordered={false}
        className={styles.content}
      >
        <Skeleton loading={loading} active>
          <Form layout="vertical" form={form}>
            {formContent}
          </Form>
        </Skeleton>
      </Card>
      <div className={styles.footer}>{actionButton}</div>
    </div>
  );
};
