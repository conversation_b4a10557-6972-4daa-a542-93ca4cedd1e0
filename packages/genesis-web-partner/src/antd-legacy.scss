@import './variables.scss';

$antd-legacy-form-prefix: #{$antd-prefix}-legacy-form;

@mixin legacyFormItem() {
  :global {
    .#{$antd-prefix}-legacy-form-item {
      display: inline-block;
      margin-bottom: 0;

      &:last-child:not(:first-child) {
        margin-bottom: 0;
      }
    }

    .#{$antd-prefix}-legacy-form-item-control {
      line-height: 32px;
    }
  }
}

#partner {
  .block-form-item-row {
    @include legacyFormItem();
    margin-bottom: 16px;

    &:last-of-type {
      margin-bottom: 0;
    }
  }

  .block-form-item {
    &:global(.#{$antd-legacy-form-prefix}-item) {
      display: inline-block;
    }
  }

  :global {
    .#{$antd-legacy-form-prefix}-item {
      -webkit-font-feature-settings: 'tnum';
      font-feature-settings: 'tnum';
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      color: var(--text-color);
      font-size: 14px;
      font-variant: tabular-nums;
      line-height: 1.5;
      list-style: none;
      margin: 0 0 24px;
      padding: 0;
      vertical-align: top;
    }

    .#{$antd-legacy-form-prefix}-item-label
      + .#{$antd-legacy-form-prefix}-item-control-wrapper {
      margin-top: 8px;
    }

    .#{$antd-legacy-form-prefix}-item-required:before {
      color: var(--error-color);
      content: '*';
      display: inline-block;
      font-size: 14px;
      line-height: 1;
      margin-right: 4px;
    }

    .#{$antd-legacy-form-prefix}-item-control.has-error
      .#{$antd-legacy-form-prefix}-explain {
      color: var(--error-color);
    }

    .#{$antd-prefix}-tabs-nav {
      margin: 0;
    }
  }
}
