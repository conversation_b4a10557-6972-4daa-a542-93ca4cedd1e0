import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { useDebounceFn } from 'ahooks';
import { Col, Form, Input, Radio } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import { debounce } from 'lodash-es';

import type { BizDictItem } from 'genesis-web-service';
import { ChannelCustomerStatus, ChannelService, YesOrNo } from 'genesis-web-service';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import { useAuthUserInfo } from '@/hooks/useUserInfo';

import styles from './index.scss';

interface Props {
  propForm: FormInstance;
  initialValue: any;
  disabled: boolean;
  relyOn: string;
  name: string;
}

/**
 *
 * @param propForm
 * @param initialValue
 * @param disabled
 * @param relyOn
 * @description account模块
 */
export const Account = ({ propForm, initialValue, disabled, relyOn, name }: Props) => {
  const { t } = useTranslation('partner');
  const createValue = Form.useWatch('isCreateAccount', propForm);
  const nameValue = Form.useWatch(relyOn, propForm);
  const accountValue = Form.useWatch('accountUserName', propForm);
  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;
  const userInfo = useAuthUserInfo();

  const { run: handleAccount } = useDebounceFn(val => {
    if (!accountValue) {
      // 回填后如果删除 accountUserName，控件校验是失败状态， 然后在输入 name，此时还会保存控件校验失败的状态
      // 使用resetFields 让控件重新挂载下
      propForm.resetFields(['accountUserName']);
      propForm.setFieldValue('accountUserName', val);
    }
  });

  useEffect(() => {
    handleAccount(nameValue);
  }, [nameValue]);

  const validateAccount = useMemo(() => {
    return debounce((val: string, callback: any) => {
      if (!val) {
        callback();
      } else {
        ChannelService.checkIamAccount({ iamUserName: val })
          .then(() => {
            callback();
          })
          .catch((error: Error) => {
            callback(error || '');
          });
      }
    }, 1000);
  }, []);

  return (
    <Col span={17}>
      {initialValue?.userId ? (
        <Form.Item
          initialValue={initialValue?.accountStatus || ChannelCustomerStatus.Effective}
          name="accountStatus"
          label={<b>{t('{{name}} Account Status', { name })}</b>}
        >
          <Radio.Group disabled={disabled}>
            <Radio value={ChannelCustomerStatus.Effective}>{t('Effective')}</Radio>
            <Radio value={ChannelCustomerStatus.Frozen}>{t('Frozen')}</Radio>
          </Radio.Group>
        </Form.Item>
      ) : (
        <div className={styles.accountSection}>
          <Form.Item
            initialValue={initialValue?.isCreateAccount || YesOrNo.NO}
            name="isCreateAccount"
            label={<b>{t('Whether to create {{name}} account?', name)}</b>}
          >
            <Radio.Group disabled={disabled}>
              {enums?.yesNo?.map(option => (
                <Radio value={option.value} key={option.value}>
                  {option.label}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
          {createValue === YesOrNo.YES && (
            <Form.Item
              initialValue={initialValue?.accountUserName || nameValue}
              name="accountUserName"
              label={t('{{name}} Account', { name })}
              hasFeedback
              rules={[
                {
                  required: true,
                },
                {
                  validator: (_, value, callback) => validateAccount(value, callback),
                },
              ]}
            >
              <Input disabled={disabled} addonAfter={`@${userInfo.tenant}.com`} />
            </Form.Item>
          )}
        </div>
      )}
    </Col>
  );
};
