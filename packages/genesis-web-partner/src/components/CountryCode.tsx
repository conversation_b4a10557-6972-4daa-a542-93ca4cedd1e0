import type { CSSProperties } from 'react';
import { useTranslation } from 'react-i18next';

import { Select } from '@zhongan/nagrand-ui';

import type { BizDictItem } from 'genesis-web-service';

import { useTenantBizDict } from '@/hooks/useTenantBizDict';

interface Props {
  disabled?: boolean;
  style?: CSSProperties;
}

/**
 *
 * @param disabled 是否禁用
 * @param style 样式
 * @description 国家码
 */
export const CountryCode = ({ disabled, style, ...restProps }: Props) => {
  const { t } = useTranslation('partner');
  const countryEnums = useTenantBizDict('country') as BizDictItem[];

  return (
    <Select
      showSearch
      allowClear
      disabled={disabled}
      optionLabelProp="label"
      dropdownMatchSelectWidth={false}
      placeholder={t('Please Select')}
      style={{ ...style }}
      getPopupContainer={triggerElement => triggerElement?.parentElement}
      {...restProps}
    >
      {countryEnums?.map(item => (
        <Select.Option
          value={item.itemExtend2}
          key={`${item.dictValueName}_${item.itemExtend2 ?? ''}`}
          label={item.itemExtend2}
          title={`${item.dictValueName}${item.itemExtend2 ?? ''}`}
        >
          <p style={{ display: 'flex' }}>
            {item.dictValueName}
            <span style={{ flex: 1, textAlign: 'right' }}>{item.itemExtend2 ?? ''}</span>
          </p>
        </Select.Option>
      ))}
    </Select>
  );
};
