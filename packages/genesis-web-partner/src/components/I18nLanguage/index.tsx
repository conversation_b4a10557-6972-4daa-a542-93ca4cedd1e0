import type { ChangeEvent } from 'react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Input, Tooltip } from 'antd';

import { useRequest } from 'ahooks';

import { Icon } from '@zhongan/nagrand-ui';

import { NewMetadataService } from '@/services/metadata/metadata.service.new';

import { I18nLanguageDrawer } from './I18nLanguageDrawer';
import styles from './index.module.scss';

export const I18nLanguage = ({
  disabled = false,
  label,
  name,
  required = true,
  onChange,
  className,
}: {
  disabled?: boolean;
  label: string;
  name: string;
  required?: boolean;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  className?: string;
}) => {
  const { t } = useTranslation('partner');

  const [visible, setVisible] = useState(false);

  const { data } = useRequest(() =>
    NewMetadataService.TenantBizDictConfigShareService.queryBizDictTenant({
      dictKeys: ['language'],
    }),
  );
  const form = Form.useFormInstance();
  const i18nInfoArr = Form.useWatch(`${name}I18n`, form);

  const saveLanguageInfo = (info: Record<string, string>) => {
    form.setFieldsValue({ [`${name}I18n`]: info });
  };

  return (
    <div className={styles.language}>
      <Form.Item
        label={label}
        name={name}
        rules={required ? [{ required: true, message: t('Please Input') }] : []}
      >
        <Input
          className={className}
          disabled={disabled}
          placeholder={t('Please Input')}
          addonAfter={
            <Tooltip placement="top" title={t('Multilingual')}>
              <Icon
                type="globe"
                style={{
                  fontSize: '16x',
                }}
                onClick={() => setVisible(true)}
              />
            </Tooltip>
          }
          onChange={onChange}
        />
      </Form.Item>
      <Form.Item noStyle name={`${name}I18n`} />
      {visible ? (
        <I18nLanguageDrawer
          options={data?.map(item => ({
            label: item.dictValueName,
            value: item.itemExtend1,
          }))}
          visible={visible}
          setVisible={setVisible}
          saveLanguageInfo={saveLanguageInfo}
          initData={i18nInfoArr || []}
          disabled={disabled}
        />
      ) : null}
    </div>
  );
};
