import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Input } from 'antd';

import { Drawer, Select } from '@zhongan/nagrand-ui';

import styles from './I18nLanguageDrawer.module.scss';

interface SelectOption {
  label: string;
  value: string;
}

interface Props {
  visible: boolean;
  options: SelectOption[];
  initData: Record<string, string>;
  disabled?: boolean;
  setVisible: (arg: boolean) => void;
  saveLanguageInfo: (obj: Record<string, string>) => void;
}
export const I18nLanguageDrawer: React.FC<Props> = ({
  visible = false,
  options = [],
  initData,
  disabled = false,
  setVisible,
  saveLanguageInfo,
}) => {
  const [t] = useTranslation(['query', 'common']);

  const [i18nLanguage, setI18nLanguage] = useState<Record<string, string>>({});

  useEffect(() => {
    setI18nLanguage(Object.fromEntries(options?.map(item => [item.value, initData?.[item.value]])));
  }, [initData, options]);

  const onClose = useCallback(() => {
    setVisible(false);
    setI18nLanguage({});
  }, [setVisible]);

  const onConfirmSubmit = useCallback(() => {
    saveLanguageInfo(Object.fromEntries(Object.entries(i18nLanguage).filter(([_, value]) => value)));
    onClose();
  }, [i18nLanguage, onClose, saveLanguageInfo, t]);

  return (
    <Drawer
      title={t('Multilingual')}
      size="small"
      onClose={onClose}
      open={visible}
      className={styles.languageDrawer}
      readonly={disabled}
      onSubmit={onConfirmSubmit}
      submitBtnProps={{ disabled: disabled }}
    >
      <Form.Item label={t('Language')} className="mb-2" />
      {Object.entries(i18nLanguage).map(([key, value]) => (
        <Input
          key={key}
          className="block mb-[8px]"
          value={value}
          placeholder={t('Please input')}
          disabled={disabled}
          onChange={event => {
            i18nLanguage[key] = event.target.value;
            setI18nLanguage({ ...i18nLanguage });
          }}
          addonBefore={
            <Select
              className="text-left w-[280px]"
              value={key}
              placeholder={t('Please select')}
              options={options}
              disabled
            />
          }
        />
      ))}
    </Drawer>
  );
};
