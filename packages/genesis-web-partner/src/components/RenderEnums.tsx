import { useEffect, useState } from 'react';

import type { BizDictItem } from 'genesis-web-service';

interface RenderEnumsProp {
  keyName: string;
  enums: BizDictItem[];
}

export const RenderEnums = ({ keyName, enums }: RenderEnumsProp) => {
  const [mappedEnums, setMappedEnums] = useState<Record<string, string>>({});

  useEffect(() => {
    if (!enums) {
      return;
    }

    setMappedEnums(
      enums.reduce(
        (out, item) => ({
          ...out,
          [item.enumItemName || item.itemExtend1 || item.dictValue]: item.dictValueName,
        }),
        {}
      )
    );
  }, [enums]);

  if (!keyName) {
    // 直接返回undefined当keyName无值时会报错
    return <></>;
  }
  return <>{mappedEnums[keyName] || keyName}</>;
};

/**
 * @description 根据Key渲染枚举Name
 * @param keyName 枚举值
 * @param enums metadata枚举数组
 * @param valueKey 自定义映射的枚举key,无需fallback逻辑,主要使用在枚举下拉选项的回显
 */
export const renderEnumsWithString = (
  keyName: string,
  enums: BizDictItem[] = [],
  valueKey?: keyof Pick<BizDictItem, 'enumItemName' | 'dictValue' | 'itemExtend1'>
) => {
  if (!keyName) {
    return undefined;
  }

  const output: Record<string, string> = {};
  for (const item of enums) {
    if (valueKey) {
      output[item[valueKey]] = item.dictValueName;
    } else {
      output[item.enumItemName || item.itemExtend1 || item.dictValue] = item.dictValueName;
    }
  }

  return output?.[keyName] || keyName;
};
