@import '@/variables.scss';
.status-tag-content {
  .tag {
    padding: 3px 10px;
    font-size: 14px;
  }
  .has-border {
    border-color: var(--border-default);
  }

  .default {
    color: var(--text-color-quaternary);
  }

  .active {
    color: var(--success-color-text-dark);
  }

  .regular {
    color: var(--info-color-text-dark);
  }

  .error {
    color: var(--error-color-text-dark);
  }

  .circle {
    border-radius: 50%;
  }
  .round {
    border-radius: 50vh;
  }

  .dot {
    display: inline-block;
    padding: 4px;
    margin-right: 5px;
    border-radius: 100%;
  }
}
