/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-09-04 17:33:07
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-09-05 14:17:38
 * @Description:
 */
import type { CSSProperties } from 'react';
import React from 'react';
import type { TagProps } from 'antd';
import { Tag } from 'antd';
import styles from './style.scss';
import clxs from 'classnames';
import { TagConfigs, TagsShape, TagsType } from '../../utils/constants';

interface PropsPramas extends TagProps {
  shape?: TagsShape;
  dotColor?: string;
  hasDot?: boolean;
  type?: TagsType;
  hasborder?: boolean;
}
/**
 * @param shape 【可选】边框形状 ---> 无圆角 ｜ 圆 ｜ 圆角-20px 默认无圆角
 * @param dotColor 【可选】圆点颜色
 * @param hasDot 【可选】是否展示原点，默认展示
 * @param type 【可选】定制的主题色，灰色 ｜ 绿色
 * @param hasborder 【可选】是否有border，默认有
 */
export const StatusTag: React.FC<PropsPramas> = pramas => {
  const {
    shape = TagsShape.Default,
    dotColor,
    hasDot = true,
    type = TagsType.Default,
    hasborder = true,
  } = pramas;

  const dotStyle: CSSProperties = {
    background: dotColor ?? TagConfigs[type].dotColor,
  };

  return (
    <div className={styles.statusTagContent}>
      <Tag
        {...pramas}
        color={pramas.color ?? TagConfigs[type].color}
        className={clxs(
          styles.tag,
          styles[shape],
          styles[type],
          hasborder ? styles.hasborder : '',
        )}
      >
        {hasDot && <span className={styles.dot} style={dotStyle} />}
        {pramas.children}
      </Tag>
    </div>
  );
};
