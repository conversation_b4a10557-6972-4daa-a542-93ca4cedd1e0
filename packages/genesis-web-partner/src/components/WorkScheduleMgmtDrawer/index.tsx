import { useCallback, useMemo, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import { DownOutlined, DownloadOutlined, SyncOutlined, UpOutlined } from '@ant-design/icons';

import { useRequest } from 'ahooks';
import { Skeleton, Tooltip, Upload, message } from 'antd';
import type { ColumnProps } from 'antd/lib/table';
import type { RcFile, UploadProps } from 'antd/lib/upload';
import { v4 as uuidV4 } from 'uuid';

import { Table } from '@zhongan/nagrand-ui';

import { ComponentWithFallback } from 'genesis-web-component/lib/components';
import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import { FileItem } from 'genesis-web-component/lib/components/FileItem';
import type { WorkScheduleExt, YearlyWorkScheduleResp } from 'genesis-web-service';
import { ChannelService, DownloadOrUploadType, ExtTemplate } from 'genesis-web-service';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { MultipleUploadOutline } from '@/components/Icons';
import { PaginationComponent } from '@/components/Pagination';
import { StatusTag } from '@/components/StatusTag';
import { WorkScheduleType } from '@/types/common-party';
import { DefaultTablePagination, TagsShape, TagsType } from '@/utils/constants';

import styles from './index.scss';

const AcceptFileTypeList = ['.xls', '.xlsx'];

const DownloadOrUploadTypeMap = {
  [WorkScheduleType.AssessmentCompany]: DownloadOrUploadType.StaffSchedule,
  [WorkScheduleType.Investigator]: DownloadOrUploadType.StaffSchedule,
  [WorkScheduleType.EmployeeManagement]: DownloadOrUploadType.EmployeeSchedule,
};

const QueryHistoryYearlyServiceMap = {
  [WorkScheduleType.AssessmentCompany]: ChannelService.queryStaffHistoryYearly,
  [WorkScheduleType.Investigator]: ChannelService.queryStaffHistoryYearly,
  [WorkScheduleType.EmployeeManagement]: ChannelService.queryEmployeeHistoryYearly,
};

interface Props {
  id: number;
  year: string;
  visible: boolean;
  readonly?: boolean;
  type: WorkScheduleType;
  onClose: (updated?: boolean) => void;
}

/**
 *
 * @param id 当前id
 * @param year 当前选择的年份
 * @param visible 是否可见
 * @param readonly 是否只读
 * @param onClose 关闭drawer
 * @description 具体年度的排班总表drawer
 */
export const WorkScheduleManagementDrawer = ({ id, year, visible, readonly, type, onClose }: Props) => {
  const { t } = useTranslation('partner');
  const [uploading, setUploading] = useState(false);
  const [curFile, setCurFile] = useState<RcFile | YearlyWorkScheduleResp>();
  const [uploadHistoryVisible, setUploadHistoryVisible] = useState(false);
  const [pagination, setPagination] = useState(DefaultTablePagination);
  const [reuploaded, setReuploaded] = useState(false); // 是否重新上传了文件，用来控制是否能点击submit

  const UploadStatusMap: Record<string, Record<string, string | TagsType>> = {
    PROGRESS: {
      name: t('In Progress', { ns: 'partner' }),
      type: TagsType.Regular,
    },
    COMPLETED: {
      name: t('Completed', { ns: 'partner' }),
      type: TagsType.Active,
    },
    ERROR: {
      name: t('Error', { ns: 'partner' }),
      type: TagsType.Error,
    },
  };

  // 初始化获取upload records
  const { data: uploadRecords, loading } = useRequest(
    () => {
      if (visible && id && year) {
        const paginationParams = {
          pageIndex: pagination?.current - 1,
          pageSize: pagination?.pageSize,
        };

        return QueryHistoryYearlyServiceMap[type](
          id,
          year,
          [WorkScheduleType.AssessmentCompany, WorkScheduleType.Investigator].includes(type)
            ? { ...paginationParams, instituteType: type }
            : paginationParams
        );
      }
    },
    {
      refreshDeps: [id, year, visible, pagination],
      onSuccess: res => {
        if (!curFile) {
          // 切换页码不重新设置current work schedule
          setCurFile(res?.data?.[0]);
        }
      },
      onError: (error: Error) => message.error(error?.message),
    }
  );

  const onCloseDrawer = useCallback(
    (updated?: boolean) => {
      setCurFile(undefined);
      setUploadHistoryVisible(false);
      setPagination(DefaultTablePagination);
      setReuploaded(false);
      onClose(updated);
    },
    [onClose]
  );

  // 提交
  const onSubmit = useCallback(() => {
    // upload query 处理
    const formData = new FormData();
    formData.append('file', curFile as RcFile);
    setUploading(true);
    const extParams = [WorkScheduleType.AssessmentCompany, WorkScheduleType.Investigator].includes(type)
      ? { year, instituteType: type }
      : { year };

    ChannelService.channelUpload(formData, {
      id,
      type: DownloadOrUploadTypeMap[type],
      ext: JSON.stringify(extParams),
    })
      .then(() => {
        message.success(t('Your file has been submitted. Please wait patiently for the result.'));
        onCloseDrawer(true);
      })
      .catch((error: Error) => message.error(error?.message || t('Uploaded failed')))
      .finally(() => setUploading(false));
  }, [curFile, id, year, type, onCloseDrawer, t]);

  const uploadProps: UploadProps = useMemo(
    () => ({
      name: 'file',
      accept: AcceptFileTypeList.join(', '),
      showUploadList: false,
      beforeUpload: (file: RcFile) => {
        const fileType = file.name.split('.').pop();
        if (AcceptFileTypeList.indexOf(`.${fileType}`) === -1) {
          message.error(t('Upload Type Limit', { type: 'XLS/XLSX' }));
          return false;
        }
        const size = file.size / 1024 / 1024;
        if (size > 20) {
          message.error(t('Upload Size Limit', { size: 20 }));
          return false;
        }
        setReuploaded(true);
        setCurFile(file);
        return false;
      },
    }),
    []
  );

  const onDownload = useCallback(
    (isTemplate: ExtTemplate, fileUniqueCode?: string) => {
      const extObj: WorkScheduleExt = {
        year,
        isTemplate,
      };
      const params = [WorkScheduleType.AssessmentCompany, WorkScheduleType.Investigator].includes(type)
        ? { ...extObj, instituteType: type }
        : extObj;

      ChannelService.download({
        id,
        fileUniqueCode,
        type: DownloadOrUploadTypeMap[type],
        ext: JSON.stringify(params),
      })
        .then(downloadFile)
        .catch((error: Error) => message.error(error?.message || t('Download failed')));
    },
    [year, id, type]
  );

  const columns: ColumnProps<YearlyWorkScheduleResp>[] = useMemo(
    () => [
      {
        title: t('File Name'),
        dataIndex: 'fileName',
        render: (fileName: string, { fileCode }) => (
          <div className={styles.textLink} onClick={() => onDownload(ExtTemplate.History, fileCode)}>
            {fileName}
          </div>
        ),
      },
      {
        title: t('Operator'),
        dataIndex: 'createdBy',
      },
      {
        title: t('Upload Date'),
        dataIndex: 'createdTime',
        render: (uploadDate: string) => dateFormatInstance.getDateTimeString(uploadDate),
      },
      {
        title: t('Status', { ns: 'partner' }),
        dataIndex: 'status',
        align: 'center',
        fixed: 'right',
        render: (status: string) => {
          return (
            <ComponentWithFallback>
              {status && (
                <StatusTag
                  hasDot={false}
                  shape={TagsShape.Round}
                  hasborder={false}
                  type={UploadStatusMap[status]?.type as TagsType}
                >
                  {UploadStatusMap[status]?.name}
                </StatusTag>
              )}
            </ComponentWithFallback>
          );
        },
      },
    ],
    [onDownload]
  );

  return (
    <DrawerForm
      className={styles.workScheduleDrawer}
      visible={visible}
      onClose={() => onCloseDrawer()}
      maskClosable={false}
      width={752}
      title={t('{{year}} Work Schedule', { year })}
      cancelText={t('Cancel')}
      sendText={t('Submit')}
      onSubmit={() => onSubmit()}
      submitBtnProps={{ loading: uploading, disabled: !reuploaded || readonly }}
      getContainer={false}
    >
      {/* 切换页码重新加载表格数据时，skeleton不需要loading，使用curFile控制 */}
      <Skeleton loading={!curFile && loading} active>
        <div className={styles.downloadTip}>
          <Trans i18nKey="Please Download Template First" ns="partner">
            Please
            <span
              className={styles.textLink}
              style={{ color: styles.primaryColor }}
              onClick={() => onDownload(ExtTemplate.Template)}
            >
              Download Template
            </span>
            First
          </Trans>
        </div>
        {curFile ? (
          <>
            <p className={styles.uploadTitle}>{t('Current Work Schedule')}</p>
            <FileItem
              fileName={(curFile as RcFile).name ?? (curFile as YearlyWorkScheduleResp).fileName}
              hoverInfoList={[
                {
                  key: uuidV4(),
                  icon: (
                    <Tooltip title={t('Download')}>
                      <DownloadOutlined
                        style={{
                          display: (curFile as YearlyWorkScheduleResp).fileCode ? 'inline-block' : 'none',
                        }}
                      />
                    </Tooltip>
                  ),
                  onClick: () => onDownload(ExtTemplate.Latest, (curFile as YearlyWorkScheduleResp).fileCode),
                },
                {
                  key: uuidV4(),
                  icon: (
                    <Tooltip title={t('Re-Upload')}>
                      <Upload {...uploadProps}>
                        <SyncOutlined
                          style={{
                            marginLeft: 16,
                            display: readonly ? 'none' : 'inline-block',
                          }}
                        />
                      </Upload>
                    </Tooltip>
                  ),
                },
              ]}
              style={{ width: '100%' }}
            />
            {!!uploadRecords?.data?.length && (
              <p onClick={() => setUploadHistoryVisible(!uploadHistoryVisible)} className={styles.recordTitle}>
                {t('Upload Record')}
                {uploadHistoryVisible ? <DownOutlined /> : <UpOutlined />}
              </p>
            )}
            {uploadHistoryVisible && (
              <>
                <Table
                  columns={columns}
                  dataSource={uploadRecords?.data}
                  loading={loading}
                  pagination={false}
                  size="small"
                />
                <PaginationComponent
                  size="small"
                  className="margin-top-16 margin-bottom-16"
                  total={uploadRecords?.totalElements}
                  pagination={pagination}
                  handlePaginationChange={(current: number, pageSize: number) => {
                    setPagination(old => ({
                      ...old,
                      current,
                      pageSize,
                    }));
                  }}
                />
              </>
            )}
          </>
        ) : (
          <Upload.Dragger {...uploadProps} className={styles.dragUploadBox} disabled={readonly}>
            <MultipleUploadOutline />
            <p className={styles.uploadText}>{t('Click or drag the file here to upload')}</p>
            <p className={styles.uploadHint}>{t('Upload Type Limit', { type: 'XLS/XLSX' })}</p>
          </Upload.Dragger>
        )}
      </Skeleton>
    </DrawerForm>
  );
};
