@import '@/variables.scss';

.work-schedule-drawer {
  .download-tip {
    margin-bottom: 24px;
    color: var(--disabled-color);
  }
  .upload-title {
    font-weight: 700;
  }
  .recordTitle {
    margin-top: 24px;
    cursor: pointer;
    font-weight: 700;
    > span {
      font-size: 12px;
      font-weight: 500;
      margin-left: 8px;
    }
  }
  .text-link {
    text-decoration: underline;
    cursor: pointer;
    &:hover {
      color: var(--primary-color);
    }
  }

  .drag-upload-box {
    width: 640px;
    background-color: var(--form-bg);
    border: 1px dashed var(--border-line-color-secondary);
    border-radius: 16px;
    > span {
      font-size: 40px;
    }
    .upload-text {
      margin-bottom: 4px;
      font-weight: 700;
      color: var(--primary-color);
      font-size: 14px;
    }
    .upload-hint {
      font-size: 12px;
      color: var(--text-disabled-color);
    }
  }
}
