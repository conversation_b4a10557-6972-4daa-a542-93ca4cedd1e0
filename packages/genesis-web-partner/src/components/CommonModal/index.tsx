import type { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

import type { ModalProps } from 'antd';
import { Modal } from 'antd';

import styles from './index.scss';

interface CommonModalProps extends ModalProps {
  children?: ReactNode;
  loading?: boolean;
}

/**
 * antd中Modal组件的封装，修改一些默认样式和文案
 * @param children 显示内容
 * @param loading 加在状态，会设置到ok button的loading和cancel disabled button上。
 * @param restProps antd中Modal组件的props
 * @constructor
 */
export const CommonModal = ({ children, loading, ...restProps }: CommonModalProps) => {
  const { t } = useTranslation('partner');

  return (
    <Modal
      className={styles.commonModal}
      width={480}
      closable={false}
      maskClosable={false}
      keyboard={false}
      title={t('Tips')}
      cancelText={t('No')}
      okText={t('Yes')}
      okButtonProps={{ loading }}
      cancelButtonProps={{ disabled: loading }}
      {...restProps}
    >
      {children}
    </Modal>
  );
};
