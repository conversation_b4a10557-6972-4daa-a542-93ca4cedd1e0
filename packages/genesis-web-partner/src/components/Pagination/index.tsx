import { Pagination } from '@zhongan/nagrand-ui';
import { TablePageSizeOptions } from '@/utils/constants';
import type { PaginationProps } from '@zhongan/nagrand-ui/dist/components/Pagination/Pagination';

export interface PaginationComponentProp extends PaginationProps {
  total: number;
  pagination: PaginationProps;
  handlePaginationChange?: (current: number, pageSize: number) => void;
  className?: string;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  pageSizeOptions?: string[];
}

export const PaginationComponent = ({
  pagination,
  total,
  handlePaginationChange,
  className,
  showQuickJumper = true,
  showSizeChanger = true,
  pageSizeOptions = TablePageSizeOptions,
  ...restProps
}: PaginationComponentProp) => {
  return total > 0 ? (
    <div className={className}>
      <Pagination
        pageSizeOptions={pageSizeOptions}
        showQuickJumper={showQuickJumper}
        showSizeChanger={showSizeChanger}
        total={total}
        current={pagination.current}
        pageSize={pagination.pageSize}
        onChange={handlePaginationChange}
        {...restProps}
      />
    </div>
  ) : (
    <></>
  );
};
