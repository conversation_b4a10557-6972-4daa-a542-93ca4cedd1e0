@import '../../variables.scss';

.pagination {
  :global {
    .common {
      height: 24px;
      min-width: 24px;
      line-height: 24px;
    }
    .#{$antd-prefix}-pagination {
      font-size: 12px;

      li {
        @extend .common;
        .#{$antd-prefix}-select-selector {
          @extend .common;
        }
        .#{$antd-prefix}-pagination-options-quick-jumper {
          @extend .common;
          input {
            @extend .common;
          }
        }
      }

      .#{$antd-prefix}-select-single .#{$antd-prefix}-select-selector .#{$antd-prefix}-select-selection-item {
        line-height: 24px;
      }
    }
    .anticon {
      vertical-align: -0.125em;
    }
    .anticon-down {
      vertical-align: 0.25em;
    }
  }

  .total-text {
    font-weight: 500;
    color: $text-color;
  }
}
