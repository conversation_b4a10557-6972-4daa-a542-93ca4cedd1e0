import type { CSSProperties } from 'react';

import Icon from '@ant-design/icons';

import ProcessOutlined from 'genesis-web-component/lib/assets/svg/process.svg';

import cssVars from '@/variables.scss';

interface ProcessOutlineProps {
  style?: CSSProperties;
}

export const ProcessOutline = ({ style }: ProcessOutlineProps) => {
  return (
    <Icon
      component={ProcessOutlined}
      style={{
        boxShadow: `0 0 0 4px ${cssVars.primaryColor20Percent}`,
        borderRadius: '50%',
        ...style,
      }}
    />
  );
};
