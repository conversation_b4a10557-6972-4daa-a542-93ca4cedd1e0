import type { ReactNode } from 'react';

import { ComponentWithFallback } from 'genesis-web-component/lib/components';

interface HyperlinkColumnProp {
  text: ReactNode;
  onClick: () => void;
}

/**
 * @description text封装成一个link，用在table的首列，table首列是
 * @param text：需要显示的内容
 * @param onClick 点击事件
 * @constructor
 */
export const HyperlinkColumn = ({ text, onClick }: HyperlinkColumnProp) => {
  return (
    <ComponentWithFallback>
      {text && (
        <a type="link" onClick={onClick}>
          {text}
        </a>
      )}
    </ComponentWithFallback>
  );
};
