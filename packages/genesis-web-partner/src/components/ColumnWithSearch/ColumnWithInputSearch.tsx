import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, Input } from 'antd';
import type { InputRef } from 'antd';
import type { ColumnProps } from 'antd/lib/table';

import clsx from 'clsx';

import { Icon } from '@zhongan/nagrand-ui';

import styles from './index.scss';

/**
 *
 * @param onSearch search callback method
 * @param placeholder placeholder
 * @param needClearFilters 是否需要清空搜索条件
 * @param needFetchAfterReset reset后是否需要重新查询
 * @description 获取表格头search dropdown
 */
export const useGetInputSearchDropdown = <T extends unknown>(
  onSearch: (content: string, dataIndex?: string) => void | Promise<unknown>,
  placeholder?: string,
  needClearFilters?: boolean,
  needFetchAfterReset = true
): ((dataIndex?: string) => Partial<ColumnProps<T>>) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const searchInputRef = useRef<InputRef>(null);
  const [filteredValue, setFilteredValue] = useState<string[]>(); // 最终搜索内容

  useEffect(() => {
    if (needClearFilters) {
      setFilteredValue([]);
    }
  }, [needClearFilters]);

  useEffect(() => {
    form.setFieldValue('inputValue', filteredValue?.[0]);
  }, [filteredValue]);

  const handleReset = useCallback(
    (confirm: () => void, dataIndex: string) => {
      form.resetFields();
      if (needFetchAfterReset) {
        setFilteredValue([]);
        confirm();
        onSearch(undefined, dataIndex);
      }
    },
    [needFetchAfterReset, onSearch]
  );

  const handleSearch = useCallback(
    (confirm: () => void, dataIndex: string) => {
      const inputValue = form.getFieldValue('inputValue');
      setFilteredValue(inputValue ? [inputValue] : []);
      confirm();
      onSearch(inputValue === '' ? undefined : inputValue, dataIndex);
    },
    [onSearch]
  );

  return (dataIndex?: string) => ({
    filterDropdown: ({ confirm }) => (
      <div className={clsx([styles.searchContainer, styles.selectInputContainer])}>
        <Form form={form}>
          <Form.Item name="inputValue">
            <Input
              ref={searchInputRef}
              placeholder={placeholder ?? t('Please input')}
              onPressEnter={() => handleSearch(confirm, dataIndex)}
            />
          </Form.Item>
        </Form>
        <div className={styles.actionBtn}>
          <Button type="primary" onClick={() => handleSearch(confirm, dataIndex)}>
            {t('Search')}
          </Button>
          <Button onClick={() => handleReset(confirm, dataIndex)} style={{ marginLeft: 16 }}>
            {t('Reset')}
          </Button>
        </div>
      </div>
    ),
    filteredValue,
    filterIcon: (filtered: boolean) => (
      <Icon type="search" className={clsx(styles.filterIcon, filtered && styles.filtered)} />
    ),
    onFilterDropdownOpenChange: (visible: boolean) => {
      if (visible) {
        setTimeout(() => searchInputRef.current?.focus(), 100);
      } else {
        // 每次关闭都恢复到上一次搜索条件，保证：修改了搜索条件，没有点击submit/reset关闭菜单时，再次打开是上一次搜索条件
        // 若是点击了submit/reset后关闭的，submit/reset相关方法有setFilteredValue，而filteredValue变化会重新setFieldValue，因此能保证再次打开时是新的搜索条件
        form.setFieldValue('inputValue', filteredValue?.[0]);
      }
    },
  });
};
