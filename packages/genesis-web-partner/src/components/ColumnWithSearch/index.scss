@import '@/variables.scss';

.search-container {
  padding: 12px;
  width: 284px;
  .action-btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 8px;
  }
  .ellipsis-text {
    max-width: 220px;
  }
  :global {
    .#{$antd-prefix}-checkbox-wrapper {
      display: flex;
      margin-left: 0px;
      margin-bottom: 16px;
    }
    .#{$antd-prefix}-form {
      max-height: 260px;

      .#{$antd-prefix}-form-item {
        margin-bottom: 0px;
      }
    }
  }
}

.select-input-container {
  :global {
    .#{$antd-prefix}-form {
      overflow: hidden;
    }
  }
}

.select-search-container {
  :global {
    .#{$antd-prefix}-form {
      border-bottom: 1px solid var(--divider-color);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
}
.filter-icon {
  font-size: 16px;
  color: var(--text-color-tertiary) !important;
  &.filtered {
    color: var(--primary-color) !important;
  }
}
