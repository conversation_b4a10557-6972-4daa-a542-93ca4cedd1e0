import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Checkbox, Form, Skeleton, Typography } from 'antd';
import type { InputRef } from 'antd';
import type { ColumnProps } from 'antd/lib/table';
import classNames from 'classnames';
import clsx from 'clsx';

import { Icon } from '@zhongan/nagrand-ui';

import type { SelectOptions } from 'genesis-web-service';

import styles from './index.scss';

/**
 *
 * @param onSearch search callback method
 * @param placeholder placeholder
 * @param needClearFilters 是否需要清空搜索条件
 * @param needFetchAfterReset reset后是否需要重新查询
 * @description 获取表格头search dropdown
 */
export const useGetSelectSearchDropdown = <T extends unknown>(
  onSearch: (content: string[]) => void | Promise<unknown>,
  dropdownData?: SelectOptions[],
  onFetchDropDownData?: () => void | Promise<unknown>,
  needClearFilters?: boolean,
  queryDepartmentListLoading = false,
  needFetchAfterReset = true
): (() => Partial<ColumnProps<T>>) => {
  const { t } = useTranslation('partner');
  const [form] = Form.useForm();
  const searchInputRef = useRef<InputRef>(null);
  const [filteredValue, setFilteredValue] = useState<string[]>(); // 最终搜索内容

  useEffect(() => {
    if (needClearFilters) {
      setFilteredValue([]);
    }
  }, [needClearFilters]);

  useEffect(() => {
    form.setFieldValue('searchValue', filteredValue?.[0]);
  }, [filteredValue]);

  const handleReset = useCallback(
    (confirm: () => void) => {
      form.resetFields();
      if (needFetchAfterReset) {
        setFilteredValue([]);
        confirm();
        onSearch(undefined);
      }
    },
    [needFetchAfterReset, onSearch]
  );

  const handleSearch = useCallback(
    (confirm: () => void) => {
      const inputValue = form.getFieldValue('searchValue');
      const inputName = inputValue?.reduce((pre: string[], cur: string) => {
        const name = dropdownData?.find(item => item.value === cur)?.label;
        return pre.concat(name);
      }, []);
      setFilteredValue(inputValue ? [inputValue] : []);
      confirm();
      onSearch(inputName);
    },
    [onSearch, dropdownData]
  );

  return () => ({
    filterDropdown: ({ confirm }) => (
      <div className={clsx([styles.searchContainer, styles.selectSearchContainer])}>
        <Form form={form}>
          <Skeleton loading={queryDepartmentListLoading}>
            <Form.Item name="searchValue">
              <Checkbox.Group>
                {dropdownData?.map(data => (
                  <Checkbox key={data?.value} value={data?.value}>
                    <Typography.Text className={styles.ellipsisText} ellipsis={true}>
                      {data?.label}
                    </Typography.Text>
                  </Checkbox>
                ))}
              </Checkbox.Group>
            </Form.Item>
          </Skeleton>
        </Form>
        <div className={styles.actionBtn}>
          <Button type="primary" onClick={() => handleSearch(confirm)}>
            {t('Search')}
          </Button>
          <Button onClick={() => handleReset(confirm)} style={{ marginLeft: 16 }}>
            {t('Reset')}
          </Button>
        </div>
      </div>
    ),
    filteredValue,
    filterIcon: (filtered: boolean) => (
      <Icon type="filter" className={classNames(styles.filterIcon, filtered && styles.filtered)} />
    ),
    onFilterDropdownOpenChange: (visible: boolean) => {
      if (visible) {
        if (onFetchDropDownData) {
          onFetchDropDownData();
        }
        setTimeout(() => searchInputRef.current?.focus(), 0);
      } else {
        // 每次关闭都恢复到上一次搜索条件，保证：修改了搜索条件，没有点击submit/reset关闭菜单时，再次打开是上一次搜索条件
        // 若是点击了submit/reset后关闭的，submit/reset相关方法有setFilteredValue，而filteredValue变化会重新setFieldValue，因此能保证再次打开时是新的搜索条件
        form.setFieldValue('searchValue', filteredValue?.[0]);
      }
    },
  });
};
