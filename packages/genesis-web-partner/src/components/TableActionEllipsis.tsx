import type { FC } from 'react';
import { useMemo } from 'react';

import { EllipsisOutlined } from '@ant-design/icons';

import { Dropdown } from 'antd';
import type { MenuItemProps } from 'antd/lib/menu/MenuItem';

type MenuItemPropsExt = MenuItemProps & { title: string; key: string };

export const TableActionEllipsis: FC<{ menuItems: MenuItemPropsExt[] }> = ({ menuItems }) => {
  const items = useMemo(
    () =>
      menuItems?.map(item => ({
        key: item.key,
        label: item.title,
        className: 'gc-action-menu',
        onClick: item?.onClick,
        disabled: item?.disabled,
      })),
    [menuItems]
  );

  return (
    <div style={{ fontSize: '16px', cursor: 'pointer' }}>
      <Dropdown menu={{ items }} placement="topRight">
        <EllipsisOutlined />
      </Dropdown>
    </div>
  );
};
