@import '@/variables.scss';

.personal-work-schedule-drawer {
  :global {
    .#{$antd-prefix}-picker-body {
      th {
        margin: 0var (--gap-xss);
        padding: var(--gap-xss) $gap-sm !important;
        text-align: left;
      }
    }
  }
  .calender-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $gap-lg;
    .header-right {
      .today {
        border-radius: 0;
      }
      .left,
      .right {
        width: 48px;
      }
      .left {
        border-radius: var(--border-radius-base) 0 0 var(--border-radius-base);
        border-right: none;
      }
      .right {
        border-radius: 0 var(--border-radius-base) var(--border-radius-base) 0;
        border-left: none;
      }
    }
  }

  .selected-date {
    :global {
      .#{$antd-prefix}-badge-status-dot {
        display: none;
      }
      .#{$antd-prefix}-badge-status-text {
        margin-left: 0;
      }
    }
  }
  :global {
    .#{$antd-prefix}-picker-calendar-date {
      margin: 0 2px !important;
    }
    .#{$antd-prefix}-picker-cell-selected {
      .#{$antd-prefix}-picker-calendar-date {
        background: var(--primary-disabled-color) !important;
      }
    }
    .#{$antd-prefix}-picker-calendar-date-value {
      text-align: left;
    }
    .#{$antd-prefix}-picker-calendar-date-content {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      padding-bottom: $gap-sm;
      height: 70px !important;
      .#{$antd-prefix}-badge-status-dot {
        width: 8px;
        height: 8px;
      }
      .#{$antd-prefix}-badge-status-text {
        font-size: $font-size-sm;
        font-weight: 700;
      }
      .#{$antd-prefix}-badge-status-success {
        + span {
          color: var(--success-color);
        }
      }
      .#{$antd-prefix}-badge-status-default {
        background-color: var(--closed-status-dot);
        + span {
          color: var(--primary-light);
        }
      }
    }
  }

  .working-type {
    width: 58px;
    height: 40px;
    .working-type-icon-container {
      display: flex;
      width: 27px;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2px;
    }

    .working-type-link {
      display: flex;
      height: 16px;
      justify-content: space-between;
      align-items: center;

      :global {
        .#{$antd-prefix}-btn-sm {
          height: 16px;
          padding: 0;
        }
      }
      .working-type-btn {
        display: flex;
        width: 58px;
        align-items: center;
        justify-content: start;
        .working-type-link-text {
          width: 40px;
          font-size: $font-size-sm;
          text-align: start;
          line-height: 16px;
        }

        .working-type-link-right-arrow {
          width: 16px;
          height: 16px;
          margin-left: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          vertical-align: middle;
        }
      }
    }
  }
}

.working-type-title {
  height: $font-size-h3;
  line-height: $font-size-h3;
  font-weight: 500;
  margin-bottom: var(--gap-xss);
}

:global {
  .#{$antd-prefix}-modal-content {
    .#{$antd-prefix}-select-selector {
      .#{$antd-prefix}-select-selection-item {
        background-color: var(--info-color-bg);
        color: var(--info-color-text-dark);
        .#{$antd-prefix}-select-selection-item-remove {
          color: var(--info-color-text-dark);
        }
      }
    }
    .#{$antd-prefix}-select-disabled {
      .#{$antd-prefix}-select-selector {
        background-color: var(--primary-disabled-color);
        .#{$antd-prefix}-select-selection-item {
          border: none;
          background-color: var(--default-color-bg);
          color: var(--default-color);
        }
      }
    }
  }
}
