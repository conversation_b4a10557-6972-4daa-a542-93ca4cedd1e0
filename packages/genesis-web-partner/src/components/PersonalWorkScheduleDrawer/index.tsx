import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useRequest } from 'ahooks';
import { Badge, Button, Calendar, Spin, Tabs, message } from 'antd';
import { cloneDeep, isEmpty, keyBy } from 'lodash-es';
import moment from 'moment';
import type { Moment } from 'moment';

import { DatePicker, Icon, Select } from '@zhongan/nagrand-ui';

import { DrawerForm } from 'genesis-web-component/lib/components/DrawerForm';
import type {
  BizDictItem,
  InstituteStaff,
  PersonalWorkScheduleParams,
  PersonalWorkScheduleResp,
} from 'genesis-web-service';
import { ChannelService, EmployeeStatus, InstituteTypeEnum } from 'genesis-web-service';

import { renderEnumsWithString } from '@/components/RenderEnums';
import { useTenantBizDict } from '@/hooks/useTenantBizDict';
import {
  useApproveModeContext,
  useWorkScheduleTypeContext,
} from '@/pages/common-party/partner-setting-v2/hooks/useContext';
import { EmployeeWorkScheduleTypeTabEnum, WorkScheduleType } from '@/types/common-party';
import { LeftOutlined, RightOutlined, SyncOutlined } from '@ant-design/icons';

import { CommonModal } from '../CommonModal';
import styles from './index.scss';

const QueryWorkScheduleServiceMap = {
  [WorkScheduleType.AssessmentCompany]: (
    code: string,
    params: Pick<PersonalWorkScheduleParams, 'endDate' | 'startDate'>
  ) =>
    ChannelService.queryStaffWorkSchedule(code, {
      ...params,
      instituteType: InstituteTypeEnum.AssessmentCompany,
    }),
  [WorkScheduleType.EmployeeManagement]: ChannelService.queryEmployeeWorkSchedule,
  [WorkScheduleType.Investigator]: (code: string, params: Pick<PersonalWorkScheduleParams, 'endDate' | 'startDate'>) =>
    ChannelService.queryStaffWorkSchedule(code, {
      ...params,
      instituteType: InstituteTypeEnum.Investigator,
    }),
};

const UpdateWorkScheduleServiceMap = {
  [WorkScheduleType.AssessmentCompany]: ChannelService.updateStaffWorkSchedule,
  [WorkScheduleType.Investigator]: ChannelService.updateStaffWorkSchedule,
  [WorkScheduleType.EmployeeManagement]: ChannelService.updateEmployeeWorkSchedule,
};

interface Props {
  personCode: string;
  visible: boolean;
  readonly?: boolean;
  type: WorkScheduleType;
  onClose: (updated: boolean) => void;
  onSubmit: (selectedDate: Moment, values: any) => void;
  isApprovalPage?: boolean;
  editRecord?: InstituteStaff;
}

/**
 *
 * @param personCode 当前person对应code
 * @param visible 是否可见
 * @param readonly 是否只读
 * @param onClose 关闭drawer
 * @workScheduleTypeEnums Employee 下的 workScheduler 配置枚举
 * @description 个人排班表drawer
 */
export const PersonalWorkScheduleDrawer = ({
  personCode,
  visible,
  readonly,
  type,
  onClose,
  onSubmit,
  isApprovalPage = false,
  editRecord,
}: Props) => {
  const { t } = useTranslation('partner');
  const [editing, setEditing] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Moment>();
  const [workTypeModalVisible, setWorkTypeModalVisible] = useState(false);
  const { isTenantIncludesWorkType } = useWorkScheduleTypeContext();
  const [activeKey, setActiveKey] = useState(EmployeeWorkScheduleTypeTabEnum.WORKING_STATUS);
  const [currentVisibleDates, setCurrentVisibleDates] = useState<Record<string, PersonalWorkScheduleResp>>();
  const [panelDate, setPanelDate] = useState<Moment>();
  const [selectedWorkingType, setSelectedWorkingType] = useState<string[]>();

  const enums = useTenantBizDict() as Record<string, BizDictItem[]>;

  const hasEmployeeWorkShifts = enums?.employeeWorkShifts?.length > 0;
  const { enableApproval } = useApproveModeContext();

  const staffScheduleMap = useMemo(() => {
    if (!enableApproval || !editRecord) {
      return {};
    }

    return keyBy(editRecord?.staffSchedule?.schedules || [], 'dateStr');
  }, [editRecord, editRecord?.staffSchedule, enableApproval]);

  useEffect(() => {
    if (visible) {
      setPanelDate(moment()); // 设置为当前时间
    }
  }, [visible]);

  // 获取startDate-endDate的排班表
  const { loading } = useRequest(
    () => {
      if (personCode && visible && panelDate) {
        const clonedPanelDate = moment(panelDate);
        const startDate = clonedPanelDate.startOf('month').format('YYYY-MM-DD');
        const endDate = clonedPanelDate.endOf('month').format('YYYY-MM-DD');
        return QueryWorkScheduleServiceMap[type](personCode, {
          startDate,
          endDate,
        });
      }
    },
    {
      refreshDeps: [personCode, visible, panelDate, type, staffScheduleMap, enableApproval],
      onSuccess: workSchedules => {
        const tempDates: Record<string, PersonalWorkScheduleResp> = {};
        workSchedules?.forEach(schedule => {
          const formattedDate = moment(schedule.dateStr).format('YYYY-MM-DD');
          tempDates[formattedDate] = schedule;
        });

        // enableApproval开启时，配置的数据保存在staff上面，回显的时候需要覆盖接口返回的数据
        if (enableApproval) {
          setCurrentVisibleDates({
            ...tempDates,
            ...staffScheduleMap,
          });
        } else {
          setCurrentVisibleDates(tempDates);
        }
      },
      onError: (error: Error) => message.error(error?.message),
    }
  );

  // 转换status
  const transferStatus = useCallback(
    (date: string) => {
      const clonedDates = cloneDeep(currentVisibleDates);
      if (!clonedDates[date]) {
        return;
      }
      if (clonedDates[date].personStatus === EmployeeStatus.OnDuty) {
        clonedDates[date].personStatus = EmployeeStatus.OnLeave;
      } else if (currentVisibleDates[date].personStatus === EmployeeStatus.OnLeave) {
        clonedDates[date].personStatus = EmployeeStatus.OnDuty;
      }
      setCurrentVisibleDates(clonedDates);
    },
    [currentVisibleDates]
  );

  // 日期单元格render，会追加到
  const renderWorkStatusDateCell = useCallback(
    (calendarCellDate: Moment) => {
      // 不作为展示，只是用来当作索引
      const formattedDateKey = calendarCellDate.format('YYYY-MM-DD');
      if (isEmpty(currentVisibleDates) || !currentVisibleDates[formattedDateKey]) {
        return null;
      }
      const isEditing = editing && calendarCellDate.isSame(selectedDate);
      const text = renderEnumsWithString(currentVisibleDates[formattedDateKey].personStatus, enums?.employeeStatus);
      let status: string;
      if (currentVisibleDates[formattedDateKey].personStatus === EmployeeStatus.OnDuty) {
        status = 'success';
      } else if (currentVisibleDates[formattedDateKey].personStatus === EmployeeStatus.OnLeave) {
        status = 'default';
      }
      return (
        status && (
          <div className={isEditing ? styles.selectedDate : null}>
            <Badge status={status} text={text} />
            {isEditing && <SyncOutlined style={{ marginLeft: 8 }} onClick={() => transferStatus(formattedDateKey)} />}
          </div>
        )
      );
    },
    [selectedDate, currentVisibleDates, editing, enums, transferStatus]
  );

  const handleClickWorkingTypeLink = useCallback(
    (formattedDate: string) => {
      setWorkTypeModalVisible(true);
      const value = cloneDeep(currentVisibleDates[formattedDate]?.workingTypes);
      setSelectedWorkingType(value);
    },
    [currentVisibleDates]
  );

  // tab 是 working type 的时候， 日期单元格的 render
  const renderWorkTypeDateCell = useCallback(
    (calendarCellDate: Moment) => {
      // 不作为展示，只是用来当作索引
      const formattedDateKey = calendarCellDate.format('YYYY-MM-DD');
      if (isEmpty(currentVisibleDates) || !currentVisibleDates[formattedDateKey]) {
        return null;
      }
      const item = currentVisibleDates[formattedDateKey];
      return (
        <div className={styles.workingType}>
          <div className={styles.workingTypeIconContainer}>
            <Icon type="insurance-company-api" />
            <div>{item?.workingTypes?.length}</div>
          </div>
          <div className={styles.workingTypeLink}>
            <Button
              className={styles.workingTypeBtn}
              onClick={() => handleClickWorkingTypeLink(formattedDateKey)}
              type="link"
              size="small"
            >
              <span className={styles.workingTypeLinkText}>{t('Details')}</span>
              <Icon className={styles.workingTypeLinkRightArrow} type="right-arrow" />
            </Button>
          </div>
        </div>
      );
    },
    [handleClickWorkingTypeLink, currentVisibleDates]
  );

  // datePicker切换年/月
  const onChangeMonthYear = useCallback((date: Moment, curDate: Moment, onChange: (date: Moment) => void) => {
    const year = date.year();
    const month = date.month();
    const newDate = curDate.clone().year(year).month(month);
    onChange(newDate);
  }, []);

  // 点击<>icon快捷切换月份
  const onChangeMonth = useCallback((month: number, curDate: Moment, onChange: (date: Moment) => void) => {
    const newDate = curDate.clone().month(month);
    onChange(newDate);
  }, []);

  const getHeaderRender = useCallback(
    (calendarCellInfo: Moment, onChange: (date: Moment) => void) => (
      <div className={styles.calenderHeader}>
        <DatePicker
          picker="month"
          value={calendarCellInfo}
          allowClear={false}
          onChange={date => onChangeMonthYear(date, calendarCellInfo, onChange)}
        />
        <div className={styles.headerRight}>
          <Button
            icon={<LeftOutlined />}
            className={styles.left}
            onClick={() => onChangeMonth(calendarCellInfo.month() - 1, calendarCellInfo, onChange)}
          />
          <Button className={styles.today} onClick={() => onChange(moment())}>
            {t('Today')}
          </Button>
          <Button
            icon={<RightOutlined />}
            className={styles.right}
            onClick={() => onChangeMonth(calendarCellInfo.month() + 1, calendarCellInfo, onChange)}
          />
        </div>
      </div>
    ),
    [onChangeMonthYear, onChangeMonth]
  );

  const onCloseDrawer = useCallback(
    (updated?: boolean) => {
      setSelectedDate(undefined);
      setPanelDate(undefined);
      setActiveKey(undefined);
      setWorkTypeModalVisible(false);
      setEditing(false);
      onClose?.(updated);
    },
    [onClose]
  );

  const submit = useCallback(() => {
    if (!editing) {
      setEditing(true);
    } else {
      const updatedDates = Object.values(currentVisibleDates ?? {});
      const assessmentCompanyParam = {
        instituteType: type,
        schedules: updatedDates,
      };
      if (enableApproval) {
        onSubmit(selectedDate, assessmentCompanyParam);
        onCloseDrawer(false);
        return;
      }
      setSubmitting(true);
      UpdateWorkScheduleServiceMap[type](
        personCode,
        [WorkScheduleType.AssessmentCompany, WorkScheduleType.Investigator].includes(type)
          ? assessmentCompanyParam
          : updatedDates
      )
        .then(() => {
          message.success(t('Save successfully'));
          onCloseDrawer(true);
        })
        .catch((error: Error) => message.error(error?.message))
        .finally(() => setSubmitting(false));
    }
  }, [editing, currentVisibleDates, type, onCloseDrawer]);

  const getCalendarByWorkScheduleType = useCallback(
    workScheduleType => {
      return (
        <Calendar
          dateCellRender={
            workScheduleType === EmployeeWorkScheduleTypeTabEnum.WORKING_TYPE
              ? renderWorkTypeDateCell
              : renderWorkStatusDateCell
          }
          headerRender={({ value: calendarCellInfo, onChange }) => getHeaderRender(calendarCellInfo, onChange)}
          value={panelDate}
          onSelect={setSelectedDate}
          onPanelChange={setPanelDate}
        />
      );
    },
    [renderWorkStatusDateCell, getHeaderRender, renderWorkTypeDateCell]
  );

  const tabItems = useMemo(() => {
    const defaultTab = [
      {
        enumItemName: EmployeeWorkScheduleTypeTabEnum.WORKING_STATUS,
        dictValueName: t('Working Status'),
      },
    ];
    if (isTenantIncludesWorkType) {
      defaultTab.push({
        enumItemName: EmployeeWorkScheduleTypeTabEnum.WORKING_TYPE,
        dictValueName: t('Working Type'),
      });
    }
    return defaultTab?.map(workScheduleTypeItem => ({
      key: workScheduleTypeItem.enumItemName,
      label: workScheduleTypeItem.dictValueName,
      children: getCalendarByWorkScheduleType(workScheduleTypeItem.enumItemName),
    }));
  }, [isTenantIncludesWorkType, getCalendarByWorkScheduleType]);

  const handleTabChange = (activeTabKey: string) => {
    setActiveKey(activeTabKey as EmployeeWorkScheduleTypeTabEnum);
  };

  const onWorkTypeModalCancel = useCallback(() => {
    setWorkTypeModalVisible(false);
    setSelectedWorkingType([]);
  }, []);

  const onWorkTypeModalConfirm = useCallback(() => {
    setWorkTypeModalVisible(false);
    const formattedDateKey = selectedDate.format('YYYY-MM-DD');
    currentVisibleDates[formattedDateKey] = {
      ...currentVisibleDates[formattedDateKey],
      workingTypes: selectedWorkingType,
    };
    setCurrentVisibleDates(cloneDeep(currentVisibleDates));
  }, [currentVisibleDates, selectedWorkingType, selectedDate]);

  const customRenderModalFooter = useMemo(() => {
    return editing ? (
      <>
        <Button onClick={onWorkTypeModalCancel}>{t('Cancel')}</Button>
        <Button type="primary" onClick={onWorkTypeModalConfirm}>
          {t('Confirm')}
        </Button>
      </>
    ) : (
      <Button type="primary" onClick={onWorkTypeModalCancel}>
        {t('OK')}
      </Button>
    );
  }, [editing, onWorkTypeModalCancel, onWorkTypeModalConfirm]);

  return (
    <DrawerForm
      className={styles.personalWorkScheduleDrawer}
      visible={visible}
      onClose={onCloseDrawer}
      width={752}
      maskClosable={false}
      title={t('Personal Work Schedule')}
      cancelText={t('Cancel')}
      sendText={editing ? t('Submit') : t('Edit')}
      onSubmit={submit}
      submitBtnTooltip={
        hasEmployeeWorkShifts ? t('Please edit the work schedule by uploading the excel file.') : undefined
      }
      submitBtnProps={{
        loading: submitting,
        disabled: isEmpty(currentVisibleDates) || readonly || hasEmployeeWorkShifts,
      }}
    >
      <Spin spinning={loading}>
        <Tabs activeKey={activeKey} onChange={handleTabChange} items={tabItems} />
      </Spin>
      <CommonModal
        getContainer={() => document.querySelector('#personal-work-schedule-drawer')}
        open={workTypeModalVisible}
        onCancel={() => setWorkTypeModalVisible(false)}
        title={t('Working Type Detail')}
        onOk={() => setWorkTypeModalVisible(false)}
        footer={customRenderModalFooter}
      >
        <div className={styles.workingTypeTitle}>{t('Working Type')}</div>
        <Select
          disabled={!editing}
          mode="multiple"
          selectAll={false}
          allowClear
          style={{ width: '100%' }}
          placeholder={t('Please select')}
          value={selectedWorkingType}
          onChange={workingTypeInfos => {
            setSelectedWorkingType([...workingTypeInfos]);
          }}
          options={enums?.workingType}
        />
      </CommonModal>
    </DrawerForm>
  );
};
