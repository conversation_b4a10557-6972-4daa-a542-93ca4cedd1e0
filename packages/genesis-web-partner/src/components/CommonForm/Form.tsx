import type { CSSProperties } from 'react';
import { Fragment } from 'react';

import { Col, Form, Row, Skeleton } from 'antd';
import type { FormInstance, FormProps } from 'antd/es';

import { FieldNode } from './FieldNode';
import type { QueryFieldsType } from './interface';
import { FieldType } from './interface';

type CommonFormProp = {
  fields: Partial<QueryFieldsType>[];
  form: FormInstance;
  style?: CSSProperties;
  loading?: boolean;
  // 容器固定宽度时,无需响应
  gutter?: number;
} & FormProps;

const colSpan = 8;
const DefaultGutters = { xs: 8, sm: 16, md: 24, lg: 24, xl: 104 };

export const CommonForm = ({ fields, form, style, loading, gutter, ...rest }: CommonFormProp): JSX.Element => {
  return (
    <Form style={{ maxWidth: 928, ...style }} layout="vertical" form={form} name="form" {...rest}>
      <Row gutter={gutter ?? DefaultGutters} align="bottom">
        {fields.map(
          ({ key, type, col, offset, ctrlProps, customerDom, customerLoadingDom, customerDomOption, ...formProp }) => (
            <Fragment>
              {loading ? (
                type === FieldType.Customize ? (
                  customerLoadingDom || <></>
                ) : (
                  <Col key={key} span={col ?? colSpan} offset={offset ?? 0} style={{ marginBottom: 16 }}>
                    <Skeleton loading={loading} round active title={false} paragraph={{ rows: 2, width: '100%' }} />
                  </Col>
                )
              ) : (
                <>
                  {type === FieldType.Customize ? (
                    typeof customerDom === 'function' ? (
                      customerDom(customerDomOption)
                    ) : (
                      customerDom
                    )
                  ) : (
                    <Col key={key} span={col ?? colSpan} offset={offset ?? 0}>
                      <Form.Item name={key} {...formProp}>
                        <FieldNode type={type} {...ctrlProps} />
                      </Form.Item>
                    </Col>
                  )}
                </>
              )}
            </Fragment>
          )
        )}
      </Row>
    </Form>
  );
};
