/*!
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

@import '../../variables.scss';

.query-form-wrapper {
  padding: 32px;
  background-color: var(--white) !important;
}

.short-input-wrapper {
  width: $form-item-width;
}

.long-input-wrapper {
  width: $form-range-item-width;
}

.button-group {
  text-align: right;

  .collapse-button {
    padding: 0;
    color: var(--primary-color);
  }

  button:not(:first-child) {
    margin-left: 24px;
  }
}
