/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import type { CSSProperties } from 'react';

import type { FormItemProps, SelectProps } from 'antd';
import type { Moment } from 'moment';

import type { BizDictItem } from 'genesis-web-service';

export interface LabeledValue<T extends string | number> {
  label: string;
  value: T;
}

export enum FieldType {
  Input = 'input',
  Select = 'select',
  Radio = 'radio',
  DateRange = 'dateRange',
  InputGroup = 'inputGroup',
  TextArea = 'textArea',
  DatePicker = 'datePicker',
  TimePicker = 'timePicker',
  Switch = 'switch',
  Customize = 'customize',
  Cascader = 'cascader',
  InputNumber = 'inputNumber',
}

export interface ControlProp {
  options: BizDictItem[] | (LabeledValue<string>[] & { children?: LabeledValue<string>[] });
  placeholder: string | [string, string];
  format: string;
  disabled: boolean;
  allowClear: boolean;
  onChange: (...args: any) => void;
  showTime: boolean;
  showCount: boolean;
  children: QueryFieldsType[];
  hideSearchIcon: boolean;
  suffixIcon: SelectProps<unknown>['suffixIcon'];
  dropdownRender: SelectProps<unknown>['dropdownRender'];
  maxLength: number;
  disabledDate: (date: Moment) => boolean;
  style: CSSProperties;
  mode: SelectProps<unknown>['mode'];
  showArrow: boolean;
  defaultValue: Moment[] | string | string[];
  loading: boolean;
}

export interface QueryFieldsType extends FormItemProps {
  type?: FieldType;
  key: string;
  col?: number;
  offset?: number;
  ctrlProps?: Partial<ControlProp>;
  customerDomOption: {
    disabled?: boolean;
  };
  customerDom?: JSX.Element | ((config: { disabled?: boolean }) => JSX.Element);
  // 用来给Customize的字段做pretty的显示
  customDisplayKey?: string;
  customerLoadingDom?: JSX.Element;
}

export enum ViewMode {
  CardMode = 'CardMode',
  TableMode = 'TableMode',
}
