/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { useTranslation } from 'react-i18next';

import { Cascader, Form, Input, InputNumber, Radio, Switch } from 'antd';
import type { Moment } from 'moment';

import { DatePicker, Select } from '@zhongan/nagrand-ui';

import { DatePickerSupport24 } from 'genesis-web-component/lib/components';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import styles from './index.scss';
import type { ControlProp } from './interface';
import { FieldType } from './interface';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface FieldNodeProp extends Partial<ControlProp> {
  type: FieldType;
  displayValue?: string;
  name: string;
  value: any;
}

export const FieldNode = ({
  type = FieldType.Input,
  showTime,
  format,
  placeholder,
  options,
  children,
  name,
  ...restProps
}: FieldNodeProp): JSX.Element => {
  const { t } = useTranslation('partner');
  const { dateFormat } = useL10n();

  switch (type) {
    case FieldType.DateRange:
      return (
        <RangePicker
          className={styles.longInputWrapper}
          showTime={showTime}
          format={format ?? (showTime ? dateFormat.dateTimeFormat : dateFormat.dateFormat)}
          placeholder={(placeholder as [string, string]) || [t('Start Time'), t('End Time')]}
          getPopupContainer={triggerElement => triggerElement?.parentElement}
          {...(restProps ?? {})}
        />
      );
    case FieldType.DatePicker:
      return (
        <DatePickerSupport24
          className={styles.shortInputWrapper}
          showTime={showTime}
          placeholder={(placeholder as string) || t('Please select')}
          getPopupContainer={triggerElement => triggerElement?.parentElement}
          {...(restProps ?? {})}
          value={restProps.value ? dateFormat.l10nMoment(restProps.value) : undefined}
          onChange={(val: Moment) => {
            if (name.includes('birthday')) {
              restProps.onChange(val ? val.format('YYYY-MM-DD') : undefined);
            } else {
              restProps.onChange(val ? dateFormat.formatTz(val) : undefined);
            }
          }}
        />
      );
    case FieldType.Radio:
      return (
        <Radio.Group {...(restProps ?? {})}>
          {options?.map(option => (
            <Radio value={option.value} key={option.value}>
              {option.label}
            </Radio>
          ))}
        </Radio.Group>
      );
    case FieldType.Switch:
      return <Switch defaultChecked {...(restProps ?? {})} />;
    case FieldType.Select:
      if (restProps.mode === 'multiple') {
        restProps.defaultValue = Array.isArray(restProps?.displayValue)
          ? restProps?.displayValue
          : (restProps?.displayValue?.split(',') as string[]);
      }
      return (
        <Select
          showSearch
          selectAll={false}
          placeholder={placeholder || t('Please Select')}
          optionFilterProp="children"
          style={{ width: 240 }}
          filterOption={(input, option) => option!.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
          getPopupContainer={triggerElement => triggerElement?.parentElement}
          {...(restProps ?? {})}
        >
          {options?.map(option => (
            <Option value={option.value} key={option.value} {...option}>
              {option.label}
            </Option>
          ))}
        </Select>
      );
    case FieldType.InputGroup:
      if (children?.length !== 2) return;
      // eslint-disable-next-line no-case-declarations
      const { key: child1Key, rules: child1Rules = [], ctrlProps: child1Props } = children[0];
      // eslint-disable-next-line no-case-declarations
      const { key: child2Key, rules: child2Rules = [], ctrlProps: child2Props } = children[1];
      return (
        <Input.Group compact style={{ display: 'flex' }}>
          <Form.Item name={child1Key} rules={child1Rules} noStyle>
            <Select
              className={child1Props.hideSearchIcon ? 'select-clear-icon' : ''}
              showSearch
              selectAll={false}
              optionFilterProp="children"
              placeholder={child1Props.placeholder || t('Please Select')}
              onChange={child1Props.onChange}
              allowClear={child1Props.allowClear}
              style={{ width: 240 }}
              disabled={child1Props.disabled}
              filterOption={(input, option) => option!.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              getPopupContainer={triggerElement => triggerElement?.parentElement}
            >
              {child1Props.options?.map(option => (
                <Option value={option.value} key={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name={child2Key} rules={child2Rules} noStyle>
            <Select
              showSearch
              selectAll={false}
              optionFilterProp="children"
              placeholder={child2Props.placeholder || t('Please Select')}
              onChange={child2Props.onChange}
              allowClear={child2Props.allowClear}
              style={{ width: 340 }}
              suffixIcon={child2Props.suffixIcon}
              dropdownRender={child2Props.dropdownRender}
              disabled={child2Props.disabled}
              filterOption={(input, option) => option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              getPopupContainer={triggerElement => triggerElement?.parentElement}
            >
              {child2Props.options?.map(option => (
                <Option value={option.value} key={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Input.Group>
      );
    case FieldType.Cascader:
      return (
        <Cascader
          showSearch
          placeholder={placeholder || t('Please Select')}
          style={{ width: 240 }}
          options={options}
          expandTrigger="hover"
          getPopupContainer={triggerElement => triggerElement?.parentElement}
          {...(restProps ?? {})}
        />
      );
    case FieldType.TextArea:
      return <Input.TextArea placeholder={(placeholder as string) || t('Please Input')} {...restProps} />;
    case FieldType.InputNumber:
      return <InputNumber placeholder={(placeholder as string) || t('Please Input')} {...restProps} />;
    default:
      return (
        <Input
          className={styles['short-input-wrapper']}
          placeholder={(placeholder as string) || t('Please Input')}
          {...restProps}
        />
      );
  }
};
