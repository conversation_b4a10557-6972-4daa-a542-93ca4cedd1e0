/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { useCallback } from 'react';

import { CustomerGender } from 'genesis-web-service';

import avatar_1 from '@/assets/avatar/avatar_1.png';
import avatar_2 from '@/assets/avatar/avatar_2.png';
import avatar_3 from '@/assets/avatar/avatar_3.png';
import avatar_4 from '@/assets/avatar/avatar_4.png';
import avatar_5 from '@/assets/avatar/avatar_5.png';
import avatar_6 from '@/assets/avatar/avatar_6.png';
import avatar_7 from '@/assets/avatar/avatar_7.png';
import avatar_8 from '@/assets/avatar/avatar_8.png';
import avatar_female from '@/assets/avatar/avatar_female.png';
import avatar_male from '@/assets/avatar/avatar_male.png';

export const AvatarList: Record<string, string> = {
  default_1: avatar_1,
  default_2: avatar_2,
  default_3: avatar_3,
  default_4: avatar_4,
  default_5: avatar_5,
  default_6: avatar_6,
  default_7: avatar_7,
  default_8: avatar_8,
  MALE: avatar_male,
  UNKNOWN: avatar_1,
  FEMALE: avatar_female,
};

export const UsableAvatar = [
  'default_1',
  'default_2',
  'default_3',
  'default_4',
  'default_5',
  'default_6',
  'default_7',
  'default_8',
];

export const useAvatar = (gender?: CustomerGender) => {
  return useCallback((avatarText: string) => AvatarList[avatarText || gender || CustomerGender.MALE], [gender]);
};
