/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-09-04 17:33:08
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-09-05 11:37:50
 * @Description:
 */
import type { FormInstance } from 'antd';

import { isEmpty, isString } from 'lodash-es';
import type { Moment } from 'moment';

import type { ColumnEditingType } from '@zhongan/nagrand-ui';

import type { AddressDisplayI18n } from 'genesis-web-component/lib/components';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';

import { FieldType } from '@/components/CommonForm';
import type { SchemaProps } from '@/types/common';
import { CustomerAddressFieldCodeList, CustomerZipCodeFieldCode } from '@/utils/constants';

export { EMAIL_PATTERN } from 'genesis-web-shared/lib/util/constants';

export const formatReg = /[=+\-@]/g;

export const NoPureSpace_PATTERN = /^(?!(\s+$))/;

export const InvalidPolicyNoReg = /[^A-Za-z0-9/-]/g;

export const MailMaxLength = 128;

/**
 *
 * @param source 依赖值
 * @param candidate 要展示的内容
 * @description 根据依赖值判断是否展示相应内容
 */
export const pickSafeString = (source: string | undefined, candidate: string): string =>
  isString(source) ? candidate : '';

/**
 *
 * @param schemas schemas
 * @description 将schema转为Table需要的Prop
 */
export const transferSchemaToTableProp = (schemas: SchemaProps[], addressI18nList?: AddressDisplayI18n[][]) =>
  schemas
    ?.sort((itemA, itemB) => itemA.order - itemB.order)
    ?.map(schema => ({
      title: schema.label,
      dataIndex: schema.key,
      render: (text, record, index) => {
        if (schema.type === 'datePicker') {
          return dateFormatInstance.getDateTimeString(text);
        } else if (schema.type === FieldType.Select && schema.ctrlProps && schema.ctrlProps.options?.length) {
          if (schema.ctrlProps.mode === 'multiple') {
            return text
              ?.split(',')
              ?.map((item: string) => schema.ctrlProps.options?.find(option => option.value === item)?.label)
              ?.join(',');
          }
          return schema.ctrlProps.options?.find(option => option.value === text)?.label;
        }
        const addressI18nInfo = addressI18nList?.[index];

        return addressI18nInfo?.find(item => item.key === schema.key)?.dictValueName ?? text;
      },
    }));

export const transferDynamicFieldsToEditableTableProp = (dynamicFields: Record<string, any>[]) =>
  dynamicFields.map(col => {
    const field: ColumnEditingType<any> = {
      title: col.label,
      dataIndex: col.key,
      fieldProps: {
        type: col.type,
        extraProps: {
          options: col.ctrlProps?.options,
        },
      },
      formItemProps: {
        rules: col.rules,
      },
      editable: true,
      width: 180,
    };
    if (col.type === FieldType.DatePicker) {
      field.formItemProps.valuePropName = 'displayValue';
      field.fieldProps = (record: Record<string, any>, form: FormInstance) => {
        const value = record?.extensions?.[col.key?.[col.key.length - 1]];
        return {
          type: col.type,
          extraProps: {
            value: value ? dateFormatInstance.l10nMoment(value) : undefined,
            onChange: (val: Moment) => {
              form.setFieldValue(col.key, val ? dateFormatInstance.formatTz(val) : undefined);
            },
          },
        };
      };
      field.render = text => dateFormatInstance.getDateTimeString(text);
    } else if (col.type === FieldType.Select) {
      if (col.ctrlProps?.mode === 'multiple') {
        field.formItemProps.valuePropName = 'displayValue';
        field.fieldProps = (record: Record<string, any>, form: FormInstance) => {
          const value = record?.extensions?.[col.key?.[col.key.length - 1]];
          return {
            type: col.type,
            extraProps: {
              options: col.ctrlProps?.options,
              mode: col.ctrlProps?.mode,
              value: value?.split(','),
              onChange: (val: string[]) => {
                form.setFieldValue(col.key, val?.length ? val.join(',') : undefined);
              },
            },
          };
        };
        field.render = text =>
          text
            ?.split(',')
            ?.map((item: string) => col.ctrlProps.options?.find(option => option.value === item)?.label)
            ?.join(',');
      } else {
        field.render = text => col.ctrlProps.options?.find(option => option.value === text)?.label;
      }
    }
    return field;
  });

/**
 *
 * @param source
 * @param target
 * @description 忽略大小写，判断source里是否包含target
 */
export const judgeIncludesIgnoreCase = (source: string, target: string) => {
  if (isEmpty(source) || isEmpty(target) || !isString(source) || !isString(target)) {
    return false;
  }
  return source.toLowerCase().includes(target.toLowerCase());
};

export const separateAddressFields = (staticFields: Record<string, any>[]) => {
  const addressFields: typeof staticFields = [];
  const nonAddressFields: typeof staticFields = [];

  staticFields.forEach(field => {
    if ([...CustomerAddressFieldCodeList, CustomerZipCodeFieldCode].includes(field.key)) {
      addressFields.push(field);
    } else {
      nonAddressFields.push(field);
    }
  });

  return [addressFields, nonAddressFields];
};
