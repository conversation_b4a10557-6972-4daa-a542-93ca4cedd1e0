/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-09-04 17:33:08
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-09-05 11:37:50
 * @Description:
 */
import { isEmpty, isString } from 'lodash-es';

import type { AddressDisplayI18n } from 'genesis-web-component/lib/components';
import { getAddressI18nInfo } from 'genesis-web-component/lib/components';

import type { SchemaProps } from '@/types/common';

export { EMAIL_PATTERN } from 'genesis-web-shared/lib/util/constants';

export const formatReg = /[=+\-@]/g;

export const NoPureSpace_PATTERN = /^(?!(\s+$))/;

export const InvalidPolicyNoReg = /[^A-Za-z0-9/-]/g;

export const MailMaxLength = 128;

/**
 *
 * @param source 依赖值
 * @param candidate 要展示的内容
 * @description 根据依赖值判断是否展示相应内容
 */
export const pickSafeString = (source: string | undefined, candidate: string): string =>
  isString(source) ? candidate : '';

/**
 *
 * @param schemas schemas
 * @description 将schema转为Table需要的Prop
 */
export const transferSchemaToTableProp = (schemas: SchemaProps[], addressI18nList: AddressDisplayI18n[][]) =>
  schemas
    ?.sort((itemA, itemB) => itemA.order - itemB.order)
    ?.map(schema => ({
      title: schema.label,
      dataIndex: schema.key,
      render: (text, record, index) => {
        const addressI18nInfo = addressI18nList?.[index];

        return addressI18nInfo?.find(item => item.key === schema.key)?.dictValueName ?? text;
      },
    }));

/**
 *
 * @param source
 * @param target
 * @description 忽略大小写，判断source里是否包含target
 */
export const judgeIncludesIgnoreCase = (source: string, target: string) => {
  if (isEmpty(source) || isEmpty(target) || !isString(source) || !isString(target)) {
    return false;
  }
  return source.toLowerCase().includes(target.toLowerCase());
};
