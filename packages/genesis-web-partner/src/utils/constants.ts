/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import type { DrawerProps } from 'antd/lib/drawer';

import { CustomerDataType } from 'genesis-web-service';

import i18nInstance from './i18n';

export enum CustomerType {
  PERSON = 'PERSON',
  COMPANY = 'COMPANY',
}

// customer配置页面只支持以下类型的配置
export const CustomerSubTypeArr = ['BASIC_INFO', 'ACCOUNT', 'ADDRESS', 'EMAIL', 'PHONE', 'SOCIAL_ACCOUNT', 'TAX_INFO'];

// company配置页面只支持以下类型的配置
export const CompanySubTypeArr = ['BASIC_INFO', 'CONTACT_PERSON', 'EMAIL', 'PHONE', 'ADDRESS', 'ACCOUNT'];

export const ExcludeFieldsInCustomer: Record<string, Record<string, Set<string>>> = {
  PERSON: {
    BASIC_INFO: new Set<string>([
      'certiType',
      'certiNo',
      'typeOfPass',
      'channelUserNo',
      'channelCode',
      'issuancePlaceOfCertificate',
      'issuanceDateOfCertificate',
      'expiryDateOfCertificate',
    ]),
  },
};

export const IdentifierFields = new Set<string>([
  'certiType',
  'certiNo',
  'typeOfPass',
  'issuancePlaceOfCertificate',
  'issuanceDateOfCertificate',
  'expiryDateOfCertificate',
]);

export const paginationSetting = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
};

export enum DrawerMode {
  Add = 'Add',
  Edit = 'Edit',
  View = 'View',
  Copy = 'copy',
}

export const getDrawerTitle = (mode: DrawerMode, title: string) =>
  ({
    [DrawerMode.Add]: i18nInstance.t('addDrawerTitle', { title }),
    [DrawerMode.Edit]: i18nInstance.t('editDrawerTitle', { title }),
    [DrawerMode.View]: i18nInstance.t('viewDrawerTitle', { title }),
    [DrawerMode.Copy]: i18nInstance.t('copyDrawerTitle', { title }),
  })[mode];

export enum RecipientType {
  BCC = 'BCC',
  CC = 'CC',
}

export const Placeholder = {
  Select: i18nInstance.t('Please select'),
  Input: i18nInstance.t('Please input'),
  Date: [i18nInstance.t('Start Date'), i18nInstance.t('End Date')],
};

export const DEFAULT_REQUIRED_RULES = {
  Select: [
    {
      required: true,
      message: i18nInstance.t('Please select'),
    },
  ],
  Input: [
    {
      required: true,
      message: i18nInstance.t('Please input'),
    },
  ],
};
export const FormItemRule = {
  PositiveInteger: {
    pattern: new RegExp(/^[1-9]\d*$/),
    message: i18nInstance.t('Please input positive integer'),
  },
  RoleSessionPattern: {
    pattern: new RegExp(/^[\w+=,.@-]*$/),
    message: 'Please input correct role session name',
  },
};

export const DefaultDrawerProps: DrawerProps = {
  destroyOnClose: true,
  maskClosable: false,
};

export enum MasterStateEnum {
  Locale = 'locale',
}

export const MinPageSizeOperation = ['5', '10', '20', '50', '100'];
export const TablePageSizeOptions = ['10', '20', '50', '100'];
export const CardPageSizeOptions = ['12', '24', '60', '120'];

export enum PartialMaskingRuleTypes {
  FROM_TO = 'From...to...',
  FROM = 'From',
  MORE_THAN = 'More than',
  COUNT_END = 'Count from the end',
}

// bizDictKey为countryCode的map，country code比较特殊，是需要用到itemExtend2的信息
export const CountryCodeFields: Record<string, boolean> = {
  countryCode: true,
  registeredPhoneCountryCode: true,
};

export const DefaultCardPagination = {
  current: 1,
  pageSize: 12,
  pageSizeOptions: CardPageSizeOptions,
};

export const FormItemGap = 100;
export const FormItemWidth = 260;
export const SelectInputFormItemSelectWidth = 140;
export const SelectInputFormItemInputWidth = FormItemWidth * 2 + FormItemGap - SelectInputFormItemSelectWidth;
export const SelectInputFormItemWidth = SelectInputFormItemSelectWidth + SelectInputFormItemInputWidth;

export const NewFormItemWidth = 280;
export const DrawerFormItemGap = 64;
export const DrawerWidth = NewFormItemWidth * 3 + FormItemGap * 2 + 24 * 2;
export const getDrawerWidth = (colNum: number) => NewFormItemWidth * colNum + DrawerFormItemGap * colNum + 24 * 2;

export const DefaultTablePagination = {
  current: 1,
  pageSize: 10,
  pageSizeOptions: TablePageSizeOptions,
};

export const YesOrNoOptions = [
  {
    label: i18nInstance.t('No'),
    value: false,
  },
  {
    label: i18nInstance.t('Yes'),
    value: true,
  },
];

// customer element config配置相关
export const CustomerZipCodeFieldCode = 'zipCode';

export enum CustomerAddressFieldCode {
  Address11 = 'address11',
  Address12 = 'address12',
  Address13 = 'address13',
  Address14 = 'address14',
  Address15 = 'address15',
  Address16 = 'address16',
  Address17 = 'address17',
  Address18 = 'address18',
}

export const CustomerAddressFieldCodeList = Object.values(CustomerAddressFieldCode);

export const CustomerSchemaDefRefMapping: Record<number, CustomerDataType> = {
  1: CustomerDataType.BASIC_INFO,
  2: CustomerDataType.ACCOUNT,
  3: CustomerDataType.ADDRESS,
  4: CustomerDataType.EMAIL,
  5: CustomerDataType.PHONE,
  13: CustomerDataType.SOCIAL_ACCOUNT,
  20: CustomerDataType.TAX_INFO,
  // organization的部分
  14: CustomerDataType.BASIC_INFO,
};

export const CustomerNameGroupFullNameMap: Record<number, string> = {
  1: 'fullName',
  2: 'fullName2',
  3: 'fullName3',
};

export const CustomerNameGroupChild: Record<string, string[]> = {
  fullName: ['firstName', 'middleName', 'lastName'],
  formattedFullName: ['formattedFirstName', 'formattedMiddleName', 'formattedLastName'],
  fullName2: ['firstName2', 'middleName2', 'lastName2'],
  formattedFullName2: ['formattedFirstName2', 'formattedMiddleName2', 'formattedLastName2'],
  fullName3: ['firstName3', 'middleName3', 'lastName3'],
  localFullName: ['localGivenName', 'localMiddleName', 'localSurname'],
};
