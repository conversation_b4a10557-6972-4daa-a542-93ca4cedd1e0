import { createI18nInstance } from 'genesis-web-shared/lib/i18n';

import partner_en_US from '../../locales/en_US.json';
import partner_hr_HR from '../../locales/hr_HR.json';
import partner_ja_<PERSON> from '../../locales/ja_JP.json';
import partner_tkf_en from '../../locales/tkf_en.json';
import partner_zh_CN from '../../locales/zh_CN.json';

const i18nInstance = createI18nInstance(['partner'], {
  'en-US': {
    partner: partner_en_US,
  },
  'ja-<PERSON>': {
    partner: partner_ja_<PERSON>,
  },
  'zh-CN': {
    partner: partner_zh_CN,
  },
  'tkf-EN': {
    partner: partner_tkf_en,
  },
  'hr-HR': {
    partner: partner_hr_HR,
  },
});

export const legacyLANG = (ns = ['partner']): Record<string, string> => {
  const LANG: Record<string, string> = {};
  return new Proxy(LANG, {
    get: (target: Record<string, string>, p: PropertyKey) => {
      if (typeof p === 'string') {
        return i18nInstance.t<string>(p, { ns });
      }

      return target[p as unknown as string];
    },
  });
};

export default i18nInstance;
