@import './variables.scss';

.antd-partner {
  :global {
    .#{$antd-prefix}-btn-link {
      color: var(--text-color);
    }
    .#{$antd-prefix}-btn-link[disabled] {
      color: var(--disabled-color);
    }

    .#{$antd-prefix}-form-vertical .#{$antd-prefix}-form-item-label {
      margin-bottom: 4px;
      padding-bottom: 0;
    }

    .#{$antd-prefix}-descriptions-item {
      padding-bottom: 8px;
    }
    .#{$antd-prefix}-card-body {
      padding: 16px 24px 24px;
    }
    .#{$antd-prefix}-tabs {
      background: var(--white);
    }
    .#{$antd-prefix}-tabs-tab-btn {
      width: 100%;
      text-align: center;
    }
    .#{$antd-prefix}-tabs-nav .#{$antd-prefix}-tabs-tab {
      padding-top: 16px;
      padding-bottom: 4px;
    }
    .#{$antd-prefix}-btn-lg {
      font-size: 14px;
    }

    .#{$antd-prefix}-drawer-footer {
      text-align: right;
      padding: 20px 32px;
      button {
        min-width: $min-btn-width;
        margin-left: 16px;
      }
    }
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)
      > .#{$antd-prefix}-menu-item:hover,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)
      > .#{$antd-prefix}-menu-submenu:hover,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)
      > .#{$antd-prefix}-menu-item-active,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)
      > .#{$antd-prefix}-menu-submenu-active,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)
      > .#{$antd-prefix}-menu-item-open,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)
      > .#{$antd-prefix}-menu-submenu-open,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)
      > .#{$antd-prefix}-menu-item-selected,
    .#{$antd-prefix}-menu-horizontal:not(.#{$antd-prefix}-menu-dark)
      > .#{$antd-prefix}-menu-submenu-selected {
      border-bottom: 2px solid var(--primary-color);
      font-weight: 700;

      &:after {
        display: none;
      }
    }

    .#{$antd-prefix}-menu-item:hover,
    .#{$antd-prefix}-menu-submenu-title:hover {
      color: var(--menu-item-hover-color) !important;
    }

    .#{$antd-prefix}-menu-item-selected {
      background-color: var(--menu-item-selected-bg) !important;
    }

    .#{$antd-prefix}-pro-table-list-toolbar-container {
      justify-content: start;
      padding: 32px 0 8px 0;
    }

    .#{$antd-prefix}-drawer-body {
      color: var(--text-color);
    }

    .#{$antd-prefix}-drawer-header-title {
      flex-direction: row-reverse; // close to the right

      > button {
        margin-right: 0;
      }
    }
  }
}

:global {
  .#{$antd-prefix}-space-compact {
    .#{$antd-prefix}-form-item {
      margin-bottom: 0;
    }
  }
}
