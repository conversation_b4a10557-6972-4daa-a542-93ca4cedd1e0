import type { ChannelSchemaDef } from 'genesis-web-service/lib/common.interface';
import type {
  BizDictItem,
  ChannelSchemaDefQueryResult,
} from 'genesis-web-service/lib/foundation/foundation.interface';
import { MetadataService } from 'genesis-web-service/lib/metadata/metadata.service';
import { useEffect, useState } from 'react';

export const useChannelSchemaFields = () => {
  const [channelSchemaDefFields, setChannelSchemaDefFields] = useState<
    ChannelSchemaDef[]
  >([]);
  const [usedBizDictkeys, setUsedBizDictkeys] = useState<string[]>([]);
  const [schemaUsedBizDictMap, setBizDictMap] =
    useState<Record<string, BizDictItem[]>>(); // 业务字典

  useEffect(() => {
    MetadataService.querySchemaDef({
      schemaDefType: 'CHANNEL',
    }).then((res: ChannelSchemaDefQueryResult) => {
      const tempUsedBizDictkeys: string[] = [];

      res.channelSchemaDefFields.forEach(section => {
        section.fields?.forEach(field => {
          if (field.bizDictKey) {
            tempUsedBizDictkeys.push(field.bizDictKey);
          }
        });
      });

      setChannelSchemaDefFields(res.channelSchemaDefFields);
      setUsedBizDictkeys(tempUsedBizDictkeys);
    });
  }, []);

  useEffect(() => {
    MetadataService.queryBizDict({ dictKeys: usedBizDictkeys }).then(res => {
      const bizData = res.reduce((out: Record<string, BizDictItem[]>, item) => {
        const mappedItem: BizDictItem = {
          ...item,
          label: item.dictValueName,
          value: (item.enumItemName || item.dictValue) as string,
        };
        if (out[mappedItem.dictKey]) {
          out[mappedItem.dictKey] = [...out[mappedItem.dictKey], mappedItem];
        } else {
          out[mappedItem.dictKey] = [mappedItem];
        }
        return out;
      }, {});

      setBizDictMap(bizData);
    });
  }, [usedBizDictkeys]);

  return {
    channelSchemaDefFields,
    schemaUsedBizDictMap,
  };
};
