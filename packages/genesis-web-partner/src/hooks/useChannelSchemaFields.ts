import { useEffect, useState } from 'react';

import type { ChannelSchemaDef } from 'genesis-web-service/lib/common.interface';
import { SchemaDefType } from 'genesis-web-service/lib/common.interface';
import type {
  BizDictItem,
  ChannelInsuranceSchemaDefQueryResult,
} from 'genesis-web-service/lib/foundation/foundation.interface';
import { MetadataService } from 'genesis-web-service/lib/metadata/metadata.service';

export const useChannelSchemaFields = () => {
  const [channelSchemaDefFields, setChannelSchemaDefFields] = useState<ChannelSchemaDef[]>([]);
  const [usedBizDictkeys, setUsedBizDictkeys] = useState<string[]>([]);
  const [schemaUsedBizDictMap, setBizDictMap] = useState<Record<string, BizDictItem[]>>(); // 业务字典

  useEffect(() => {
    Promise.all([
      MetadataService.querySchemaDef({
        schemaDefType: SchemaDefType.CHANNEL,
      }),
      MetadataService.querySchemaDef({
        schemaDefType: SchemaDefType.CHANNEL_INSURANCE,
      }),
    ]).then(res => {
      const tempUsedBizDictkeys: string[] = [];

      const fields = [
        ...res[0].channelSchemaDefFields,
        ...(res[1] as unknown as ChannelInsuranceSchemaDefQueryResult).channelInsuranceSchemaDefFields,
      ];
      fields.forEach(section => {
        section?.fields?.forEach(field => {
          if (field.bizDictKey) {
            tempUsedBizDictkeys.push(field.bizDictKey);
          }
        });
      });

      setChannelSchemaDefFields(fields);
      setUsedBizDictkeys(tempUsedBizDictkeys);
    });
  }, []);

  useEffect(() => {
    MetadataService.queryBizDict({ dictKeys: usedBizDictkeys }).then(res => {
      const bizData = res.reduce((out: Record<string, BizDictItem[]>, item) => {
        const mappedItem: BizDictItem = {
          ...item,
          label: item.dictValueName,
          value: (item.enumItemName || item.dictValue) as string,
        };
        if (out[mappedItem.dictKey]) {
          out[mappedItem.dictKey] = [...out[mappedItem.dictKey], mappedItem];
        } else {
          out[mappedItem.dictKey] = [mappedItem];
        }
        return out;
      }, {});

      setBizDictMap(bizData);
    });
  }, [usedBizDictkeys]);

  return {
    channelSchemaDefFields,
    schemaUsedBizDictMap,
  };
};
