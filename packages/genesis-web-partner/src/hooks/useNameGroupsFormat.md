# useNameGroupsFormat Hook 使用说明

## 概述

`useNameGroupsFormat` 是一个基于 `ahooks` 的 `useRequest` 实现的自定义 Hook，用于获取客户姓名组格式配置。它提供了缓存、错误处理和自动刷新等功能。

## 特性

- ✅ **缓存支持**: 使用全局缓存键 `nameGroupsFormat`，避免重复请求
- ✅ **页面刷新重新执行**: 页面获得焦点时自动刷新数据
- ✅ **错误重试**: 自动重试 2 次
- ✅ **缓存时间**: 5 分钟缓存时间
- ✅ **TypeScript 支持**: 完整的类型定义

## 基本用法

```tsx
import { useNameGroupsFormat } from '@/utils/utils';

const MyComponent = () => {
  const { data, loading, error, refresh } = useNameGroupsFormat();

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;

  return (
    <div>
      <button onClick={refresh}>刷新</button>
      {data && (
        <pre>{JSON.stringify(data, null, 2)}</pre>
      )}
    </div>
  );
};
```

## 返回值

| 属性 | 类型 | 描述 |
|------|------|------|
| `data` | `Record<string, CustomerNameGroupFormat> \| undefined` | 客户姓名组格式配置数据 |
| `loading` | `boolean` | 加载状态 |
| `error` | `Error \| undefined` | 错误信息 |
| `refresh` | `() => void` | 手动刷新数据的方法 |

## 配置选项

Hook 内部使用的 `useRequest` 配置：

```typescript
{
  cacheKey: 'nameGroupsFormat',        // 全局缓存键
  cacheTime: 5 * 60 * 1000,           // 5分钟缓存
  refreshOnWindowFocus: true,          // 页面获得焦点时刷新
  retryCount: 2,                       // 错误重试2次
}
```

## 与原方法的对比

### 原方法 (getNameGroupsFormat)
```typescript
// 每次调用都会发起新的请求
const data = await getNameGroupsFormat();
```

### 新 Hook (useNameGroupsFormat)
```typescript
// 自动缓存，支持状态管理
const { data, loading, error } = useNameGroupsFormat();
```

## 迁移指南

### 从原方法迁移到 Hook

**之前:**
```typescript
const [loading, setLoading] = useState(false);
const [data, setData] = useState(null);

useEffect(() => {
  const fetchData = async () => {
    setLoading(true);
    try {
      const result = await getNameGroupsFormat();
      setData(result);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };
  fetchData();
}, []);
```

**现在:**
```typescript
const { data, loading, error } = useNameGroupsFormat();
```

## 最佳实践

1. **在组件顶层使用**: Hook 应该在组件的顶层调用，不要在循环或条件语句中使用
2. **错误处理**: 始终检查 `error` 状态并提供适当的错误处理
3. **加载状态**: 使用 `loading` 状态提供良好的用户体验
4. **手动刷新**: 在需要时使用 `refresh` 方法手动刷新数据

## 注意事项

- Hook 只能在 React 函数组件或其他 Hook 中使用
- 数据会在页面刷新时重新获取
- 缓存是全局的，多个组件使用同一个 Hook 会共享缓存
- 如果需要不同的缓存策略，可以考虑传入参数来自定义配置
