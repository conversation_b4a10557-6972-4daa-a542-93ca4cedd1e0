import { useSelector } from '@umijs/max';

import { DefaultDateFormat } from 'genesis-web-shared/lib/util/constants';

import type { ConnectState } from '@/models/connect';

export const useTenantDateFormat = () => {
  return useSelector(({ global }: ConnectState) => global.tenantTimeInfo?.dateFormat ?? DefaultDateFormat);
};

// 获取租户设置的默认时区
export const useTenantTimezone = () => {
  return useSelector(({ global }: ConnectState) => Object.keys(global.tenantTimeInfo?.defaultZoneInfo ?? {})?.[0]);
};

// 获取全量时区列表
export const useTimezoneList = () => {
  return useSelector(({ global }: ConnectState) => global.tenantTimeInfo?.zoneOptionList ?? []);
};
