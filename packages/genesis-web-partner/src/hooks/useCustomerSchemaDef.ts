/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-09-04 17:33:07
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-09-04 18:19:51
 * @Description:
 */
import { useCallback, useEffect, useState } from 'react';

import { MetadataService } from 'genesis-web-service';
import type { CustomerSubType, SchemaDefField } from 'genesis-web-service';

import type { CustomerType } from '@/utils/constants';

type CustomerSchemaMap = Record<CustomerSubType | string, SchemaDefField[]>;

/**
 *
 * @param userType 用户类型；取自CustomerType
 * @description 获取用户配置schema
 */
export const useCustomerSchemaDef = (userType: CustomerType) => {
  const [customerSchemas, setCustomerSchemas] = useState<CustomerSchemaMap>();
  const getSchemas = useCallback(async () => {
    const { customerSchemaDefFields } = await MetadataService.querySchemaDef({
      schemaDefType: 'CUSTOMER',
    });
    let schemaFields: CustomerSchemaMap;
    if (customerSchemaDefFields) {
      schemaFields = customerSchemaDefFields
        .filter(field => field.customerType === userType)
        .reduce<CustomerSchemaMap>(
          (cur, next) => ({
            ...cur,
            [next.customerSubType]: next.fields,
          }),
          {} as CustomerSchemaMap
        );
    }
    setCustomerSchemas(schemaFields);
  }, [userType]);

  useEffect(() => {
    getSchemas();
  }, [getSchemas]);

  return customerSchemas;
};
