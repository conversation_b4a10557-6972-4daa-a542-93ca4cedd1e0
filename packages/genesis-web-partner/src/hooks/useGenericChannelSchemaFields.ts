import { useEffect, useState } from 'react';

import type { ChannelSchemaDef } from 'genesis-web-service/lib/common.interface';
import type { BizDictItem } from 'genesis-web-service/lib/foundation/foundation.interface';
import { MetadataService } from 'genesis-web-service/lib/metadata/metadata.service';

const getSchemaDefFieldsKey = (schemaDefType: string) => {
  const camelCaseType = schemaDefType.toLowerCase().replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  return `${camelCaseType}SchemaDefFields`;
};

export const useGenericChannelSchemaFields = (
  schemaDefType: 'CHANNEL' | 'CHANNEL_PARTNER' | 'CHANNEL_ORGANIZATION' | 'CHANNEL_INSURANCE'
) => {
  const [schemaDefFields, setSchemaDefFields] = useState<ChannelSchemaDef[]>([]);
  const [usedBizDictKeys, setUsedBizDictKeys] = useState<string[]>([]);
  const [schemaUsedBizDictMap, setBizDictMap] = useState<Record<string, BizDictItem[]>>();

  useEffect(() => {
    MetadataService.querySchemaDef({ schemaDefType }).then((res: any) => {
      const fieldsKey = getSchemaDefFieldsKey(schemaDefType);
      const fields = res[fieldsKey] || [];
      const tempUsedBizDictKeys: string[] = [];

      fields.forEach((section: any) => {
        section.fields?.forEach((field: any) => {
          if (field.bizDictKey) {
            tempUsedBizDictKeys.push(field.bizDictKey);
          }
        });
      });

      setSchemaDefFields(fields);
      setUsedBizDictKeys(tempUsedBizDictKeys);
    });
  }, [schemaDefType]);

  useEffect(() => {
    MetadataService.queryBizDict({ dictKeys: usedBizDictKeys }).then(res => {
      const bizData = res.reduce((out: Record<string, BizDictItem[]>, item: any) => {
        const mappedItem: BizDictItem = {
          ...item,
          label: item.dictValueName,
          value: (item.enumItemName || item.dictValue) as string,
        };
        if (out[mappedItem.dictKey]) {
          out[mappedItem.dictKey] = [...out[mappedItem.dictKey], mappedItem];
        } else {
          out[mappedItem.dictKey] = [mappedItem];
        }
        return out;
      }, {});

      setBizDictMap(bizData);
    });
  }, [usedBizDictKeys]);

  return {
    schemaDefFields,
    schemaUsedBizDictMap,
  };
};

export const useChannelSchemaFields = () => useGenericChannelSchemaFields('CHANNEL');
export const useChannelPartnerSchemaFields = () => useGenericChannelSchemaFields('CHANNEL_PARTNER');
export const useChannelOrganizationSchemaFields = () => useGenericChannelSchemaFields('CHANNEL_ORGANIZATION');
export const useChannelInsuranceSchemaFields = () => useGenericChannelSchemaFields('CHANNEL_INSURANCE');
