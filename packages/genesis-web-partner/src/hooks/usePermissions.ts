import type { ConnectState } from '@/models/connect';
import type { Permission } from 'genesis-web-service/model/common';
import { useSelector } from '@umijs/max';

export const usePermissionMap = (): Record<string, Permission> =>
  useSelector(({ global }: ConnectState) => global.permissionMap);

export const usePermission = (id: string): boolean => {
  const permissionMap = usePermissionMap();
  return !!permissionMap?.[id];
};
