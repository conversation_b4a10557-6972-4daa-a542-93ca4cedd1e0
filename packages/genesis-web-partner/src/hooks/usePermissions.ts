import { useSelector } from '@umijs/max';

import type { Permission } from 'genesis-web-service/model/common';

import type { ConnectState } from '@/models/connect';

export const usePermissionMap = (): Record<string, Permission> =>
  useSelector(({ global }: ConnectState) => global.permissionMap);

export const usePermission = (id: string): boolean => {
  const permissionMap = usePermissionMap();
  return !!permissionMap?.[id];
};
