/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import type { Theme } from '@zhongan/nagrand-ui';

import { MasterStateName } from 'genesis-web-shared/lib/util/constants';

import { useModel } from '@umijs/max';

export interface ClientConfig {
  version: string;
}

export interface StateFromMaster {
  locale: string;
  tenant: string;
  title?: string;
  theme?: Theme;
  messageDuration?: number;
}

export type StateKeyFromMaster = keyof StateFromMaster;

/**
 * @deprecated
 */
export const useMasterState = function <T = StateFromMaster[StateKeyFromMaster]>(key: StateKeyFromMaster): T {
  const state = useModel(MasterStateName) || {};
  return state[key] as T;
};
