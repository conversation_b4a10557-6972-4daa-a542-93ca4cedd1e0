import type { ChannelSchemaDef } from 'genesis-web-service/lib/common.interface';
import type { BizDictItem } from 'genesis-web-service/lib/foundation/foundation.interface';
import { createContext } from 'react';

export type ChannelSchemaFieldsContextType = {
  channelSchemaDefFields: ChannelSchemaDef[];
  schemaUsedBizDictMap: Record<string, BizDictItem[]>;
};

export const initialConfigInfo: ChannelSchemaFieldsContextType = {
  channelSchemaDefFields: [],
  schemaUsedBizDictMap: {},
};

export const ChannelSchemaFieldsContext =
  createContext<ChannelSchemaFieldsContextType>(initialConfigInfo);
