import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

import type { FormInstance } from 'antd';

import moment from 'moment';

import type { SchemaDefField } from 'genesis-web-service';
import { SchemaDataType, YesOrNo } from 'genesis-web-service';
import type { DateFormat } from 'genesis-web-shared/lib/l10n';

import { FieldType } from '@/components/CommonForm';
import { ChannelSchemaFieldsContext } from '@/hooks/ChannelSchemaFieldsContext';
import { ChannelTypeEnum, PartnerTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';

export enum EntityType {
  CHANNEL = 'CHANNEL',
  ORGANIZATION = 'ORGANIZATION',
  PARTNER = 'PARTNER',
  CHANNEL_INSURANCE = 'CHANNEL_INSURANCE',
}

type Option<T extends EntityType> = {
  staticOrDynamic?: 'STATIC' | 'DYNAMIC' | 'ALL';
  category: string;
  entityType: T;
  type: string;
  disabled?: boolean;
  fieldKeyPrefix?: string;
};

export const useGenericChannelSchemaFields = <T extends EntityType>({
  staticOrDynamic = 'ALL',
  category,
  entityType,
  type,
}: Option<T>) => {
  const { channelSchemaDefFields } = useContext(ChannelSchemaFieldsContext);

  const getFields = () => {
    switch (entityType) {
      case 'CHANNEL':
        return (
          channelSchemaDefFields.find(item => item.category === category && item.channelType === type)?.fields || []
        );
      case 'ORGANIZATION':
        return channelSchemaDefFields.find(item => item.category === category && item.type === type)?.fields || [];
      case 'PARTNER':
        return (
          channelSchemaDefFields.find(item => item.category === category && item.partnerType === type)?.fields || []
        );
      case 'CHANNEL_INSURANCE':
        return (
          channelSchemaDefFields.find(item => item.category === category && item.schemaDefType === entityType)
            ?.fields || []
        );
      default:
        return [];
    }
  };

  const renderedFields = getFields()
    .filter(field => {
      if (field.displayOnTanentLevel === YesOrNo.NO) return false;
      if (staticOrDynamic === 'STATIC') return field.isExtension === YesOrNo.NO;
      if (staticOrDynamic === 'DYNAMIC') return field.isExtension === YesOrNo.YES;
      return true;
    })
    .sort((a, b) => a.orderNo - b.orderNo);

  return renderedFields;
};

export const useGenericSchemaFormItemFields = <T extends EntityType>({
  staticOrDynamic = 'ALL',
  category,
  entityType,
  type,
  disabled,
  fieldKeyPrefix = 'extensions',
}: Option<T>) => {
  const { t } = useTranslation(['partner']);
  const { schemaUsedBizDictMap } = useContext(ChannelSchemaFieldsContext);

  const renderedFields = useGenericChannelSchemaFields({
    staticOrDynamic,
    category,
    entityType,
    type,
  });

  const formItems = renderedFields.map(field => {
    const basicFieldInfo: Record<string, any> = {
      key: staticOrDynamic === 'STATIC' ? field.code : [fieldKeyPrefix, field.code],
      label: field.displayName ?? field.name,
      type: FieldType.Input,
      rules:
        field.required === YesOrNo.YES
          ? [{ required: true, message: t('channel.common.required', { label: field.name }) }]
          : [],
      ...(entityType === EntityType.ORGANIZATION
        ? {
            col: 12,
            ctrlProps: {
              allowClear: true,
              disabled,
              style: {
                width: '100%',
              },
            },
          }
        : {
            ctrlProps: { allowClear: true, disabled },
          }),
    };

    switch (field.dataType) {
      case SchemaDataType.SELECT:
        basicFieldInfo.type = FieldType.Select;
        basicFieldInfo.ctrlProps.options = schemaUsedBizDictMap[field.bizDictKey!] || [];
        break;
      case SchemaDataType.DATETIME:
        basicFieldInfo.type = FieldType.DatePicker;
        basicFieldInfo.ctrlProps.showTime = true;
        break;
      case SchemaDataType.DATE:
        basicFieldInfo.type = FieldType.DatePicker;
        break;
      case SchemaDataType.NUMBER:
        basicFieldInfo.type = FieldType.InputNumber;
        basicFieldInfo.ctrlProps.style = { width: 240 };
        break;
      case SchemaDataType.MULTISELECT:
        basicFieldInfo.type = FieldType.Select;
        basicFieldInfo.ctrlProps = {
          ...basicFieldInfo.ctrlProps,
          options: schemaUsedBizDictMap[field.bizDictKey!] || [],
          mode: 'multiple',
          onChange: (value: string[], form: FormInstance) => {
            form.setFieldValue(basicFieldInfo.key, value?.length ? value.join(',') : undefined);
          },
        };
        basicFieldInfo.valuePropName = 'displayValue';
        break;
      default:
        break;
    }

    return basicFieldInfo;
  });

  return { formItems, schemaFields: renderedFields };
};

export const dealSchemaFieldValueForDisplay = (values: Record<string, any>, schemaFields: SchemaDefField[]) => {
  if (!values) return;

  const dateFieldCodes = schemaFields
    .filter(field => field.dataType === SchemaDataType.DATETIME || field.dataType === SchemaDataType.DATE)
    .map(field => field.code);

  for (const key in values) {
    if (dateFieldCodes.includes(key) && values[key]) {
      values[key] = moment(values[key]);
    }
  }
};

export const dealSchemaFieldValueForSubmit = (
  values: Record<string, any>,
  schemaFields: SchemaDefField[],
  options: { dateFormatInstance: DateFormat }
) => {
  if (!values) return;

  const { dateFormatInstance } = options;
  const dateFieldCodes = schemaFields
    .filter(field => field.dataType === SchemaDataType.DATETIME || field.dataType === SchemaDataType.DATE)
    .map(field => field.code);

  for (const key in values) {
    if (dateFieldCodes.includes(key) && values[key]) {
      values[key] = dateFormatInstance.formatTz(values[key]);
    }
  }
};

export const covertChannelType2SchemaChannelType = (channelTypeEnum: ChannelTypeEnum): string => {
  const map: Partial<Record<ChannelTypeEnum, string>> = {
    [ChannelTypeEnum.AGENCY]: 'AGENCY_COMPANY',
    [ChannelTypeEnum.SALE_CHANNEL]: 'SALES_PLATFORM',
    [ChannelTypeEnum.LeaseChannel]: 'LEASING_CHANNEL',
    [ChannelTypeEnum.Bank]: 'BANK',
    [ChannelTypeEnum.BrokerCompany]: 'BROKER_COMPANY',
    [ChannelTypeEnum.TiedAgent]: 'TIED_AGENT',
    [ChannelTypeEnum.FRONT_LINE_CHANNEL]: 'FRONT_LINE_CHANNEL',
    [ChannelTypeEnum.KEY_ACCOUNT_CHANNEL]: 'KEY_ACCOUNT_CHANNEL',
  };

  return map[channelTypeEnum];
};

export const covertPartnerType2SchemaPartnerType = (partnerTypeEnum: PartnerTypeEnum): string => {
  const map: Record<PartnerTypeEnum, string> = {
    [PartnerTypeEnum.Hospital]: 'HOSPITAL',
    [PartnerTypeEnum.Clinic]: 'CLINIC',
    [PartnerTypeEnum.Investigator]: 'INVESTIGATOR',
    [PartnerTypeEnum.LegalServiceCompany]: 'LEGAL_SERVICE_COMPANY',
    [PartnerTypeEnum.AssessmentCompany]: 'ASSESSMENT_COMPANY',
    [PartnerTypeEnum.ExternalInsuranceCompany]: 'EXTERNAL_INSURANCE_COMPANY',
    [PartnerTypeEnum.SERVICE_COMPANY]: 'SERVICE_COMPANY',
  };

  return map[partnerTypeEnum];
};
