import { useMemo } from 'react';

import { isNil } from 'lodash-es';

import type { BizDictItem } from 'genesis-web-service';

import type { ConnectState } from '@/models/connect';
import { useSelector } from '@umijs/max';

export const useTenantBizDict: (key?: string) => Record<string, BizDictItem[]> | BizDictItem[] = (bizKey?: string) => {
  const tenantBizDict: Record<string, BizDictItem[]> = useSelector(({ global }: ConnectState) => global.tenantBizDict);

  return useMemo(() => (bizKey ? tenantBizDict?.[bizKey] : tenantBizDict), [bizKey, tenantBizDict]);
};

export const useNationality: () => BizDictItem[] = () => {
  const countries = useTenantBizDict('country') as BizDictItem[];
  return useMemo(() => {
    return countries
      ?.filter(item => item.itemExtend3Desc)
      ?.map(item => ({
        ...item,
        label: item.itemExtend3Desc,
      }));
  }, [countries]);
};

/**
 *
 * @param keyField map key field，不传时默认取enums.enumItemName || enums.dictValue的值作为key
 * @description 处理全量enum map
 */
export const useTenantBizDictMap = (keyField?: 'enumItemName' | 'dictValue') => {
  const allBizDict = useTenantBizDict() as Record<string, BizDictItem[]>;

  return useMemo(() => {
    const bizDictMap: Record<string, Record<string, BizDictItem>> = {};

    Object.entries(allBizDict || {}).forEach(([key, value]) => {
      bizDictMap[key] = value.reduce(
        (out, current) => {
          const keyPath = keyField ? current[keyField] : (current.enumItemName ?? current.dictValue);
          if (!isNil(keyPath)) {
            out[keyPath] = current;
          }
          return out;
        },
        {} as Record<string, BizDictItem>
      );
    });
    return bizDictMap;
  }, [allBizDict, keyField]);
};
