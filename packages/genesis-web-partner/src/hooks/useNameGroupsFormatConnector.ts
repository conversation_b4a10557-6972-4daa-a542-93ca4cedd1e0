import { useCallback, useEffect } from 'react';

import type { FormInstance } from 'antd';

import { useDispatch, useSelector } from '@umijs/max';

import type { ConnectState } from '@/models/connect';
import { CustomerNameConnectedType, NameConnector } from '@/types/common';

export const useNameGroupsFormatChange = (form: FormInstance) => {
  const dispatch = useDispatch();

  const { connector } = useSelector(({ global }: ConnectState) => ({
    connector: global.nameGroupsFormatConnector,
  }));

  useEffect(() => {
    if (!connector) {
      dispatch({
        type: 'global/getNameGroupsFormatConnector',
      });
    }
  }, [dispatch, connector]);

  return useCallback(
    ({ nameList, fullNameKey }: { nameList: string[]; fullNameKey: string }) => {
      const fullNameValue = nameList
        .filter(Boolean)
        ?.join(connector ?? NameConnector[CustomerNameConnectedType.HalfWidth]);
      form?.setFieldValue(fullNameKey, fullNameValue);
      form.validateFields([fullNameKey]);
    },
    [connector]
  );
};
