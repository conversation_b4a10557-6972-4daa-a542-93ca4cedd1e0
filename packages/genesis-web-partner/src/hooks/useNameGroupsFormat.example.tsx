import React from 'react';
import { Spin, Alert } from 'antd';
import { useNameGroupsFormat } from '@/utils/utils';

/**
 * 使用 useNameGroupsFormat Hook 的示例组件
 */
export const NameGroupsFormatExample: React.FC = () => {
  // 使用 hook 获取数据
  const { data: nameGroupsFormat, loading, error, refresh } = useNameGroupsFormat();

  if (loading) {
    return <Spin tip="加载客户姓名组格式配置中..." />;
  }

  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error.message}
        type="error"
        showIcon
        action={
          <button onClick={refresh}>重试</button>
        }
      />
    );
  }

  return (
    <div>
      <h3>客户姓名组格式配置</h3>
      <button onClick={refresh}>刷新数据</button>
      
      {nameGroupsFormat && (
        <div>
          <h4>配置数据：</h4>
          <pre>{JSON.stringify(nameGroupsFormat, null, 2)}</pre>
        </div>
      )}
    </div>
  );
};

/**
 * 在表单中使用的示例
 */
export const FormWithNameGroupsFormat: React.FC = () => {
  const { data: nameGroupsFormat, loading } = useNameGroupsFormat();

  // 在表单中使用配置数据
  const handleFormSubmit = (values: any) => {
    if (nameGroupsFormat) {
      // 使用 nameGroupsFormat 处理表单数据
      console.log('表单数据:', values);
      console.log('姓名格式配置:', nameGroupsFormat);
    }
  };

  if (loading) {
    return <Spin />;
  }

  return (
    <div>
      {/* 你的表单组件 */}
      <p>表单组件会在这里，使用 nameGroupsFormat 数据</p>
    </div>
  );
};
