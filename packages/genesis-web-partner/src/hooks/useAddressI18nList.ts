import { useCallback, useEffect, useState } from 'react';

import { message } from 'antd';

import type { AddressDisplayI18n } from 'genesis-web-component/lib/components';
import { getAddressI18nInfo } from 'genesis-web-component/lib/components';
import type { AddressItem } from 'genesis-web-service';

import { CustomerType } from '@/utils/constants';

export const useAddressI18nList = (
  addresses: AddressItem[],
  customerType = CustomerType.PERSON
): AddressDisplayI18n[][] => {
  const [addressI18nList, setAddressI18nList] = useState<AddressDisplayI18n[][]>([]);

  const getAddressI18nList = useCallback(async () => {
    if (addresses?.length) {
      try {
        const tempAddressI18nList = await Promise.all(
          addresses.map(async addressItem => {
            const i18nInfo = await getAddressI18nInfo(addressItem, customerType);
            return i18nInfo;
          })
        );

        setAddressI18nList(tempAddressI18nList);
      } catch (error) {
        message.error((error as Error)?.message);
        setAddressI18nList([]);
      }
    } else {
      setAddressI18nList([]);
    }
  }, [customerType, addresses]);

  useEffect(() => {
    getAddressI18nList();
    return () => setAddressI18nList([]);
  }, [addresses, customerType]);

  return addressI18nList;
};
