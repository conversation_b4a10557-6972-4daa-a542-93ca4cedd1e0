import { useEffect, useState } from 'react';

import type { AddressDisplayI18n } from 'genesis-web-component/lib/components';
import { useGetAddressI18nData } from 'genesis-web-component/lib/components';
import type { AddressItem } from 'genesis-web-service';

import { CustomerType } from '@/utils/constants';

export const useAddressI18nList = (
  addresses: AddressItem[],
  customerType = CustomerType.PERSON
): AddressDisplayI18n[][] => {
  const [addressI18nList, setAddressI18nList] = useState<AddressDisplayI18n[][]>([]);

  const tempAddressI18nList = useGetAddressI18nData(addresses, customerType);
  useEffect(() => {
    setAddressI18nList(tempAddressI18nList ?? []);
  }, [addresses, customerType, tempAddressI18nList]);

  return addressI18nList;
};
