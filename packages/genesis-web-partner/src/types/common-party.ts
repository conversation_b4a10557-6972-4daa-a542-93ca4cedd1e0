/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-10-17 14:22:37
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-11-14 17:08:37
 * @Description:
 */
import type { ChannelTeamInfo, YesOrNo } from 'genesis-web-service';
import { InstituteTypeEnum } from 'genesis-web-service';
import type { ChannelTeamType } from 'genesis-web-service';

import type { ChannelTypeEnum } from '@/pages/common-party/partner-setting/models/index.interface';
import type { ModeEnum } from '@/types/common';

export interface GroupListType {
  id: number;
  goodsName: string[];
  goodsIds: number[];
  salesLicenseType: string;
  isDeleted: boolean;
}

export interface LocationQueryParam {
  id: string;
  parentId: string;
  level: string;
  configLevel: string;
  originMaxLevel?: string; // TODO: originMaxLevel为了兼容老数据没有maxLevel的情况，如果后续后端洗数据的话整个originMaxLevel都删掉
  configName: string;
  type: ChannelTypeEnum;
  modeType: ModeEnum;
  isVirtual: YesOrNo;
  teamType: ChannelTeamType;
  instituteType: string;
}

export interface AgentContextProps {
  updateTeamTree: (team: ChannelTeamInfo) => unknown;
}

export enum AgentFormColumnType {
  TeamInfo = 'TeamInfo',
  ManagerInfo = 'ManagerInfo',
  AgentInfo = 'AgentInfo',
}

export interface EmployeeContextProps {
  updateTeamTree: (team: ChannelTeamInfo) => unknown;
}

export enum EmployeeFormColumnType {
  TeamInfo = 'TeamInfo',
  EmployeeInfo = 'EmployeeInfo',
  RelativeInfo = 'RelativeInfo',
  DepartmentInfo = 'DepartmentInfo',
}

export enum WorkScheduleType {
  EmployeeManagement = 'employee-management',
  AssessmentCompany = InstituteTypeEnum.AssessmentCompany,
  Investigator = InstituteTypeEnum.Investigator,
}

export enum EmployeeWorkScheduleTypeTabEnum {
  WORKING_STATUS = 'WORKING_STATUS',
  WORKING_TYPE = 'WORKING_TYPE',
}
