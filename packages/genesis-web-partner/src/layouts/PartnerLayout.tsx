/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

/*
 * @Autor: za-tanyouwei
 * @Date: 2023-09-04 17:33:07
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-09-05 10:16:09
 * @Description:
 */
import { useEffect } from 'react';
import { AliveScope } from 'react-activation';
import { I18nextProvider } from 'react-i18next';
import { Provider } from 'react-redux';

import classNames from 'classnames';
import { createStore } from 'redux';

import type { Theme } from '@zhongan/nagrand-ui';
import '@zhongan/nagrand-ui/dist/antd-reset.scss';
import '@zhongan/nagrand-ui/dist/resources/styles/fonts/font.css';

import { CommonAntdConfigProvider, CommonNagrandConfigProvider, Copyright } from 'genesis-web-component/lib/components';
import { L10nProvider } from 'genesis-web-shared/lib/l10n';

import { useMasterState } from '@/hooks/masterState';
import type { ConnectState } from '@/models/connect';
import i18n from '@/utils/i18n';
import { Outlet, useDispatch, useSelector } from '@umijs/max';

import '../antd-legacy.scss';
import styles from '../antd-reset.scss';
import '../global.scss';

const AntdPrefix = 'antd-partner';

export default function PartnerLayout() {
  const { lang } = useSelector(({ global }: ConnectState) => global) || {};
  const { version: imageVersion } = useSelector(({ global }: ConnectState) => global.clientConfig) || {};
  const dispatch = useDispatch();
  const { dateFormat, zoneInfoList } = useSelector(({ global }: ConnectState) => global.tenantTimeInfo) || {};
  const { tenant } = useSelector(({ global }: ConnectState) => global.oAuthUserInfo) || {};
  const tenantTimeFormatByZeus =
    useSelector(({ global }: ConnectState) => {
      return global.tenantTimeFormatByZeus;
    }) || {};

  const reduxStore = createStore(
    state => state,
    // umi4 无法正常使用 master redux，这里手动传入一些公共组件（如 F-timezonepicker）需要的 state
    {
      timeZone: { dateFormat },
    }
  );

  const theme = useMasterState<Theme>('theme');
  const messageDuration = useMasterState<number>('messageDuration');
  useEffect(() => {
    dispatch({ type: 'global/getTenantTimeInfo' });
    dispatch({ type: 'global/getLocale' });
    dispatch({ type: 'global/getClientConfig' });
  }, [dispatch]);

  useEffect(() => {
    if (tenant) {
      dispatch({ type: 'global/getTenantTimeFormatByZeus', payload: tenant });
    }
  }, [dispatch, tenant]);

  return (
    <L10nProvider
      tenantDateTimeFormat={dateFormat}
      zoneInfoList={zoneInfoList}
      timeFormat={tenantTimeFormatByZeus?.timeFormat}
      timeRangeFormat={tenantTimeFormatByZeus?.timeRangeFormat}
    >
      <I18nextProvider i18n={i18n}>
        <div className={classNames('partner-global', styles.antdPartner)}>
          <CommonNagrandConfigProvider prefixCls={AntdPrefix} currentLang={lang}>
            <CommonAntdConfigProvider
              getTargetContainer={() => document.getElementsByClassName('partner-global')[0] as HTMLElement}
              getPopupContainer={() => document.getElementsByClassName('partner-global')[0] as HTMLElement}
              prefixCls={AntdPrefix}
              currentLang={lang}
              currentTheme={theme}
              messageDuration={messageDuration}
            >
              <Provider store={reduxStore}>
                <AliveScope>
                  <Outlet />
                </AliveScope>
              </Provider>
            </CommonAntdConfigProvider>
          </CommonNagrandConfigProvider>
          <Copyright version={imageVersion} whiteList={['/partner/relationship-management']} />
        </div>
      </I18nextProvider>
    </L10nProvider>
  );
}
