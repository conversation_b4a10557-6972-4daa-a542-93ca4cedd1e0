module.exports = {
  plugins: ['react'],
  extends: [
    require.resolve('@umijs/fabric/dist/eslint'),
    'prettier', // Uses eslint-config-prettier to disable ESLint rules from @typescript-eslint/eslint-plugin that would conflict with prettier
  ],
  parserOptions: { project: './tsconfig.json' },
  rules: {
    'no-confusing-arrow': 1,
    'no-plusplus': [2, { allowForLoopAfterthoughts: true }],
    'no-console': ['warn'],
    indent: ['warn', 2, { SwitchCase: 1 }],
    'arrow-parens': ['error', 'as-needed'],
    quotes: [
      'warn',
      'single',
      {
        avoidEscape: true,
        allowTemplateLiterals: true,
      },
    ],
    'react/jsx-curly-brace-presence': ['warn', { props: 'never' }],
  },
};
