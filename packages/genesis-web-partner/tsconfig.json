{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "importHelpers": true,
    "jsx": "react-jsx",
    "strictNullChecks": false,
    "esModuleInterop": true,
    "sourceMap": true,
    "baseUrl": "./",
    "strict": true,
    "paths": {
      "@/*": ["src/*"],
      "@@/*": ["src/.umi/*"],
      // 支持 IDE 跳转
      "genesis-web-component": ["../genesis-web-component"],
      "genesis-web-component/*": ["../genesis-web-component/*"],
      "genesis-web-service": ["../genesis-web-service"],
      "genesis-web-service/*": ["../genesis-web-service/*"],
      "genesis-web-shared": ["../genesis-web-shared"],
      "genesis-web-shared/*": ["../genesis-web-shared/*"]
    },
    "allowSyntheticDefaultImports": true
  },
  "include": ["mock/**/*", "src/**/*", "config/**/*", ".umirc.ts", "typings.d.ts", ".eslintrc.js"],
  "exclude": ["node_modules", "lib", "es", "dist", "typings", "**/__test__", "test", "docs", "tests", "dependencies"]
}
