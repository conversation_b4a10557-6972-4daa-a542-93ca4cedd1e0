{"0": "", "50": "", "--": "", "(n)th": "", "{{frequency}}(s)": "", "{{name}} Account": "", "{{name}} Account Status": "", "{{year}} Work Schedule": "", "00": "", "1st": "", "21st": "", "22nd": "", "23rd": "", "2nd": "", "31st": "", "3rd": "", "500+": "", "A total of {totalNumber} documents were uploaded, {failNumber} of them failed to upload.": "", "Abbreviation": "", "Account": "", "Account Information Settings": "", "account number": "", "Account Type": "", "Account Usage": "", "Accumulated to Premium": "", "Action": "", "Actions": "", "Add": "", "Add {{Team}} Information": "", "Add Account Information Settings": "", "Add Address": "", "Add Branch": "", "Add Contact Person Information": "", "Add EmployeeBranch": "", "Add New": "", "Add New Address": "", "Add New Agency": "", "Add New Agent": "", "Add New at the Same Level": "", "Add New at the Sub Level": "", "Add New Bank Account": "", "Add New Branch": "", "Add New Branch Manager": "", "Add New Channel": "", "Add New Contacts Person": "", "Add New Department": "", "Add New Doctor": "", "Add New Employee": "", "Add New Group": "", "Add New Headquarter": "", "Add New Insurance Company": "", "Add New Level": "", "Add New License Type": "", "Add New Manager": "", "Add New Relative": "", "Add New Retail": "", "Add New Sales Agreement": "", "Add New Sales Channel": "", "Add New Service Agreement": "", "Add New Service Company": "", "Add New Staff": "", "Add New Team": "", "Add New Team Manager": "", "Add Reconciliation": "", "Add Region": "", "Add successfully": "", "Address Type": "", "Address Zip Code": "", "After the agent type is configured, it can not be modified. Please confirm.": "", "Agency": "", "Agency Code": "", "Agency Company": "", "Agency Name": "", "Agency No": "", "Agent": "", "Agent Code": "", "Agent Information": "", "Agent Name": "", "Agent Sales Hierarchy": "", "Agent Sales Hierarchy Configuration": "", "Agent Type": "", "Agent Type Configuration": "", "All affiliated partner information will be deleted at the same time. Are you sure to delete this record?": "", "and in formula <commGST> we could use schema factor netCommission calculated in order 1 to configure formula": "", "API Content:": "", "Are you sure to change Partner status to {status}?": "", "Are you sure to change the status? The resigned agent will not be able to continue to issue policies.": "", "Are you sure to delete this bank and all relevant information?": "", "Are you sure to delete this branch and all relevant information?": "", "Are you sure to delete this branch?": "", "Are you sure to delete this broker company and all relevant information?": "", "Are you sure to delete this headquarter and all relevant branch information?": "", "Are you sure to delete this record?": "", "Are you sure to delete this region and all relevant branch information?": "", "Are you sure to delete this relationship info?": "", "Are you sure to delete this retail and all relevant region/branch information?": "", "Are you sure to delete?": "", "Are you sure to Submit?": "", "Assessment Company Code": "", "Assessment Company Comments": "", "Assessment Company Name": "", "Associated": "", "at the Same Level": "", "at the Sub Level": "", "Attachment": "", "Back": "", "Back to task pool": "", "Bank": "", "bank account": "", "Bank Code": "", "Bank Name": "", "Base on Sales Channel": "", "Basic Info": "", "Basic Information": "", "Batch Number": "", "Batch tips Date Range": "", "Batch tips Day": "", "Batch Upload": "", "Belonging Agency": "", "Blank Template": "", "Blank Template: An upload template that does not contain any data. Users can add new data to create, edit, or delete employee information and its internal details.'": "", "Blank Template: An upload template that does not contain any data. Users can add new data to create, edit, or delete partner information and its internal details (deletion applies only to some related information).": "", "BP Number in SAP": "", "Branch": "", "Branch Code": "", "Branch Information": "", "Branch Manager Information": "", "Branch manager is existing, you can’t add more branch manager": "", "Branch Name": "", "Broker Company": "", "Broker Company Code": "", "Broker Company Name": "", "Business Activity Description": "", "Cancel": "", "Category": "", "Certificate No": "", "Certificate Type": "", "Change the status failed.": "", "Change the status successfully.": "", "Changing the classification will clear all the details. Confirm to change?": "", "Changing the goods name will affect the selling authority, please confirm.": "", "channel": "", "Channel Code": "", "Channel Name": "", "Channel No": "", "channel.common.required": "", "Clawback calculation method is configured here while where to trigger clawback calculation is defined in business module configuration such as POS item agreement.": "", "Click or drag the file here to upload": "", "Click to add channel/insurance company": "", "Click to add relationship": "", "Close": "", "Code": "", "Collapse": "", "collection service fee(%)": "", "Comments": "", "Commission Collection Service Fee(%)": "", "Commission Settlement Method": "", "Commission Type": "", "Commission&Commission Clawback Rule Configuration": "", "Complete Data Set": "", "Complete Data Set: An upload template that contains all data. Users can select portions of this data for processing.": "", "Completed": "", "Condition Query": "", "Configuration": "", "Configure the association between fields": "", "Confirm": "", "Contact Number": "", "Contact Person": "", "Contact Person ID": "", "Contact Person ID Type": "", "Contact Person Information": "", "Contacts Name": "", "Contacts Person": "", "Contacts Person Birthday": "", "Contacts Person Type": "", "Content": "", "Contract Service": "", "Country": "", "Country Code": "", "Create": "", "Create Time": "", "Currency": "", "Current Work Schedule": "", "Custom Cycle": "", "Customized Query Conditions Setting": "", "Data cannot be selected repeatedly": "", "Date of Birth": "", "Day(s)": "", "Default Settlement Method": "", "Delete": "", "Delete successfully": "", "Department": "", "Department & Doctor": "", "Department Code": "", "Department Name": "", "Department Type": "", "Description": "", "Details": "", "Diagnosis": "", "Display Name": "", "Doctor": "", "Doctor ID type": "", "Doctor Name": "", "Document": "", "Does the settled commission need to be transferred to the new sales channel": "", "Does the settled commission need to be transferred to the new sales channel?": "", "download": "", "Download": "", "Download Template": "", "Download to View Details": "", "Duplicate formula configuration for same formula category and sub category. Please edit original one.": "", "During from": "", "E-mail": "", "Edit": "", "Edit Account Information Settings": "", "Edit Address": "", "Edit Agent": "", "Edit Branch": "", "Edit Branch Information": "", "Edit Branch Manager": "", "Edit Contact Person Information": "", "Edit Department Information": "", "Edit Doctor information": "", "Edit Employee Information": "", "Edit Group": "", "Edit Headquarter Information": "", "Edit License Type": "", "Edit Manager": "", "Edit Reconciliation": "", "Edit Relative Information": "", "Edit Sales Agreement": "", "Edit Service Agreement": "", "Edit Staff information": "", "Edit Team": "", "Edit Team Information": "", "Edit Team Manager": "", "Edited successfully": "", "Effective": "", "Effective Date": "", "Effective Period": "", "Email": "", "Email is invalid": "", "Employee": "", "Employee Code": "", "Employee Department Name": "", "Employee Information": "", "Employee Management": "", "Employee Name": "", "EmployeeBranch": "", "End of month (default value: 31st)": "", "Error": "", "Error Detail": "", "EU member": "", "Every": "", "Every {{dayInterval}} day {{time}}": "", "Every {{n}}": "", "Every {{weekInterval}} weeks on {{weekday}}": "", "Every day {{time}}": "", "Every other day {{time}}": "", "Every other week on {{weekday}}": "", "Every week on {{weekday}}": "", "everyday": "", "Exclusive Agency or Not": "", "Existed rows": "", "Expand All": "", "Experienced Fee": "", "Experienced Fee is required; {{name}} is required": "", "Expiry Date": "", "External Code": "", "External Insurance Company": "", "External Insurance Company Code": "", "External Insurance Company Name": "", "Extraction Day": "", "Extraction Duration": "", "Extraction Frequency": "", "Extraction Schedule Date": "", "Fax Number": "", "File Creator": "", "file name": "", "File Name": "", "File Path": "", "File Upload Time": "", "Fill in the existing sales agreement": "", "Fill Information": "", "Financial Institutions": "", "Financial Institutions Branch": "", "First Name": "", "Fixed Rate": "", "Formula Category": "", "Formula Category (Clawback)": "", "Formula Code": "", "Formula Name": "", "Formula Name (Clawback)": "", "Formula Sub Category": "", "Formula Sub Category (Clawback)": "", "Frequency": "", "Front Line Channel": "", "Front Line Channel Code": "", "Front Line Channel Name": "", "Frozen": "", "Full Re-generation": "", "Gender": "", "Goods": "", "Goods Name": "", "Group": "", "Group By Selection": "", "Group Name": "", "GST Registered Organization": "", "GST Registration Number": "", "Headquarter": "", "Headquarter Code": "", "Headquarter Information": "", "Headquarter Name": "", "History": "", "Hotline": "", "IBAN": "", "ID No": "", "ID Number": "", "ID Type": "", "If Commission GST is configured based on NetCommission, then we could configure as": "", "If other sales channels have used this sales agreement, editing it will impact the related sales channels. Please check before making changes.": "", "If some months do not have this date, the configured date automatically becomes the last day of the month.": "", "If the switch turns on, sub-channel data will be reconciled together.": "", "If the value is equal to Yes, the premium collection or refund will through Claim.": "", "If turn on the switch, then all historical un-settled data will be included.": "", "in": "", "In": "", "In Progress": "", "In Total": "", "Individual or Organization Agency": "", "Institute": "", "Institute Code": "", "Institute Grade": "", "Institute Name": "", "Institute Status": "", "Institute Type": "", "Insurance Company": "", "Insurance Company Code": "", "Insurance Company Name": "", "Insurance Company No": "", "Introduction": "", "Invalid rows": "", "Investigation Area": "", "Investigation Hospital": "", "Investigator": "", "Investigator Code": "", "Issue Date": "", "Issue without payment": "", "It should be noted that switching the agent sales hierarchy will cause the agent information to be cleared.": "", "Key Account Channel": "", "Key Account Channel Code": "", "Key Account Channel Name": "", "Last Name": "", "Leasing Channel": "", "Leasing Channel Code": "", "Leasing Channel Name": "", "Legal Service Company": "", "Legal Service Company Code": "", "Legal Service Company Name": "", "Level": "", "License Effective Date": "", "License No.": "", "loading": "", "Login Account Type": "", "Logo": "", "Long Term": "", "Malaysia Standard Industrial Classification (MSIC) Code": "", "Manager": "", "Mapping with Organization": "", "Mobile Phone": "", "month": "", "Monthly": "", "months": "", "More Partners below": "", "Name": "", "Name is required": "", "Name of Account Holder": "", "Name_Code": "{{name}}_{{code}}", "Nationality": "", "Native Account": "", "Network": "", "New Sales Agreement": "", "New Sales Channel": "", "next": "", "Next": "", "Next {{n}}": "", "No": "", "No content yet": "", "No search results found": "", "Number of Records": "", "of the Current Month": "", "of the Next 2 Month": "", "of the Next 2 Week": "", "of the Next Month": "", "of the Next Week": "", "Office City": "", "Office Hour": "", "On": "", "On {{date}} of every {{yearInterval}} year": "", "On {{date}} of every other year": "", "On {{date}} of every year": "", "On day {{date}} of every {{monthInterval}} months": "", "On day {{date}} of every month": "", "On day {{date}} of every other month": "", "Onboard Date": "", "onRecurrenceDate": "", "Opening Date": "", "Operated successfully": "", "Operation Time": "", "Operator": "", "Order": "", "Organization ID No.": "", "Organization ID Type": "", "Original Sales Agreement Code": "", "Original Sales Channel Code": "", "Other Insurance Company Name": "", "Other Related Business": "", "out": "", "Out": "", "Overriding Commission": "", "Panel Hospital": "", "Params are invalid": "", "Partial Re-generation": "", "Partner": "", "Partner Code": "", "Partner Details": "", "Partner List": "", "Partner List Upload History": "", "Partner Management": "", "Partner Name": "", "Partner Type": "", "Pay Method": "", "Payment Method": "", "Payment Method / Account Type": "", "Payment/Collection": "", "Payment/Collection Method": "", "Period of Validity": "", "Period Start Date": "", "period_validity": "", "Personal Work Schedule": "", "Phone No.": "", "Phone Number": "", "Please complete branch information first.": "", "Please complete Team Information first": "", "Please configure insurance company, agency, sales channel and relationship before saving": "", "Please create the basic information first.": "", "Please Download Template First": "", "Please enter a valid character": "", "Please enter at least one condition": "", "Please enter at least one search criteria": "", "Please finish editing first": "", "Please Input": "", "Please input correct firstname.": "", "Please input correct format": "", "Please input correct lastname.": "", "Please input Employee Code/Name": "", "Please input in correct format": "", "Please input Sales Channel Code/Name": "", "Please Select": "", "Please Select a company": "", "Please Select a Sales Channel": "", "Please select a sales channel first.": "", "Please select a specific date first.": "", "Please select an employee first.": "", "Please select an Organization ID Type": "", "Please select correct upload template(xlsx)": "", "Please select related formula for Commission": "", "Please select related formula for Commission Clawback": "", "Please select the number of sales hierarchy": "", "Please select to add at least one relationship": "", "Please Upload File": "", "Policy Effective Date": "", "Policy No.": "", "Policy Transfer": "", "Policy Transfer Reason": "", "Policyholder Name": "", "Position": "", "Premium Collection and Refund for Customer": "", "Private": "", "Public": "", "Public/Private": "", "Rate for Additional": "", "Rate for Initial": "", "Rate per KM": "", "Re-generation Option": "", "Re-Upload": "", "reconciled": "", "Reconciliation": "", "Reconciliation Code": "", "Reconciliation Configuration": "", "Reconciliation Data: data that already reconciled.": "", "Reconciliation Data: data that already reconciled. Reconciliation Data: data that already reconciled": "", "Reconciliation Detail": "", "Reconciliation Fields": "", "Reconciliation File Format": "", "Reconciliation Frequency": "", "Reconciliation Goods Code": "", "Reconciliation Method": "", "Reconciliation Method & File": "", "Reconciliation Period": "", "Reconciliation Policy Dimension": "", "Reconciliation Recurrence": "", "Reconciliation Recurrence Date": "", "Reconciliation Result Display": "", "Reconciliation Time Zone": "", "Reconciliation& Settlement": "", "Reconciliation& Settlement Configuration": "", "Reconciliation&Settlement": "", "Reconciliation&Settlement Configuration": "", "Record": "", "Records": "", "Records / Number of total records": "", "Records_TotalRecords": "", "Recurrence Date": "", "Region": "", "Register": "", "Registration": "", "Registration Date": "", "Relation Setting": "", "Relationship": "", "Relationship Basic Info": "", "Relationship Code": "", "Relationship List": "", "Relationship Management": "", "Relationship Name": "", "Relationship No.": "", "Relationship of Agency & Channel": "", "Relationship of Insurance Company & Agency": "", "Relationship of Insurance Company & Service Company": "", "Relationship Setting": "", "Relationship With Employee": "", "Relative": "", "Relative Name": "", "Remark": "", "Repeat Every": "", "Resign Date": "", "Response Message": "", "Result": "", "Retail": "", "Saleable Insurance Product Type": "", "Sales Agreement": "", "Sales Agreement Code": "", "Sales Agreement List": "", "Sales Agreement Name": "", "Sales Agreement Name/Code": "", "Sales Agreement Signing Party": "", "Sales Agreement Status": "", "Sales Agreement(After Change)": "", "Sales Agreement(Before Change)": "", "Sales Authority Information": "", "Sales Channel": "", "Sales Channel Code": "", "Sales Channel Information": "", "Sales Channel Name": "", "Sales Channel Name/Code": "", "Sales Channel Type": "", "Sales Channel(After Change)": "", "Sales Channel(Before Change)": "", "Sales Hierarchy": "", "Sales License Type": "", "Sales Platform": "", "Sales Related Information": "", "Same Level": "", "Save": "", "Save Success": "", "Save successfully!": "", "Search": "", "Search by name": "", "Search Criteria {{n}}": "", "Select All": "", "Select Employee": "", "Select Employee or Channel Staff": "", "Select Reconciliation": "", "Selected Reconciliation Code": "", "Self Agent": "", "Self-agency": "", "Self-channel": "", "Service Agreement": "", "Service Agreement Code": "", "Service Agreement information has been updated. Please submit a new service agreement code.": "", "Service Agreement Name": "", "Service Agreement Status": "", "Service Company": "", "Service Company Code": "", "Service Company Name": "", "Service Related Information": "", "Setting Code": "", "settled": "", "settlement": "", "Settlement": "", "Settlement Configuration": "", "Settlement Currency": "", "Settlement File Format": "", "Settlement Frequency": "", "Settlement Method": "", "Settlement Method Category": "", "Settlement Only": "", "Settlement Option": "", "Settlement Period": "", "Settlement Recurrence": "", "Settlement Recurrence Date": "", "Settlement Rule": "", "Settlement Scope": "", "Settlement Sources": "", "Settlement Time Type": "", "Settlement Time Zone": "", "Settlement Type": "", "Short Institute Name": "", "Sorry, the transfer can't be conducted, because failed to find the same  Agent Type, Sales Channel Code or Sales Agreement Code on the chosen policies, please check again.": "", "Source Data: data that do not need reconciliation.": "", "SSO Account": "", "SST Registration Number": "", "Staff": "", "Staff Code": "", "Staff ID": "", "Staff Name": "", "StaffId is required": "", "Start from": "", "Status": "", "Structure Details": "", "Sub Bill Item": "", "Sub Level": "", "Sub-level Usage Permission": "", "Submit": "", "Surgery": "", "SWIFT Code": "", "System will delete the hospital and the doctors, experienced fee under the hospital. Confirm to delete?": "", "System will delete the investigator. Confirm to delete?": "", "t - 1": "", "Taxpayer ID number": "", "Team Code": "", "Team Information": "", "Team Manager": "", "Team Manager Information": "", "Team manager is existing, you can’t add more team manager": "", "Team Name": "", "Team-Code": "", "Team-Name": "", "Telephone": "", "Terminated": "", "The new added address information cannot be empty": "", "The new added Contact Person Information cannot be empty": "", "The policy has been transferred successfully.": "", "The same partner already exists. If you click \"Save\", the new information will overwrite the old information, please confirm.": "", "The same partner already exists. If you click Save\"": "", "The same partner already exists. If you click Save\",The same partner already exists. If you click Save\"\"": "", "The upload file size is too large. Upload failed.": "", "The vacation end time needs to be after the current time.": "", "There is a duplicate department name in the newly added information, please confirm.Please": "", "There is a duplicate department name in the uploaded file. Upload fails.": "", "There is still required information that has not been saved, please fill in all required information first!": "", "Third-party assessment company user name": "", "This flow will run": "", "Tied Agent": "", "TIN": "", "Tips": "", "Title": "", "to": "", "To config the columns that displayed in downloaded reconciliation result file.": "", "To config the reconciliation fields, only when all selected fields were matched, the reconciled record is marked as Matched.": "", "To config the unique key to directing one same business transaction within provided reconciliation file and local data source.": "", "Today": "", "Total upload number": "", "Total:": "", "Transaction Business Type": "", "Transaction Type": "", "Unique Combination Fields": "", "Unknown error, Please check your file or retry later": "", "Upload": "", "Upload Date": "", "Upload failed information": "", "Upload File": "", "Upload Record": "", "Upload Size Limit": "", "Upload successfully number": "", "Uploaded failed": "", "Uploaded successfully": "", "Uploading files will overwrite historical data, please confirm whether it needs to be modified.": "", "Vacation Date": "", "VAT Number": "", "View": "", "View Account Information Settings": "", "View Address": "", "View Agent": "", "View Branch": "", "View Branch Manager": "", "View Contact Person Information": "", "View Department Information": "", "View Doctor information": "", "View Employee Information": "", "View Headquarter Information": "", "View Manager": "", "View Relative Information": "", "View Sales Agreement": "", "View Service Agreement": "", "View Staff information": "", "View Structure": "", "View Team": "", "View Team Manager": "", "Warning": "", "Website": "", "week": "", "Weekly": "", "weeks": "", "Whether Base on Sales Channel": "", "Whether including all historical data": "", "Whether Settlement": "", "Whether to create {{name}} account?": "", "Whether to generate debit note details list?": "", "Whether to Include 2nd level Channel": "", "Work Schedule Management": "", "Working Status": "", "Working Type": "", "Working Type Detail": "", "Workload Rate": "", "Yes": "", "You can only upload  CSV/EXCEL/JPG/WORD/PDF": "", "You can only upload PDF/DOC/XLS/PNG/JPG": "", "You need to select specific agent, please check.": "", "You should choose a policy first!": "", "Your file has been submitted. Please wait patiently for the result.": "", "Policy Transfer Effective Date": "", "Zip Code": ""}