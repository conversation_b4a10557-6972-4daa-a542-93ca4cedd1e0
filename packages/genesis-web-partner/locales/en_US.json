{"0": "0", "50": "50", "--": "--", "(n)th": "{{n}}th", "{{frequency}}(s)": "{{frequency}}(s)", "{{name}} Account": "{{name}} Account", "{{name}} Account Status": "{{name}} Account Status", "{{year}} Work Schedule": "{{year}} Work Schedule", "00": "00", "1st": "1st", "21st": "", "22nd": "", "23rd": "", "2nd": "2nd", "31st": "", "3rd": "3rd", "500+": "500+", "A total of {totalNumber} documents were uploaded, {failNumber} of them failed to upload.": "A total of {{totalNumber}} documents were uploaded, {{failNumber}} of them failed to upload.", "Abbreviation": "Abbreviation", "Account": "Account", "Account Information Settings": "Account Information Settings", "account number": "account number", "Account Type": "Account Type", "Account Usage": "Account <PERSON><PERSON>", "Accumulated to Premium": "Accumulated to Premium", "Action": "Action", "Actions": "Actions", "Add": "Add", "Add {{Team}} Information": "Add {{team}} Information", "Add Account Information Settings": "Add Account Information Settings", "Add Address": "Add Address", "Add Branch": "Add Branch", "Add Contact Person Information": "Add Contact Person Information", "Add EmployeeBranch": "Add Branch", "Add New": "Add New", "Add New Address": "Add New Address", "Add New Agency": "Add New Agency", "Add New Agent": "Add New Agent", "Add New at the Same Level": "Add New at the Same Level", "Add New at the Sub Level": "Add New at the Sub Level", "Add New Bank Account": "Add New Bank Account", "Add New Branch": "Add New Branch", "Add New Branch Manager": "Add New Branch Manager", "Add New Channel": "Add New Channel", "Add New Contacts Person": "Add New Contacts Person", "Add New Department": "Add New Department", "Add New Doctor": "Add New Doctor", "Add New Employee": "Add New Employee", "Add New Group": "Add New Group", "Add New Headquarter": "Add New Headquarter", "Add New Insurance Company": "Add New Insurance Company", "Add New Level": "Add New Level", "Add New License Type": "Add New License Type", "Add New Manager": "Add New Manager", "Add New Relative": "Add New Relative", "Add New Retail": "Add New Retail", "Add New Sales Agreement": "Add New Sales Agreement", "Add New Sales Channel": "Add New Sales Channel", "Add New Service Agreement": "Add New Service Agreement", "Add New Service Company": "Add New Service Company", "Add New Staff": "Add New Staff", "Add New Team": "Add New {{team}}", "Add New Team Manager": "Add New {{team}} Manager", "Add Reconciliation": "Add Reconciliation", "Add Region": "Add Region", "Add successfully": "Add successfully", "Address Type": "Address Type", "Address Zip Code": "Address Zip Code", "After the agent type is configured, it can not be modified. Please confirm.": "After the agent type is configured, it can not be modified. Please confirm.", "Agency": "Agency", "Agency Code": "Agency Code", "Agency Company": "Agency Company", "Agency Name": "Agency Name", "Agency No": "Agency No", "Agent": "Agent", "Agent Code": "Agent Code", "Agent Information": "Agent Information", "Agent Name": "Agent Name", "Agent Sales Hierarchy": "Agent Sales Hierarchy", "Agent Sales Hierarchy Configuration": "Agent Sales Hierarchy Configuration", "Agent Type": "Agent Type", "Agent Type Configuration": "Agent Type Configuration", "All affiliated partner information will be deleted at the same time. Are you sure to delete this record?": "All affiliated partner information will be deleted at the same time. Are you sure to delete this record?", "and in formula <commGST> we could use schema factor netCommission calculated in order 1 to configure formula": "and in formula <commGST> we could use schema factor netCommission calculated in order 1 to configure formula", "API Content:": "API Content: {{-content}}", "Are you sure to change Partner status to {status}?": "Are you sure to change Partner status to {{status}}?", "Are you sure to change the status? The resigned agent will not be able to continue to issue policies.": "Are you sure to change the status? The resigned agent will not be able to continue to issue policies.", "Are you sure to delete this bank and all relevant information?": "Are you sure to delete this bank and all relevant information?", "Are you sure to delete this branch and all relevant information?": "Are you sure to delete this branch and all relevant information?", "Are you sure to delete this branch?": "Are you sure to delete this branch?", "Are you sure to delete this broker company and all relevant information?": "Are you sure to delete this broker company and all relevant information?", "Are you sure to delete this headquarter and all relevant branch information?": "Are you sure to delete this headquarter and all relevant branch information?", "Are you sure to delete this record?": "Are you sure to delete this record?", "Are you sure to delete this region and all relevant branch information?": "Are you sure to delete this region and all relevant branch information?", "Are you sure to delete this relationship info?": "Are you sure to delete this relationship info?", "Are you sure to delete this retail and all relevant region/branch information?": "Are you sure to delete this retail and all relevant region/branch information?", "Are you sure to delete?": "Are you sure to delete?", "Are you sure to Submit?": "Are you sure to Submit?", "Assessment Company Code": "Assessment Company Code", "Assessment Company Comments": "Assessment Company Comments", "Assessment Company Name": "Assessment Company Name", "Associated": "Associated", "at the Same Level": "at the Same Level", "at the Sub Level": "at the Sub Level", "Attachment": "Attachment", "Back": "Back", "Back to task pool": "Back to task pool", "Bank": "Bank", "bank account": "bank account", "Bank Code": "Bank Code", "Bank Name": "Bank Name", "Base on Sales Channel": "Base on Sales Channel", "Basic Info": "Basic Info", "Basic Information": "Basic Information", "Batch Number": "Batch Number", "Batch tips Date Range": "{{type}} scope will be business data in every {{extractionDuration}} {{extractionFrequency}} from {{periodStartDate}}, and will be {{operationType}} in the {{extractionScheduleDate}} of each period.", "Batch tips Day": "{{type}} scope will be business data in {{frequency}}, and will be {{operationType}} in the next day.", "Batch Upload": "Batch Upload", "Belonging Agency": "Belonging Agency", "Blank Template": "Blank Template", "Blank Template: An upload template that does not contain any data. Users can add new data to create, edit, or delete employee information and its internal details.'": "Blank Template: An upload template that does not contain any data. Users can add new data to create, edit, or delete employee information and its internal details.'", "Blank Template: An upload template that does not contain any data. Users can add new data to create, edit, or delete partner information and its internal details (deletion applies only to some related information).": "Blank Template: An upload template that does not contain any data. Users can add new data to create, edit, or delete partner information and its internal details (deletion applies only to some related information).", "BP Number in SAP": "BP Number in SAP", "Branch": "Branch", "Branch Code": "Branch Code", "Branch Information": "Branch Information", "Branch Manager Information": "Branch Manager Information", "Branch manager is existing, you can’t add more branch manager": "Branch manager is existing, you can’t add more branch manager.", "Branch Name": "Branch Name", "Broker Company": "Broker Company", "Broker Company Code": "Broker Company Code", "Broker Company Name": "Broker Company Name", "Business Activity Description": "Business Activity Description", "Cancel": "Cancel", "Category": "Category", "Certificate No": "Certificate No", "Certificate Type": "Certificate Type", "Change the status failed.": "Change the status failed.", "Change the status successfully.": "Change the status successfully.", "Changing the classification will clear all the details. Confirm to change?": "Changing the classification will clear all the details. Confirm to change?", "Changing the goods name will affect the selling authority, please confirm.": "Changing the goods name will affect the selling authority, please confirm.", "channel": "Channel", "Channel Code": "Channel Code", "Channel Name": "Channel Name", "Channel No": "Channel No", "channel.common.required": "{{- label}} is required", "Clawback calculation method is configured here while where to trigger clawback calculation is defined in business module configuration such as POS item agreement.": "Clawback calculation method is configured here while where to trigger clawback calculation is defined in business module configuration such as POS item agreement.", "Click or drag the file here to upload": "Click or drag the file here to upload", "Click to add channel/insurance company": "Click to add channel/insurance company", "Click to add relationship": "Click to add relationship", "Close": "Close", "Code": "Code", "Collapse": "Collapse", "collection service fee(%)": "Collection Service Fee(%)", "Comments": "Comments", "Commission Collection Service Fee(%)": "Commission Collection Service Fee(%)", "Commission Settlement Method": "Commission Settlement Method", "Commission Type": "Commission Type", "Commission&Commission Clawback Rule Configuration": "Commission&Commission Clawback Rule Configuration", "Complete Data Set": "Complete Data Set", "Complete Data Set: An upload template that contains all data. Users can select portions of this data for processing.": "Complete Data Set: An upload template that contains all data. Users can select portions of this data for processing.", "Completed": "Completed", "Condition Query": "Condition Query", "Configuration": "Configuration", "Configure the association between fields": "Configure the association between fields", "Confirm": "Confirm", "Contact Number": "Contact Number", "Contact Person": "Contact Person", "Contact Person ID": "Contact Person's ID", "Contact Person ID Type": "Contact Person's ID Type", "Contact Person Information": "Contact Person Information", "Contacts Name": "Contacts Name", "Contacts Person": "Contacts Person", "Contacts Person Birthday": "Contacts Person Birthday", "Contacts Person Type": "Contacts Person Type", "Content": "Content", "Contract Service": "Contract Service", "Country": "Country", "Country Code": "Country Code", "Create": "Create", "Create Time": "Create Time", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Current Work Schedule": "Current Work Schedule", "Custom Cycle": "Custom Cycle", "Customized Query Conditions Setting": "Customized Query Conditions Setting", "Data cannot be selected repeatedly": "Data cannot be selected repeatedly", "Date of Birth": "Date of Birth", "Day(s)": "Day(s)", "Default Settlement Method": "Default Settlement Method", "Delete": "Delete", "Delete successfully": "Delete successfully", "Department": "Department", "Department & Doctor": "Department & Doctor", "Department Code": "Department Code", "Department Name": "Department Name", "Department Type": "Department Type", "Description": "Description", "Details": "Details", "Diagnosis": "Diagnosis", "Display Name": "Display Name", "Doctor": "Doctor", "Doctor ID type": "Doctor ID type", "Doctor Name": "Doctor Name", "Document": "Document", "Does the settled commission need to be transferred to the new sales channel": "Does the settled commission need to be transferred to the new sales channel", "Does the settled commission need to be transferred to the new sales channel?": "Does the settled commission need to be transferred to the new sales channel?", "download": "download", "Download": "Download", "Download Template": "Download Template", "Download to View Details": "Download to View Details", "Duplicate formula configuration for same formula category and sub category. Please edit original one.": "Duplicate formula configuration for same formula category and sub category. Please edit original one.", "During from": "During from", "E-mail": "E-mail", "Edit": "Edit", "Edit Account Information Settings": "Edit Account Information Settings", "Edit Address": "Edit Address", "Edit Agent": "Edit Agent", "Edit Branch": "Edit Branch", "Edit Branch Information": "Edit Branch Information", "Edit Branch Manager": "Edit Branch Manager", "Edit Contact Person Information": "Edit Contact Person Information", "Edit Department Information": "Edit Department Information", "Edit Doctor information": "Edit Doctor information", "Edit Employee Information": "Edit Employee Information", "Edit Group": "Edit Group", "Edit Headquarter Information": "Edit Headquarter Information", "Edit License Type": "Edit License Type", "Edit Manager": "Edit Manager", "Edit Reconciliation": "Edit Reconciliation", "Edit Relative Information": "Edit Relative Information", "Edit Sales Agreement": "Edit Sales Agreement", "Edit Service Agreement": "Edit Service Agreement", "Edit Staff information": "Edit Staff information", "Edit Team": "Edit {{team}}", "Edit Team Information": "Edit {{team}} Information", "Edit Team Manager": "Edit {{team}} Manager", "Edited successfully": "Edited successfully", "Effective": "Effective", "Effective Date": "Effective Date", "Effective Period": "Effective Period", "Email": "Email", "Email is invalid": "<PERSON><PERSON> is invalid", "Employee": "Employee", "Employee Code": "Employee Code", "Employee Department Name": "Department Name", "Employee Information": "Employee Information", "Employee Management": "Organization & Employee Management", "Employee Name": "Employee Name", "EmployeeBranch": "Branch", "End of month (default value: 31st)": "※ End of month (default value: 31st)", "Error": "Error", "Error Detail": "<PERSON><PERSON><PERSON>", "EU member": "EU member", "Every": "Every", "Every {{dayInterval}} day {{time}}": "Every {{dayInterval}} day {{time}}", "Every {{n}}": "Every {{n}}", "Every {{weekInterval}} weeks on {{weekday}}": "Every {{weekInterval}} weeks on {{weekday}}", "Every day {{time}}": "Every day {{time}}", "Every other day {{time}}": "Every other day {{time}}", "Every other week on {{weekday}}": "Every other week on {{weekday}}", "Every week on {{weekday}}": "Every week on {{weekday}}", "everyday": "everyday", "Exclusive Agency or Not": "Exclusive Agency or Not", "Existed rows": "Existed rows: {{num}}", "Expand All": "Expand All", "Experienced Fee": "Experienced Fee", "Experienced Fee is required; {{name}} is required": "Experienced Fee is required; {{name}} is required", "Expiry Date": "Expiry Date", "External Code": "External Code", "External Insurance Company": "External Insurance Company", "External Insurance Company Code": "External Insurance Company Code", "External Insurance Company Name": "External Insurance Company Name", "Extraction Day": "Extraction Day", "Extraction Duration": "Extraction Duration", "Extraction Frequency": "Extraction Frequency", "Extraction Schedule Date": "Extraction Schedule Date", "Fax Number": "Fax Number", "File Creator": "File Creator", "file name": "file name", "File Name": "File Name", "File Path": "File Path", "File Upload Time": "File Upload Time", "Fill in the existing sales agreement": "Fill in the existing sales agreement", "Fill Information": "Fill Information", "Financial Institutions": "Financial Institutions", "Financial Institutions Branch": "Financial Institutions Branch", "First Name": "First Name", "Fixed Rate": "Fixed Rate", "Formula Category": "Formula Category", "Formula Category (Clawback)": "Formula Category (Clawback)", "Formula Code": "Formula Code", "Formula Name": "Formula Name", "Formula Name (Clawback)": "Formula Name (Clawback)", "Formula Sub Category": "Formula Sub Category", "Formula Sub Category (Clawback)": "Formula Sub Category (Clawback)", "Frequency": "Frequency", "Front Line Channel": "Front Line Channel", "Front Line Channel Code": "Front Line Channel Code", "Front Line Channel Name": "Front Line Channel Name", "Frozen": "Frozen", "Full Re-generation": "Full Re-generation", "Gender": "Gender", "Goods": "Goods", "Goods Name": "Goods Name", "Group": "Group", "Group By Selection": "Group By Selection", "Group Name": "Group Name", "GST Registered Organization": "GST Registered Organization", "GST Registration Number": "GST Registration Number", "Headquarter": "Headquarter", "Headquarter Code": "Headquarter Code", "Headquarter Information": "Hea,dquarter Information", "Headquarter Name": "Headquarter Name", "History": "History", "Hotline": "Hotline", "IBAN": "IBAN", "ID No": "ID No", "ID Number": "ID Number", "ID Type": "ID Type", "If Commission GST is configured based on NetCommission, then we could configure as": "If Commission GST is configured based on NetCommission, then we could configure as", "If other sales channels have used this sales agreement, editing it will impact the related sales channels. Please check before making changes.": "If other sales channels have used this sales agreement, editing it will impact the related sales channels. Please check before making changes.", "If some months do not have this date, the configured date automatically becomes the last day of the month.": "If some months do not have this date, the configured date automatically becomes the last day of the month.", "If the switch turns on, sub-channel data will be reconciled together.": "If the switch turns on, sub-channel data will be reconciled together.", "If the value is equal to Yes, the premium collection or refund will through Claim.": "If the value is equal to Yes, the premium collection or refund will through Claim.", "If turn on the switch, then all historical un-settled data will be included.": "If turn on the switch, then all historical un-settled data will be included.", "in": "in", "In": "In", "In Progress": "In Progress", "In Total": "In Total", "Individual or Organization Agency": "Individual or Organization Agency", "Institute": "Institute", "Institute Code": "Institute Code", "Institute Grade": "Institute Grade", "Institute Name": "Institute Name", "Institute Status": "Institute Status", "Institute Type": "Institute Type", "Insurance Company": "Insurance Company", "Insurance Company Code": "Insurance Company Code", "Insurance Company Name": "Insurance Company Name", "Insurance Company No": "Insurance Company No", "Introduction": "Introduction", "Invalid rows": "Invalid rows: {{num}}", "Investigation Area": "Investigation Area", "Investigation Hospital": "Investigation Hospital", "Investigator": "Investigator", "Investigator Code": "Investigator Code", "Issue Date": "Issue Date", "Issue without payment": "Issue without payment", "It should be noted that switching the agent sales hierarchy will cause the agent information to be cleared.": "It should be noted that switching the agent sales hierarchy will cause the agent information to be cleared.", "Key Account Channel": "Key Account Channel", "Key Account Channel Code": "Key Account Channel Code", "Key Account Channel Name": "Key Account Channel Name", "Last Name": "Last Name", "Leasing Channel": "Leasing Channel", "Leasing Channel Code": "Leasing Channel Code", "Leasing Channel Name": "Leasing Channel Name", "Legal Service Company": "Legal Service Company", "Legal Service Company Code": "Legal Service Company Code", "Legal Service Company Name": "Legal Service Company Name", "Level": "Level", "License Effective Date": "License Effective Date", "License No.": "License No.", "loading": "loading", "Login Account Type": "Login Account Type", "Logo": "Logo", "Long Term": "Long Term", "Malaysia Standard Industrial Classification (MSIC) Code": "Malaysia Standard Industrial Classification (MSIC) Code", "Manager": "Manager", "Mapping with Organization": "Mapping with Organization", "Mobile Phone": "Mobile Phone", "month": "month", "Monthly": "Monthly", "months": "months", "More Partners below": "{{count}} More Partners below", "Name": "Name", "Name is required": "Name is required", "Name of Account Holder": "Name of Account Holder", "Name_Code": "{{name}}_{{code}}", "Nationality": "Nationality", "Native Account": "Native Account", "Network": "Network", "New Sales Agreement": "New Sales Agreement", "New Sales Channel": "New Sales Channel", "next": "next", "Next": "Next", "Next {{n}}": "Next {{n}}", "No": "No", "No content yet": "No content yet", "No search results found": "No search results found", "Number of Records": "Number of Records", "of the Current Month": "of the Current Month", "of the Next 2 Month": "of the Next 2 Month", "of the Next 2 Week": "of the Next 2 Week", "of the Next Month": "of the Next Month", "of the Next Week": "of the Next Week", "Office City": "Office City", "Office Hour": "Office Hour", "On": "On", "On {{date}} of every {{yearInterval}} year": "On {{date}} of every {{yearInterval}} year", "On {{date}} of every other year": "On {{date}} of every other year", "On {{date}} of every year": "On {{date}} of every year", "On day {{date}} of every {{monthInterval}} months": "On day {{date}} of every {{monthInterval}} months", "On day {{date}} of every month": "On day {{date}} of every month", "On day {{date}} of every other month": "On day {{date}} of every other month", "Onboard Date": "Onboard Date", "onRecurrenceDate": "on {{recurrenceDate}} day.", "Opening Date": "Opening Date", "Operated successfully": "Operated successfully", "Operation Time": "Operation Time", "Operator": "Operator", "Order": "Order", "Organization ID No.": "Organization ID No.", "Organization ID Type": "Organization ID Type", "Original Sales Agreement Code": "Original Sales Agreement Code", "Original Sales Channel Code": "Original Sales Channel Code", "Other Insurance Company Name": "Other Insurance Company Name", "Other Related Business": "Other Related Business", "out": "out", "Out": "Out", "Overriding Commission": "Overriding Commission", "Panel Hospital": "Panel Hospital", "Params are invalid": "Params are invalid", "Partial Re-generation": "Partial Re-generation", "Partner": "Partner", "Partner Code": "Partner Code", "Partner Details": "Partner Details", "Partner List": "Partner List", "Partner List Upload History": "Partner List Upload History", "Partner Management": "Partner Management", "Partner Name": "Partner Name", "Partner Type": "Partner Type", "Pay Method": "Pay Method", "Payment Method": "Payment Method", "Payment Method / Account Type": "Payment Method / Account Type", "Payment/Collection": "Payment/ Collection", "Payment/Collection Method": "Payment/Collection Method", "Period of Validity": "Period of Validity", "Period Start Date": "Period Start Date", "period_validity": "Period of Validity", "Personal Work Schedule": "Personal Work Schedule", "Phone No.": "Phone No.", "Phone Number": "Phone Number", "Please complete branch information first.": "Please complete branch information first.", "Please complete Team Information first": "Please complete {{team}} information first.", "Please configure insurance company, agency, sales channel and relationship before saving": "Please configure insurance company, agency, sales channel and relationship before saving", "Please create the basic information first.": "Please create the basic information first.", "Please Download Template First": "Please <1>Download Template</1> First", "Please enter a valid character": "Please enter a valid character", "Please enter at least one condition": "Please enter at least one condition", "Please enter at least one search criteria": "Please enter at least one search criteria", "Please finish editing first": "Please finish editing first", "Please Input": "Please input", "Please input correct firstname.": "Please input correct firstname.", "Please input correct format": "Please input correct format", "Please input correct lastname.": "Please input correct lastname.", "Please input Employee Code/Name": "Please input Employee Code/Name", "Please input in correct format": "Please input in correct format", "Please input Sales Channel Code/Name": "Please input Sales Channel Code/Name", "Please Select": "Please select", "Please Select a company": "Please Select a company", "Please Select a Sales Channel": "Please Select a Sales Channel", "Please select a sales channel first.": "Please select a sales channel first.", "Please select a specific date first.": "Please select a specific date first.", "Please select an employee first.": "Please select an employee first.", "Please select an Organization ID Type": "Please select an Organization ID Type", "Please select correct upload template(xlsx)": "Please select correct upload template(xlsx)", "Please select related formula for Commission": "Please select related formula for Commission", "Please select related formula for Commission Clawback": "Please select related formula for Commission Clawback", "Please select the number of sales hierarchy": "Please select the number of sales hierarchy", "Please select to add at least one relationship": "Please select to add at least one relationship", "Please Upload File": "Please Upload File", "Policy Effective Date": "Policy Effective Date", "Policy No.": "Policy No.", "Policy Transfer": "Policy Transfer", "Policy Transfer Effective Date": "Policy Transfer Effective Date", "Policy Transfer Reason": "Policy Transfer Reason", "Policyholder Name": "Policyholder Name", "Position": "Position", "Premium Collection and Refund for Customer": "Premium Collection and Refund for Customer", "Private": "Private", "Public": "Public", "Public/Private": "Public/Private", "Rate for Additional": "Rate for Additional", "Rate for Initial": "Rate for Initial", "Rate per KM": "Rate per KM", "Re-generation Option": "Re-generation Option", "Re-Upload": "Re-Upload", "reconciled": "reconciled", "Reconciliation": "Reconciliation", "Reconciliation Code": "Reconciliation Code", "Reconciliation Configuration": "Reconciliation Configuration", "Reconciliation Data: data that already reconciled.": "Reconciliation Data: data that already reconciled.", "Reconciliation Data: data that already reconciled. Reconciliation Data: data that already reconciled": "Reconciliation Data: data that already reconciled. Reconciliation Data: data that already reconciled", "Reconciliation Detail": "Reconciliation Detail", "Reconciliation Fields": "Reconciliation Fields", "Reconciliation File Format": "Reconciliation File Format", "Reconciliation Frequency": "Reconciliation Frequency", "Reconciliation Goods Code": "Reconciliation Goods Code", "Reconciliation Method": "Reconciliation Method", "Reconciliation Method & File": "Reconciliation Method & File", "Reconciliation Period": "Reconciliation Period", "Reconciliation Policy Dimension": "Reconciliation Policy Dimension", "Reconciliation Recurrence": "Reconciliation Recurrence", "Reconciliation Recurrence Date": "Reconciliation Recurrence Date", "Reconciliation Result Display": "Reconciliation Result Display", "Reconciliation Time Zone": "Reconciliation Time Zone", "Reconciliation& Settlement": "Reconciliation& Settlement", "Reconciliation& Settlement Configuration": "Reconciliation& Settlement Configuration", "Reconciliation&Settlement": "Reconciliation&Settlement", "Reconciliation&Settlement Configuration": "Reconciliation&Settlement Configuration", "Record": "Record", "Records": "Records", "Records / Number of total records": "Records / Number of total records", "Records_TotalRecords": "{{records}}/{{totalRecords}}", "Recurrence Date": "Recurrence Date", "Region": "Region", "Register": "Register", "Registration": "Registration", "Registration Date": "Registration Date", "Relation Setting": "Relation Setting", "Relationship": "Relationship", "Relationship Basic Info": "Relationship Basic Info", "Relationship Code": "Relationship Code", "Relationship List": "Relationship List", "Relationship Management": "Relationship Management", "Relationship Name": "Relationship Name", "Relationship No.": "Relationship No.", "Relationship of Agency & Channel": "Relationship of Agency & Channel", "Relationship of Insurance Company & Agency": "Relationship of Insurance Company & Agency", "Relationship of Insurance Company & Service Company": "Relationship of Insurance Company & Service Company", "Relationship Setting": "Relationship Setting", "Relationship With Employee": "Relationship With Employee", "Relative": "Relative", "Relative Name": "Relative Name", "Remark": "Remark", "Repeat Every": "Repeat Every", "Resign Date": "Resign Date", "Response Message": "Response Message", "Result": "Result", "Retail": "Retail", "Saleable Insurance Product Type": "Saleable Insurance Product Type", "Sales Agreement": "Sales Agreement", "Sales Agreement Code": "Sales Agreement Code", "Sales Agreement List": "Sales Agreement List", "Sales Agreement Name": "Sales Agreement Name", "Sales Agreement Name/Code": "Sales Agreement Name/Code", "Sales Agreement Signing Party": "Sales Agreement Signing Party", "Sales Agreement Status": "Sales Agreement Status", "Sales Agreement(After Change)": "Sales Agreement(After Change)", "Sales Agreement(Before Change)": "Sales Agreement(Before Change)", "Sales Authority Information": "Sales Authority Information", "Sales Channel": "Sales Channel", "Sales Channel Code": "Sales Channel Code", "Sales Channel Information": "Sales Channel Information", "Sales Channel Name": "Sales Channel Name", "Sales Channel Name/Code": "Sales Channel Name/Code", "Sales Channel Type": "Sales Channel Type", "Sales Channel(After Change)": "Sales Channel(After Change)", "Sales Channel(Before Change)": "Sales Channel(Before Change)", "Sales Hierarchy": "Sales Hierarchy", "Sales License Type": "Sales License Type", "Sales Platform": "Sales Platform", "Sales Related Information": "Sales Related Information", "Same Level": "Same Level", "Save": "Save", "Save Success": "Save Success", "Save successfully!": "Save successfully!", "Search": "Search", "Search by name": "Search by name", "Search Criteria {{n}}": "Search Criteria ({{n}})", "Select All": "Select All", "Select Employee": "Select Employee", "Select Employee or Channel Staff": "Select Employee or Channel Staff", "Select Reconciliation": "Select Reconciliation", "Selected Reconciliation Code": "Selected Reconciliation Code", "Self Agent": "Self Agent", "Self-agency": "Self-agency", "Self-channel": "Self-channel", "Service Agreement": "Service Agreement", "Service Agreement Code": "Service Agreement Code", "Service Agreement information has been updated. Please submit a new service agreement code.": "Service Agreement information has been updated. Please submit a new service agreement code.", "Service Agreement Name": "Service Agreement Name", "Service Agreement Status": "Service Agreement Status", "Service Company": "Service Company", "Service Company Code": "Service Company Code", "Service Company Name": "Service Company Name", "Service Related Information": "Service Related Information", "Setting Code": "Setting Code", "settled": "settled", "settlement": "settlement", "Settlement": "Settlement", "Settlement Configuration": "Settlement Configuration", "Settlement Currency": "Settlement Currency", "Settlement File Format": "Settlement File Format", "Settlement Frequency": "Settlement Frequency", "Settlement Method": "Settlement Method", "Settlement Method Category": "Settlement Method Category", "Settlement Only": "Settlement Only", "Settlement Option": "Settlement Option", "Settlement Period": "Settlement Period", "Settlement Recurrence": "Settlement Recurrence", "Settlement Recurrence Date": "Settlement Recurrence Date", "Settlement Rule": "Settlement Rule", "Settlement Scope": "Settlement Scope", "Settlement Sources": "Settlement Sources", "Settlement Time Type": "Settlement Time Type", "Settlement Time Zone": "Settlement Time Zone", "Settlement Type": "Settlement Type", "Short Institute Name": "Short Institute Name", "Sorry, the transfer can't be conducted, because failed to find the same  Agent Type, Sales Channel Code or Sales Agreement Code on the chosen policies, please check again.": "Sorry, the transfer can't be conducted, because failed to find the same  Agent Type, Sales Channel Code or Sales Agreement Code on the chosen policies, please check again.", "Source Data: data that do not need reconciliation.": "Source Data: data that do not need reconciliation.", "SSO Account": "SSO Account", "SST Registration Number": "SST Registration Number", "Staff": "Staff", "Staff Code": "Staff Code", "Staff ID": "Staff ID", "Staff Name": "Staff Name", "StaffId is required": "StaffId is required", "Start from": "Start from", "Status": "Status", "Structure Details": "Structure Details", "Sub Bill Item": "Sub <PERSON>", "Sub Level": "Sub Level", "Sub-level Usage Permission": "Sub-level Usage Permission", "Submit": "Submit", "Surgery": "Surgery", "SWIFT Code": "SWIFT Code", "System will delete the hospital and the doctors, experienced fee under the hospital. Confirm to delete?": "System will delete the hospital and the doctors, experienced fee under the hospital. Confirm to delete?", "System will delete the investigator. Confirm to delete?": "System will delete the investigator. Confirm to delete?", "t - 1": "t - 1", "Taxpayer ID number": "Taxpayer ID number", "Team Code": "Team Code", "Team Information": "{{team}} Information", "Team Manager": "{{team}} Manager", "Team Manager Information": "{{team}} Manager Information", "Team manager is existing, you can’t add more team manager": "{{team}} manager is existing, you can’t add more {{lowerCaseTeam}} manager.", "Team Name": "Team Name", "Team-Code": "{{team}} Code", "Team-Name": "{{team}} Name", "Telephone": "Telephone", "Terminated": "Terminated", "The new added address information cannot be empty": "The new added address information cannot be empty.", "The new added Contact Person Information cannot be empty": "The new added Contact Person Information cannot be empty.", "The policy has been transferred successfully.": "The policy has been transferred successfully.", "The same partner already exists. If you click \"Save\", the new information will overwrite the old information, please confirm.": "The same partner already exists. If you click \"Save\", the new information will overwrite the old information, please confirm.", "The same partner already exists. If you click Save\"": "The same partner already exists. If you click Save\"", "The same partner already exists. If you click Save\",The same partner already exists. If you click Save\"\"": "The same partner already exists. If you click Save\",The same partner already exists. If you click Save\"\"", "The upload file size is too large. Upload failed.": "The upload file size is too large. Upload failed.", "The vacation end time needs to be after the current time.": "The vacation end time needs to be after the current time.", "There is a duplicate department name in the newly added information, please confirm.Please": "There is a duplicate department name in the newly added information, please confirm.", "There is a duplicate department name in the uploaded file. Upload fails.": "There is a duplicate department name in the uploaded file. Upload fails.", "There is still required information that has not been saved, please fill in all required information first!": "There is still required information that has not been saved, please fill in all required information first!", "Third-party assessment company user name": "Third-party assessment company user name", "This flow will run": "This flow will run", "Tied Agent": "Tied Agent", "TIN": "TIN", "Tips": "Tips", "Title": "Title", "to": "to", "To config the columns that displayed in downloaded reconciliation result file.": "To config the columns that displayed in downloaded reconciliation result file.", "To config the reconciliation fields, only when all selected fields were matched, the reconciled record is marked as Matched.": "To config the reconciliation fields, only when all selected fields were matched, the reconciled record is marked as Matched.", "To config the unique key to directing one same business transaction within provided reconciliation file and local data source.": "To config the unique key to directing one same business transaction within provided reconciliation file and local data source.", "Today": "Today", "Total upload number": "Total upload number: {{num}}", "Total:": "Total: {{total}} Data", "Transaction Business Type": "Transaction Business Type", "Transaction Type": "Transaction Type", "Unique Combination Fields": "Unique Combination Fields", "Unknown error, Please check your file or retry later": "Unknown error, Please check your file or retry later", "Upload": "Upload", "Upload Date": "Upload Date", "Upload failed information": "Upload failed information:", "Upload File": "Upload File", "Upload Record": "Upload Record", "Upload Size Limit": "Please upload a file with maximum size of {{size}}MB.", "Upload successfully number": "Upload successfully number: {{num}}", "Uploaded failed": "Uploaded failed", "Uploaded successfully": "Uploaded successfully", "Uploading files will overwrite historical data, please confirm whether it needs to be modified.": "Uploading files will overwrite historical data, please confirm whether it needs to be modified.", "Vacation Date": "Vacation Date", "VAT Number": "VAT Number", "View": "View", "View Account Information Settings": "View Account Information Settings", "View Address": "View Address", "View Agent": "View Agent", "View Branch": "View Branch", "View Branch Manager": "View Branch Manager", "View Contact Person Information": "View Contact Person Information", "View Department Information": "View Department Information", "View Doctor information": "View Doctor information", "View Employee Information": "View Employee Information", "View Headquarter Information": "View Headquarter Information", "View Manager": "View Manager", "View Relative Information": "View Relative Information", "View Sales Agreement": "View Sales Agreement", "View Service Agreement": "View Service Agreement", "View Staff information": "View Staff information", "View Structure": "View Structure", "View Team": "View {{team}}", "View Team Manager": "View {{team}} Manager", "Warning": "Warning", "Website": "Website", "week": "week", "Weekly": "Weekly", "weeks": "weeks", "Whether Base on Sales Channel": "Whether Base on Sales Channel", "Whether including all historical data": "Whether including all historical data", "Whether Settlement": "Whether Settlement", "Whether to create {{name}} account?": "Whether to create {{name}} account?", "Whether to generate debit note details list?": "Whether to generate debit note details list?", "Whether to Include 2nd level Channel": "Whether to Include 2nd level Channel", "Work Schedule Management": "Work Schedule Management", "Working Status": "Working Status", "Working Type": "Working Type", "Working Type Detail": "Working Type Detail", "Workload Rate": "Workload Rate", "Yes": "Yes", "You can only upload  CSV/EXCEL/JPG/WORD/PDF": "You can only upload  CSV/EXCEL/JPG/WORD/PDF", "You can only upload PDF/DOC/XLS/PNG/JPG": "You can only upload PDF/DOC/XLS/PNG/JPG", "You need to select specific agent, please check.": "You need to select specific agent, please check.", "You should choose a policy first!": "You should choose a policy first!", "Your file has been submitted. Please wait patiently for the result.": "Your file has been submitted. Please wait patiently for the result.", "Zip Code": "Zip Code"}