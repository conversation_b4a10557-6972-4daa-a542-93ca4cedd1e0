/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

<!--
 * @Autor: za-tanyouwei
 * @Date: 2023-09-04 17:33:07
 * @LastEditors: za-tanyouwei
 * @LastEditTime: 2023-09-05 10:17:17
 * @Description: 
-->
# umi project

# partner 微前端声明
channel-partner 相关的功能转移到该模块

## Getting Started
Install dependencies,

```bash
$ pnpm i
```

step1: Start the dev server: `pnpm start` or `pnpm start:dev`

step2: open new terminal (only for `pnpm start` in step1)
```bash
$ cd genesis-base-web
$ pnpm start
```
step3: 页面使用localhost:8080启动
